function result = run(scenario_path,scenario_name,ue_num,spilt_ue,indoor,set_los,nfloor,bs_isd,myseed,schedule,no_snapshots)

warning('off','all');
    
scenario_path = '/home/<USER>/quadriga/ne_v0/';
scenario_name = '3GPP_38.901_InF_SH'; % 3GPP_38.901_UMi_NLOS, 3GPP_38.901_UMi_LOS, 3GPP_38.901_UMa_NLOS, 3GPP_38.901_UMa_LOS, O2I
bs_num = int64(3); % 增加基站数量以支持邻区，采用7个基站的六边形布局
ue_num = int64(1000);
spilt_ue = int64(1);
spilt_ue_num = int64(10);
spilt_ue_seg = int64(ue_num/spilt_ue_num);
disp("start run");
disp(datetime);

speed = 300/3.6; %3km/h->m/s
time_unit = 0.02; %s
angle_base_array = (rand(1,spilt_ue_num)*2-1)*pi/6;
dist_base_array = round(rand(1,spilt_ue_num)*10)+15;

% 为不同UE设置不同的移动角度
angle_speed_array = [-pi/3, -pi/6, 0, pi/6, pi/3]; % 多种移动方向

indoor = 1; % indoor=1, outdoor=0
no_snapshots = 1;
myseed = 0723;
set_los = false;
schedule = true; %设置是否选择最佳cell

A3_OFFSET = 0; % dB，降低偏移量，更容易触发切换
A3_HYSTERESIS = 0.5; % dB，适当的滞后防止频繁切换
A3_TIME_TO_TRIGGER = 0.02; % s，稍微增加触发时间确保稳定
A3_MEASUREMENT_GAP = 0.004; % s，测量间隔（6ms）

addpath(rmpath('./config'));
addpath(genpath('./config'));

my_rng = rng;
if myseed > 0
    seed = int64(myseed);
else
    seed = my_rng.State(1,1);
end
rng(seed);

floder = scenario_path;

%仿真参数配置
s = qd_simulation_parameters;
s.center_frequency = 28e9;
s.use_3GPP_baseline = 0; %增强3GPP，能够支持移动
s.show_progress_bars = 0;%显示进度条
scenario = scenario_name;
scenario_base = scenario(1:15);
num_spilt = ue_num/spilt_ue;%spilt ue

%通信参数
BW = 48960e3*4; %10M频带 BW=60KHZ*12sc*16prb*17sub
nSub = 17; %子载波个数
indoor_percent = indoor; %室内用户比例

%基站和用户
no_bs = bs_num; %bs个数 
no_rx = ue_num; %用户数量

no_go_dist = 1; 
bs_height = 8; 
bs_isd = 5; % uma=500, umi=200

isd = bs_isd; %最大范围,uma=500,umi=200

%天线面板配置
downtilt = 0;
H_ants = 8;
V_ants = 4;
V_d = 0.5;
UE_H_ants = 1;
UE_V_ants = 2;
UE_port = UE_H_ants*UE_V_ants*2;

%模型选择，垂直天线数，水平天线数，中心频率，极化方式，下倾，天线间的间距，垂直面板个数，水平面板个数，垂直面板间距，水平面板间距
BS_array  = qd_arrayant( '3gpp-mmw', 1, 1, s.center_frequency, 3, downtilt, 0.5, V_ants, H_ants, V_d, 0.5 ); 
UE_array = qd_arrayant('3gpp-mmw', 1, 1, s.center_frequency, 3, 0, 0.5, UE_V_ants, UE_H_ants, 0.5, 0.5);

cell_type = 'indoor'; %选择基站产生的小区类型

% 设置移动参数
spilt_ue_speed = speed;
spilt_ue_time_unit = time_unit;
time = time_unit*(no_snapshots - 1);
move_len = time*speed;
if no_snapshots > 1
    move_enable = true;
else
    move_enable = false;
end

% 创建输出文件
file_H_time = fopen(strcat(floder,'H_time.txt'),'w');
file_H_freq = fopen(strcat(floder,'H_freq.txt'),'w');
file_log = fopen(strcat(floder,'log.txt'),'w');
file_loc = fopen(strcat(floder,'loc.txt'),'w');
file_pg = fopen(strcat(floder,'pg.txt'),'w');
file_pg_all = fopen(strcat(floder,'pg_all.txt'),'w');
% 新增邻区相关文件
file_neighbor_H_time = fopen(strcat(floder,'neighbor_H_time.txt'),'w');
file_neighbor_H_freq = fopen(strcat(floder,'neighbor_H_freq.txt'),'w');
file_neighbor_pg = fopen(strcat(floder,'neighbor_pg.txt'),'w');
file_serving_bs_pos = fopen(strcat(floder,'serving_bs_pos.txt'),'w');
file_neighbor_bs_pos = fopen(strcat(floder,'neighbor_bs_pos.txt'),'w');
file_cell_selection = fopen(strcat(floder,'cell_selection.txt'),'w');
% A3协议相关文件
file_a3_events = fopen(strcat(floder,'a3_events.txt'),'w');
file_handover_log = fopen(strcat(floder,'handover_log.txt'),'w');

fprintf(file_log,scenario_base);
fprintf(file_log,"\n");
fprintf(file_log,"random seed is %d\n",seed);
fprintf(file_log,"BS cell number is %d\n",no_bs);
fprintf(file_log,"UE number is %d\n",no_rx);
fprintf(file_log,"A3 Offset: %.1f dB\n", A3_OFFSET);
fprintf(file_log,"A3 Hysteresis: %.1f dB\n", A3_HYSTERESIS);
fprintf(file_log,"A3 Time-to-Trigger: %.3f s\n", A3_TIME_TO_TRIGGER);

% Remove the problematic fprintf line that was causing the error
% fprintf(file_log,'UE%d: Current_BS%d(%.2fdBm) vs Best_Neighbor_BS%d(%.2fdBm) Diff:%.2fdB\n', ...
%         ue_idx, current_serving_bs, current_rsrp, best_neighbor_idx, max_neighbor_rsrp, ...
%         max_neighbor_rsrp - current_rsrp);

if schedule == true
    fprintf(file_log,"BS schedule is true\n");
end
fprintf(file_log,"H_ants is %d\n",H_ants);
fprintf(file_log,"V_ants is %d\n",V_ants);
fprintf(file_log,"UE Port is %d\n",UE_port);
fprintf(file_log,"nSub is %d\n",nSub);
fprintf(file_log,"sim time unit is %d\n",time_unit*1000);
fprintf(file_log,"sim time number is %d\n",no_snapshots);
fprintf(file_log,"sim par of cm\n");
fprintf(file_log,"UE are divided into %d parts\n",int64(num_spilt));
fprintf(file_log,"parts are divided into %d groups\n",int64(spilt_ue_num));
fprintf(file_log,"UE are divided by %d\n",int64(spilt_ue));

% 创建场景 - 使用七个基站的六边形布局
scene = qd_layout.generate('indoor', no_bs, isd, BS_array);
scene.simpar = s;

% 设置基站位置 - 六边形布局
% 中心基站
scene.tx_position(:,1) = [0, 0, bs_height];
% 周围6个基站的位置（六边形布局）
for bs_idx = 2:7
    angle = (bs_idx-2) * pi/3;  % 每60度一个基站
    scene.tx_position(:,bs_idx) = [isd*cos(angle), isd*sin(angle), bs_height];
end

scene.name = 'setsumi_a3_handover';
scene.rx_array = UE_array;

% 记录基站位置
for bs_idx = 1:no_bs
    fprintf(file_log,"bs %d location: x: %.4f y: %.4f z: %.4f\n",bs_idx,...
            scene.tx_position(1,bs_idx),scene.tx_position(2,bs_idx),scene.tx_position(3,bs_idx));
end

% Initialize UE serving BS array and A3 protocol variables
ue_serving_bs = ones(1, no_rx);  % Initially all UEs served by BS 1
ue_a3_trigger_time = zeros(1, no_rx);  % A3事件触发时间记录
ue_a3_candidate_bs = zeros(1, no_rx);  % A3事件候选基站
ue_a3_triggered = false(1, no_rx);  % A3事件是否被触发
ue_last_measurement_time = zeros(1, no_rx);  % 上次测量时间

% A3协议函数定义
a3_handover_algorithm = @(current_rsrp, neighbor_rsrp, serving_bs_id, current_time, ue_idx, ...
    ue_a3_trigger_time, ue_a3_candidate_bs, ue_a3_triggered, A3_OFFSET, A3_HYSTERESIS, A3_TIME_TO_TRIGGER) ...
    deal_with_a3_handover(current_rsrp, neighbor_rsrp, serving_bs_id, current_time, ue_idx, ...
    ue_a3_trigger_time, ue_a3_candidate_bs, ue_a3_triggered, A3_OFFSET, A3_HYSTERESIS, A3_TIME_TO_TRIGGER);

for spilt_idx = 1:num_spilt
    no_rx = spilt_ue;
    scene.no_rx = no_rx;

    %default move away from center
    move_len = rem(spilt_idx-1,spilt_ue_seg);
    move_idx = floor((double(spilt_idx)-1)/double(spilt_ue_seg));
    move_dist = spilt_ue_speed*spilt_ue_time_unit*(double(move_len));
    angle_base = angle_base_array(move_idx+1);
    dist_base = dist_base_array(move_idx+1);
    
    % Fix the angle_speed variable issue
    angle_speed = angle_speed_array(mod(move_idx, length(angle_speed_array)) + 1);
    
    res_idx = rem( spilt_idx , 100 );
    if res_idx == 0
        fprintf("run %d datas\n",spilt_idx);
        disp(datetime);
    end

    current_time = spilt_idx * spilt_ue_time_unit;  % 当前仿真时间

    % 随机撒播UE的位置
    for ue_idx = 1: no_rx
        scene.rx_position(1,ue_idx) = dist_base*cos(angle_base) + move_dist*cos(angle_speed);
        scene.rx_position(2,ue_idx) = dist_base*sin(angle_base) + move_dist*sin(angle_speed);
        scene.rx_position(3,ue_idx) = 1.5;
    end

    % 确保UE不会太靠近基站
    for n = 1 : no_bs
        dist_r = sqrt((scene.rx_position(1,:) - scene.tx_position(1,n)).^2 + (scene.rx_position(2,:) - scene.tx_position(2,n)).^2);
        change_location = dist_r < no_go_dist;
        x_scale = scene.rx_position(1,:) - scene.tx_position(1,n);
        y_scale = scene.rx_position(2,:) - scene.tx_position(2,n);
        scene.rx_position(1,:) = scene.rx_position(1,:)+x_scale./dist_r.*(no_go_dist-dist_r).*change_location;
        scene.rx_position(2,:) = scene.rx_position(2,:)+y_scale./dist_r.*(no_go_dist-dist_r).*change_location;
    end

    % 设置移动轨迹
    if move_enable
        for n = 1 : scene.no_rx
            t = qd_track('linear',move_len,-pi/3);
            t.initial_position = scene.rx_position(:,n);
            t.movement_profile = [ 0,time;0,move_len]; 
            t.interpolate('time',0.02);
            t.name = scene.rx_name{1,n};
            scene.rx_track(1,n) = t;
        end
    end

    % 配置楼内楼层
    scene.set_scenario(scenario,[],[],indoor_percent);

    % 初始化信道建模
    b = scene.init_builder;
    sic = size(b);
    for ib = 1 : numel(b)
        [ i1,i2 ] = qf.qind2sub( sic, ib );
        scenpar = b(i1,i2).scenpar;
        b(i1,i2).scenpar_nocheck = scenpar;
    end

    b_ue = b;
    b_ue = split_multi_freq( b_ue );
    gen_parameters( b_ue );
    cm = get_channels( b_ue );

    % 重排信道矩阵
    pattern = '\S+Tx(\d+)_Rx(\d+)';
    idx = zeros(numel(cm),1);
    for cm_idx = 1:numel(cm)
        cm_name = cm(1,cm_idx).name;    
        cm_result = regexp(cm_name, pattern, 'tokens');
        tx_idx = str2num(cm_result{1,1}{1,1});
        rx_idx = str2num(cm_result{1,1}{1,2});
        txrx_idx = (tx_idx-1)*no_rx + rx_idx;
        idx(cm_idx) = txrx_idx;
    end
    cm(1,idx) = cm(1,:);

    % 计算所有基站到所有UE的RSRP（dBm）
    rsrp_all = zeros(no_bs, no_rx);
    for ue_idx = 1:no_rx
        for bs_idx = 1:no_bs
            cs_idx = (bs_idx - 1)*no_rx + ue_idx;
            cs_data = cm(1,cs_idx).coeff;
            if(move_enable)
                tmp = cs_data(:,1,:,1);
            else
                tmp = cs_data(:,1,:);
            end
            % 将线性功率转换为dBm
            rsrp_linear = sum(abs(tmp(:).^2)/2);
            rsrp_all(bs_idx,ue_idx) = 10*log10(rsrp_linear*1000); % 转换为dBm
        end
    end

    % Initialize serving BS for first iteration
    if spilt_idx == 1
        for ue_idx = 1:no_rx
            [~, best_bs] = max(rsrp_all(:, ue_idx));
            ue_serving_bs(ue_idx) = best_bs;
        end
    end

    % 为每个UE执行A3协议切换判决
    serving_bs = zeros(1, no_rx);
    neighbor_bs = zeros(1, no_rx);
    
    for ue_idx = 1:no_rx
        current_serving_bs = ue_serving_bs(ue_idx);
        current_rsrp = rsrp_all(current_serving_bs, ue_idx);
        
        % 获取所有邻区RSRP（除了当前服务基站）
        neighbor_rsrp = rsrp_all(:, ue_idx);
        neighbor_rsrp(current_serving_bs) = -inf;  % 排除当前服务基站
        
        % 计算最佳邻区（用于日志输出）
        [max_neighbor_rsrp, best_neighbor_idx] = max(neighbor_rsrp);
        
        % 检查是否需要进行测量（基于测量间隔）
        if (current_time - ue_last_measurement_time(ue_idx)) >= A3_MEASUREMENT_GAP
            ue_last_measurement_time(ue_idx) = current_time;
            
            [handover_decision, target_bs, ue_a3_trigger_time, ue_a3_candidate_bs, ue_a3_triggered] = ...
    deal_with_a3_handover(current_rsrp, neighbor_rsrp, current_serving_bs, current_time, ue_idx, ...
    ue_a3_trigger_time, ue_a3_candidate_bs, ue_a3_triggered, ...
    A3_OFFSET, A3_HYSTERESIS, A3_TIME_TO_TRIGGER);

            
            % 增加详细的调试日志
            fprintf(file_log,'Time:%.3f UE%d: Current_BS%d(%.2fdBm) vs Best_Neighbor_BS%d(%.2fdBm) Diff:%.2fdB A3_Condition:%d\n', ...
                    current_time, ue_idx, current_serving_bs, current_rsrp, best_neighbor_idx, max_neighbor_rsrp, ...
                    max_neighbor_rsrp - current_rsrp, ...
                    (max_neighbor_rsrp > (current_rsrp + A3_OFFSET + A3_HYSTERESIS)));
            
            % 记录A3事件
            if ue_a3_triggered(ue_idx)
                fprintf(file_a3_events,'Time:%.3f UE%d A3_Triggered Serving:BS%d(%.2fdBm) Candidate:BS%d(%.2fdBm) TimeSinceTriggered:%.3fs\n', ...
                        current_time, ue_idx, current_serving_bs, current_rsrp, ...
                        ue_a3_candidate_bs(ue_idx), rsrp_all(ue_a3_candidate_bs(ue_idx), ue_idx), ...
                        current_time - ue_a3_trigger_time(ue_idx));
            end
            
            % 执行切换
            if handover_decision
                fprintf(file_handover_log,'Time:%.3f UE%d Handover: BS%d -> BS%d (RSRP: %.2fdBm -> %.2fdBm)\n', ...
                        current_time, ue_idx, current_serving_bs, target_bs, ...
                        current_rsrp, rsrp_all(target_bs, ue_idx));
                ue_serving_bs(ue_idx) = target_bs;
            end
        end
        
        % 设置当前服务基站和最佳邻区基站
        serving_bs(ue_idx) = ue_serving_bs(ue_idx);
        
        % 选择最强邻区作为neighbor_bs（用于数据输出）
        neighbor_rsrp_temp = rsrp_all(:, ue_idx);
        neighbor_rsrp_temp(serving_bs(ue_idx)) = -inf;
        [~, neighbor_bs(ue_idx)] = max(neighbor_rsrp_temp);
    end
    
    serving_rsrp = zeros(1, no_rx);
    neighbor_rsrp = zeros(1, no_rx);
    for ue_idx = 1:no_rx
        serving_rsrp(ue_idx) = rsrp_all(serving_bs(ue_idx), ue_idx);
        neighbor_rsrp(ue_idx) = rsrp_all(neighbor_bs(ue_idx), ue_idx);
    end
    
    % 记录小区选择信息
    for ue_idx = 1:no_rx
        fprintf(file_cell_selection,'UE%d: Serving_BS%d(RSRP=%.2fdBm) Neighbor_BS%d(RSRP=%.2fdBm)\n', ...
                ue_idx, serving_bs(ue_idx), serving_rsrp(ue_idx), ...
                neighbor_bs(ue_idx), neighbor_rsrp(ue_idx));
    end
    
    % 提取服务小区和邻区的信道
    serving_cm_idx = (serving_bs-1)*no_rx + (1:no_rx);
    neighbor_cm_idx = (neighbor_bs-1)*no_rx + (1:no_rx);
    
    serving_cm = cm(1,serving_cm_idx);
    neighbor_cm = cm(1,neighbor_cm_idx);

    % 记录服务基站和邻区基站位置
    for ue_idx = 1:no_rx
        % 服务基站位置
        serving_bs_id = serving_bs(ue_idx);
        fprintf(file_serving_bs_pos,'%.4f %.4f %.4f\n', ...
                scene.tx_position(1,serving_bs_id), ...
                scene.tx_position(2,serving_bs_id), ...
                scene.tx_position(3,serving_bs_id));
        
        % 邻区基站位置
        neighbor_bs_id = neighbor_bs(ue_idx);
        fprintf(file_neighbor_bs_pos,'%.4f %.4f %.4f\n', ...
                scene.tx_position(1,neighbor_bs_id), ...
                scene.tx_position(2,neighbor_bs_id), ...
                scene.tx_position(3,neighbor_bs_id));
    end

    % 记录日志信息
    for cm_idx = 1:numel(serving_cm)
        fprintf(file_log,"serving cm %d name is %s,npath is %d,no_snap is %d\n",...
                cm_idx,serving_cm(1,cm_idx).name,serving_cm(1,cm_idx).no_path,serving_cm(1,cm_idx).no_snap);
    end
    
    for cm_idx = 1:numel(neighbor_cm)
        fprintf(file_log,"neighbor cm %d name is %s,npath is %d,no_snap is %d\n",...
                cm_idx,neighbor_cm(1,cm_idx).name,neighbor_cm(1,cm_idx).no_path,neighbor_cm(1,cm_idx).no_snap);
    end

    % 写入UE位置数据
    for ue_idx = 1 : no_rx
        if move_enable
            no_snap = scene.rx_track(1,ue_idx).no_snapshots;
            for snap_idx = 1 : no_snap
                fprintf(file_loc,"%.4f ",scene.rx_track(1,ue_idx).positions(:,snap_idx)+scene.rx_track(1,ue_idx).initial_position);
                fprintf(file_loc,"\n");
            end
        else
            fprintf(file_loc,"%.4f ",scene.rx_position(:,ue_idx));
            fprintf(file_loc,"\n");
        end
    end

    % 写入服务小区时域数据
    n_tr = numel(serving_cm);
    for tr_idx = 1 : n_tr
        tr_builder = serving_cm(1,tr_idx);
        H_time = tr_builder.coeff; 
        temp_H = [real(reshape(H_time,1,[]));imag(reshape(H_time,1,[]))];
        data = reshape(temp_H,1,[]);
        fprintf(file_H_time,'%.6g ',data);
        fprintf(file_H_time,"\n");
        fprintf(file_pg,'%.6f ',serving_cm(1,tr_idx).par.pg);
        fprintf(file_pg,"\n");
    end

    % 写入邻区时域数据
    for tr_idx = 1 : n_tr
        tr_builder = neighbor_cm(1,tr_idx);
        H_time = tr_builder.coeff; 
        temp_H = [real(reshape(H_time,1,[]));imag(reshape(H_time,1,[]))];
        data = reshape(temp_H,1,[]);
        fprintf(file_neighbor_H_time,'%.6g ',data);
        fprintf(file_neighbor_H_time,"\n");
        fprintf(file_neighbor_pg,'%.6f ',neighbor_cm(1,tr_idx).par.pg);
        fprintf(file_neighbor_pg,"\n");
    end

    % 写入服务小区频域数据
    for tr_idx = 1 : n_tr
        tr_builder = serving_cm(1,tr_idx);
        H_freq = tr_builder.fr(BW,nSub);
        temp_H = [real(reshape(H_freq,1,[]));imag(reshape(H_freq,1,[]))];
        data = reshape(temp_H,1,[]);
        fprintf(file_H_freq,'%.6g ',data);
        fprintf(file_H_freq,"\n");
    end
    
    % 写入邻区频域数据
    for tr_idx = 1 : n_tr
        tr_builder = neighbor_cm(1,tr_idx);
        H_freq = tr_builder.fr(BW,nSub);
        temp_H = [real(reshape(H_freq,1,[]));imag(reshape(H_freq,1,[]))];
        data = reshape(temp_H,1,[]);
        fprintf(file_neighbor_H_freq,'%.6g ',data);
        fprintf(file_neighbor_H_freq,"\n");
    end

    % 写入所有基站的pg数据（用于分析）
    n_tr_all = numel(cm);
    for tr_idx = 1 : n_tr_all
        fprintf(file_pg_all,'%.6f ',cm(1,tr_idx).par.pg);
        fprintf(file_pg_all,"\n");
    end

end

% 关闭所有文件
fclose(file_loc);
fclose(file_H_time);
fclose(file_pg);
fclose(file_H_freq);
fclose(file_log);
fclose(file_pg_all);
fclose(file_neighbor_H_time);
fclose(file_neighbor_H_freq);
fclose(file_neighbor_pg);
fclose(file_serving_bs_pos);
fclose(file_neighbor_bs_pos);
fclose(file_cell_selection);
fclose(file_a3_events);
fclose(file_handover_log);

disp("end run");
disp(datetime);
result = "setsumi_a3_handover_complete";

end

% A3协议切换算法函数
function [handover_decision, target_bs, ue_a3_trigger_time, ue_a3_candidate_bs, ue_a3_triggered] ...
    = deal_with_a3_handover(current_rsrp, neighbor_rsrp, serving_bs_id, current_time, ue_idx, ...
    ue_a3_trigger_time, ue_a3_candidate_bs, ue_a3_triggered, A3_OFFSET, A3_HYSTERESIS, A3_TIME_TO_TRIGGER)

    
    handover_decision = false;
    target_bs = serving_bs_id;
    
    % 找到最强的邻区信号
    [max_neighbor_rsrp, best_neighbor_idx] = max(neighbor_rsrp);
    
    % A3事件条件: 邻区RSRP > 服务小区RSRP + 偏移 + 滞后
    a3_condition = max_neighbor_rsrp > (current_rsrp + A3_OFFSET + A3_HYSTERESIS);
    
    if a3_condition
        if ~ue_a3_triggered(ue_idx) || ue_a3_candidate_bs(ue_idx) ~= best_neighbor_idx
            % 新的A3事件触发或候选基站改变，重新开始计时
            ue_a3_trigger_time(ue_idx) = current_time;
            ue_a3_candidate_bs(ue_idx) = best_neighbor_idx;
            ue_a3_triggered(ue_idx) = true;
        elseif (current_time - ue_a3_trigger_time(ue_idx)) >= A3_TIME_TO_TRIGGER
            % A3事件持续时间超过Time-to-Trigger，执行切换
            handover_decision = true;
            target_bs = best_neighbor_idx;
            ue_a3_triggered(ue_idx) = false;  % 重置触发状态
        end
    else
        % A3条件不满足，清除触发状态
        ue_a3_triggered(ue_idx) = false;
        ue_a3_trigger_time(ue_idx) = 0;
        ue_a3_candidate_bs(ue_idx) = 0;
    end
end