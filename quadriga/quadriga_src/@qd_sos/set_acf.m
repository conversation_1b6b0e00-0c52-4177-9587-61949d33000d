function h_sos = set_acf( acf_type, distribution, dist_decorr )
%SET_ACF Creates a new 'qd_sos' object.
%
% Calling object:
%   None (static method)
%
% Description:
%    There are 3 ACF types that can be selected by acf_type. The 3D SOS frequencies are precomputed
%    for 150, 300, 500, and 1000 sinusoids.
%
%       * Exponential ACF (Exp150, Exp300, Exp500, Exp1000 )
%       * Gaussian ACF (Gauss150, Gauss300, Gauss500, Gauss1000 )
%       * Combined Gaussian and Exponential ACF (Comb150, Comb300, Comb500, Comb1000)
%
%    The distributer function can be either ('normal' or 'uniform').
%
% Input:
%   acf_type
%   String describing the shape of the autocorrelation function and the number of sinusoids,
%   Default: 'Comb300'
%
%   distribution
%   Distribution of the random variables ('normal' or 'uniform'), Default: 'normal'
%
%   dist_decorr
%   Decorrelation distance in [m], Default: 10 m
%
% Output:
%   h_sos
%   Handle of the created qd_sos object
%
% 
% QuaDRiGa Copyright (C) 2011-2019
% Fraunhofer-Gesellschaft zur Foerderung der angewandten Forschung e.V. acting on behalf of its
% Fraunhofer Heinrich Hertz Institute, Einsteinufer 37, 10587 Berlin, Germany
% All rights reserved.
%
% e-mail: <EMAIL>
%
% This file is part of QuaDRiGa.
%
% The Quadriga software is provided by Fraunhofer on behalf of the copyright holders and
% contributors "AS IS" and WITHOUT ANY EXPRESS OR IMPLIED WARRANTIES, including but not limited to
% the implied warranties of merchantability and fitness for a particular purpose.
%
% You can redistribute it and/or modify QuaDRiGa under the terms of the Software License for 
% The QuaDRiGa Channel Model. You should have received a copy of the Software License for The
% QuaDRiGa Channel Model along with QuaDRiGa. If not, see <http://quadriga-channel-model.de/>. 

% The default distance vector for all ACF types
D = [0, 0.25, 0.5, 0.75, 1, 1.25, 1.5, 1.75, 2, 2.25, 2.5, 2.75, 3, 3.25, 3.5, 3.75, 4, 4.25, 4.5, 4.75, ...
    5, 5.25, 5.5, 5.75, 6, 6.25, 6.5, 6.75, 7, 7.25, 7.5, 7.75, 8, 8.25, 8.5, 8.75, 9, 9.25, 9.5, 9.75, ...
    10, 10.25, 10.5, 10.75, 11, 11.25, 11.5, 11.75, 12, 12.25, 12.5, 12.75, 13, 13.25, 13.5, 13.75, 14, 14.25, 14.5, 14.75, ...
    15, 15.25, 15.5, 15.75, 16, 16.25, 16.5, 16.75, 17, 17.25, 17.5, 17.75, 18, 18.25, 18.5, 18.75, 19, 19.25, 19.5, 19.75, ...
    20, 20.25, 20.5, 20.75, 21, 21.25, 21.5, 21.75, 22, 22.25, 22.5, 22.75, 23, 23.25, 23.5, 23.75, 24, 24.25, 24.5, 24.75, ...
    25, 25.25, 25.5, 25.75, 26, 26.25, 26.5, 26.75, 27, 27.25, 27.5, 27.75, 28, 28.25, 28.5, 28.75, 29, 29.25, 29.5, 29.75, ...
    30, 30.25, 30.5, 30.75, 31, 31.25, 31.5, 31.75, 32, 32.25, 32.5, 32.75, 33, 33.25, 33.5, 33.75, 34, 34.25, 34.5, 34.75, ...
    35, 35.25, 35.5, 35.75, 36, 36.25, 36.5, 36.75, 37, 37.25, 37.5, 37.75, 38, 38.25, 38.5, 38.75, 39, 39.25, 39.5, 39.75, ...
    40, 40.25, 40.5, 40.75, 41, 41.25, 41.5, 41.75, 42, 42.25, 42.5, 42.75, 43, 43.25, 43.5, 43.75, 44, 44.25, 44.5, 44.75, ...
    45, 45.25, 45.5, 45.75, 46, 46.25, 46.5, 46.75, 47, 47.25, 47.5, 47.75, 48, 48.25, 48.5, 48.75, 49, 49.25, 49.5, 49.75 ];

switch acf_type
    case {'Exp150','Exp300','Exp500','Exp1000'}
        R = [1, 0.97531, 0.95123, 0.92774, 0.90484, 0.8825, 0.86071, 0.83946, 0.81873, 0.79851, 0.7788, 0.75957, 0.74082, 0.72253, 0.70469, 0.68729, 0.67032, 0.65377, 0.63763, 0.62188, ...
            0.60653, 0.59155, 0.57695, 0.5627, 0.54881, 0.53526, 0.52204, 0.50915, 0.49658, 0.48432, 0.47236, 0.4607, 0.44933, 0.43823, 0.42741, 0.41686, 0.40657, 0.39653, 0.38674, 0.37719, ...
            0.36788, 0.35879, 0.34993, 0.34129, 0.33287, 0.32465, 0.31663, 0.30882, 0.30119, 0.29375, 0.2865, 0.27943, 0.27253, 0.2658, 0.25924, 0.25284, 0.24659, 0.24051, 0.23457, 0.22878, ...
            0.22313, 0.21762, 0.21224, 0.207, 0.20189, 0.19691, 0.19205, 0.18731, 0.18268, 0.17817, 0.17377, 0.16948, 0.1653, 0.16121, 0.15723, 0.15335, 0.14957, 0.14587, 0.14227, 0.13876, ...
            0.13533, 0.13199, 0.12873, 0.12555, 0.12245, 0.11943, 0.11648, 0.11361, 0.1108, 0.10807, 0.1054, 0.10279, 0.10026, 0.097781, 0.095367, 0.093012, 0.090716, 0.088476, 0.086291, 0.084161, ...
            0.082083, 0.080056, 0.07808, 0.076152, 0.074272, 0.072438, 0.070649, 0.068905, 0.067204, 0.065544, 0.063926, 0.062348, 0.060808, 0.059307, 0.057843, 0.056415, 0.055022, 0.053663, 0.052338, 0.051046, ...
            0.049786, 0.048556, 0.047357, 0.046188, 0.045048, 0.043936, 0.042851, 0.041793, 0.040761, 0.039754, 0.038773, 0.037816, 0.036882, 0.035971, 0.035083, 0.034217, 0.033372, 0.032548, 0.031745, 0.030961, ...
            0.030196, 0.029451, 0.028724, 0.028014, 0.027323, 0.026648, 0.02599, 0.025348, 0.024723, 0.024112, 0.023517, 0.022936, 0.02237, 0.021818, 0.021279, 0.020754, 0.020241, 0.019741, 0.019254, 0.018779, ...
            0.018315, 0.017863, 0.017422, 0.016992, 0.016572, 0.016163, 0.015764, 0.015375, 0.014995, 0.014625, 0.014264, 0.013911, 0.013568, 0.013233, 0.012906, 0.012588, 0.012277, 0.011974, 0.011678, 0.01139, ...
            0.011108, 0.010834, 0.010567, 0.010306, 0.010051, 0.0098032, 0.0095612, 0.0093251, 0.0090948, 0.0088703, 0.0086513, 0.0084377, 0.0082294, 0.0080262, 0.007828, 0.0076347, 0.0074462, 0.0072624, 0.0070831, 0.0069082 ];
        
    case {'Comb150','Comb300','Comb500','Comb1000'}
        R = [1, 0.99938, 0.9975, 0.99439, 0.99005, 0.9845, 0.97775, 0.96984, 0.96079, 0.95063, 0.93941, 0.92716, 0.91393, 0.89976, 0.8847, 0.86881, 0.85214, 0.83475, 0.81668, 0.79801, ...
            0.7788, 0.75909, 0.73896, 0.71847, 0.69767, 0.67663, 0.6554, 0.63405, 0.61262, 0.59118, 0.56978, 0.54846, 0.52729, 0.50629, 0.48553, 0.46504, 0.44485, 0.42501, 0.40555, 0.38649, ...
            0.36788, 0.35879, 0.34993, 0.34129, 0.33287, 0.32465, 0.31663, 0.30882, 0.30119, 0.29375, 0.2865, 0.27943, 0.27253, 0.2658, 0.25924, 0.25284, 0.24659, 0.24051, 0.23457, 0.22878, ...
            0.22313, 0.21762, 0.21224, 0.207, 0.20189, 0.19691, 0.19205, 0.18731, 0.18268, 0.17817, 0.17377, 0.16948, 0.1653, 0.16121, 0.15723, 0.15335, 0.14957, 0.14587, 0.14227, 0.13876, ...
            0.13533, 0.13199, 0.12873, 0.12555, 0.12245, 0.11943, 0.11648, 0.11361, 0.1108, 0.10807, 0.1054, 0.10279, 0.10026, 0.097781, 0.095367, 0.093012, 0.090716, 0.088476, 0.086291, 0.084161, ...
            0.082083, 0.080056, 0.07808, 0.076152, 0.074272, 0.072438, 0.070649, 0.068905, 0.067204, 0.065544, 0.063926, 0.062348, 0.060808, 0.059307, 0.057843, 0.056415, 0.055022, 0.053663, 0.052338, 0.051046, ...
            0.049786, 0.048556, 0.047357, 0.046188, 0.045048, 0.043936, 0.042851, 0.041793, 0.040761, 0.039754, 0.038773, 0.037816, 0.036882, 0.035971, 0.035083, 0.034217, 0.033372, 0.032548, 0.031745, 0.030961, ...
            0.030196, 0.029451, 0.028724, 0.028014, 0.027323, 0.026648, 0.02599, 0.025348, 0.024723, 0.024112, 0.023517, 0.022936, 0.02237, 0.021818, 0.021279, 0.020754, 0.020241, 0.019741, 0.019254, 0.018779, ...
            0.018315, 0.017863, 0.017422, 0.016992, 0.016572, 0.016163, 0.015764, 0.015375, 0.014995, 0.014625, 0.014264, 0.013911, 0.013568, 0.013233, 0.012906, 0.012588, 0.012277, 0.011974, 0.011678, 0.01139, ...
            0.011108, 0.010834, 0.010567, 0.010306, 0.010051, 0.0098032, 0.0095612, 0.0093251, 0.0090948, 0.0088703, 0.0086513, 0.0084377, 0.0082294, 0.0080262, 0.007828, 0.0076347, 0.0074462, 0.0072624, 0.0070831, 0.0069082 ];
        
    case {'Gauss150','Gauss300','Gauss500','Gauss1000'}
        R = [1, 0.99938, 0.9975, 0.99439, 0.99005, 0.9845, 0.97775, 0.96984, 0.96079, 0.95063, 0.93941, 0.92716, 0.91393, 0.89976, 0.8847, 0.86881, 0.85214, 0.83475, 0.81668, 0.79801, ...
            0.7788, 0.75909, 0.73896, 0.71847, 0.69767, 0.67663, 0.6554, 0.63405, 0.61262, 0.59118, 0.56978, 0.54846, 0.52729, 0.50629, 0.48553, 0.46504, 0.44485, 0.42501, 0.40555, 0.38649, ...
            0.36787, 0.34971, 0.33203, 0.31485, 0.29819, 0.28206, 0.26646, 0.25141, 0.23692, 0.22298, 0.2096, 0.19678, 0.18451, 0.17279, 0.16162, 0.15097, 0.14085, 0.13125, 0.12215, 0.11353, ...
            0.10539, 0.097718, 0.090487, 0.083687, 0.077301, 0.071313, 0.065707, 0.060466, 0.055573, 0.051013, 0.046768, 0.042823, 0.039161, 0.035768, 0.032629, 0.029727, 0.02705, 0.024583, 0.022313, 0.020228, ...
            0.018314, 0.016561, 0.014957, 0.013491, 0.012154, 0.010936, 0.0098273, 0.0088201, 0.0079063, 0.0070783, 0.0063291, 0.0056521, 0.0050412, 0.0044908, 0.0039954, 0.0035502, 0.0031507, 0.0027927, 0.0024723, 0.0021859, ...
            0.0019302, 0.0017023, 0.0014995, 0.0013192, 0.0011591, 0.0010171, 0.00089147, 0.00078035, 0.00068223, 0.0005957, 0.0005195, 0.00045247, 0.00039361, 0.00034197, 0.00029674, 0.00025717, 0.00022259, 0.00019243, 0.00016614, 0.00014327, ...
            0.00012339, 0.00010613, 9.1179e-05, 7.8233e-05, 6.7042e-05, 5.738e-05, 4.9049e-05, 4.1875e-05, 3.5706e-05, 3.0407e-05, 2.5863e-05, 2.197e-05, 1.864e-05, 1.5795e-05, 1.3367e-05, 1.1298e-05, 9.538e-06, 8.0418e-06, 6.7718e-06, 5.6953e-06, ...
            4.7839e-06, 4.0134e-06, 3.3627e-06, 2.8141e-06, 2.352e-06, 1.9633e-06, 1.6368e-06, 1.3629e-06, 1.1334e-06, 9.414e-07, 7.8093e-07, 6.4701e-07, 5.3538e-07, 4.4246e-07, 3.6521e-07, 3.0107e-07, 2.4788e-07, 2.0384e-07, 1.6741e-07, 1.3732e-07, ...
            1.125e-07, 9.2049e-08, 7.5222e-08, 6.1394e-08, 5.0045e-08, 4.0744e-08, 3.3129e-08, 2.6904e-08, 2.1822e-08, 1.7677e-08, 1.4302e-08, 1.1557e-08, 9.3268e-09, 7.5177e-09, 6.052e-09, 4.8659e-09, 3.9074e-09, 3.1338e-09, 2.5102e-09, 2.0082e-09, ...
            1.6046e-09, 1.2805e-09, 1.0206e-09, 8.1239e-10, 6.4587e-10, 5.1284e-10, 4.0671e-10, 3.2213e-10, 2.5483e-10, 2.0133e-10, 1.5887e-10, 1.252e-10, 9.855e-11, 7.7473e-11, 6.0828e-11, 4.7699e-11, 3.7358e-11, 2.9222e-11, 2.2829e-11, 1.7813e-11 ];
        
    otherwise
        error('QuaDRiGa:qd_sos:type','ACF type not found.');
end

switch acf_type
    case 'Exp150'
        F(:,1) = [1.3162, -0.13039, -0.025572, 2.2998, 1.2091, 0.6963, -0.41394, 1.3469, 0.58572, -0.80388, 0.54997, -6.0964, -1.767, -2.6067, 0.2818, -3.5395, -1.5032, 0.30466, 0.21513, 0.80118, ...
            0.20883, 0.45997, 0.46331, -0.41939, -0.026711, 1.1475, -0.92087, 2.1368, 0.63491, -1.1284, -6.7532, -0.87134, 0.075587, 0.01378, 0.87033, -0.87885, -1.0236, -1.4047, 1.9186, 1.0824, ...
            1.7898, 5.0017, -0.56216, -0.21606, 2.4161, 0.35818, -0.50869, 0.76365, -2.774, -0.14128, -0.055857, 1.052, 3.1601, -0.79607, -5.3814, -1.941, -1.594, -3.0171, 0.50136, 0.071249, ...
            -0.031569, -1.3548, 0.44483, -1.3086, 3.5448, 2.9926, 3.4947, 0.41238, 0.85307, -1.3189, 2.532, 0.028312, 0.74676, 0.85208, -1.5232, 0.1784, -4.4129, -0.29204, 0.87501, -0.16194, ...
            -0.67604, -1.9805, 0.18983, -1.6308, 0.51477, 0.43874, -0.24757, -4.572, -0.40427, -0.014647, 2.5921, -0.081384, -6.0736, -0.1238, 0.58876, -0.4202, -0.20271, -0.39748, -0.62978, 0.49136, ...
            0.10199, 0.029235, 3.8913, -0.39522, 0.54127, 0.73002, 0.73306, 0.64705, -0.22823, -0.2182, 0.7414, -0.34987, 2.6768, -3.544, 4.1551, -0.74301, -1.0249, 4.1817, 0.94019, -1.5494, ...
            0.15504, -0.16851, -0.2467, -1.5468, -0.93651, 0.070692, -4.0844, -0.19549, -0.30738, 5.1616, -0.31954, 2.35, -0.25113, -0.18941, 0.52957, 0.57701, -1.5811, 1.1764, 0.0041694, -0.39575, ...
            -4.4901, 1.716, 1.4857, 0.6383, -2.0934, 0.11839, -0.069765, 0.93615, -0.050062, -0.33559 ];
        
        F(:,2) = [1.1243, -0.59628, -2.0037, -0.90265, 7.0787, -0.78669, -0.65487, 0.83382, 0.070787, 0.082022, -0.39027, -3.8721, 0.59424, -0.52129, -2.9173, 3.2634, -0.29968, 0.67729, -0.11511, 0.54343, ...
            -1.153, -0.482, 1.148, -4.6262, 0.62481, 1.8795, -0.18268, 0.14695, 0.026095, -0.41445, -0.45956, -0.44771, 1.0418, -0.18955, 0.53856, -2.3498, 1.8306, 2.2709, 0.55585, 2.7312, ...
            -2.3382, 0.25167, 0.090752, 1.2057, 1.3869, 5.3291, -0.38307, -0.38791, 5.0682, -0.3365, -0.20481, -1.2874, 0.52746, -0.92079, -1.0474, -3.2545, -0.89248, -4.0275, 0.48419, -2.5865, ...
            1.278, 0.83772, 1.7798, -1.3386, 2.1327, -0.94779, 2.27, 0.56234, -1.1247, 0.63945, 3.7035, -3.556, -1.8858, -0.37913, -4.615, 0.22278, 0.0033555, -1.7981, -6.1847, 1.7518, ...
            0.68823, -0.41788, -1.4217, 0.41211, -0.57461, -0.88123, 0.1987, -2.0358, -0.47406, 0.17296, 0.69415, 0.2107, 1.4028, 0.54232, 0.4418, 0.019056, 0.55432, 0.40374, -1.1098, -1.0065, ...
            0.065943, -0.0075741, -5.7203, 0.010828, -1.1828, -0.065515, -0.99017, 0.12432, 0.61173, 4.4428, 0.40706, -0.25701, -2.7095, -3.6425, 0.32893, -0.43233, -1.4181, 1.4801, -0.2669, 0.25275, ...
            -0.21098, 0.27207, 1.6529, 2.0209, -0.19426, 0.55756, 1.3036, -0.7046, -0.38819, -0.62752, 0.071982, -0.38467, 0.045275, 1.0504, 1.0112, -0.58057, -0.88723, 0.10595, -0.037628, -1.1563, ...
            5.6235, 0.59146, -0.50906, -0.019598, 4.3334, -0.64873, -0.009889, -0.25031, 0.48679, 0.42276 ];
        
        F(:,3) = [0.45199, 0.15803, -0.4543, -1.8089, -0.59893, -0.64078, -1.1824, 0.6719, 1.4031, 0.24806, -0.41831, 0.10171, -0.29478, -1.4378, 0.91449, -1.0644, 1.2954, 0.024047, 0.32299, -0.22358, ...
            -0.4215, -0.12584, 0.30068, 3.507, -0.98815, -8.6915, -1.4484, -9.0729, -1.0255, -2.3398, 1.3717, -1.343, -2.3475, -2.9642, -1.3113, -7.4761, 0.54822, -1.5137, -0.55173, -2.0917, ...
            0.38956, -2.0799, 1.7476, -1.7459, -0.606, 3.161, -0.27484, -0.82478, -2.2577, -0.78911, 0.30494, -0.67285, -0.34904, 0.47451, -0.35091, 3.5817, 0.39669, -6.4355, 0.45017, -1.086, ...
            -0.14917, -1.024, 1.8332, -2.3325, 1.0321, -4.1839, -1.0377, -2.7575, 2.2932, 0.11545, -6.7708, 0.33945, 0.17613, 0.14821, 4.1171, -0.032699, -6.8769, 0.032538, 0.86894, -0.53057, ...
            -0.5895, -0.43483, -5.0097, 1.0097, 0.66677, -0.39658, 0.56199, 2.2962, 1.0392, -0.36567, 3.1843, -0.45904, 0.97523, -0.29698, -0.10521, -0.7026, 0.56508, -4.1412, 0.054772, 1.2704, ...
            -0.72041, 0.023432, 1.6366, 0.62063, 0.30702, -0.23407, 0.31739, 0.44231, 0.28506, 1.5583, -1.828, -0.71344, -3.2294, 4.9672, -5.7398, -0.070241, -0.98195, 4.7092, -0.22614, 0.15172, ...
            0.70786, -0.16435, 1.2869, -1.114, 0.1078, -0.49807, -0.76855, 1.0538, 0.49945, 4.4892, 0.43999, -6.1333, 0.32233, 1.3149, 1.3211, 8.0377, 0.26434, 2.3541, 0.075213, 0.14377, ...
            -0.38499, 0.23764, -1.1228, 0.93295, -0.27327, 0.11517, -0.044561, 0.3483, -1.7094, -5.2426 ];
        
    case 'Exp300'
        F(:,1) = [-1.624, 3.3072, 0.32881, 6.699, -1.1881, -0.19954, -1.8906, 1.3111, 0.029737, 2.6669, -1.6592, 1.5841, -0.47833, -2.0738, 0.74092, 2.2036, -1.2699, -0.28521, -3.187, -0.45079, ...
            -1.5834, 5.6669, -0.49042, -0.32949, -0.22858, 3.4415, 0.37891, -4.007, -9.4284, -7.2864, 1.183, 0.48726, -0.76854, 3.2967, -1.9489, -0.18812, -8.029, 1.5768, -0.1275, 0.57438, ...
            -0.83103, 1.2201, 1.0946, -2.0119, -1.0989, 1.7471, 7.4801, -1.0324, 3.4078, 0.51191, -0.9709, -2.5753, -0.94307, 0.28702, -3.0977, 0.38041, 2.9722, -3.6307, 0.60811, 1.9611, ...
            -0.10254, -2.2384, -0.65744, -0.27565, 4.3239, -3.1753, 1.3981, -0.0047139, 0.54837, -0.73723, 0.61179, -7.5397, -0.68526, -2.7423, 1.843, -6.5762, -0.79107, 0.69717, 0.96457, -0.30058, ...
            2.0801, 0.22897, -0.90103, 4.0034, 1.941, -0.12174, 0.76046, 0.65194, 5.8834, -2.5573, 0.65032, 0.5508, 0.56601, -0.25784, 0.95535, 0.29325, -0.58007, -2.5646, 1.5378, -0.94686, ...
            1.1248, -0.66736, 0.34117, 3.8336, 0.17181, 0.30787, 0.36704, 0.17844, -0.66558, -0.023155, 0.11724, 0.11936, -0.2034, 0.33515, -0.58053, -0.4973, 5.3117, -2.3814, -1.8056, 2.1219, ...
            4.5645, 1.0228, -8.1474, 0.37944, 1.2422, -0.16878, -0.30299, -0.24503, 0.0039973, -2.3753, 0.93485, -0.42717, 0.27597, 0.26054, 0.45406, -10.613, 0.69728, 0.12766, 1.268, 0.2526, ...
            0.38369, 0.17335, 5.3874, -0.25108, 0.53939, -0.32559, -0.86486, -1.3079, 2.9353, 2.0244, 4.4451, -0.58781, 0.82814, 1.0048, 1.4094, 1.6819, -1.5478, -5.0847, -2.8149, 0.20948, ...
            0.085009, 0.43403, 2.5009, 0.37893, -1.7809, 1.0545, 0.9156, 0.045067, -0.026433, -0.039475, -9.1247, 10.091, 6.1489, 3.4783, -1.7366, -0.010646, -5.363, 0.069963, 6.1944, 0.2339, ...
            -0.34958, 0.048251, 5.8863, -0.10651, -0.47183, -0.21711, 0.41716, 0.2194, -0.31805, -0.96451, 6.7396, -1.7087, 1.2975, 5.1057, 0.29172, -4.5553, -0.054863, 0.20411, -0.6665, -0.47217, ...
            0.025264, 8.8931, -0.41189, -0.89929, -1.3918, -0.054132, -0.39006, -0.83801, 2.2255, 0.71598, -0.18451, 0.55687, -1.4166, -0.21123, 0.3439, 0.22766, -0.81913, -0.24589, -1.087, 0.1221, ...
            0.33109, -1.1881, -1.101, 0.65242, -0.58531, -10.427, 0.27242, -0.023639, -0.88873, 1.1053, 2.1067, -0.79148, 0.51004, 0.2722, -0.036221, 8.0858, -0.6825, -0.43088, -0.98216, 0.70714, ...
            -0.68194, 0.80907, -0.58986, 1.1333, -1.1078, 0.19798, 1.1765, -0.31871, 0.62389, -1.7473, -0.40766, -0.12758, 0.61445, -1.3447, 0.29497, 2.3245, 0.59392, 0.37822, -0.33965, -2.171, ...
            0.26658, 0.35375, -0.38287, 1.0537, 1.6136, -0.60912, 1.3281, -0.22489, -0.53961, 1.0621, -0.35065, -0.22894, -8.7328, -1.8545, 0.73744, -0.26144, -0.0044926, -2.6658, -1.0729, 0.10196, ...
            -0.52806, 0.5964, -4.2484, -1.2322, 0.38533, 0.93112, 0.68733, 0.48649, 1.1311, 0.16574, -0.0050911, 0.25626, -1.154, -0.83198, -10.06, -0.1239, 1.582, 1.2403, 0.18365, 0.26722 ];
        
        F(:,2) = [0.51002, -3.2355, 1.8442, -5.5441, 0.59626, -0.52239, -1.4751, -0.46716, -0.059995, 0.25918, -3.1573, -1.7386, 0.024049, 0.45581, 2.2808, 0.092559, -2.3124, 0.93629, 1.0769, 0.37462, ...
            -0.29009, 2.6023, 0.11434, 0.93594, -2.2862, 1.0985, 0.80848, -1.4122, -1.1508, -3.1073, 7.8664, -2.0129, -0.74368, 1.429, -1.7285, 0.88715, 6.4896, 0.57282, -2.6569, -1.6265, ...
            -0.79842, 3.2345, -1.3684, 0.26826, 3.0422, 0.40364, -4.0521, 0.016616, -1.8378, -2.8743, -0.72917, 4.6355, 0.35431, -5.706, 0.70032, -0.39609, 0.099459, 0.43845, 0.74076, 0.006337, ...
            0.11893, -1.1233, 0.30785, -1.7631, -6.7919, -7.4895, 0.17483, 0.00069985, 0.079345, -0.040648, 0.73711, -2.3376, -0.26518, -0.51718, 0.21, 3.9351, 0.81514, 0.15106, -1.1419, -0.25273, ...
            1.061, 0.35266, 2.6526, -1.5926, -2.8139, 0.83865, -1.4284, -1.0447, -3.737, -4.9863, -0.51101, -1.058, 6.5029, 0.56745, 0.18466, -8.4201, -0.70881, 3.7351, -1.3847, 3.6026, ...
            1.7689, 0.17199, 1.6603, 0.20617, 0.5014, 0.24378, 0.90314, -0.30572, -0.44982, 0.2497, -0.017766, -0.35205, 1.0166, -0.8835, 0.19186, -0.027586, 0.14859, -7.9875, -6.087, -3.6167, ...
            5.7215, 4.4504, -2.8061, -0.28533, 0.24115, -7.2075, -0.39994, -0.049787, -0.054157, 3.851, -4.041, 0.21038, -5.0144, -0.064618, 1.6818, -2.5075, 1.7502, 0.478, 1.2332, -0.4342, ...
            -0.2784, -0.48894, 0.6684, -0.54197, 1.4009, -1.3108, -0.473, -2.133, 2.9581, -0.74655, 0.81828, 0.99694, 0.51995, 0.062011, -3.4846, -0.41281, 1.8035, 1.9703, 1.2631, -0.58152, ...
            0.45397, 0.45612, -1.0128, -0.13968, 0.53937, -0.060168, 2.6615, 0.2925, -0.47499, -1.3225, -0.66915, 1.4267, -0.15702, 0.543, 0.27522, 0.68837, 1.5511, 1.0792, 2.7351, 0.49099, ...
            0.25405, 1.986, 0.51468, 0.30306, -0.045278, -0.21561, -0.44387, -0.78964, -0.50097, -2.586, -4.4339, 6.4044, 1.0106, 5.946, 1.0337, 4.6088, 0.57886, 0.96403, 1.6766, 1.2777, ...
            0.79992, -2.2824, 0.011624, -0.34302, -0.25058, -0.14596, 0.87249, 0.32265, -0.45294, -0.41261, 0.028319, 0.56217, -1.1861, -0.040792, -0.11165, 0.5778, -0.090826, 0.43125, -0.78241, 0.38775, ...
            -0.30078, -0.17486, -1.6252, -0.058934, -2.6835, 3.9129, 0.35821, 2.4564, 0.23244, -0.66197, -2.7498, -0.12535, -1.714, -1.4199, 0.11321, -0.70819, -1.1586, -0.40392, 0.6357, -0.081938, ...
            0.28758, -1.9488, 0.30719, 0.16322, -0.39678, 0.63276, -0.90053, -0.48099, 0.88908, 2.1305, -0.031057, -3.9026, -0.78684, 0.5816, -1.0857, 3.7792, 0.34037, -0.39993, -0.23952, 1.277, ...
            -0.012502, -0.54629, 1.2675, 1.2888, 4.1592, 0.53569, -0.39906, -1.2095, 1.8751, 0.43694, 0.034242, 0.028887, -2.4152, -0.5973, -1.11, 3.3644, -0.0015187, -3.2965, 0.043039, 0.82275, ...
            -0.88563, 0.51963, 2.3642, 0.84046, -0.94836, -0.46092, -0.065356, 0.80088, 0.044077, 1.0596, -0.00081121, 1.5541, -1.1484, 2.1092, -0.12987, 0.01592, 0.83091, -0.73796, -0.42834, 0.25532 ];
        
        F(:,3) = [-0.73648, -1.0186, 0.71881, -3.5352, 0.75869, 1.7847, -0.80838, 0.77874, 0.69034, -2.1416, -3.156, -1.4928, 0.60307, 0.10231, 1.4577, -1.3889, 0.95717, -1.3438, 0.67247, -0.77131, ...
            -0.26802, 5.0653, -0.15998, -1.0205, 0.43001, 0.10892, -1.0048, 2.1348, -5.9141, -0.19695, 1.4494, 0.91989, 1.3355, 0.055746, 1.201, -0.43643, -4.3593, -0.31242, -1.9422, -0.20839, ...
            -1.142, 1.5025, -1.3066, -1.7338, 0.76783, 0.038535, -7.2497, 1.2613, -2.718, 4.6962, -0.63323, 2.2109, 0.28612, 0.43429, -0.78002, -0.38279, 1.4809, -0.91348, -0.26208, -0.50103, ...
            0.68875, -4.4778, -2.4195, 0.81884, -2.4997, -2.2103, 0.12081, 0.0043042, -0.97156, -0.19036, 0.38009, 1.6746, -0.12621, 0.14024, 1.3783, 2.6661, -1.8618, -0.1872, -0.47156, 0.71658, ...
            0.53361, 0.5215, -0.71663, -1.2762, 1.8226, -0.54289, -0.77353, -2.3376, 3.7291, -3.4449, -0.041285, -8.0227, -0.93118, -0.095658, -0.87289, -1.0858, -1.4592, 1.2975, 0.26158, 1.8578, ...
            -1.6726, -0.57136, -5.361, 2.4193, 1.0099, 0.54771, -0.25001, -0.56, 0.48396, -0.62159, -0.02852, -0.55476, 1.4323, -0.30708, 1.9299, 0.93212, -0.063983, -4.1399, 3.6468, 0.056761, ...
            -0.20241, -6.6655, 7.1444, 0.42547, 0.56511, -0.80903, -1.7353, -4.3152, -1.0812, 0.52819, 2.2241, 0.8637, -4.2968, 0.031446, 0.91506, -0.67308, 0.73641, 0.45094, -3.0127, -0.41992, ...
            0.82323, 0.48315, 6.0517, 0.30224, -0.49974, -0.26233, -0.98347, 2.9076, 1.0859, -2.8897, -1.1727, -3.3857, -0.20743, 0.15291, -7.2095, -1.9638, 7.4177, -0.6763, -4.3015, 0.15746, ...
            0.43469, -0.011935, -7.5628, 0.04562, 0.66834, 0.12072, 2.6839, -1.2929, 1.9492, -0.34656, 1.4022, -4.5908, -4.9218, 1.2362, -0.99572, 0.82351, -4.044, -0.16204, -1.5444, 0.31746, ...
            -0.38262, -0.16682, -3.6221, 0.47355, -0.025138, -0.48022, -0.16305, 0.58414, 0.24933, 0.64746, -0.97243, -0.1686, -3.2802, -1.6369, -0.24497, 4.8753, 0.33171, -1.3669, -1.269, 0.29417, ...
            0.57559, -6.4263, 0.35176, -0.051444, 0.0046737, -0.0072746, -0.31544, 1.1013, -4.7958, 0.021388, -0.051236, -0.5615, 2.1125, -0.15839, 0.38491, -0.24988, -0.22658, -0.42938, 0.28571, -0.49142, ...
            0.44446, 2.322, 0.29978, 0.56053, 7.3754, 1.3831, 0.15426, -2.859, -0.80708, -0.14821, -4.5651, 0.31897, 0.82494, -0.1071, 0.11039, 0.8032, -0.43666, 0.42192, -0.69831, -1.9431, ...
            0.38053, -0.5996, -1.5575, 0.79906, 0.91984, 0.4363, 0.64817, 0.37585, 0.30333, 2.5228, -0.15367, -2.1905, -0.12735, -0.29019, -1.144, 6.7598, 5.4446, -0.25891, -0.56956, 0.66543, ...
            0.11006, 0.1939, 0.35286, -0.72068, -0.27078, 1.3669, 1.9527, -2.7855, 1.1447, 0.9381, 0.058197, 0.33713, 4.4127, -0.9542, -0.11365, 3.0217, 0.0043298, 1.5977, 0.84985, -1.2776, ...
            -0.31461, 1.8551, 5.0449, -0.63715, -0.38478, -0.011359, 1.545, 0.36716, -0.77076, 1.1137, -0.0038287, 1.1837, 0.50155, 0.096836, -1.4877, 0.038029, -0.9124, 0.0456, -0.18464, -0.52586 ];
        
    case 'Exp500'
        F(:,1) = [-0.59665, 1.8125, -3.6981, 1.2435, -1.3343, 1.5626, -0.77711, -0.072733, -0.90334, -1.0965, -0.0038369, -6.5887, -1.4918, 0.71029, 0.53709, -3.7132, 0.50285, 2.1624, -1.9319, -0.40475, ...
            0.66921, 2.0156, 0.04417, -0.62403, -1.1973, 0.35827, 0.69361, 2.6048, -3.6053, -0.25231, -0.005828, -0.26104, -8.3426, -2.0342, -1.0186, -1.7445, 0.38088, 0.57226, -0.05668, -0.97905, ...
            5.6401, 0.39418, 0.47664, -0.47762, -0.4338, 0.030572, -0.12565, 7.8643, -1.0699, 4.5216, 0.032692, 2.9729, 3.2071, 0.20484, -1.849, 4.5112, -5.5871, -4.6335, 0.40206, -0.19196, ...
            -8.8746, 0.76917, 0.16137, 2.6177, -0.94848, 1.3099, 4.0649, -2.8802, 0.36789, 0.016498, 8.6466, -0.88815, -0.24519, -7.8764, 0.033559, 0.77268, -9.115, 0.070017, 0.72917, 4.9802, ...
            1.2651, -2.3824, 0.050999, -1.8276, 0.59257, -0.27585, 1.5271, -0.24219, 1.666, 1.2545, -1.8019, 0.54448, 0.014967, -2.4631, 1.313, 0.22095, 0.11893, -0.037646, -9.382, 0.21819, ...
            -1.4694, 2.1727, 0.72084, -1.0829, -0.76387, -1.4752, 0.23128, 0.88492, 2.3745, 0.86862, 0.59261, 0.66368, 1.5482, 10.548, 0.059466, -0.69711, 7.4602, -0.48732, -0.86419, -0.064342, ...
            10.626, -0.51808, -1.4813, -10.442, -0.25423, 8.2994, -0.21551, 6.0152, 0.43449, 0.69262, 0.56688, 5.3953, -6.3939, 0.065276, -0.27525, 0.39148, 3.273, -0.12873, 3.6973, -6.6302, ...
            0.17871, 1.8379, 5.9067, -1.7986, 0.33237, -2.4768, 0.68436, 0.13072, 0.2967, -0.30318, 0.3709, -0.96101, -0.69899, -1.7556, 1.008, 2.9497, 1.0957, 0.29032, -3.6313, 0.66035, ...
            0.41563, 0.35173, -2.6885, -1.3176, -0.65949, -1.1832, 0.63716, -0.8026, 0.59587, 1.2271, 1.1774, 1.4811, -10.793, 2.3598, -3.1512, 0.77276, -0.25268, 3.9634, -0.80162, 2.5644, ...
            0.60077, 0.50603, 3.4957, 2.1755, 1.1488, 1.6393, 2.0742, 0.53959, 1.322, -0.84551, 0.32379, -0.48012, -1.7098, 7.0065, -0.066867, 0.39685, -6.2847, 0.83696, -1.9373, -0.11903, ...
            -0.091388, -0.24701, 1.0227, -0.58084, 1.8556, 0.22918, -0.164, -1.1837, -1.2944, 0.12468, 1.186, 0.88027, -1.3252, -1.4826, -2.1602, 0.26151, -0.096276, 0.10576, -0.33573, 0.42422, ...
            1.2739, 2.0509, -1.1133, 1.2277, 0.78017, -2.41, -1.3793, 0.49413, 1.9631, -1.9134, -0.0065186, -0.18336, -0.65126, -0.70817, -1.0026, -1.1863, -2.0016, 0.77519, -0.67736, 0.16348, ...
            -0.13851, 0.42455, 0.062145, -0.35894, 0.31731, -1.8964, 0.1183, 1.566, -4.4746, -1.7457, -0.33577, -0.56429, 3.4979, 4.7185, 0.86562, -1.1894, 2.3424, 9.5583, -0.11475, -0.71023, ...
            -0.061219, 4.2125, 0.33212, -0.65089, -1.105, 0.64507, 0.15112, 0.32183, -0.026973, 0.30139, -0.46431, 1.2821, 0.26218, 3.226, 2.7552, -0.94677, 0.299, 0.97966, 0.69383, -1.4678, ...
            0.70349, -0.92558, 2.3678, 0.89614, -0.57393, -0.29201, -0.17403, -0.71598, -0.84716, -0.72609, 0.091466, 0.59487, -2.5566, -0.24383, -0.47557, -1.5947, 1.6217, -5.3525, -0.4199, 0.93484, ...
            0.11819, -0.50149, -2.9797, -0.39723, 0.63872, -0.47303, 1.3142, -1.5647, 0.5661, 0.73771, -1.3745, 0.39283, 5.8125, 0.66559, -0.73059, 0.58027, -0.40053, 0.88699, -0.67049, -0.48839, ...
            -0.10798, -3.2489, -0.82714, 3.7619, 5.3404, 1.662, -1.6064, -0.35568, 0.2309, 1.7421, -3.7875, 0.15026, 3.0422, 0.58524, 0.32364, -5.7821, 3.984, 0.69682, -5.29, 6.9679, ...
            0.092175, 0.1484, 0.2449, -0.15227, 0.37166, -0.37269, 0.008901, -0.52928, 0.30788, 0.092576, -3.2062, 8.9638, 5.0001, 0.25002, 0.85478, 0.55188, -0.26012, -0.17722, 9.9698, -0.5184, ...
            -8.1668, 0.17779, -0.076963, 0.54549, -2.472, 2.0423, 2.5623, -0.46177, 0.49123, 10.111, 0.46432, 1.0129, -0.17241, 0.058556, 1.5206, -1.7588, 1.6945, -4.5462, 0.27104, -0.25463, ...
            1.0256, -0.58225, 0.24977, 0.88249, 1.9511, 1.1107, 7.9684, -6.6125, -0.096915, -2.6338, -0.63868, 0.67793, -0.93527, -3.4885, 0.049412, -0.63287, 0.62798, 4.4449, -0.75042, 0.96867, ...
            -1.5352, 1.0218, -0.30827, -1.0309, 0.0026234, -1.1622, -2.0685, 1.9997, -2.6238, 1.1739, 6.1893, 0.43066, -0.005559, 0.0070117, -0.043574, -0.64229, -1.8309, 0.035611, 3.1276, -0.0020496, ...
            1.6436, 1.2829, 1.1777, -2.6132, -0.36031, 0.72188, 1.002, -0.026617, -3.0221, -2.0665, 0.87975, 0.80733, 0.020744, -0.69767, 0.36833, 0.030379, 0.016733, -0.46467, 0.53652, 0.21273, ...
            0.0062573, 1.5999, -0.36457, 0.67675, 0.58265, 0.047776, -2.7188, -0.48307, -0.0054524, -0.0062746, -0.18041, 3.1249, 1.4605, 0.10228, 0.089886, 0.49433, -0.75416, -0.18095, -0.18443, -0.34587, ...
            0.81193, -2.4893, -0.77433, -0.033666, 0.20701, 0.82533, 0.71956, 0.5044, -0.0037726, 1.5213, -0.49976, -0.87296, 0.40301, 0.53975, 0.2419, -0.61143, 0.005241, -0.021392, 0.12154, 0.0057028, ...
            1.1351, 0.14335, 1.5709, -1.1803, -0.15208, -1.3092, 0.077982, -0.37039, -0.94061, -0.83654, -0.89307, 1.8744, -0.12038, -0.91817, -0.62622, -0.99642, 0.0059397, 1.4504, 0.11086, 0.052389 ];
        
        F(:,2) = [0.28877, -3.7398, 4.398, 2.2405, 0.54568, -1.1232, 2.8384, 1.7806, -1.6086, 1.9973, 0.40465, 5.1404, -2.2184, -2.2426, -0.42141, -1.0875, -1.97, -0.10601, 2.6474, 2.0895, ...
            0.062594, -3.0931, 2.688, 0.21904, 0.56977, 2.3296, -2.476, -3.2968, 3.67, 1.1062, 0.0020373, -0.51998, -3.1331, 2.1233, -0.95887, -4.7279, 0.43942, -2.7939, 0.36331, -6.6475, ...
            5.1414, -0.45367, 0.15017, 0.28524, -1.0596, -0.35638, -0.56147, 2.1535, -0.49781, 1.8366, 1.3405, -0.74563, 0.0049096, 0.37258, -0.23252, 3.3065, -0.38257, 4.0664, 0.44038, -1.1646, ...
            -2.4574, 1.197, -3.0887, 2.0057, -0.95305, -2.11, 6.4086, 5.5095, 2.1718, 1.5217, -0.54087, -0.5125, 0.63357, 2.9776, 0.047201, -1.6182, 4.9747, -0.042735, 0.22526, -2.3456, ...
            0.32018, -0.93847, 0.48129, -5.7383, -1.5855, -0.56112, -0.18742, 0.65962, 0.95599, -0.30785, -2.3037, 1.5471, 0.42495, -0.59221, 1.1254, 1.4516, 0.74984, -0.26455, -1.5196, 0.44769, ...
            1.6782, 3.0937, -0.27647, -0.099056, 0.11301, 0.18349, 0.94407, 2.7278, -0.73501, 0.037278, -0.50235, 3.6549, 1.189, -1.853, -3.9998, -0.72737, -2.7573, 1.1001, -1.1996, -0.015193, ...
            1.7342, 0.3669, 0.43323, 4.6495, 0.51165, 1.8189, -1.0031, -2.7491, 0.48442, 0.66429, -1.354, -2.1231, 3.5214, -2.1142, -0.47854, -0.53132, 1.1096, -0.36701, 5.0618, 0.5687, ...
            -0.50151, 3.9162, 4.3121, 6.7438, -1.8161, 6.2818, 0.53455, 0.13056, 0.4657, -1.0224, -5.575, 3.2771, 0.17396, 8.1572, -4.4515, 3.7181, 1.4538, 0.52437, 5.7369, -0.22477, ...
            0.099376, -0.13251, 7.9922, -0.3413, 0.567, -1.297, 5.3749, 1.3999, 0.069482, -0.047647, -1.2219, 0.4954, -2.698, 3.8794, 2.4292, 0.069652, 0.40276, -4.3574, -0.061642, -0.35841, ...
            -4.5128, 0.20183, 6.5466, -0.79989, 0.82386, -1.1268, 1.015, -0.27692, -0.29767, 0.67159, 4.2779, -0.13711, -1.8614, -6.1577, -0.093878, 0.27743, -0.081697, -0.013267, -6.2142, -0.10131, ...
            0.091124, 7.9715, 0.87665, 0.10666, -7.518, 0.86133, -0.60864, 6.5506, -0.54483, 4.707, -0.8453, -0.97064, 0.58035, -2.4154, 4.6924, -0.51426, -0.77135, -0.56299, 6.1799, -0.10181, ...
            -0.41917, -2.6562, -5.8433, 6.6894, 2.0367, 0.6053, 2.1847, 1.214, 2.8519, -6.6195, 0.77124, -0.57812, 3.2815, -0.123, 0.52777, -0.27631, -4.9088, 0.55576, 0.08524, -2.8468, ...
            0.78048, 0.057491, -1.3008, 0.42555, -0.72903, 0.80395, -0.30053, -0.29972, -7.1587, -2.1967, -0.99729, -0.42699, 7.813, 6.2815, 1.0873, 3.4974, 1.5676, -1.9795, -0.47523, 0.0075978, ...
            0.019561, 0.037053, 2.4478, 0.034071, -0.5942, -0.0030015, 7.8296, -0.27317, 1.3828, -0.024534, -0.21391, 1.918, -0.7544, -0.50643, 1.7955, -2.2204, 0.20604, 1.0087, 0.055161, -0.62126, ...
            0.74536, -0.35041, -0.76622, -0.14787, -0.094994, 0.08965, 0.74741, -0.33268, 0.95604, -0.57851, 0.17482, 0.31395, -0.24856, 0.83528, 1.03, -2.9184, 0.15767, -0.16884, -1.3554, -0.10517, ...
            1.3297, 0.67243, 0.97219, -0.48108, 0.13819, -0.23833, 0.0046076, -2.7023, -1.157, -0.064121, -0.05944, 0.20042, -3.8985, 0.21097, 0.41407, -0.35247, 0.26619, 1.1073, -0.95953, 0.53368, ...
            0.20927, 2.1592, 1.2826, -0.90529, -0.94236, 0.2738, -0.54478, 0.34909, 1.2371, -0.46508, 2.4138, -1.3217, -2.7769, 1.393, -0.59155, 5.8427, -1.517, -0.68137, -0.041062, -5.8738, ...
            0.48239, -0.63305, -0.22683, -0.64629, 0.48514, -0.12719, -1.3637, -7.8078, 1.44, -1.9651, -2.942, -2.7773, -1.8286, 0.92396, -1.0995, -0.14136, -0.063821, 0.40102, 1.6827, -0.70527, ...
            -4.8505, 0.10575, -0.56252, 1.3237, 1.2084, -1.5392, 0.41107, -0.3949, -0.31355, -2.9236, 0.26749, -0.1155, 0.53972, -0.49068, -0.8949, -7.8773, -0.92206, -1.1894, -0.0086264, 0.831, ...
            0.76414, -1.8731, -0.60481, 1.8709, -0.070705, -0.92186, -0.26927, 4.8112, 0.50469, 1.9266, 1.2744, 0.10897, 0.9642, 1.212, 0.57628, -2.0709, -0.29264, 2.0789, -0.69253, 0.60699, ...
            1.9126, -0.026898, 2.6055, 1.8556, -0.00063159, -0.42973, -0.14253, 0.603, -0.13546, -2.6497, -1.4215, -0.64792, 1.6866e-05, 0.7717, -0.77734, -0.68599, 0.93571, -0.094148, 1.039, -0.39062, ...
            -0.7463, -0.49591, -1.5204, 0.86817, 0.82568, 2.156, 4.9373, -0.79813, -1.4886, -1.107, -0.56779, -0.062663, 0.021042, 1.1789, 0.58767, -0.17162, 0.22849, 1.2497, -0.36378, 0.791, ...
            -0.2294, 1.8195, 0.020939, -1.1134, 0.51812, -0.0775, -1.2315, -0.42574, -0.00042028, 0.00099702, -2.1241, -1.2695, -0.42437, 0.70908, 0.014923, 0.68268, 0.53177, -0.05343, -0.83493, 0.63365, ...
            -2.0149, 2.0245, 0.27317, 0.76487, 0.44704, -0.95268, -0.37233, -0.68386, -0.0037173, 0.24571, 0.82883, 0.1197, -1.1513, 0.98588, 0.023976, -0.60808, 0.0030984, 0.082564, 1.4644, 0.0021677, ...
            0.70591, 0.28858, 1.4251, 1.4815, 0.3243, -1.7026, -0.23627, 1.9253, 0.94815, 0.38871, 0.75874, 3.4481, 1.3535, 0.74457, -0.020572, -1.614, -0.0013319, -0.35568, 0.21599, 0.04957 ];
        
        F(:,3) = [0.24425, -7.3797, -7.8069, 0.81541, -1.1736, -0.72203, -0.33795, 1.4712, 1.3842, -0.26271, -0.45165, -2.1184, -0.25857, -0.23479, 0.1816, 4.4939, 1.2872, -0.533, 1.7276, 1.6536, ...
            -0.082074, -0.13923, 0.31247, -0.61521, 0.4747, -0.6228, 0.84439, 7.2662, 6.5998, -0.94394, 0.0017667, 0.24379, -2.9449, -2.1934, 0.2605, 6.6848, 0.223, -2.4036, -0.55899, -8.1498, ...
            3.5616, 0.15368, -0.44556, 0.37494, -0.82216, 2.5826, -0.14593, 1.9918, 0.25956, 2.7958, -0.49622, -1.8294, 1.2051, 0.70236, -0.72656, 0.64771, -0.26359, 1.1406, -0.55197, -0.77903, ...
            0.63772, -0.0078041, -2.1419, 7.7361, 0.48425, 4.5759, -7.3912, 5.6393, 1.2752, -7.0686, -0.32067, 0.33238, -0.091311, -4.3677, -0.79146, 3.2438, 2.4542, -0.76443, 0.34182, -3.0131, ...
            -3.4554, 0.19188, -0.31604, 5.8467, 1.1095, -0.32894, -1.5542, 0.16575, -0.11788, 0.36849, 2.306, -3.3411, 0.75227, 2.7479, 3.3055, -2.0401, -2.1108, -1.571, 4.9264, 0.26998, ...
            -1.2647, -0.55276, -1.9015, 0.30275, -0.30594, 1.0924, -0.7547, 0.01666, 1.8344, 0.0032818, 0.2938, 0.32022, 1.6826, -0.63839, -2.4396, 1.931, 5.5718, 0.42942, -3.7496, -0.89113, ...
            4.3979, 0.59403, 1.3803, 1.3093, -0.097968, -3.8917, -0.76202, 0.46636, -0.53804, 0.14324, -0.36502, 3.2483, -5.7523, 1.3706, -0.10791, 0.11706, -0.63696, -0.72412, -2.6524, -0.85546, ...
            -0.6364, -1.8733, -6.3257, -5.0719, -4.761, 0.8558, -0.042487, 0.81687, 0.56996, -1.8445, -3.8645, -3.2497, -0.38029, 2.0561, 0.22839, 0.47174, -1.8145, 0.70166, 1.7286, -0.45087, ...
            -1.707, 0.89359, -1.8747, -1.1848, -2.3903, 1.362, -4.2189, -2.6319, -0.57443, 1.3073, 1.4103, 1.0708, 2.1004, 1.459, -2.5588, 0.0042004, -0.42467, 3.5394, -2.496, -1.6282, ...
            -0.61229, 0.28155, 4.3, 2.9855, -1.7271, 0.092646, 0.94087, 2.1869, -0.43238, -1.5064, 3.7072, 0.32097, -3.9262, 0.051387, -0.62638, -0.36579, 2.4075, 0.58545, -2.221, 0.68581, ...
            -0.6189, 3.4922, 0.64059, -0.54652, -3.938, 0.30285, 0.63756, 7.0145, 0.35823, 8.4496, 0.47696, -0.55859, -1.2823, 2.3836, -2.4427, 0.017338, -0.067451, 1.6996, -7.4324, -0.4882, ...
            0.47064, -3.1908, -5.9716, -1.0257, -1.8659, 0.47876, 3.8427, -0.56628, 3.0737, -0.20472, -0.18684, 0.68601, 5.0926, -0.41194, 4.4898, 1.3709, 8.0785, 0.048935, 0.29892, 0.3709, ...
            -0.14828, -0.66272, -5.921, -0.55459, -1.0669, -0.53678, 0.71957, -0.02817, -1.828, 0.59265, -2.4533, -9.6513, -0.3971, 1.2559, -0.30907, 0.2943, 1.9182, -0.94157, -0.38448, -0.22572, ...
            0.92751, -4.4206, 1.2913, 0.43895, -0.95301, -0.43347, -1.6036, 0.43538, -0.36566, -0.53076, -0.84434, 1.185, -0.11158, -3.3893, 0.18155, -1.4628, -0.48626, 0.071275, 0.1208, -0.67651, ...
            1.2215, -1.6524, -0.15565, 0.16044, 0.3664, -1.3022, -0.022143, 0.31046, -0.64085, -2.3965, -0.87323, -0.16916, -1.0608, 0.5539, 0.88345, 3.9576, -0.55642, -7.7141, -0.38173, -0.85939, ...
            -0.61409, 0.98473, 3.5214, -0.19859, 0.34184, -0.16481, 0.30821, -4.1253, -0.61669, 0.21794, -0.3719, 0.33651, 2.1805, 0.2133, 0.075392, -0.24276, -0.27649, 0.06532, -0.75618, -0.51179, ...
            10.628, 4.7628, -0.67663, -0.11542, 8.247, -1.1695, -0.37743, 0.089948, 1.1524, 1.6531, -0.88436, 0.58895, 3.2811, 0.76502, 2.6861, 6.4595, -1.6302, -0.22224, 7.7779, -2.999, ...
            0.65704, -0.16273, -0.2085, 0.017033, 0.65192, 1.3662, -0.4322, 3.7228, -0.013416, 1.7324, 1.8732, -4.0593, -8.9394, 1.0625, -0.049498, 5.2385, 0.12334, -0.82275, 2.4628, -5.0686, ...
            4.6355, 0.080471, 0.16303, -1.1015, 2.0344, 1.4057, -9.0414, -0.46068, -0.82052, -1.0304, -0.88031, -0.85533, -0.2937, -0.45158, -0.18595, 2.0274, 1.0306, 6.8588, -0.25087, 1.8068, ...
            0.60554, 0.42195, -0.14517, 0.76752, -1.0278, -1.6517, -0.57418, 2.7544, -1.522, 0.66665, -0.17245, 1.696, 0.25776, -7.5787, 1.8093, -0.32258, 0.38699, -8.1602, -0.79411, -0.84907, ...
            -0.85872, 0.90108, 9.1238, 0.67822, 0.001641, 0.042266, 0.82246, -0.18822, 4.5379, 4.3626, -7.2929, -0.052713, 0.0032147, 0.15011, 0.73305, -2.0031, 0.81923, 0.46728, -0.73901, 0.32895, ...
            -0.78346, 0.51196, 1.221, 0.77861, -0.049135, -0.035054, -9.2161, 1.0177, -0.13744, 1.6942, 2.8473, -2.245, -0.021259, -0.39644, 0.047843, -0.20592, 0.04406, 1.0491, 1.0022, 2.688, ...
            -0.05188, 1.1619, 0.33436, 0.52793, -0.66294, 0.01464, 1.9658, -0.45043, 0.0033662, -0.00093366, 0.88047, -0.080548, 0.18532, -1.0771, 0.011865, -0.030143, -0.90653, 0.0047613, 0.97068, 0.80241, ...
            -1.6143, -0.74324, -0.99956, 0.4278, 0.42187, -0.6766, -1.2259, -1.2083, -0.0036312, -0.15423, 0.90172, 0.14863, 0.81927, -0.82388, 1.3477, 1.5004, 0.0020419, -0.024123, 0.15962, 0.002004, ...
            -0.87811, -0.025943, 0.4877, 1.3129, -0.46193, 0.72358, -0.066538, 0.65472, 0.071972, 1.2607, -0.82446, 3.4565, 0.12103, 1.0237, 1.4456, -0.9087, 0.0020452, -0.19316, -0.14181, -0.035036 ];
        
    case 'Exp1000'
        F(:,1) = [-0.1797, -1.9157, -0.28523, -0.48705, 2.5011, 1.6717, -0.081262, -0.054538, -0.67724, 0.4998, -1.133, -1.7511, 0.3691, 1.3294, -0.96807, -0.29679, 1.0387, -0.79099, 0.54613, -0.65984, ...
            -1.2248, 0.72551, -0.60455, -0.39766, 0.015715, -0.67004, 1.116, -0.89956, 1.1419, 12.353, 0.061388, -1.2676, 0.95423, -2.2742, -0.94963, -1.2326, -1.4342, -0.35102, 2.802, 10.283, ...
            -1.2202, -2.1008, 4.3468, -1.965, 2.1219, -1.1627, -4.6737, -4.2463, -2.2639, 0.1484, -1.7845, -1.1318, 1.4126, 1.2766, 1.5421, -0.58256, -0.90052, -0.30291, 0.092677, 0.49264, ...
            0.40526, -12.281, 0.89233, 1.5836, 2.3287, -0.94157, 12.131, 0.57175, 1.1296, -12.361, -1.1767, -4.4147, -0.72977, 13.657, 0.079019, -0.54906, 0.31899, 1.1424, 1.2057, -0.84513, ...
            0.94148, 1.4107, 1.749, -2.5267, 0.37591, -1.5746, -10.611, 0.38094, 6.1264, 13.683, -2.0103, 3.8398, -0.10905, 0.71639, 0.42995, 0.5886, 0.29688, 1.606, 0.65576, -1.2782, ...
            0.48486, 3.0486, 0.012371, -3.078, 0.28451, -0.091272, 4.5466, -1.2524, 6.5635, -1.2036, 0.9309, -1.3912, 12.945, -11.409, -2.5442, -1.8952, 0.5233, -0.0023339, -0.48492, -4.4824, ...
            -0.19308, -0.14411, 0.025671, 10.209, -0.0086182, -0.15017, 0.60856, -2.561, 1.8417, -1.7247, 8.7337, -0.48545, -0.81897, 1.0621, 0.13658, 1.3585, -0.85946, 4.937, -0.79465, 4.0067, ...
            -1.5602, -1.7403, -8.0727, 0.84871, -10.632, -0.025146, 0.84553, -1.1679, 0.025574, -0.87184, -0.25254, 0.088615, 0.73772, 0.25254, -0.21671, -0.30403, -7.497, -0.82967, -0.55943, 2.411, ...
            -1.4119, -0.69814, -0.53769, -0.44147, 0.51344, 5.605, -0.53735, -12.432, -1.7722, -0.61578, 0.0049615, 1.8675, 0.89177, 0.69297, -0.3498, -0.58745, -0.39913, -0.0075133, 0.37568, -2.0333, ...
            0.16888, 0.50137, 0.47451, 1.1671, -0.061816, 0.17129, 1.7673, -1.163, -0.5275, -0.72111, 10.631, 0.48017, 1.3566, 10.967, -1.5947, -0.32701, 1.0188, 12.154, 6.8219, -0.050255, ...
            -0.63132, 0.14312, -0.95158, -0.53204, -0.93112, -0.44046, 1.3967, -2.4887, 1.6152, -2.9204, 4.5706, 2.7743, 0.876, -0.16158, -1.8798, 2.716, 0.70285, -2.1359, -0.895, -1.1702, ...
            -1.1434, -0.61521, -0.50367, -3.1273, 0.64519, 1.1474, -0.67622, -1.5816, -0.3603, -0.40259, -1.4096, -0.64913, 1.4219, -0.4671, 0.13181, 2.6322, 0.46757, -1.3168, -2.7976, -1.2744, ...
            -1.0487, 0.28327, -0.23674, -1.2867, 0.87403, -0.56963, -1.4938, 0.6136, 0.1713, 9.656, -1.9463, 0.39351, -0.66906, 0.22934, 1.5222, 3.5347, 1.7034, 0.36961, -1.2437, -2.0832, ...
            5.9989, 1.1348, 1.3034, 1.3735, -0.082651, 2.5788, -0.41207, -0.55363, -2.9663, 2.5693, 0.22426, 0.18981, 0.30226, -1.2585, -1.1944, 0.50213, 0.0067866, -0.40536, 10.798, -1.4138, ...
            3.5309, -1.0318, -3.0212, 1.2593, 0.075353, 2.3567, -0.057499, 2.0863, -1.3474, 1.3823, -0.981, -0.18571, 0.6381, 7.0488, -2.0222, 3.9082, 1.281, 0.57999, -12.173, -1.9798, ...
            0.15653, -2.4357, 0.2964, -0.2456, -2.412, -1.7568, 2.054, 1.2855, -2.6017, 1.4525, -0.78805, -0.547, -0.9397, 0.27798, -0.38559, 0.49131, -0.52316, -1.1034, -0.14815, 0.59675, ...
            -0.22439, 0.57155, 0.17404, 0.11353, 0.39426, 12.998, 11.2, 0.61471, -1.2379, -3.8022, -1.7905, -0.63636, 0.39216, -0.4334, -0.050114, 0.32073, 14.054, -0.49778, 0.079519, -0.14606, ...
            3.5954, 0.4651, 1.116, -0.36357, 0.99704, 0.71803, 1.79, -0.42173, -1.438, -8.6215, -0.26409, 0.41278, 11.721, 1.8568, -0.78572, -1.4399, 0.1063, -1.7972, -1.5581, -0.59588, ...
            0.38568, -1.2685, -9.3709, 0.51286, -1.8695, -0.089789, 1.582, -1.4359, -1.1726, 0.87502, 5.2076, 0.3523, 0.51296, -1.6078, 1.4072, -8.2806, 1.0622, -10.43, 0.97832, -0.23108, ...
            -1.3254, 1.955, 0.45963, -3.9025, -0.22867, 2.2012, -0.92962, 1.9746, 11.205, -0.14968, 0.70026, -0.55846, 0.61999, -1.7173, -1.7866, -0.25242, 1.1135, 4.9957, 5.0178, -1.5076, ...
            0.5811, 0.61121, -9.9217, -0.48432, 0.53113, 11.763, 0.11159, 1.6605, -0.17669, -0.3244, 0.38822, -0.80239, -0.62887, -2.2699, -2.571, -1.9568, 0.68645, -0.72051, 0.20703, -2.3933, ...
            -1.3152, -0.48682, 1.1021, 1.8874, 0.60986, -1.3851, -3.0142, 0.94572, 2.4792, -1.8436, -0.73958, 1.9022, 2.3628, 0.13957, 2.3604, -0.84737, 2.0823, -0.14043, 0.45181, 1.7598, ...
            0.85934, 0.80756, -0.68544, 10.805, -4.6158, 3.2832, 0.27912, -1.3275, 0.68768, -3.0645, 1.6157, 0.29829, -0.14731, 1.398, 0.31731, -1.1797, -0.45081, 0.82424, 0.86422, -3.4866, ...
            0.26762, -0.17389, 1.2292, -3.9829, 0.061317, -0.21745, 2.1283, 2.9728, 3.7767, 0.78033, -0.083109, 0.075642, 4.3838, 0.14338, -1.4873, 0.8301, 4.6396, 0.11754, 0.67674, -0.019017, ...
            0.015472, -7.5841, -9.1833, -0.18025, 1.3583, 0.10065, -0.64528, 1.1722, 6.1514, 0.18271, 0.51352, 0.40009, 2.0304, 5.7287, 0.3231, -0.67362, 0.36513, -0.22076, 2.966, -10.461, ...
            -3.3181, 0.83265, -1.0196, -0.15106, 1.786, -0.49952, -2.3869, -0.40561, -1.0344, -4.6279, -1.9627, 2.3547, 1.2249, -0.32535, -3.9595, 1.2514, -1.5539, 1.842, -0.6491, 5.4172, ...
            3.0896, -3.1958, 0.30193, 0.91642, 2.4112, 1.2896, -0.50491, 0.13399, 3.7918, -1.2136, -3.8924, 0.1309, -2.8346, -3.6813, -0.25163, 0.42228, -1.1954, 4.0501, -0.45989, 0.58598, ...
            0.6511, 0.52421, 0.19548, -0.48147, 1.908, -4.6841, 2.2647, -0.69811, -0.80166, 0.83512, -0.33377, -0.55587, 4.0189, -0.24627, -0.66877, -4.9555, -0.10816, 0.52582, 0.44124, -0.51382, ...
            0.10126, 0.5301, 1.4383, -2.7285, 1.818, -0.4579, -0.22892, -1.1381, 4.9937, 0.088266, 0.60259, -2.3856, 0.23041, 0.5084, -6.5478, 7.3121, 0.46989, -0.75582, 0.52079, 0.038602, ...
            2.7503, 0.0019869, 2.7169, 2.4501, -0.26045, -0.75555, -0.25121, 3.7208, -0.45583, 0.2924, -1.8871, 0.98646, 0.24048, -0.70195, 4.435, -0.52529, 0.98003, 0.54581, -4.6972, 0.45417, ...
            -4.2849, -0.45274, -6.8481, 2.8446, 0.36562, 2.4123, 0.22097, -7.4762, -3.1775, 1.1119, -0.62864, -3.2078, -0.16723, -2.0823, -0.39898, -0.61174, 0.29451, -0.0066585, 0.18362, -1.7602, ...
            -0.54291, -1.939, 0.11279, -0.39841, 1.6508, 0.097844, 8.422, -3.6878, 6.3862, -0.1976, 0.44123, 0.6656, -0.92525, 3.1995, 0.066337, 0.070456, -1.2053, 0.67691, 0.44051, 0.016468, ...
            0.47522, 0.7171, 0.28855, 0.38829, -2.859, -3.8267, 0.80307, -1.0945, -2.0097, -0.57771, 0.59943, 9.4537, 2.0317, 0.027761, -0.54767, -0.40867, 1.9907, 1.8542, 0.19152, 0.56337, ...
            -4.7118, -0.031228, 3.4865, 5.0051, -0.5105, 2.0403, 1.9006, -1.0426, -0.29195, -9.5992, -0.49093, 0.7487, 0.19713, -0.73462, -4.1121, -4.5177, 0.17517, 0.09027, 5.5386, 0.51164, ...
            -0.81668, 0.61667, 1.0995, -0.11784, 3.6547, -0.26721, 0.059553, 0.96666, -0.20796, -0.55463, 0.15284, -0.19803, 0.40611, 0.54606, -0.0092328, -0.75075, 0.18579, 0.11175, -1.4253, 0.12735, ...
            0.93963, -0.59338, -0.37369, -0.31187, -0.91925, -2.9861, 1.0681, -3.0486, 1.5298, 0.25632, -0.1771, 1.0857, -1.292, -0.035737, -7.8751, -5.1854, 4.1029, -2.4426, 0.19766, 2.3242, ...
            -0.0028671, -3.5972, -3.0419, 0.13967, -0.39551, 8.3931, -0.082966, 0.74571, 0.14152, -0.5005, 0.40483, -0.89213, 0.47096, 0.3354, 8.6326, 0.54235, 9.5318, 11.331, -0.022186, 0.74906, ...
            0.20674, -0.95243, -0.0058095, 0.052768, 1.0721, -0.2558, -1.7101, -0.047395, -0.091388, -0.19091, -0.7493, -0.19219, 0.14257, -0.18557, -0.17886, 0.1966, 6.0384, -0.081047, 1.0501, -2.2864, ...
            -2.6817, -0.096109, 0.2222, -1.0809, -1.163, 0.43142, -1.6178, -0.11396, -0.10714, 0.48491, -0.16698, 0.50415, -6.5409, -0.64736, 10.154, 0.71326, 0.023344, 0.63665, -5.4312, -2.9013, ...
            0.29843, 0.026212, 1.3792, -0.17612, 0.59842, -0.10425, -1.0428, -1.0606, 0.10373, -0.68961, -0.88339, 1.4813, 0.067448, 7.469, -0.16099, 0.032768, 0.13341, 0.14326, 1.5186, -1.6115, ...
            7.593, -6.7289, -0.78717, 0.7669, 0.015583, 0.17274, 1.8098, 0.62075, 0.42011, -1.9438, 1.4759, -0.19569, 0.5715, -0.15471, 0.31273, 0.19252, -0.66709, -0.22559, 0.63459, 0.71533, ...
            0.31041, 0.90402, -0.24256, 0.38774, -4.356, 5.4916, -1.1727, 0.29098, -0.3577, 0.33944, 0.13683, -1.0772, -0.69479, -0.0092212, -0.77001, -1.1786, 1.0745, -0.4766, -0.57842, -13.232, ...
            0.68074, 0.034385, -0.5182, 0.12324, 1.524, 0.77996, -0.012346, -3.2657, -2.8796, 0.070222, -3.1011, 0.28246, -0.19321, -0.11088, 0.74939, -0.6357, -0.05933, -0.28163, 0.050073, 0.56857, ...
            -0.68324, -2.6268, 0.57807, 0.037122, 0.62036, -0.7698, 0.052216, -0.030382, 0.86278, -0.75141, -0.0010613, -0.70698, 0.1552, 0.22542, -3.5755, -0.10459, 0.18399, 0.24576, 1.2532, -2.1328, ...
            -2.2091, 0.58472, -10.279, -3.6268, -5.0022, 0.022889, 0.92247, 0.055337, 1.1527, 0.11767, -0.36682, 2.3327, -0.45259, -0.040172, -12.725, -0.027383, -0.14452, 2.3733, -0.72232, 0.65309, ...
            0.46746, -4.6962, -0.80149, 0.71126, 0.31689, 1.4026, -0.8291, 11.551, 0.48325, -0.4093, 0.17145, 0.33619, -1.0726, -0.092279, -0.16769, -0.16842, 0.74458, -0.16258, 0.90787, -0.050433, ...
            -0.38777, -0.33555, -0.18075, -0.13935, -3.6601, 11.452, -1.1913, -1.296, 0.24945, -0.88824, 3.3232, 0.39876, 5.8873, 0.10083, 0.19893, -0.63501, 0.51536, -0.0068052, -0.25191, 0.017387, ...
            5.4334, -0.64272, 0.93758, 5.8374, 2.2145, -0.75013, -1.2185, 0.59392, 11.13, 0.010387, -0.80905, 1.0198, -0.29465, 0.12373, 0.066038, 0.62004, -0.92143, 0.4266, 0.16916, 0.0819, ...
            0.27191, -1.0693, -0.11227, -0.18621, -1.4158, -0.30133, 0.70176, -5.1237, -0.62573, 0.040937, -0.7661, 0.54065, 1.6073, -0.061231, -0.83676, 1.3022, -0.58635, -0.42182, -1.7301, 0.11147, ...
            -0.19087, 0.19553, 0.51299, 0.030833, -0.87745, -0.47137, -0.33589, -0.99528, -0.21786, 0.10178, 0.20583, 0.77221, 0.8661, -0.83247, -2.3799, -0.039024, 0.018282, 10.17, -0.98185, -0.51427 ];
        
        F(:,2) = [0.16637, -0.89756, 0.3834, -0.10538, 1.1889, 2.3367, -1.4174, 0.10837, 0.19597, 0.43464, -0.066724, 0.97423, 1.1662, 0.50147, -0.64966, -0.64827, 1.2765, 1.0511, 1.5764, 0.2273, ...
            -0.3958, -1.7047, 1.5307, 1.3266, 1.4299, 1.472, 0.78494, -1.402, 0.028425, -1.4514, -1.2988, 3.9445, 1.3689, -2.3804, 0.10046, -0.70138, -2.9341, 6.5722, -5.8932, 9.5899, ...
            -0.38696, 1.1015, -3.0556, 0.84173, 10.119, 0.084639, -1.0107, -3.8696, 2.5145, 0.27264, 1.9017, -0.19501, 0.48237, 0.12169, -0.36167, -0.31276, -1.885, -0.66675, 3.6824, -1.8626, ...
            2.5825, 0.31605, -1.1387, -3.2511, 0.95585, -2.0831, 9.0119, 0.2678, 1.7454, 6.7001, -1.6412, 0.3838, 0.41059, -5.1419, -1.6516, -1.8637, -0.36023, 1.1356, 0.60789, -0.56418, ...
            7.4972, -2.1085, 1.1641, 4.6513, 0.20061, 0.51258, -3.1186, 2.6737, 2.5111, -3.8066, -4.3905, -7.8591, 1.6659, -1.5179, -2.5465, 0.1416, -0.13893, 1.5561, 1.7209, -0.20186, ...
            -0.7194, -1.1474, 1.6623, 7.1872, 1.7957, -0.80592, 0.37397, 0.81384, 6.9456, -9.2276, 3.3634, -0.62117, -0.38991, 4.6563, -1.5722, -0.93632, -0.39052, 2.7447, 0.15884, -5.8769, ...
            -0.055618, 0.78109, -0.59775, -9.9202, -0.045759, 0.14579, 0.048128, -2.5958, -0.4961, -0.89002, -7.5596, -0.38048, -0.72114, 0.96715, -0.75128, 0.32181, -0.034194, 0.87466, 2.6965, 2.4543, ...
            0.64719, 2.7174, 0.046853, 2.7482, 7.9043, 0.96019, -1.8149, 1.7794, 0.46818, 0.34196, 0.5441, 0.084523, 0.4987, -1.0344, 0.016483, 2.0472, 4.2922, -1.3882, -0.37859, -2.6886, ...
            0.99688, -1.1779, -1.2424, -0.12177, -3.0584, -0.19021, -1.0292, -4.4075, -1.7459, 0.21028, 1.2454, 1.0868, -0.57431, -0.20973, -0.20702, 1.493, -0.92441, 0.54465, -0.55936, -0.31989, ...
            -0.42882, 2.0787, -0.32048, 2.6219, 0.05329, 1.1176, -0.25077, 0.3621, 0.52476, 1.8996, 7.4128, -0.27309, -1.5365, -0.4803, 0.49344, -0.6833, 1.3585, -2.4573, 6.4394, -3.8048, ...
            -0.059966, -1.3607, 1.0086, 0.68736, -0.42744, 0.31873, -1.5275, -0.096831, 2.0239, -8.7199, -3.7432, -1.009, 1.0481, 1.8305, -6.5062, 0.86393, 0.0020232, -2.5366, 0.094796, 0.77898, ...
            0.105, 1.0341, -3.8808, 1.6747, -0.17773, -1.9234, 5.141, -0.59952, -0.48922, 3.0458, -0.16082, 2.849, 0.98376, -0.42811, 0.40039, 1.5951, 0.39351, 4.2008, 3.7385, -0.16582, ...
            -2.4417, 0.0098007, -0.10068, 2.098, 0.11958, 0.14205, 1.5901, -0.2417, 0.067511, -8.0574, 3.0469, -0.24791, -5.416, -0.83221, -0.15379, 0.23561, 0.078505, -1.3441, -0.69209, -1.3421, ...
            -0.034075, -0.31919, -0.22575, -0.39095, -0.74717, -3.0889, -0.64472, -0.0096501, 0.88685, 1.1621, 0.17356, -0.91304, -0.4629, 0.105, 0.60749, -0.50592, 0.54211, -0.32201, 5.4936, -1.8613, ...
            -9.0898, 0.29554, 0.98706, -0.30493, -9.8169, -0.42619, 4.5761, 2.641, -0.69187, -0.58931, 0.33865, 1.4432, 2.8045, 6.3936, -0.29228, 1.0226, 2.1388, -0.23537, 6.0032, 2.491, ...
            -0.039165, -3.6461, -3.5802, -0.50249, 4.042, -3.9487, -2.8173, 2.1951, 3.6911, -0.3889, 0.20774, -0.29246, 0.85993, 0.8437, -4.6042, -2.4654, 0.42551, -2.7045, -0.53176, 0.3405, ...
            1.5096, -1.0162, -0.50653, 0.08321, 0.56025, 1.5202, 0.07028, 0.30126, 0.65785, -2.1112, 0.83738, -3.1097, -4.6277, 0.0028481, -0.4752, -0.94792, 1.7634, 0.0084515, -0.6305, 0.34123, ...
            1.5356, -0.42903, 0.22557, 0.092636, 5.3941, 0.6577, -1.1186, 0.87943, -0.82468, 3.909, -0.26494, 2.1427, 2.2168, -2.1479, -2.0493, 0.087003, -2.09, 0.5572, 1.3162, -2.7009, ...
            0.388, 0.78166, 2.6054, 0.63343, -1.7106, 2.2474, -0.026036, 2.1703, 0.051951, -0.88375, -2.3928, 0.47517, 0.53751, -0.32194, -0.54353, -7.7163, -1.4556, -7.9133, -1.6125, 0.9494, ...
            -0.35316, 1.5131, -0.29131, -0.9942, 0.71653, 0.060801, 1.0337, -0.64504, 4.2018, 0.9942, 0.72848, -0.61798, -0.84767, 0.18008, 2.1873, -0.86346, 0.60695, -1.8703, 10.252, 1.9164, ...
            -0.73079, -0.21595, -0.18633, 0.89596, -0.41425, -5.7618, 0.73793, 0.28789, 0.020243, -0.055542, -5.6146, 0.17657, 0.12576, -0.65412, -2.4839, -1.1995, 0.74546, 0.80974, -1.206, -3.4609, ...
            -0.95033, 10.143, -0.22579, 1.8349, -1.2022, -1.2493, -2.9166, -0.66535, -2.9039, -0.82237, -2.246, -0.76026, -1.3202, 0.26811, -0.097266, 0.10527, 0.17651, 3.3826, -0.67072, -2.251, ...
            -3.6303, -0.67322, -0.86814, 6.2551, -3.1028, 0.42048, 0.3492, 0.91973, -1.2342, 0.83955, 0.099556, -0.56485, -0.25303, -0.091432, -0.33307, -0.13357, -0.39196, -0.88357, 0.51564, -1.3313, ...
            1.4488, -1.1586, 2.6938, 2.8961, -0.58827, -0.9506, 0.46664, -2.1866, -1.5404, -1.0266, 2.7233, 0.72572, -1.2178, -1.9739, 2.8837, -0.80909, -0.8523, -3.7548, -0.39924, 2.5801, ...
            0.83571, -3.0972, 0.89087, -0.73808, 1.8069, -0.75843, 1.4541, -0.31898, -4.7423, -0.66015, -0.36622, -1.8104, -0.36733, -2.215, -1.0053, 2.422, -0.59841, -3.1989, -2.1083, -0.19977, ...
            -4.6445, 1.7623, -0.98458, 0.67966, 0.32273, 0.34659, -0.36063, -0.63705, -0.40718, 4.9447, -5.1357, -0.093454, -0.88832, -1.2136, 1.7927, 1.5421, 0.98671, -0.38608, -0.55938, -2.2702, ...
            0.39089, 3.4422, 6.3779, -2.4323, -0.61553, 0.87737, -0.44158, -1.4071, -3.9189, 2.933, -1.3905, -0.59813, -3.808, 4.044, -0.48262, 0.26989, 4.1733, -3.0425, -0.19087, -0.11405, ...
            0.15062, 0.18261, 1.4156, 0.46474, -1.4497, -0.42479, -0.73679, -1.0059, -0.59803, -1.0679, -0.67863, 0.22585, -2.2176, 0.94288, 0.93119, -5.9965, -0.38453, 0.4556, -0.40018, -2.0149, ...
            -0.085753, -0.21176, -1.1377, -2.0136, 0.98104, -0.49616, -0.68113, 1.3034, 4.205, 0.64813, 0.30496, -0.60004, 0.7377, 7.0143, 1.5067, 3.2111, -0.58846, 7.7338, 0.528, 1.5804, ...
            1.7823, 1.0938, 4.807, -0.49619, 0.034325, -0.3549, 0.90128, 0.47639, 0.32516, -1.1091, 2.5348, 0.22803, -0.57458, 1.9054, 6.2454, -0.070747, 1.1017, -0.21956, -1.458, -5.8319, ...
            3.6281, 0.19601, -1.9871, 0.078757, -0.28366, -8.1874, 0.83118, 1.3182, -6.7655, 3.8961, 0.10884, 3.8392, 0.12336, -0.65148, -0.3722, 0.25541, -8.3161, -1.4919, -0.67656, -1.136, ...
            0.063023, -0.59732, 0.21873, -0.23745, -0.56269, -0.6601, -6.412, -0.86019, -0.32688, 0.66155, 0.57525, 0.24518, -0.98875, 0.82367, -0.67758, 0.73323, -0.61693, -6.7341, 0.62144, 0.24502, ...
            0.13601, -1.5342, 1.293, 0.095518, 0.12397, 0.17254, -0.4281, -1.1616, -5.2142, -1.2413, 1.9513, 1.5113, 4.3194, 1.3588, 0.1601, -1.0623, 0.40061, -0.55273, -0.65906, -0.18296, ...
            -2.5629, 0.92894, 3.606, 1.6094, -0.56264, -0.62755, 2.5373, 4.0718, 0.38154, 5.2706, -0.57986, -1.0187, 0.044964, -1.5323, 4.3365, 1.9058, 0.1882, 0.058826, -0.65152, -1.8991, ...
            0.33351, -0.18541, -0.47562, 0.062765, -1.2244, 0.2536, 0.33468, 0.41276, -0.033549, 0.080767, 0.60309, -0.71512, -0.62108, 0.24863, 0.37142, 0.11866, -0.82173, 0.58554, -4.3826, 0.38082, ...
            4.7145, -0.73818, 0.53738, 0.69538, 0.688, -1.3839, -1.1786, 4.4513, -2.1039, 0.50064, -0.94286, 0.56179, -1.4948, -0.46792, 0.096495, 0.34007, -4.1774, 0.42693, -0.49502, 1.6086, ...
            0.0032306, -0.45486, -4.8221, 0.20111, 0.15264, -0.53961, 0.0083421, -1.6675, 0.18509, -0.21607, -2.966, 5.0059, 0.1329, 0.027527, 4.2948, -0.13146, -6.5676, 1.9545, 0.79691, -1.6083, ...
            2.1498, 1.1097, 0.0021, 0.30284, 0.19825, -0.04415, 1.1001, -0.10044, 0.30878, -0.18312, 0.12544, 0.25172, 0.59191, 0.061688, -0.73181, -1.6319, -7.9704, -1.301, -0.58876, 1.2982, ...
            1.0705, -0.22603, 0.075463, 0.25596, 1.5189, 0.42678, 6.4452, 0.055798, -0.73342, -0.27544, -0.54588, 0.45789, -8.4492, -0.090029, -1.2597, -0.28003, -0.0032009, -0.21668, 1.4144, -2.1079, ...
            -0.76761, 2.7242, 3.3246, -1.3325, 0.50694, 0.80049, 0.33882, 0.57819, -0.15934, 0.48861, -0.28857, -10.599, 0.036322, -2.4122, -0.6921, 0.97026, 0.63764, 0.040566, 1.1267, -1.1656, ...
            0.44414, -0.87182, 0.62016, -0.5457, -0.023377, 0.25643, -0.1257, -0.42168, 0.57733, 1.3955, -0.96778, -0.12436, -0.54868, -0.0028617, 0.64765, 0.083161, -9.7528, -0.019102, 1.1394, 0.12021, ...
            0.33604, -2.0103, -0.046481, 0.24226, -2.7749, 2.7735, -0.4598, -1.0223, -0.73193, 0.30267, 0.1115, -0.25841, -0.88809, 0.80956, -1.2381, -1.7498, -0.085349, 0.22505, 0.6467, 1.3797, ...
            0.55698, 1.951, 0.062753, -0.52651, -7.305, 0.16786, 1.3679, 0.29512, 6.1116, 7.4379, 2.3206, 0.18538, 0.54528, -1.3935, 0.65059, 0.64735, 0.36789, -0.35992, -2.1626, 0.93646, ...
            -0.043228, 1.327, -0.069071, 0.1361, 0.64388, 0.22289, 0.065902, -0.34546, 0.89117, -0.27917, -0.996, -1.3516, 0.69185, -0.10801, 0.42251, 0.86036, 0.20594, 0.58166, 0.20227, 1.0011, ...
            -0.52948, 0.25565, 4.2935, 0.52417, -1.3299, -0.028063, 0.45194, -0.07348, 0.13043, -0.054697, 0.54773, -0.86205, 0.55788, -0.094963, 3.6125, -0.52908, 0.31908, 2.4732, -1.2113, 0.90731, ...
            -0.66055, -2.9875, 1.4365, 1.236, -0.69162, -0.69099, 0.4769, -1.7642, 1.869, 0.90836, 0.13287, -1.0327, -0.78559, -0.29789, 0.12622, 0.45215, 1.1301, 0.74383, -0.32817, 0.0071263, ...
            -0.29363, 0.31929, 0.2487, 0.18403, 9.7382, 5.2505, 0.055648, 8.5172, 0.85606, -0.41149, -8.4664, 0.17174, 4.832, -0.036494, -0.1956, -0.34552, -0.2448, -0.62332, 0.62648, -0.007658, ...
            8.8812, 0.52568, 0.64836, -0.92622, -1.0998, -0.90044, 0.13516, -0.81019, -4.9757, -0.022752, 0.3285, 0.065621, 0.18347, -1.3866, -0.48359, 1.1394, -1.2459, 0.33341, -7.8407, 0.21601, ...
            -0.30193, 0.56771, -0.13357, -0.06628, -0.017796, -0.93413, 0.2898, 6.3238, 0.56067, -1.1241, -0.83155, 0.042369, -0.68413, -0.084441, -0.2895, -1.3734, 0.1242, 0.37776, -1.6114, 0.14424, ...
            0.13445, -0.028307, 0.042046, 0.51054, 3.1029, -0.011314, 1.298, -0.028019, -0.18139, 0.022593, -0.57768, -2.5479, -0.9886, 0.1878, 0.18344, -0.00053621, -0.098647, 9.4732, -1.1918, 0.096464 ];
        
        F(:,3) = [0.8351, -0.26051, -0.72632, 0.71045, -10.631, 2.2095, 0.079686, 0.86157, -0.19153, -0.59501, 1.9203, 2.0874, -1.1428, -0.3749, -0.84095, -0.15544, -0.2831, 2.0971, -0.060367, -0.5784, ...
            -3.14, 2.3117, 0.36533, -0.84421, -0.042104, -1.5008, -0.28363, 1.912, 0.57195, 2.3415, 0.99537, -3.3549, -0.049399, -1.5237, -0.53294, -2.3859, 1.7253, -1.1855, -4.4444, 1.6917, ...
            -2.4831, -1.1881, 8.0087, -0.23603, 5.0505, 2.3168, 0.69632, 0.74858, -0.1881, 0.70217, -0.53501, -1.9051, 1.7095, 0.3548, -1.6155, 0.20942, -0.97753, 0.84821, -0.27187, -2.3181, ...
            -0.47171, 3.952, -1.0203, -0.32896, -1.4528, -0.4492, 0.86847, -0.25168, 1.1255, 1.6367, 0.45195, 1.9068, 2.5274, 3.8481, 1.2841, -2.3258, 2.5862, -0.26432, 0.89858, 2.9611, ...
            4.4958, 0.81375, 0.94851, -9.7914, -0.64412, 0.33126, 7.3991, 2.771, -4.1808, -5.2628, 10.111, -1.0732, 0.29446, -1.4572, -0.67554, -0.30057, -0.61124, -0.63918, -2.5795, -0.30973, ...
            0.068908, 1.6054, -0.3772, 0.88156, -3.4663, -0.27479, 10.095, -0.0049268, 2.0223, 2.9512, 0.85701, -2.4315, -3.0506, -4.9694, -2.6367, -1.761, -0.15903, -1.9521, 2.6658, 2.749, ...
            -0.7041, -0.39352, 0.76277, 1.6624, -0.9683, -0.94656, -0.27676, -0.66359, 0.95327, -2.5745, 5.8994, 0.26236, -0.029123, 0.64994, 0.094551, -1.8564, -0.28593, 3.2396, -1.7861, 3.0909, ...
            -1.0245, -1.6638, -0.20884, -2.4906, 1.2437, 0.2565, -0.74845, 1.9602, 1.2184, -1.0462, -0.46072, -11.706, -2.4944, -1.0923, -0.75515, 1.4455, 1.3608, -0.2248, -0.0093902, -0.7373, ...
            -2.6855, -0.37291, 1.1535, 1.2098, 1.9769, 0.10381, 0.5728, 2.7687, 0.70522, -0.12942, 1.1215, 0.080867, 0.52728, -0.076366, -0.67252, 0.642, 6.6113, -0.76303, -0.8971, 0.48596, ...
            0.66174, -0.544, 0.37745, -2.299, 0.80228, -0.43756, -4.3409, 0.22748, -0.54469, 2.5128, -1.0994, 0.36807, -2.5153, -7.5865, -0.02368, -0.13332, -0.78119, 5.0669, -0.78859, -0.54238, ...
            -0.19046, 0.66362, -0.18259, -4.6124, 0.73842, -0.42069, -0.80912, -1.5444, 0.64889, 3.4944, -7.5849, -3.6964, -0.69043, 1.7616, -8.6599, -3.6496, -0.2234, -7.9018, 0.42093, 0.013351, ...
            0.81717, -1.0076, -2.4754, -3.1295, -0.016146, 1.2451, 6.8335, -3.8778, -0.92467, -2.0175, 0.32157, -3.1235, 1.2761, -0.1935, -3.8836, 0.2574, 0.25579, 1.441, 0.028932, 0.66795, ...
            0.16549, 1.254, -0.88534, 1.02, 1.9299, -0.32166, 1.3971, 0.11487, -0.94402, 3.2555, 0.36542, -0.64469, -1.3547, -0.40611, 0.051885, -0.92948, 2.3596, -0.91254, -0.45286, 5.126, ...
            -0.29823, -0.5252, -0.63613, -0.54839, -0.17707, 3.7678, 0.11615, 0.75206, -4.5287, -0.93945, 5.7072, -1.4179, 0.73888, 0.80349, -0.65967, 0.53888, 0.9808, -0.59334, -4.6969, 1.2911, ...
            -1.1821, 4.9924, -4.7606, 0.54696, -0.33283, -5.1654, -1.1941, 0.15906, 0.35091, -3.1003, 0.24863, 1.609, 1.6777, 1.95, 0.73181, 4.0414, -0.78765, -0.23736, 4.1904, -1.7139, ...
            0.782, 3.6795, -0.85825, 0.58927, 0.7691, -2.0171, 0.99036, -0.63145, 1.3869, -0.058294, 0.16024, 1.0253, 4.0733, -0.33002, 0.82612, -0.88007, -0.06726, 1.7122, -0.98666, -0.0070422, ...
            1.5076, -1.2007, 1.0105, 0.95061, -0.90807, -2.2009, -6.9973, -0.11146, 1.4985, 3.6499, 0.074491, 1.96, -0.90791, 1.1659, -0.8909, 0.54082, 1.343, -0.58664, -0.43377, -1.1164, ...
            -0.81618, -0.28402, 1.7807, 1.0668, -0.47958, 0.31994, 1.873, -2.7635, 2.0339, 2.4148, -2.8449, -0.35533, -5.9501, -0.86815, -0.094822, 0.056413, 1.1289, 5.3873, 1.9974, 5.0208, ...
            -0.9469, 0.10365, -9.174, 0.32126, -5.0986, 1.4378, -0.20375, 0.59876, 0.9838, -0.33551, -1.7546, -0.81424, -0.29852, 0.7553, -2.7431, -6.655, 0.26257, 0.17721, -0.61219, -0.59741, ...
            -0.43008, -2.0547, -0.43003, 4.066, -0.23226, -0.45571, -0.41344, -0.89646, 5.8899, 0.5124, 0.53907, 0.77468, -0.51068, -0.13795, 4.9764, -0.75421, -3.2384, 1.8023, -0.080885, 2.3804, ...
            -0.70143, 0.48723, -8.8895, 0.58049, -0.77628, -0.90918, 0.77224, 0.61381, -2.88, -1.0266, -7.7947, 0.78116, -1.1049, 0.030684, 8.817, -0.40465, -0.63064, -0.49983, -0.17571, -3.889, ...
            9.4684, 5.2431, 0.82405, -2.4108, 0.78141, -2.4841, -3.9623, -0.43741, -4.2235, -0.0036973, -1.646, 1.1923, -3.3748, 0.76515, 0.38482, -0.78525, 3.0572, -4.6129, -0.75522, -0.60115, ...
            -0.0054868, -3.5237, -0.53735, -4.6426, 8.7955, -1.8214, 1.0544, 1.275, 0.40692, -2.0974, 0.55805, 1.9483, 0.70581, -0.13188, 0.61023, 0.90602, 0.28939, 0.23293, -0.011052, 4.0312, ...
            -0.10635, 0.48883, 2.1008, 8.2141, 0.9544, -0.83767, -0.74021, 1.0269, 0.53132, -0.13419, -0.80784, -0.036771, -4.7365, -0.51089, 0.93985, -3.2714, 8.5007, -0.22688, -0.28516, -1.7671, ...
            1.877, 4.7976, -2.2363, -0.27983, 0.87342, -0.081656, -0.2234, 0.81799, -1.4771, 0.29368, -0.2243, 1.2246, -0.16899, -7.5138, -0.45294, -0.95002, -0.31717, -5.8037, 2.0353, 9.5357, ...
            -8.6104, 5.1665, 0.28272, 0.22134, -1.6012, 1.5917, -0.75629, -0.042866, -2.9764, -5.0323, -0.89448, 0.31505, 2.8558, -6.4858, -4.9544, 0.37987, -0.41006, 0.11445, -0.67947, -6.0477, ...
            -7.8761, -7.1117, -5.2928, 0.64142, 0.29912, -6.4018, -0.2216, -0.40486, -1.7418, 1.1923, -5.1594, 0.52745, -2.7901, 1.5846, 0.50722, -0.81399, 3.3875, 4.4886, -0.53462, 0.26887, ...
            -0.038808, 1.021, 0.31122, -0.018134, 0.6, 4.8801, -0.65305, 3.4983, 0.059579, -4.0947, 0.097305, -0.19399, -0.17835, 0.35517, 0.47451, 2.363, 0.61171, 0.62164, 0.46344, -0.79018, ...
            0.74463, 0.27173, 0.0043422, 1.7603, 1.4615, -1.7515, -0.27048, -0.026943, -8.3789, 0.46269, -0.46595, 10.355, -0.040968, 3.8437, 8.7471, 7.7185, 0.17009, -1.7686, -0.20495, 1.3487, ...
            -1.5618, 2.6107, -1.796, 0.62681, 0.68171, 0.18187, -0.20134, 0.44988, 0.33936, -1.0346, 4.7766, 3.5823, -1.5638, -0.0038203, 3.7172, -0.3416, 0.3083, 0.2246, 10.069, 0.48283, ...
            0.42438, -1.2764, -8.3465, 2.2155, -0.7158, 0.18806, -0.38094, 2.1627, 8.0792, -4.0567, 0.14708, 2.2254, -0.40273, 2.4256, -0.31608, 0.099006, 2.5169, -0.50664, 0.20566, -0.38347, ...
            -0.3908, 0.081789, 0.85647, 0.45429, -1.3494, -0.33186, -1.0548, -0.57783, -8.8192, -0.23886, 0.20981, -0.84913, -0.83464, 1.4845, -2.0667, -0.036292, -0.66143, -0.79393, -0.32382, 0.44326, ...
            -0.37172, 0.74211, -0.93518, 0.6476, -10.39, 0.0077541, -0.28896, 0.1506, -0.10761, -0.55545, -0.0088185, 9.2782, 6.0029, 0.39747, -0.35861, 1.3531, -0.0046312, -0.53127, 0.25041, -0.090448, ...
            -1.9478, -0.51121, 1.9402, -5.585, -0.12707, -0.50506, 1.068, 5.117, -1.283, 7.5934, -0.32899, 0.6075, 0.068898, -1.11, -0.12813, -5.8883, -0.34419, 0.14362, 2.2308, 1.0001, ...
            1.4111, -0.42268, 1.2822, 0.13336, -0.056016, -0.0039702, 0.34102, 0.046424, 0.53808, 0.17538, -0.9142, 0.28611, -0.39735, 0.41683, 0.46022, -0.16222, 0.14403, 0.54312, 3.207, -1.7242, ...
            3.5273, -0.057214, -0.50731, 0.23269, -1.3565, 8.9922, -0.27513, -2.5822, -0.39193, -3.6879, -0.44821, 1.3125, 0.36764, -0.12246, -0.77983, 2.7803, 1.4503, 0.8588, 0.29174, 10.534, ...
            -0.00090663, -1.0382, 1.899, 0.76664, 0.69128, 4.8197, 0.11929, 0.0674, 0.026645, 0.44855, 0.8414, 1.734, 0.064445, 0.73414, 1.2446, -0.60464, -0.6785, 1.3329, 0.2164, -0.31068, ...
            -0.96985, -7.4691, 0.0025743, 0.33191, -1.3374, 0.072148, -1.4384, 0.035919, 0.12064, -0.79463, 0.75904, 0.47954, -0.47059, 0.14768, 0.28771, -0.099628, -3.7351, -0.089007, -1.3675, 1.4805, ...
            2.3642, -0.049884, 0.13379, -0.98274, 0.88174, 0.54386, 8.8991, -0.039088, 1.6052, 0.29433, -0.24743, -0.018342, -0.078356, 0.43638, -3.2306, -0.078443, 0.029623, 0.37398, -0.030626, 0.5656, ...
            0.3203, 0.2071, 0.22633, 0.35053, 0.048971, 0.47766, -0.66333, -1.3755, -0.076197, -1.6524, 0.24787, 1.1916, -0.03891, 0.98926, 0.74862, -1.5601, -1.1592, -0.048428, 0.34612, 1.4226, ...
            2.3793, 0.33913, 0.3307, -0.092385, 0.094699, -0.12972, 1.2458, 0.2586, -0.82832, 0.46612, -1.1095, 1.8558, -0.96075, -0.0136, 0.089218, 0.77359, -1.1689, -0.026189, 0.98137, -1.7314, ...
            1.2923, 0.099019, 0.077171, -0.065334, -2.0863, 5.5809, -1.4267, 1.6179, 1.5636, 1.5834, -0.12081, -0.26077, 1.6147, -0.42405, 0.72458, 0.79831, 0.68664, 1.6735, -0.048968, -0.79265, ...
            0.15763, 1.0299, -0.29655, 0.51094, -2.2211, -0.39234, 0.060513, -1.6852, 6.8027, 2.7259, -0.14355, -2.1804, 0.16303, 1.6947, -0.35209, -0.88784, 0.35803, -0.41096, 0.36371, -1.7005, ...
            -1.1127, -2.6104, -0.75831, 0.18452, 0.77892, 0.58731, 0.22549, -0.58443, -1.0327, 0.69476, 0.50928, -1.3767, 1.123, 0.82285, -0.19741, -0.86262, -0.11687, 0.020184, -0.045866, -1.0741, ...
            -1.2025, -1.6405, 10.205, 1.4215, 2.2051, -0.11498, 0.1013, 0.07847, 1.4948, 0.28476, -0.35223, -0.66361, -1.2561, 0.51733, 0.38276, -0.42699, -0.33867, 1.1701, -1.409, 1.2443, ...
            0.29401, 1.1188, -0.36931, -0.80821, 0.23102, 1.5787, -0.70373, 9.5876, 1.7246, -1.7773, 0.44143, 1.7616, 1.6338, -0.70003, 0.76969, -0.28126, 0.22045, -0.34941, 1.8577, 0.062483, ...
            0.0046103, -1.2873, 1.3338, 0.42293, 5.1827, -8.3659, -0.26928, -7.7606, 0.12829, -0.41859, 7.1604, -0.34526, 3.9669, -0.27584, 0.16258, -0.85587, 0.14518, -0.10223, -0.47422, -0.010344, ...
            4.9716, -0.020814, -1.9083, -0.96819, 0.12215, 1.2883, -1.7446, 0.83521, -3.0573, -0.019688, 0.94791, 0.29097, 0.1298, -1.6129, -0.50312, 1.805, -0.55285, 0.17633, -3.9777, 0.41517, ...
            -1.1364, -0.96014, -0.16597, -0.23346, -0.13946, 0.4258, -0.81322, 3.2243, 0.15706, -1.247, 1.2734, -0.24132, -1.1096, 0.054524, 0.58829, 0.30712, 0.30005, 1.3814, 0.79256, -2.215, ...
            1.0999, -0.072951, -0.66067, 0.083073, -1.662, 0.69216, -2.198, -0.18416, 0.058093, 0.061572, -0.2672, -0.029839, 0.40816, -0.35541, -9.3051, -0.01163, 0.085399, 2.7971, -0.443, -0.69435 ];
        
    case 'Comb150'
        F(:,1) = [-0.10989, -0.15351, 0.23059, 0.013998, -0.40359, 0.48869, -0.3498, 0.57469, -2.1845, -0.62803, 0.71699, -0.19376, -0.77927, -1.0984, -0.54724, -0.001738, 0.46674, -1.0359, 0.24032, 0.95066, ...
            0.0088463, 0.58683, 0.016074, 0.7866, -0.90075, -0.59334, 1.0519, 0.083345, 1.9765, 0.1847, -0.50864, -0.41267, -0.70365, 2.7235, -3.0173, 0.80885, -1.0405, -0.71496, 0.39182, 0.31376, ...
            -0.54354, -0.17652, -0.20123, -1.3842, 0.69445, -1.183, 0.1479, 0.77296, 0.47077, 0.47103, -1.4293, 1.457, 0.15049, -0.57512, -1.2548, 0.71215, 1.4749, -0.23435, 0.43342, -0.54501, ...
            -0.047493, 2.8105, -0.22933, -1.8555, 3.1153, 0.73308, 0.21359, 1.3957, -0.30876, -1.9422, 1.0418, -0.10019, 0.091576, 0.62248, -3.3125, -1.8155, -0.079748, -2.0063, 1.1495, -1.2178, ...
            0.84354, 0.80452, -1.6696, -2.5206, -0.34825, 0.0078023, 1.3303, -0.28726, -0.9144, -1.2644, -0.48282, -1.2102, -1.3106, 0.49991, 0.47672, -0.054693, -0.44905, 0.17694, -1.6305, -1.9372, ...
            0.43576, 1.0615, 0.97493, 0.99124, -2.6125, 0.25095, 1.927, 1.1423, -0.0032258, 1.9363, 0.74464, 0.659, 0.45691, 0.081565, 2.4629, -0.71802, -0.078319, 0.99768, 0.87783, 2.2806, ...
            0.25404, 2.6669, 1.611, 1.0324, -0.34846, -0.060717, 1.2476, -0.24765, -0.39805, 1.0984, 2.4417, -0.028133, -1.7247, 0.34854, 0.17603, 0.36076, 0.46396, 0.56705, 0.94305, 1.4239, ...
            -0.25553, -0.68748, -0.38267, 1.851, -0.29273, 0.14945, -1.5393, 0.38997, -1.7838, 0.15044 ];
        
        F(:,2) = [-0.62042, -0.45239, -0.41767, -0.010484, 0.1762, 0.45546, 0.50609, 0.71408, -1.1554, -0.76427, -0.27798, -1.4658, 0.43712, -0.41388, -1.157, -0.13422, -0.46694, -1.2893, 1.4831, -0.91392, ...
            -1.4539, -1.8381, -2.4089, 2.8545, -0.91573, -0.85553, 2.7131, 0.052742, -0.21084, -0.6148, -0.12838, 0.21437, -0.06121, -1.1228, 0.96923, -2.0002, -0.51715, 1.3445, -0.18717, 0.36417, ...
            -0.26778, -0.70392, 0.55842, -1.7375, -0.055812, 0.75609, -0.59843, -0.40873, 0.36138, 0.11181, -0.25841, 0.87985, 0.63552, -0.2215, 0.16097, -1.2326, 0.9961, 0.9499, 0.42033, 0.61332, ...
            -0.30685, -0.017203, 0.56988, 1.6002, 1.1801, 0.98769, 3.6474, -0.91499, -0.13973, -0.32514, 0.77424, -0.023777, -2.3847, 0.84826, -1.0307, 1.3155, 0.00058972, -0.81846, 1.1563, 0.093995, ...
            -0.99106, 0.11402, 2.9537, 0.49177, -0.19025, 1.9749, -0.64717, 0.37016, 1.0988, 2.6817, 0.78006, 1.4468, -0.22535, 0.45026, -1.4234, 1.2678, 0.33583, 0.97633, -1.3206, -1.8743, ...
            -2.9281, 0.028866, 1.975, -0.34395, -1.6146, -0.31617, -0.39341, -0.4359, 0.0013321, 0.12149, 2.0234, -0.1365, 0.5367, -0.84077, 0.34947, 0.10659, -0.031106, 1.5986, -2.0808, 2.5415, ...
            -0.20741, -1.757, -1.0242, 0.19643, 0.089932, -0.21873, -0.077229, -1.8268, -0.40448, 0.40146, 1.686, -1.1349, -1.3248, -0.69454, 0.56817, 0.80159, 0.35605, -0.8129, 3.1916, 0.20643, ...
            0.48848, -1.6989, 0.22608, -0.70713, 0.087635, -0.56966, 2.1706, -1.0893, -2.1918, 3.0184 ];
        
        F(:,3) = [0.025152, -0.54893, 0.43446, 0.67227, -0.50418, 0.18652, 0.09137, 0.59643, 0.55535, 1.0826, 0.24283, -1.0325, -0.66564, -0.62667, 0.67846, 1.058, 0.78283, 0.051716, 0.10948, 0.092661, ...
            -0.34202, 1.0434, 0.041958, -0.98949, 1.0994, 0.5501, -2.138, -0.66502, 0.27367, -1.5811, -1.6235, -0.48767, -0.012933, -0.37895, -1.5073, -0.81507, 0.020629, 0.0061746, -0.49128, -0.4168, ...
            0.02449, -0.65858, -0.0022949, -0.68181, -2.3657, -2.7957, -0.12873, 3.3964, -0.18604, 0.95858, 0.48787, -1.9866, -0.60954, 0.087937, 0.32589, -2.0807, 1.2348, 0.56654, -0.65408, 0.46165, ...
            1.3178, 0.043849, 0.014557, -0.14906, -1.0941, 0.32499, -0.33983, 0.36852, -0.59997, -0.66648, 1.4279, -0.036674, -0.30958, 0.74151, -0.040518, -1.5193, -0.70191, -1.415, -0.014895, 0.63801, ...
            -2.5454, -1.5718, -1.1084, 2.0826, 0.02477, -1.2375, 1.3377, 0.6103, -0.54077, -0.30016, 1.4388, 0.27707, -0.0079283, 0.34823, 0.63678, -0.31041, -2.0304, 0.071052, 0.62983, 0.24325, ...
            -0.71995, 2.8223, -0.58151, -0.97122, 1.1854, -0.6742, -0.3432, -1.7921, 0.0021141, -1.9069, -1.0215, 2.5047, -1.7314, -2.8811, -1.1054, 0.082624, 0.0006119, 1.7621, -0.13659, 1.1599, ...
            0.002275, -0.91739, 1.0806, 1.2865, -0.85074, 0.62239, -1.1333, -0.8514, 0.10536, 0.37501, 0.064183, 2.3637, -0.2869, 1.6307, 1.7312, -0.41076, -0.1015, -1.2423, 1.3558, 0.44622, ...
            -0.22005, 1.712, -0.43628, -0.98894, 0.027328, -0.07412, 1.045, 0.76573, -1.9902, -1.6217 ];
        
    case 'Comb300'
        F(:,1) = [-0.2976, 0.47962, -0.035394, -0.19674, 1.3489, -0.060103, -0.025301, 0.33221, 0.40668, 0.0035804, 0.88556, -0.35277, 2.4487, -0.067291, 0.23301, -0.13845, -1.1986, -0.86092, -0.43647, -1.6678, ...
            0.23003, 0.83092, -0.62357, 0.37584, 0.78747, -0.64497, -0.48983, 1.6365, 0.36798, 0.40731, -0.87051, -0.72281, -0.24347, -1.3501, -0.57615, -0.36672, -0.69088, 0.75175, 0.34211, -0.24058, ...
            -1.7006, -0.86746, -0.44007, 0.73281, 0.44874, 2.998, 1.9135, -1.2543, 0.90145, -2.2849, 0.66116, 0.22605, 0.32172, 0.72104, -0.17135, -0.091095, 0.098668, 0.79971, -0.48469, 1.0175, ...
            -0.40329, 2.6616, -0.0027811, 0.91923, -1.6433, 1.5261, 1.8602, -1.4585, -0.37585, 0.4632, 0.62386, 0.66842, -0.029143, 2.6715, -0.19527, 0.25471, -3.2096, -0.20824, -0.61164, 0.78137, ...
            -0.4247, 0.27131, 2.7299, -0.264, 1.0684, -1.4147, 0.90209, 0.24621, -2.0564, 2.3364, -0.64375, 1.9727, -2.123, 0.76507, 1.3262, -0.11483, -0.053885, -2.5951, 1.4161, -0.31201, ...
            -0.79327, -0.63164, -1.4849, 0.62164, -1.3111, 0.68579, 2.717, -0.18256, 1.9559, -0.58202, 0.51993, 0.7279, -0.91727, 2.1727, -0.43995, 1.5553, 0.61826, 2.8264, 0.47449, -0.93564, ...
            0.31404, -0.86019, -0.88096, 0.025863, -0.2705, -0.66046, 0.2585, -0.67181, 0.16173, 1.8003, 1.3443, -0.97663, 3.027, -0.090144, 0.027269, -2.0873, -2.3556, 1.0692, 0.66895, 0.45551, ...
            -0.2397, -0.22212, 1.351, 2.1184, -0.41051, -0.58863, -2.8979, -1.211, 0.86281, -0.29084, 2.201, 0.53447, -1.0718, 0.42568, -0.28879, -0.72502, -0.89319, -1.8793, 0.19503, -0.058794, ...
            0.25819, 0.92396, 1.3215, -0.77223, 0.094499, -0.75506, -1.0831, 0.21766, 1.4503, 1.378, 0.39275, 2.1209, -0.35661, -0.66266, -0.48156, 0.42309, 0.99729, 0.77721, 1.3, -0.18746, ...
            -2.2358, 0.091838, 0.84263, -0.06907, 0.033483, -0.047018, -1.0226, 0.98638, 0.45216, -1.6156, -0.78857, -1.2274, -1.1649, -0.47515, 0.0074517, 3.1833, -1.7164, 1.717, -2.5947, 0.30461, ...
            -0.45478, -0.38838, 1.1735, -0.11142, 1.7602, -0.85088, 1.3406, -2.0392, 0.33371, -0.080471, -1.1857, -0.00098446, -0.28955, 0.078943, 0.58464, 0.093315, 1.7463, -0.6978, -0.0030687, -1.3611, ...
            -0.72187, 0.69325, 0.7815, -0.8064, 1.4598, 0.20169, 1.4917, -0.41009, 1.9206, -0.52579, 0.66194, -0.64967, -0.065687, -0.26491, -0.48948, 0.85152, 2.2286, -0.43076, 0.45417, 0.16332, ...
            0.0018423, -1.1615, -1.6418, -0.38605, 1.1631, -0.0027992, 1.2471, -3.3934, -0.12606, -1.0147, 0.0012321, -1.4071, 1.1203, -0.1394, -1.3843, -2.1589, 0.30657, 0.54422, -0.039536, -0.78368, ...
            -1.2849, -1.6332, -0.36125, 1.2571, 0.0048284, -0.34627, 1.3565, 1.2769, -1.8958, 2.2157, -0.28174, -0.58224, 1.4125, -2.2126, -0.26684, 0.029423, 0.62241, -0.11878, -2.8125, 0.076119, ...
            -1.095, 0.55809, -1.1507, 0.065365, 0.011621, -0.56687, 1.6205, 0.33317, 0.88769, 0.0049479, -0.2281, 0.52161, 0.46699, 0.80697, 1.2871, 0.46163, -0.32335, 0.29807, 1.6934, -0.27002 ];
        
        F(:,2) = [0.1882, -0.96862, 0.68093, -0.65474, 1.5522, 0.73792, 1.2424, 1.3296, -0.31099, -0.00083335, 0.11628, -2.9552, -1.4224, 0.54732, -0.060786, 3.358, -1.402, 0.36723, -0.61405, -0.47433, ...
            0.083811, 2.709, 0.42485, 0.011626, 0.19057, 1.11, -0.16668, -1.5152, 0.022537, 0.61841, 1.8725, 1.2791, 0.66249, -1.5249, -0.31635, 1.6061, -1.5603, 0.67157, 0.52783, -0.63539, ...
            1.9581, 0.080409, -0.093703, -0.9843, -0.050351, 0.47951, -0.63734, 0.95354, 1.2272, -1.4463, 0.24887, -0.56756, 0.53929, -0.0012548, 0.33647, 0.30713, -1.0984, -0.22835, 1.7988, 0.76801, ...
            0.5582, 1.9802, -0.88013, 0.99018, -0.62293, 2.0792, 1.1562, 1.9145, -1.8496, -0.38796, -0.81501, 0.13709, -0.84236, -0.04191, 1.9873, -0.18383, 0.96021, 0.64458, -0.7441, -0.16298, ...
            -0.083425, 0.029868, 0.689, -0.61975, 0.36718, -0.10755, 0.09592, 0.56833, -2.4631, -0.37229, 0.66376, 0.036218, 0.53938, 2.0031, -0.20751, -0.66672, 0.5024, -1.0417, -0.31881, 0.95299, ...
            -0.30383, 0.17946, 0.51682, 0.8388, 0.9343, -2.2586, 0.41428, 0.5948, 0.25488, -0.63337, -0.38212, -1.4138, 0.578, -1.864, 0.46783, 0.38934, 0.82998, -1.0329, 0.44842, -1.8882, ...
            -2.6213, 0.60167, -1.2729, -0.88443, -0.67636, 1.087, 0.56833, -1.1677, -0.44579, 1.2652, 1.4261, 0.15551, -0.66378, 1.5324, -1.2689, 0.091843, 0.98537, 0.20666, -0.14703, -1.4182, ...
            1.8763, -1.6184, -1.9126, 0.52628, -0.51086, -1.3396, 0.3814, 0.92226, -0.43543, 0.41939, -0.089039, -0.96872, -3.2826, -0.24956, 1.0723, -0.60651, 0.0704, -2.0203, -0.37024, 0.62328, ...
            0.63051, 0.70423, -2.9467, 0.72814, -1.7598, 1.1635, -0.41661, 1.5172, -1.0686, -0.34623, -2.0721, 2.5762, 0.49434, 2.3525, 0.75177, 0.18808, 2.5517, 0.38221, -0.51568, 0.41045, ...
            1.3389, -1.9459, 0.2995, -0.73821, 0.37816, 0.411, 0.4419, 1.0231, 2.6804, -0.7088, 1.5234, -1.8991, -1.4391, -1.0912, 2.4658, 0.03129, -0.32898, 2.1898, -1.5425, 0.55338, ...
            0.0063374, 0.29596, 0.94037, -0.96085, -1.1762, -0.012125, 2.9159, -2.4938, -0.13815, -0.19132, 0.6911, -0.0002177, 0.052792, 1.0211, -0.071816, -0.054833, -0.8192, -0.58739, -0.0032834, 0.53558, ...
            0.35137, 3.1978, -0.15169, 1.6911, 0.48855, -0.26526, -2.289, -1.7999, 0.31739, 1.1108, -0.28787, 0.095558, -0.033708, -0.021632, -0.1993, 2.1061, -0.14888, -0.23532, 0.18699, 0.1705, ...
            -0.00093855, -0.59639, -0.72818, 0.82898, -0.097653, 0.0024338, -1.2648, -0.59179, -1.6321, -0.29181, 0.0024769, 0.5321, -1.4215, -2.9509, -0.35491, 0.75324, -0.48035, 0.70344, -2.3917, 2.854, ...
            -2.5531, 0.71284, 0.84849, -1.5415, -0.003001, -0.44239, 0.48001, 0.35259, 1.3351, 0.78052, -1.3463, -1.3853, 0.81783, 2.2406, 0.80885, 0.59982, -0.24376, 0.12448, 1.89, -0.046816, ...
            0.10715, 0.18351, 0.2524, 0.0042592, 0.012787, -0.79795, -1.3509, 0.916, 0.95088, -0.1391, -1.3548, -1.1437, 0.97273, -2.6841, 0.010528, 1.5419, 0.22683, 0.45961, -0.17708, -1.3889 ];
        
        F(:,3) = [-0.5539, -1.718, 0.06693, 0.39764, 1.8242, 2.188, -0.7842, 2.8988, -0.40753, -0.0052653, 0.43604, -1.8848, 0.29272, 1.0355, 0.65043, -0.94307, -0.8753, 1.4384, 0.76512, -0.18971, ...
            0.64586, -1.343, 0.24583, 0.5632, 0.034299, -1.3145, -0.49991, 0.88966, -0.64416, 0.29466, 1.1989, -0.015607, -0.029296, -0.98219, -0.44468, 0.47929, -2.7421, -2.2419, 0.27395, 0.10464, ...
            1.8855, 0.45776, 0.62258, 0.84685, 1.3435, -0.90383, 0.67965, -0.36489, 0.32116, 0.92635, 0.93902, -0.18215, 0.2797, -0.48731, -0.65348, 0.65659, -1.2028, -1.1923, 0.33656, 1.1757, ...
            0.011037, 1.473, 0.62252, 0.98202, 1.1378, -1.9676, 0.17026, 0.57638, 0.57043, -0.47638, -1.8394, -0.99391, 2.6295, -0.68569, -0.44192, 0.72029, -0.54094, 0.029868, 0.46463, -1.186, ...
            0.63261, -0.71938, 1.9174, 0.050978, 1.1731, -1.5675, 2.1974, -0.27589, 0.070907, -1.9599, 1.4538, 1.0232, 0.11786, 0.56326, -0.24876, 2.6125, 0.45138, -1.1102, 2.6961, -1.4514, ...
            0.41466, 0.087935, 0.25975, 0.76029, -0.24689, 2.1701, 0.39943, -0.26734, -2.3042, 1.8807, -0.53124, -0.93862, 0.43827, 0.42776, -0.21487, 2.6226, 0.59034, -0.28189, 0.18308, 1.6095, ...
            -1.7677, -0.45881, 0.1441, 0.6643, 0.42702, 0.94708, 0.28686, 0.87511, -0.47248, 0.16343, -1.3991, -1.5626, -1.3479, 0.54987, -0.96362, -1.6753, 0.91117, -0.2631, 0.11917, -2.2981, ...
            -2.6672, -0.6999, -0.94412, -0.41915, 0.020241, 0.15665, 0.1444, 0.60255, 0.075722, 1.3575, 0.68356, 0.0097872, -0.10365, -0.7788, -1.8756, 3.1684, 1.9758, 0.24858, -0.81969, -0.32964, ...
            0.19253, 0.17968, -0.70916, -0.048713, 0.21631, 2.7565, 0.96158, -0.481, -2.5544, 0.67783, -1.3913, 1.3883, -0.70316, -1.2121, -1.7283, -0.44944, 0.17579, 0.62731, 0.55974, -0.42805, ...
            2.014, -0.28913, 3.1832, -0.49001, -0.48281, -1.3148, -0.12104, -1.6317, 1.7458, -1.5335, 2.6569, -1.6434, 0.96049, 1.1357, 1.34, 1.2123, 1.6, 0.47988, 1.5786, -0.13342, ...
            -0.48123, 0.45792, -0.10581, 0.91903, 2.3954, 0.66046, -0.75447, 0.56779, 1.9895, 1.3984, -0.58932, -0.006342, 1.1361, -1.1362, 0.71817, 0.53845, 1.0097, -0.57464, -0.0045869, -2.8534, ...
            -0.0048583, -0.49504, 1.1835, -0.78361, -0.347, -0.61384, 1.2089, 1.0775, -2.5674, -0.090033, 1.1691, -0.018272, -0.63134, -0.32211, -0.42761, -2.2959, 2.3221, 3.1556, 0.35177, -0.24814, ...
            0.0060796, 0.75135, -1.1303, -0.12258, 0.071793, 0.0052419, -0.071582, 0.65524, -1.8159, 1.6459, 0.0057952, 0.078255, -0.34839, 0.21719, 0.4351, 1.9008, 0.034257, -0.59386, 0.88215, -1.7331, ...
            -1.2364, 0.67746, 0.46006, -0.77079, -0.0029861, 0.19163, 0.62307, -1.4898, 0.8319, -0.65031, 1.4713, 0.086184, 0.35061, 0.18637, 0.58022, 0.57145, 0.84393, -0.31987, -1.2014, 0.047043, ...
            -0.24019, -0.23703, 0.0057767, -0.010517, -0.0026627, -0.43291, -1.3293, 0.8386, -0.42794, -0.079362, 1.9417, 2.0058, 0.68027, -2.1205, 1.5622, 1.1557, -0.11032, 1.2719, -0.87475, -0.25402 ];
        
    case 'Comb500'
        F(:,1) = [0.57607, -2.478, 0.13874, -0.45436, 0.65595, 0.64442, 2.237, 1.4022, 0.96811, -0.52145, 0.0034252, -1.7606, -1.6648, 1.455, 0.09946, 0.43949, -0.79583, -0.7064, -0.87068, -2.2397, ...
            0.91195, -0.06363, -0.93247, -1.2816, 0.37155, 0.15816, 1.5997, 0.58428, -0.15636, 1.5436, 0.095743, -0.09749, -0.38902, -1.191, -0.82634, 0.79252, 1.3307, -0.39681, -0.60393, -2.9158, ...
            0.50306, -0.84515, 0.066966, -0.4651, 1.7827, 1.6062, -0.46638, 1.5225, 2.5111, -1.4929, -0.41198, 0.47266, -1.6328, -0.44229, 0.74285, 0.089587, 0.89489, -1.3072, -0.18848, 0.67749, ...
            -0.045094, 2.7658, -3.6032, -0.8374, -0.25928, -0.53345, 0.54574, -0.16792, -1.1715, -0.1334, -1.4394, 0.24313, -0.92768, 0.42287, 1.5696, -1.0717, 0.098521, 0.85242, 0.11448, -0.16333, ...
            -2.379, 1.6497, -1.711, 0.4733, 0.50553, -0.74486, 0.16536, -0.0041572, -0.67845, 0.50034, 2.1062, 1.269, 0.44512, 0.68226, 1.5591, 0.020391, 0.8842, -2.3172, 0.78417, -2.4325, ...
            -0.62384, -1.2558, -0.45041, 2.6972, 2.5832, 0.4034, -0.71038, 0.26255, -0.44266, 2.6428, -1.7053, -0.19458, 1.7725, 0.84123, -0.13376, 0.5027, 0.1212, 0.14372, 0.86749, -0.72173, ...
            -0.97248, 1.2981, -0.20653, -1.6028, -1.344, -1.4527, -1.6132, -1.0142, -1.231, 1.3873, 2.3939, 0.95834, 0.20719, 2.5967, -0.86632, -0.66264, 0.74602, -0.64064, -0.54047, -0.61891, ...
            0.058234, -2.9361, 2.1344, -0.057816, 1.1078, 0.23224, -0.063494, 1.3911, 1.0022, -3.3867, -0.96304, -0.28659, -0.10279, 0.52136, -0.46801, 2.0254, -0.19108, -0.62172, 0.36751, 0.011165, ...
            -0.48563, 0.43675, -0.39248, 0.8663, 1.1197, -0.037981, 1.1544, -0.68786, -0.11558, 0.95948, -0.078073, 0.56799, 0.0037368, -1.6451, 0.45199, 0.059081, -0.6228, 0.33185, -0.9714, -2.1411, ...
            -0.22998, -0.62068, -0.042546, -0.85664, -0.44171, -1.1648, -2.4639, -0.5596, -0.51524, 0.4664, -0.14676, -0.59639, 1.6974, 0.34884, -0.43682, 0.060943, -0.51052, 0.34876, -1.0194, 0.6369, ...
            -1.0682, -0.1099, -0.1841, 1.0721, -3.1262, -0.71939, 1.0019, -1.0743, -0.82962, -1.8802, -0.45962, -0.56598, -0.50664, 1.0782, -0.54222, 0.80199, 1.4451, -1.1678, -0.72766, 0.68951, ...
            -0.69193, -0.90175, 0.3862, -0.29238, 1.1093, -1.4543, -1.2676, 1.0098, -0.3934, 2.1496, 0.48316, 0.4932, -0.28904, 0.16476, 1.5031, 1.0057, -0.19996, 0.52052, 1.5076, 1.7958, ...
            0.74466, 0.27218, 1.3693, -1.8442, 0.19651, 1.8094, 0.047595, 1.4242, 0.77024, -1.5795, -0.96178, 1.9467, 0.25649, -1.8088, -2.6071, -0.54703, -1.0772, 0.67995, -0.5421, 2.3744, ...
            1.2353, -0.18844, 0.86624, 1.3594, 0.23431, 2.1357, 0.095408, 0.13244, 0.34877, 0.88715, 0.28959, 0.88574, -0.5268, 0.11185, 0.37628, -1.399, 0.39877, 0.9835, 2.0108, -1.0599, ...
            0.049874, -0.98897, -0.9498, 1.789, -2.164, 1.5919, 1.2009, -1.4586, -1.2384, -2.6405, -0.23069, 2.2951, 0.066744, -0.019894, 1.5698, -0.69324, -2.731, -0.40831, -2.8925, -2.4225, ...
            -0.26828, -1.7901, -0.1993, 0.44888, -3.0271, 0.92846, -0.21786, 0.96204, 0.32199, 2.5151, 1.1643, -1.2011, 1.6272, -3.4267, 0.38828, -1.4774, 0.082749, 2.5308, -1.0227, -1.2194, ...
            0.037623, 0.2592, 1.4292, -1.9344, -3.1074, -1.9131, 2.708, 3.3509, -0.33833, -1.5652, 0.085782, 2.4762, -0.5013, -0.20237, 0.21721, 1.585, 0.062736, 0.016117, -0.73664, -0.076158, ...
            -0.57604, 0.17088, 1.683, 0.0039391, 1.5867, 0.86298, 0.35741, 0.94374, 0.51295, 0.9292, 0.023465, -0.67149, 0.371, -0.56937, -0.40841, -1.987, 1.6543, -0.61638, -1.9515, -0.05798, ...
            -0.89772, -0.31919, -0.63095, 0.47013, -2.1373, 0.25162, 0.15074, 0.21921, -0.87513, -1.3762, 0.68461, 0.37447, -0.76579, -1.298, 0.38256, -1.6352, 0.19354, -1.0235, -1.5272, 0.83886, ...
            -1.9002, 3.2203, 0.52732, -0.718, 0.0041355, -0.79173, 0.48105, 0.37063, -0.50023, -0.016925, 0.00105, -0.0071238, -0.95807, 0.65969, 0.69499, 0.49601, -0.14833, 0.56853, 0.51992, 0.40608, ...
            0.039988, 0.89518, 0.33855, 0.96896, -0.032608, 1.7849, 0.042216, -0.91819, 0.94575, -0.20715, -0.001727, 0.1084, -0.27366, -2.1009, -0.15483, -1.5844, -0.42397, 2.325, -0.378, -0.18158, ...
            -0.77698, 0.495, -1.2669, -0.35454, 0.38062, -0.11902, 0.5553, 0.027428, -0.82194, -0.13546, -0.0012105, -0.46235, -0.78428, 0.0042164, -0.099113, -0.69744, -0.24274, -0.88823, 1.279, -0.25018, ...
            -3.354, 0.0032643, -0.032041, -1.1633, -0.20824, 0.28359, 0.94159, -1.9554, 0.22529, 1.206, 0.1873, -1.893, 1.7179, 0.74279, -0.0055294, -0.44003, -2.186, 0.23971, -1.3263, 0.64976, ...
            -0.65876, -0.0057495, -0.03126, 1.7476, 1.0126, -1.0289, 0.22217, -0.11001, -2.2044, 0.18761, -0.56426, -0.60177, 1.0097, -2.7663, -0.0039575, 0.28546, -0.077141, 0.55857, -0.095522, -0.1565, ...
            0.0027179, 0.7801, 0.36079, -0.51121, -0.75691, 0.58784, 0.046792, 1.0486, 0.76833, -1.3024, 0.07854, 0.89659, 0.69938, 0.037961, 0.43498, 0.34363, -0.083225, -0.75337, 0.018674, 0.29318 ];
        
        F(:,2) = [0.48776, 0.66385, -0.7262, 0.39657, 0.69811, 0.31491, -1.2162, 0.49, -0.25091, 0.47163, -0.48039, 1.638, -0.75094, 1.5426, -0.66325, 1.1122, -0.35363, -0.046163, -0.63955, 0.0052453, ...
            -1.1246, -0.91578, -0.13064, 0.90212, 1.3847, -0.28168, 0.61298, 0.83534, -0.021219, -0.89224, 0.64128, 0.90264, -0.44905, -1.8538, -0.86397, -1.833, -1.4051, 0.86653, 0.47029, 1.234, ...
            -0.45182, 0.0013699, 0.86867, -1.6792, 1.6936, -1.5548, -0.038354, 1.0334, -0.36404, -0.23906, 0.71617, -0.92511, -2.2611, 2.392, 1.0686, 1.6033, -0.54672, 0.56784, -0.53106, -0.13229, ...
            -0.72903, 0.29958, 0.39849, -1.3235, 0.96148, -2.4129, 0.72371, -1.7287, 0.71224, -0.42976, 0.18589, -0.12095, 1.0436, 0.3527, 2.604, -2.5224, 2.0007, -0.40496, -0.559, 1.3117, ...
            0.95648, 0.69649, 0.54791, 1.3298, -0.071288, -0.26075, 1.8498, 0.86421, 0.38799, 2.6129, -0.65926, -0.29833, 0.60986, -0.24437, -0.024875, 1.4932, -0.72405, 0.56505, 0.24357, 2.5234, ...
            -0.49527, 0.56804, 3.0574, 0.21927, 0.46247, 0.29285, 0.28075, -0.77671, -0.68057, 1.9247, -0.53111, 2.675, 0.28268, 0.043869, -0.63723, -0.96115, -0.14025, 0.020674, -0.46518, -0.20428, ...
            -1.6752, -2.175, 0.23239, -0.49147, -1.3125, -0.92381, 0.30163, 1.0457, 0.35304, 0.20801, 0.856, 1.959, -0.58064, 0.95694, -0.25165, 1.5986, -0.9529, 0.91787, -0.8476, -0.37246, ...
            0.984, 0.26353, 0.45908, 0.95554, 1.1466, -1.6407, 0.45597, 1.4685, 0.31449, -1.1691, -0.30678, -0.52423, 0.24355, -0.093599, -0.22178, -2.0276, 0.10914, -0.10929, -0.96627, 0.49564, ...
            0.040263, 0.2536, -0.078668, -0.63236, -0.31092, -0.79315, -0.029106, 0.32324, -2.6575, -0.47072, 1.3428, -0.058409, 1.9995, -0.83453, -1.6976, -2.2173, -0.14344, 0.6401, 0.98449, -1.297, ...
            1.5576, 3.2449, 0.61275, 1.7586, -0.3108, 1.2757, 1.7261, 0.15354, 0.9737, 0.77103, -0.6905, -0.054609, -0.086959, 1.133, 0.5694, -0.3062, -0.93318, 0.11186, 0.58703, 0.0011413, ...
            -2.7341, -1.5928, 0.038996, 0.25402, 1.1284, -0.8492, 0.1194, 0.41774, -0.97491, -1.0407, 0.52767, -0.95667, 1.3386, 1.1669, 0.0096915, -0.53005, -2.2866, -0.29639, -0.49831, 0.036363, ...
            -1.4306, 0.78378, -0.52327, 0.2932, 0.51666, -1.8501, 0.51252, -0.26038, -1.4712, -1.1293, 0.41046, 0.25675, -0.80756, -0.84751, 0.28236, -1.3198, -2.0173, -1.4832, -1.1104, -1.5523, ...
            -1.38, -0.68423, 2.9816, 1.5287, -0.40775, -0.49057, 0.64544, -2.7862, -1.1319, -1.4682, 1.023, -1.8659, -0.49115, -0.14327, 1.4421, 0.24931, -0.30388, 1.874, -0.80775, -0.60823, ...
            -0.62635, 1.2607, -1.7581, 0.75058, -1.0835, 0.0023593, -0.21771, -0.29558, -1.8174, -0.60583, -0.35796, 0.94188, -0.4915, -1.616, 0.55366, 0.34717, -0.54293, 0.15763, 1.2611, 0.35363, ...
            -0.72817, 2.3024, 0.22983, -0.86374, -0.73797, -0.62965, 0.93047, 0.46057, 0.029122, 0.1173, -2.5662, -2.7132, 0.59929, -0.52828, -1.926, -0.35568, -1.9448, 2.5069, -1.096, 2.3696, ...
            0.79761, -2.0569, -3.2254, -1.1031, -0.20213, -1.727, -0.52101, 0.12041, -0.44688, 0.97769, -2.1397, 0.65967, -1.0714, 0.75238, -1.4232, -3.257, -1.307, 0.81477, -2.0874, 2.0821, ...
            1.3202, 0.64374, -0.74504, -1.034, -0.40624, -0.43666, 2.1239, 0.0081412, -0.11053, 2.3663, -0.065435, 1.6821, -3.5499, -1.3664, -1.8377, -0.57555, -0.56065, 0.70278, -0.16708, 0.010775, ...
            0.50881, -1.4851, 0.066457, 0.0048011, -0.49001, -1.0662, 1.4129, -0.74847, -0.75405, 1.1186, 0.15484, -0.56323, -0.077265, 1.7876, 0.45282, -2.0524, -0.10975, -2.1891, -2.4238, -0.90457, ...
            -2.0403, -0.78359, -0.013321, 1.3839, 0.54738, -2.3782, -0.033889, -0.44021, -0.85064, -0.39081, -3.0468, 0.57917, 2.2629, -0.5504, -0.21864, -1.8006, -0.32078, -0.60957, -0.27888, 0.51937, ...
            1.3207, -1.594, 0.28623, 1.132, -0.0049084, -0.92397, 0.099828, -2.5085, 1.2675, -0.0061367, -0.77179, 0.075618, -0.34038, -0.26397, 2.5502, -0.27147, -0.10521, 0.53154, -0.41131, -0.32688, ...
            -0.15391, -2.3738, -0.0039928, 1.3649, -0.95655, -0.96964, -1.6825, -0.12453, -1.2614, 0.6298, 0.0032212, -0.021456, 0.19359, -0.00072863, -0.13217, -1.2951, 0.39237, 0.20438, -0.68233, 0.14908, ...
            2.0466, 0.58527, -0.093535, 0.59505, -0.16424, -0.11838, 0.159, -0.22191, 3.1919, -0.80539, -0.26602, -0.064476, -1.385, 0.0010387, -0.11562, -1.4268, -0.34722, -0.43878, 0.78554, -2.7073, ...
            -1.1744, -0.00031648, 0.045806, 1.4752, 0.060342, -0.55216, -1.5525, 0.41678, 0.33787, -1.7624, 0.88957, -2.674, -1.2906, 0.95309, -0.002679, -0.027906, 2.4295, -0.63225, -1.1705, -0.018932, ...
            1.0305, -0.0012012, -0.57875, 0.72783, -0.044802, 0.89302, 0.086882, 0.11746, -0.30395, 3.0946, -3.5625, 0.099072, 3.1491, 0.69523, 0.0013711, 0.096237, 1.0109, -0.90459, -0.68923, -0.035867, ...
            -0.001732, 0.15692, 1.4242, 0.36708, -1.6, -0.41397, -0.01357, -1.3423, 0.62082, 0.075166, 0.69055, 1.2908, -0.64367, 0.13508, -0.090733, 0.14533, -0.042728, -1.8349, -0.77433, 0.80043 ];
        
        F(:,3) = [-0.43142, -0.14342, 0.4879, 0.1555, 0.26377, 2.2247, 0.20873, 0.61176, -1.3527, 0.068402, 1.0234, -0.90756, 1.4342, 1.5249, 2.4526, -0.29019, 0.695, -1.758, 0.16973, -1.125, ...
            -0.74988, 0.1521, 0.7597, -1.553, 0.37937, 0.62823, 0.016019, -0.14549, -0.68857, -0.29045, -0.16647, -0.34229, -0.38217, -0.78142, 0.44937, 0.51561, 3.1189, 0.22676, -0.48548, 1.5778, ...
            -0.4154, -0.24402, 0.30415, -0.46427, 1.366, 1.9554, 0.36518, -2.3312, -0.66085, 0.92264, 0.71644, 1.195, 0.231, -1.2214, -0.3407, -0.044909, 0.43468, -0.71285, 0.93721, 1.003, ...
            -0.031912, -0.88831, 0.29961, -0.77599, 0.87815, -1.4116, 0.0045528, -2.197, 0.70614, -1.0362, -1.2731, -0.64006, -0.18135, 0.96861, 0.69822, -0.63909, 0.5127, -3.5629, -0.35005, 0.9236, ...
            -0.39016, -0.33625, 0.0022985, 0.86361, -0.49475, -0.2385, 0.57862, -2.9223, 0.66852, 0.71832, -1.49, -0.16647, 1.1962, 0.044508, 1.9939, -0.5564, -2.8319, 0.6427, -0.16479, 0.43513, ...
            0.17002, 2.4662, 0.0035247, -2.418, -0.66156, 0.50054, 0.82294, 1.8273, -0.33867, -1.5407, -0.55799, -1.5917, -0.90799, -0.21552, 2.2352, -0.068368, -0.68033, 1.1151, 2.0544, -0.42578, ...
            -0.64149, 1.2641, 0.61999, 1.1927, 1.1736, 0.8737, -1.1816, 0.58386, 0.19393, -1.8367, 0.42218, -1.9576, 2.5484, 1.0804, -0.48197, 0.74885, -1.9445, -0.87697, 0.048533, -0.60972, ...
            0.41476, -0.83607, -1.1662, 1.2738, 0.19716, -1.1658, -0.60817, -0.59158, 2.0403, 0.18152, -0.39675, -0.28627, -0.64123, -0.091271, 0.25351, 0.52808, -0.65775, 2.7961, -0.094533, -0.43945, ...
            -0.47182, 0.50537, 0.59147, -0.0064394, -0.13189, 0.72914, -0.18045, -0.68321, -0.49897, 0.038438, -1.4274, -0.22628, 0.53346, 0.011486, -1.3014, -2.1775, 0.69253, 0.11788, 1.7191, 0.12516, ...
            -1.7614, 1.3105, 0.28436, -1.4281, 0.52704, -0.64887, -0.071337, 1.8168, -0.49183, -1.7193, 1.8573, -1.0372, -2.5159, 2.3734, 0.3201, 0.65987, -0.38307, 0.7715, 0.58736, -1.1126, ...
            0.24111, -0.9252, 0.70267, -0.10172, -1.3664, 0.74301, 0.27297, -2.6416, -1.1452, -1.2339, 0.3575, 1.0492, -2.7165, -0.12362, 0.34654, -0.12506, -1.4137, -1.9464, 0.40246, -1.5315, ...
            -1.5631, 0.71176, 0.57809, 0.631, 1.9808, 1.2657, 1.7275, 0.97933, -0.56241, 0.28875, -1.5874, 1.7138, -0.52648, -2.8302, 1.6862, 0.19119, -0.045508, 0.26233, -0.35857, -0.23991, ...
            -1.0017, 0.56995, 1.2324, -0.88007, 0.81826, 0.34643, 0.097743, 0.7348, -1.0574, 1.4639, 1.6977, -0.86095, 0.38064, 2.9552, 0.76553, 2.2919, 3.3009, 0.0037256, 0.095459, -0.77092, ...
            -0.63289, 1.0387, -0.59736, 1.1094, 1.3289, 1.1088, 1.1476, 0.65476, 1.4162, -0.085755, 0.56723, -0.93158, -1.6565, 0.038037, 0.0018259, 1.1211, 0.45752, -0.64344, -0.52356, -0.348, ...
            0.032089, -0.055328, -1.2338, 1.3352, -0.70834, 3.0365, 0.61119, -0.021974, 1.1549, -0.33106, 0.014407, -0.25023, 0.90235, 0.35628, -2.4559, -1.5186, -0.5252, -1.0024, -1.3793, 0.18229, ...
            -0.37731, 2.185, -0.50285, -0.1929, 1.5206, -2.907, 0.26063, 0.41437, -0.71175, -0.97594, 2.5221, -3.1967, -1.5313, 0.31864, -3.1668, 0.074252, 0.47637, 2.3108, -0.25082, -0.56349, ...
            -0.88627, 0.33426, -0.93194, -1.5296, 1.544, 0.60801, -0.78791, -1.0588, -0.3437, -2.2765, -2.367, 1.9307, 0.65969, 0.46534, 0.22272, 3.0725, -0.39504, -0.16819, 0.29178, 0.088088, ...
            -0.21863, 1.8486, 2.4451, -0.0016338, 0.33228, -1.1177, -0.37562, 1.1905, 0.78467, -1.0158, -1.2127, 0.35415, -1.7742, -0.41956, 0.67864, 1.1392, 0.49326, 1.6721, 0.042995, -0.20686, ...
            -1.6839, 1.0268, -0.31741, 1.3649, -0.96076, -1.4686, 0.27923, -1.018, 0.27585, 0.42901, 0.2216, 1.0131, 1.577, 0.49309, -0.22241, 1.5224, 1.0411, 1.975, 1.7477, -0.41479, ...
            -0.86215, 0.051634, -0.91429, -1.158, -0.00020523, 2.1121, -0.044537, -0.8343, -0.77859, 0.058386, 0.057449, -0.058415, -0.44428, -1.213, -2.0896, -0.1806, 0.14624, 0.64551, -0.18058, 0.79769, ...
            0.29567, 2.1671, -0.41452, -0.96192, -2.0582, -1.3793, 1.2055, -0.75236, 1.0347, 1.0079, -0.00528, -0.5548, 0.085411, -2.8983, -0.075148, 0.58618, 0.13647, 0.67545, -2.7585, 0.26214, ...
            0.39468, -0.20543, 0.25264, 0.22438, -0.23354, 0.10007, 0.49995, -0.12538, 0.41698, -0.83242, -0.058923, -1.803, -1.6177, 0.0047307, 0.04472, 0.81234, -0.15794, -1.0891, 0.57245, -2.4886, ...
            -0.6052, -0.005521, 0.042849, 1.7379, -0.21139, -1.8536, 0.25097, 1.2685, 1.454, 2.9542, -1.8635, -1.6545, 2.2422, 1.9403, 0.001867, 0.39741, 1.597, 0.92436, -0.041126, 0.33347, ...
            0.77636, 0.0025956, -0.24625, -0.62497, 1.3493, 0.43056, 0.24126, 0.076144, -1.205, -0.75395, 0.41631, 0.05734, 0.90913, 2.136, 0.0048678, 0.83181, -0.56221, 0.68326, 0.12191, -0.28067, ...
            -0.0055543, 0.11163, 3.2795, -0.052325, -0.089504, 0.12971, -0.38849, 0.34571, 0.54996, 0.72717, -1.091, 0.23482, -0.90756, -0.26405, -0.28126, -0.56287, -1.3109, 1.1921, 0.52527, 0.53501 ];
        
    case 'Comb1000'
        F(:,1) = [-0.029454, 2.1451, -1.291, 0.05292, 0.88723, -1.1519, -0.31207, -1.0704, -0.3766, 0.4075, 0.94099, -0.13653, -0.33561, -0.43899, -1.6788, -0.66296, 0.83759, -0.81731, -0.87101, 3.0427, ...
            -2.7308, -0.64061, -0.4512, 0.018972, 0.0030399, -0.0069081, 1.2176, 0.50708, -0.59255, -0.518, 0.16752, 0.88263, 2.3541, -0.47873, 0.57706, 0.35243, -0.31559, 0.48313, 3.2731, -0.8678, ...
            0.42715, -0.046389, -0.021815, 2.1653, 0.35253, -0.13423, 0.97323, 0.30608, -0.37214, -0.91286, 0.29966, -0.31461, -0.74586, -0.42005, -0.12316, -0.64518, 1.0557, 3.278, -0.10534, -0.52936, ...
            0.18797, -0.61684, -0.44039, 0.16058, 0.28237, -1.6782, 0.38911, 1.1367, 1.7857, -0.00056078, 2.4754, -1.7537, 1.0334, -2.1647, -1.3225, -0.21369, 2.4641, -0.3931, 1.6618, -1.8647, ...
            -0.085978, -0.66079, 1.2941, -1.1126, -0.77832, 0.71244, -0.27341, -0.63646, -0.38373, 1.0108, -0.035744, -0.798, 1.0057, 0.68961, 2.1949, 0.60093, 0.017136, -0.66486, 2.0083, -0.13624, ...
            -0.73247, -0.29715, 0.94597, -0.13755, 0.077434, -0.090208, -0.025237, 1.1959, -1.3648, -0.85917, -0.53372, 1.2363, -0.66087, 0.32031, 0.032532, 1.391, -0.82132, 0.54687, -1.2183, -0.07902, ...
            0.14887, -0.00028264, -1.2997, -3.0907, 0.070911, -1.3542, 1.0169, -1.083, -1.549, 0.0042783, -2.4649, -0.13059, -0.19388, 0.93626, 3.0306, 0.026245, 2.6773, 0.68922, 0.23171, 1.7431, ...
            1.0543, 0.70744, 1.4236, -0.065074, -2.6699, -0.99215, 0.17226, 0.084765, -0.37691, 0.011003, -0.12146, -0.16399, 0.40123, -0.2106, 0.16122, 0.22525, 2.6865, -2.8899, 1.0637, 0.014862, ...
            -0.18195, 3.4332, -0.54758, -0.50987, 1.0569, -1.5851, 0.2646, -0.14152, -0.23308, 1.198, -0.080055, 0.10855, -0.73248, 0.089331, -0.5883, 0.24365, -0.17465, -0.36545, 3.2285, -0.87455, ...
            -1.9449, 0.72632, 1.0631, 0.28593, 0.2094, 0.092348, 0.76665, -1.5882, 0.56382, -0.45944, -0.64492, -0.72557, -1.1031, 1.7852, 2.03, -0.53326, 0.66392, 0.93695, 0.97708, 0.59026, ...
            -0.046494, 0.16394, 2.134, 0.29624, -1.6856, -0.42107, -0.99662, 1.5253, -3.3527, -0.70621, -0.14844, 1.0696, -1.5943, -0.49842, 0.62906, -0.75955, -0.3214, -1.3393, -0.14165, 0.28602, ...
            0.80038, -0.11998, 0.13092, 1.1364, 0.32085, 0.89099, -0.19091, -0.41459, 2.4767, 1.67, 1.1493, -1.4391, -1.6422, -0.81788, -1.2618, -1.232, 0.33614, 1.2262, -0.037728, -0.046822, ...
            -1.5224, -0.95557, 0.02395, 0.90406, 0.35967, -1.646, -0.45164, 0.45344, 0.076516, -0.95256, 0.58398, -0.62667, 0.43983, 1.3658, -0.50444, 1.7414, -0.31475, -0.62936, 0.51029, 1.8438, ...
            -1.9148, -0.72349, 2.4016, 0.18638, -0.53148, 0.56881, -0.02781, -0.18874, 0.97972, 0.59133, -0.58981, 0.98302, 0.24813, 0.26741, 0.63607, -0.39327, -2.3413, 0.60153, 2.4891, -0.67229, ...
            2.1273, -0.62201, 0.30486, 0.58419, 1.1765, 0.80554, 0.39946, -0.17477, -2.1022, -0.60394, 0.75759, -0.034011, -1.5237, -0.10899, -3.6086, -1.4302, 1.263, 0.8455, 1.8168, -0.35241, ...
            -0.2873, 0.16966, 0.089036, 0.76831, -0.27996, 1.9371, 2.0889, -1.068, -0.82206, -1.4436, -1.3493, -0.88887, 0.35783, -0.15925, 0.057778, 0.37027, -1.2421, -0.10841, -0.85458, -2.6654, ...
            -1.875, 0.55655, -0.089856, -0.91161, -1.153, -2.0295, 0.77383, -0.026629, -0.7847, -1.4153, -1.592, 0.58964, -0.29398, -1.8638, 1.1658, 0.11542, 0.52091, -1.7711, -2.1691, 1.1549, ...
            -0.69087, 1.4969, -1.1244, -0.68927, -0.87349, -0.50021, 0.061581, 0.60086, 0.12422, 0.00037126, -1.4953, -2.5565, -0.45699, -0.081393, 0.57349, 0.75079, -0.015713, 1.1378, -0.35616, -0.6946, ...
            0.72867, 0.36303, 0.69254, 0.28159, 0.34735, -1.1364, 1.9773, -2.6943, 2.5696, 0.080014, -2.8369, 1.5191, 0.74087, 1.0803, 1.4176, 0.85479, -1.2351, -1.3804, 1.281, -2.3801, ...
            -1.0534, -1.5986, -2.8806e-05, -0.61301, -0.21326, 1.5296, -0.97672, -0.8817, 1.3038, -2.4533, -1.9052, 0.52099, -1.0356, -2.0585, -0.72017, -2.0368, 0.14617, -0.051856, 0.38118, 0.013064, ...
            -1.0069, 0.90323, 1.2907, 0.074704, -1.0613, 0.2817, 0.068896, 0.39315, 0.22816, 1.223, 0.087877, 0.060079, 0.87853, 0.65825, 1.117, -0.67877, 0.89041, 1.4858, -1.5623, 1.9511, ...
            1.3561, 0.76999, 0.6894, -0.47745, -1.089, -0.72982, 0.70145, -1.0923, -1.04, 0.045297, -0.25848, 0.41314, -0.5397, -1.9174, 0.41197, 0.6865, 1.0637, -0.85138, 1.475, 0.42754, ...
            0.44319, -1.8244, 0.10346, -2.3173, 0.48209, 0.46058, 0.99537, 0.031844, -0.0059928, 0.032466, -0.33658, -2.4078, -0.12892, 0.34295, 1.3582, 0.43346, -1.8132, 0.5309, -0.012462, 0.12004, ...
            0.55693, 0.71073, -1.3336, -0.62227, 0.5466, 0.72235, 2.2707, -0.79281, 1.6337, 0.17465, 2.663, -1.4465, 0.87844, 1.6757, -1.5805, -1.0694, 0.55003, 2.0022, -0.29117, 0.0072099, ...
            -0.29704, -0.86072, -0.22769, 0.87164, -0.10256, -0.69952, 3.5541, -0.32522, 0.33344, 0.038633, 0.42188, -0.56243, 0.44645, 0.77296, 0.58191, -0.086194, 2.1364, -0.26646, -0.17229, 0.25706, ...
            -0.34371, 0.36784, -1.1265, 1.8401, 1.1052, 0.62338, -0.86428, -0.25355, -0.80107, -0.64154, 0.058423, 0.18456, 0.43923, -0.77372, 0.56315, 0.22661, 1.3266, -0.6414, 1.6496, 0.91406, ...
            0.93409, -0.66346, -1.3638, 0.41405, 0.50046, -2.2815, 0.35698, 0.61693, 0.96795, -1.5253, 1.2328, -1.2071, 1.6027, -0.92059, 0.41403, -2.628, -1.7894, 0.71042, 1.493, 1.0461, ...
            -1.4245, 0.57688, -0.93483, 0.81727, 1.8934, 0.55052, 2.7296, 0.29398, -0.44265, 0.82177, 1.4006, 0.684, -0.93062, -0.24359, -0.63189, -0.49327, -0.047007, -1.1539, 0.20881, -0.12392, ...
            1.1325, 2.4723, -0.62844, -1.1357, -1.3656, -0.60135, 1.0806, 1.5674, 1.5472, 0.15869, -0.048442, 0.037142, -0.81336, 0.29118, -1.7292, -0.31704, 0.33678, -1.0876, 0.53765, -0.6094, ...
            0.49254, 0.68818, -2.2086, 1.2225, 1.9878, 1.7717, 1.0642, -1.0768, -0.040774, 0.9701, -1.8474, -2.7996, -0.085663, -0.25868, -0.26548, -1.1415, 0.80191, -1.0431, -0.24188, -0.60096, ...
            0.80108, -0.1803, -1.5645, 2.4817, -1.0629, -0.80396, 0.95986, -0.10596, 0.66899, -0.29558, 0.19976, -0.41466, -0.83386, -1.826, -0.3025, -0.60981, -0.50547, 1.6024, 0.48193, -1.3144, ...
            -1.487, 1.8822, 0.20303, -0.21523, -0.52272, 0.53707, 1.0143, 0.13032, 0.047918, 0.35535, -0.91854, 0.12911, 0.36579, 0.8556, 0.021645, -0.77014, -0.48561, -0.95639, 0.63529, 0.39643, ...
            2.1802, -0.22405, -0.52911, -0.43685, 0.69853, 0.20235, 1.9441, -0.0085153, 0.86848, -0.63865, -0.21267, 1.1924, -0.43803, -0.69229, -1.993, -0.5619, 0.37105, -0.79531, -3.1793, 0.64215, ...
            0.25835, -1.2065, -0.33062, -0.52071, -0.0027405, -0.24577, 0.084094, -0.00026012, 1.5909, 1.4353, 0.02879, -1.9596, 0.60167, -0.9642, 0.062915, 0.79737, 0.50515, 0.37243, 0.36272, 0.52492, ...
            0.49182, 0.1054, 0.0035099, -0.014844, 0.20783, -0.58915, 0.5523, -0.01308, -1.1777, 0.19771, -1.7771, 0.051536, 2.4254, 1.4415, 2.2812, 0.0037168, 0.038536, 0.91279, 0.78155, 0.070455, ...
            0.074469, -1.551, 0.68312, 0.16551, 0.032301, -0.52032, 2.3266, -0.3668, 0.0010466, -1.6614, -0.17033, -0.11444, -0.59674, -0.39554, -0.34095, 0.0032712, -1.6372, -0.24203, 1.6876, 0.022353, ...
            -0.9903, -2.0851, 1.2102, 1.4181, 2.4658, 1.1381, 0.30076, 0.57403, 0.095068, 1.6571, 0.82757, 0.53751, -0.1431, -0.031674, 1.5161, 1.7201, -0.16323, -1.4204, 1.2148, -1.6137, ...
            0.32122, 0.39369, 0.18084, -1.6404, 0.084609, 0.7307, 0.094303, 1.5291, 2.1145, 0.74641, 0.61287, 2.0585, -0.066581, -0.0040355, -0.80124, 1.5614, -0.0014666, 1.4117, -0.071303, -1.0206, ...
            0.43883, 0.41597, -0.47011, -0.50712, -1.4485, -2.0633, 0.52203, 0.75304, -0.44449, -1.5608, 0.73484, 0.099031, 1.6692, 1.2373, 0.27538, -0.52578, -2.3514, -0.11257, 0.54273, 2.4065, ...
            0.55788, -0.53747, -0.86372, -0.0035534, -0.44501, 0.68261, 0.82897, -0.99536, -2.6373, -0.69689, 0.68242, 2.6916, 0.50855, 0.44713, -3.2666, 0.73372, -1.8025, -0.53105, 1.5554, 0.0038908, ...
            0.91498, -0.64302, -0.27384, -1.4524, 0.0040629, 1.33, 0.81275, 0.24678, 0.00050816, 1.672, 2.6958, 0.0031187, -2.2795, 0.0010913, -1.334, 0.0020116, 0.19229, -0.045583, -1.4027, -0.00078275, ...
            0.93107, -0.11952, 0.66626, 0.76913, -1.8549, -1.2217, 0.0020741, -0.50455, -0.94842, 0.56295, -0.33974, 1.4742, -2.0849, 0.67342, -3.3482, -0.59674, -0.33934, -1.6297, 0.61578, -2.6167, ...
            -3.0566, 1.9056, 1.1262, 2.6255, -0.48657, 2.6808, 0.93836, 0.08415, 0.00163, -0.25143, -0.31688, -2.0634, 0.26444, -0.12618, -0.00256, -0.75307, -1.3809, 1.8813, 0.14426, -0.00061784, ...
            1.7416, 1.4436, -0.53907, 0.083372, 0.85892, 0.003177, 1.4294, -0.00077162, -0.076117, 0.065571, 1.2626, -1.1207, 0.98351, 3.0404, 0.53865, -0.52151, 1.2961, -0.042774, -2.439, 0.067993, ...
            -0.035465, 0.14284, -0.6257, 0.46383, -0.60987, 1.3432, 0.46735, -2.9618, -1.4545, 0.30334, 0.23295, -2.0921, 0.0042823, -0.2599, 0.1469, -0.063588, -1.1052, 1.0149, 0.21811, 0.0040877, ...
            -1.3508, -0.48747, -0.53051, 1.8512, -0.21456, -0.43918, 0.35064, 0.40956, 0.67171, 0.62797, -1.9899, 0.69535, 0.57148, 0.3735, 0.0019125, -0.0012539, 0.57356, 1.1871, 0.98081, 2.9651, ...
            1.773, -0.67182, -0.63683, -1.6775, -0.60718, 0.69605, -0.16389, 0.19339, 0.29829, 1.9617, 2.561, -0.27777, 2.3986, -1.0016, -0.17192, 0.17871, -2.2124, 0.33105, 0.83375, 0.75582, ...
            -0.32752, -0.44007, 0.46556, -0.30854, -0.75845, -0.46202, -0.0028889, -0.31229, -0.88922, 1.4615, 0.59021, -0.42977, -2.3711, -0.17179, -1.7957, -0.75361, -1.4857, 0.6808, 0.22194, 0.048214, ...
            0.0034006, -0.42963, -0.30967, 1.0455, -0.34515, 3.2389, 0.10889, 1.8108, -0.044818, 1.1349, 0.62354, 0.73182, 1.3245, 0.015896, -1.5746, 2.673, -2.0336, 1.0731, 1.6139, 1.0322, ...
            -0.70689, -0.72216, -0.10144, 0.12712, 0.96847, -1.4796, -0.38922, 3.1643, -3.5109, -1.5089, 0.42234, 0.60623, 0.27543, -2.251, -0.48749, 1.9753, 0.63412, -1.5051, -0.99835, 0.2628 ];
        
        F(:,2) = [0.72712, 0.18603, 0.3848, 0.70093, -0.52314, 0.68005, 0.25878, -0.44117, -0.78385, 0.55856, -0.24646, 0.60881, 0.27515, -0.54736, -1.5639, 0.49892, -1.0961, -1.0496, 1.2093, -1.8422, ...
            -1.7022, -1.5244, -0.38401, -0.82407, -0.0038987, -0.29186, -1.7113, -0.015921, -1.0246, -0.3589, -0.17462, -1.5184, 1.6042, 0.00070767, -0.19116, -0.53292, -1.241, 2.4087, 0.29404, 0.86412, ...
            -0.48875, -0.49521, 0.68755, 0.10512, 0.18063, 0.67589, 0.19552, 1.9275, -0.61147, -0.87027, 0.077753, 1.2285, 2.1183, -0.25639, 0.14288, -1.1192, -1.1508, -1.4174, -1.2046, 1.5209, ...
            1.4432, -0.27553, -0.55237, -0.66737, 0.64358, 0.94443, -0.39298, 1.4461, -0.47096, -0.0044361, -1.0717, -1.6487, 1.0682, 1.9809, -1.1838, -0.62251, -2.4107, -0.54993, -1.2339, 0.10076, ...
            -0.66042, -1.5339, -1.6122, 1.273, 0.20522, 0.19697, -0.31431, 0.097271, -1.6066, -0.95378, 1.1026, 2.9252, -0.68118, -0.092853, -0.28088, 0.36432, 0.50075, -1.2417, 0.18466, 1.1097, ...
            -0.75438, 1.8733, 0.47254, -0.19809, 0.24627, 0.28221, -0.70082, 0.72326, 1.4155, -1.2371, 2.7992, -0.17434, -0.91726, 0.59078, 0.24565, 0.093916, -0.038606, 0.39672, -2.7916, -0.33035, ...
            -0.21528, -0.22083, 1.7244, -0.12055, -0.45058, 0.77228, 2.62, -1.303, -0.25598, 0.00072974, -0.44693, -2.4479, 0.54356, 0.93911, 0.40243, -0.05362, 0.99159, 0.60335, -0.44228, 0.47735, ...
            -0.41872, 1.7671, -1.7779, -0.53364, -1.9305, -0.32091, 1.8033, -1.001, -0.32356, 1.2695, 0.45291, -1.8802, -2.3694, 0.031889, 1.9947, -0.81382, 0.60734, 0.70116, -1.8726, -2.293, ...
            0.25322, 0.36494, 0.023776, 1.479, 1.8616, -0.088338, -0.83986, 0.49536, -0.69235, 1.0995, -0.3655, 0.4272, 2.4197, -1.1421, -0.89652, -0.45636, -0.30078, 0.61413, -0.91217, -2.4553, ...
            0.043347, -0.12748, 0.79986, 0.095629, -1.4512, -0.31347, 0.6475, -0.078368, -0.399, -0.55231, 1.2262, -1.7243, 1.2445, 1.0461, -0.14198, -0.44635, 0.93588, -1.6363, 0.22094, 0.25672, ...
            -0.57411, 0.50338, 0.4918, 1.9581, 1.5985, 0.32772, 1.0903, 0.89166, 0.95705, -2.0365, -0.69291, 0.28094, 1.0833, 1.8102, -0.099074, 0.15298, -0.69386, 1.0847, -0.52859, -0.57436, ...
            -1.7795, 1.9148, 0.21011, 1.4043, -0.085332, -0.85719, 1.4041, 1.6765, 2.4393, -2.5937, -0.91975, -0.97595, -1.8039, -1.1498, 0.18792, -1.9358, -1.3626, 1.3026, 0.69206, 0.45608, ...
            3.1884, -2.4455, -0.4526, -0.50915, -0.10179, -1.4869, -0.23717, -3.0798, 0.4417, -0.8598, 0.33419, 0.5479, 1.1386, 0.37403, 1.535, -0.3218, -1.0992, 1.1088, 1.7447, -0.77131, ...
            -0.44007, -0.40656, -0.30149, 0.76361, 0.47734, 0.57149, -0.34696, -1.4656, 0.36028, -0.96292, 1.8442, 1.464, 0.4121, -0.71999, -1.6485, 0.33853, -1.0692, -0.34907, 2.2668, 1.5054, ...
            1.4273, 0.49412, 0.4169, 0.27397, -0.056966, -0.027035, 1.2198, -0.92168, 0.46808, 0.43208, 0.25411, 0.36425, 0.43516, -0.49356, 0.57631, -0.42118, 1.8717, 0.97971, 1.307, 0.1254, ...
            2.002, -0.46315, 0.11125, 0.14393, 0.59615, -1.4642, -0.35618, -2.9053, 1.0996, 2.1174, -0.7, 1.8327, -1.4213, 0.75004, 0.1566, 0.51034, -0.11908, -1.7501, -0.54714, 0.38253, ...
            0.27545, 0.045726, -1.8783, 1.3791, 1.9146, -0.42202, 0.045101, 0.11551, 2.237, 0.37197, 0.3579, -0.34891, -1.6609, 2.1752, 1.8362, -0.88967, -0.48709, 1.3989, 1.7623, 0.087676, ...
            1.0055, 0.1176, 0.3384, -1.2483, 0.85184, 0.32688, 0.13155, 0.43048, -0.94163, 0.076216, 0.49695, -0.24418, -1.502, 0.23758, 0.39201, -0.1658, -0.42442, 0.57449, 1.8419, -0.35289, ...
            0.041331, 0.23984, 0.2673, 0.66929, -0.54359, -1.4531, 0.90358, -0.31357, 0.38827, -0.58241, -1.1019, -1.6943, 0.22385, -0.59907, -1.4876, 0.090113, -2.7147, 0.63424, 0.071019, -1.4702, ...
            -0.75248, 2.4554, -0.66173, -0.66118, 0.72716, -2.1906, -0.23863, 0.24842, 0.85432, -1.2605, 0.98879, 0.21094, -0.6375, 1.5424, 0.42498, 0.89334, -0.32433, -2.2164, -0.95752, -1.2119, ...
            0.38029, 0.9929, -0.51943, 0.51913, 1.016, -1.8859, -0.55109, -1.5715, 0.45559, -0.052436, 0.47496, 0.76541, 2.0295, -0.10995, 1.3994, 0.05666, -0.48836, -0.21396, -0.45667, -1.2293, ...
            0.26349, -0.0012173, -1.414, 0.58514, 0.58018, 0.9015, -0.064084, 0.080785, -0.073979, -1.7222, 0.36303, -0.7127, 0.33552, -1.0406, -1.5179, 0.26343, -0.6642, -0.38805, -0.38423, -0.93931, ...
            0.41901, -0.8714, -0.7249, 1.2464, 0.56074, -0.46003, 0.5026, 0.22288, -0.82364, -0.76103, -1.4231, -0.019498, 4.136, 0.67506, 0.66889, 2.0512, -0.90085, -0.056259, -0.50634, -0.77383, ...
            0.4332, 0.42488, 0.91083, -0.44898, 0.16446, 0.69806, -1.2205, -1.2293, -2.2999, -2.4723, -1.4789, 1.4701, -2.003, -1.9505, -0.30006, -0.8484, -0.19997, 0.15401, -0.45372, -0.73777, ...
            -0.73355, -0.0056296, 0.56443, -2.029, 0.20229, -0.28667, -0.00071063, 0.62836, -0.65748, 0.72594, 0.57595, -2.5069, -0.71305, 0.30774, 0.34502, -0.70161, -1.3082, 0.7571, 2.3789, 1.347, ...
            -0.50949, 2.5131, -1.6585, 1.1909, 1.0765, 0.78077, 2.5584, 0.70029, 0.69625, 0.29925, -0.71844, 0.93612, 0.55608, 2.3176, -0.64214, 1.3636, -0.63736, -0.12938, -0.79437, 0.42117, ...
            -0.59807, 0.075052, 2.3983, 0.75763, -0.43754, 1.2318, 0.85604, 2.2164, -0.25289, 2.2525, -2.2353, -1.0486, -0.25402, -0.31602, -0.15727, 0.29775, 1.2184, 1.0951, -0.86225, -2.4261, ...
            -0.55543, 0.40677, -0.64227, -0.3198, 0.90917, -2.7356, -0.04175, -2.8747, -0.66424, 0.93065, -2.2779, 0.85993, -0.07467, -0.30595, 2.2235, -0.096476, 0.98818, 0.032114, -0.064982, -1.0767, ...
            2.3125, -0.91918, 0.29975, 0.59263, -1.9821, 0.37034, 0.97568, 0.2132, 0.39823, -0.16263, -0.8868, -0.12147, 1.4146, -0.49885, -0.54105, -2.1187, -2.7366, -1.3201, 0.1549, -0.19606, ...
            -1.1298, -1.2127, 0.83084, -2.437, 2.042, -0.54102, -0.30786, 2.3061, -1.5298, -0.45865, 0.59356, -1.8969, -0.00379, -0.22777, 0.44662, 1.3206, 0.0061068, 1.546, -0.0071204, 0.29093, ...
            -0.89527, 0.018273, 1.8255, -0.083704, -0.655, 0.57722, -1.5783, -2.7736, -0.17342, -1.4153, -0.079253, 0.037108, -1.5553, -2.291, -0.89416, 0.26907, -0.086242, -0.11536, -0.14201, -2.3704, ...
            1.7333, 1.1759, 0.1205, -2.9224, 0.82589, -0.37832, -0.70621, 1.0298, -1.9774, -0.10149, -2.2854, 0.76022, -0.078419, 0.46658, -0.47521, -2.6484, -0.28199, 0.39431, -0.29155, -1.5935, ...
            0.7573, 0.21964, -0.91456, 0.48069, 0.015443, 0.055474, -0.48164, 0.75546, -0.38028, -0.2732, 0.18435, 0.94818, -1.1574, 0.33022, -2.1047, 0.069187, -0.94395, -1.9766, -1.4856, 0.86123, ...
            0.95298, 0.34619, -1.0717, 2.9624, -0.00050125, -0.54233, -1.0465, 0.0027949, 0.13618, -0.1645, -0.79355, 2.1337, -0.30729, -2.702, -0.76733, -0.28469, -0.44492, 0.50178, -0.8708, 1.0259, ...
            -0.48801, -0.39721, -0.0035452, -0.14018, 0.46916, 0.8698, 1.5564, 1.1231, -0.95141, -0.58765, 1.1145, -1.5002, -0.29431, -0.15098, 0.44573, -0.0030975, -1.1519, 2.8438, -0.35201, 2.158, ...
            0.12613, 2.0894, 0.97909, -1.1714, 1.1418, 0.10728, -0.75517, -1.4001, 0.0021303, -1.669, -1.2425, -1.0233, -0.12522, 0.57757, -0.041614, 0.0027528, -2.4662, -1.9797, -0.20249, 0.23223, ...
            -0.1889, 0.82, -3.1812, 1.881, 0.5038, -0.87167, 1.2272, -0.1563, 0.22445, -0.43085, -0.93267, -1.0974, -0.067939, -1.2191, 1.3315, -2.4843, -3.0595, -0.42583, -0.60787, 0.67168, ...
            1.8526, -1.2445, -2.7239, 2.9359, -0.50682, -0.43501, -0.81357, 0.078016, 2.3974, 1.2329, 0.11429, -0.60597, 0.027332, 0.00079029, 0.92108, 1.5258, 0.0022116, 0.69908, -0.55508, 0.94576, ...
            -0.55156, 2.7091, -0.42391, 1.0881, 0.72592, -1.4391, 0.4758, -1.0031, 1.1455, -0.10667, 0.0057524, 3.293, 0.88484, 0.68562, -3.167, -0.30688, -0.86865, -0.66549, -0.010379, 0.039363, ...
            2.9225, -0.21815, -0.40511, 0.0026994, 0.94744, 0.81616, -1.0208, 0.75794, 0.16428, 0.92516, -0.74792, -0.093005, -0.030206, -1.6597, -0.67947, -1.5706, -1.0063, 0.38314, 3.1702, 0.0018126, ...
            0.43357, 0.20003, 1.2959, 2.9961, 0.0025692, 0.33975, -1.4938, 0.066008, 0.0025362, -0.11571, -2.1832, -0.0037657, -0.031819, 0.0035535, 0.54316, 7.0329e-05, 2.9626, -0.65961, 0.98095, 0.0041312, ...
            0.60262, -2.8052, -0.61121, 1.4548, -1.4933, 1.119, 0.0038637, 1.2226, 0.98547, -1.2494, -0.61896, 0.76157, -1.5639, 1.2101, 0.18618, -0.42183, 0.19214, 3.0615, 0.072076, 1.2825, ...
            1.5572, 2.9919, -0.22932, -0.69312, 1.4539, -1.2385, 2.8961, 3.5321, -0.0038293, 0.53923, -0.10979, -0.91536, 0.64236, -0.34713, 0.0026738, -0.1499, -0.25794, -0.076681, -0.048901, -3.326e-05, ...
            2.4979, -2.8523, 0.29183, -0.064885, 1.4667, -0.0037224, 1.1487, 0.0035809, 0.17664, -0.23949, -0.48252, -0.037819, 0.39774, -0.88349, -0.90446, 3.296, 1.8769, -0.016049, -1.2213, -0.20582, ...
            -0.023405, 0.51061, -0.1555, 0.25631, 0.21582, 0.56639, 0.26779, 0.72521, -0.30677, 2.2705, 0.02036, -2.4156, -0.0009687, 0.28208, 0.66945, -1.1321, 0.95615, -0.22475, -0.35246, 0.00252, ...
            -0.90506, -1.9546, 0.15845, 1.9139, 0.64735, -0.45573, 0.45409, -0.4413, 1.4287, 0.16371, -1.5061, 0.017393, 0.067951, -0.62847, 0.0036677, -0.00094102, 2.2095, 1.6186, 3.2913, -1.5326, ...
            -0.71983, -0.5469, -0.21213, 0.36159, 0.53103, -0.47735, -0.23253, -0.18839, 0.97559, 0.44188, 0.31835, 1.5946, -1.5372, -0.77801, -0.00018905, 0.187, -1.5897, 0.17463, 1.3329, -0.011826, ...
            -1.3061, 1.1594, -1.9856, 0.75614, 1.0445, -1.0251, -0.0033056, -0.83277, -1.748, -3.2868, -0.57027, 1.0859, 0.99066, 0.60608, 1.745, -1.1038, -2.824, 0.050398, 1.3616, 0.37193, ...
            0.0014868, 0.5156, 0.63463, 0.70934, 0.4455, 0.85342, 0.24667, -0.57065, 0.20356, -1.4652, 3.5678, -0.31831, 1.368, -1.4194, -0.85988, 2.151, 0.94632, -0.15367, 0.3941, -0.07214, ...
            -0.0076014, -0.77587, 0.59062, 0.5569, 0.22494, 0.11805, -0.16133, -0.033109, -0.54862, 1.3477, -0.38427, 1.436, -1.8705, -0.57402, 1.1738, -0.25642, -3.455, 0.62812, 1.3224, -0.81438 ];
        
        F(:,3) = [0.054248, 1.0746, 1.38, -0.16468, 1.1384, -0.43439, 0.55315, -1.9319, 0.7245, 0.14489, -1.19, -0.30292, -0.5574, -0.13324, -0.77834, -0.11388, -0.60522, -0.94298, -0.26781, -0.19964, ...
            1.5504, 1.1674, 1.4242, -0.1885, -0.0040983, 0.73463, 0.74491, -0.46146, 1.5467, 1.7868, -1.8528, 0.34947, 0.46333, -0.73577, -0.62458, 0.33799, 2.7979, 1.5212, -1.3303, -1.2228, ...
            0.193, -0.32396, 0.28343, 1.0972, -0.54926, -0.24271, -0.27744, -0.47738, 0.61503, -1.2004, 0.61379, -1.5937, 0.18849, -0.47853, -0.68077, 1.218, -0.54592, 0.353, 1.3247, 0.8093, ...
            0.82361, 1.1516, 0.17463, 0.2697, -0.19642, 0.86281, -0.43953, 0.29349, -0.7265, -0.0046091, -0.68127, 0.57834, 1.3407, -2.0746, 1.5172, -0.30963, 1.0554, 0.2605, 2.2078, -1.4951, ...
            -0.29827, -0.34975, -1.1639, -0.00028756, 0.049196, 1.0671, 1.8309, -0.43849, -0.51336, 2.1207, 2.2377, 0.51046, -0.11632, 0.34654, -1.0367, 1.2264, -0.44715, 0.94695, -1.5999, -2.9118, ...
            0.32633, 0.61391, 0.3873, -1.2096, 0.8055, -0.36781, -0.16758, -0.62409, -0.11028, -0.94029, -0.25463, 0.10686, -0.013181, -0.27917, 1.4054, -1.1068, -0.45029, -0.36548, -0.55152, 0.60471, ...
            0.6423, 0.67102, -1.4529, 1.8534, 0.52249, -1.6714, -0.43186, 1.0505, -0.24521, 0.0047329, 1.2602, -0.93714, -0.44799, -1.1244, -1.9405, -0.70389, 2.2405, -0.6644, 1.0331, 1.675, ...
            0.24554, -0.88473, -1.4279, 0.4132, -1.5053, -0.68045, -1.7266, 0.97055, -0.6125, -1.45, -0.5648, 1.7234, -0.74299, 2.5547, -0.53023, 0.80476, -1.2952, -2.0541, -1.4755, 1.0433, ...
            0.7213, 0.9969, 0.93664, 0.95811, 1.6403, -0.13845, -1.3447, 0.56213, 0.00858, -2.3119, 1.1982, -1.727, 0.12967, 0.2087, 0.027672, -0.56954, 0.67817, 0.18222, -1.3443, -0.11925, ...
            0.94083, -0.10789, -0.48028, -0.90278, 1.8193, -0.63504, -0.2777, -0.10508, -0.25946, 0.73116, -0.59137, 0.10899, -0.71683, 0.21284, -0.49071, -1.8453, 0.12628, 1.6422, 0.43267, -0.74313, ...
            0.68551, -0.75414, 0.99756, 0.51503, 0.84653, -0.64553, -0.0074001, 2.5372, 1.0123, -0.21262, -0.57605, 0.5429, -1.9438, 0.042855, -1.4223, -0.13108, 0.16694, 0.35716, -0.9427, 0.37658, ...
            0.49422, -1.0962, 0.77564, 0.10605, 2.6316, 2.1487, -1.1339, -0.48412, 1.0474, -0.52257, 0.32023, 2.4817, -0.36543, -1.0599, -0.80121, -0.16415, -0.51095, 0.3843, 0.13659, 1.0399, ...
            -0.65589, 0.41903, -1.6614, 1.2145, -1.5378, 2.0976, 1.6809, 1.8817, 0.68861, -0.37365, 0.37322, 1.5301, 0.86074, 2.3969, 2.2098, -0.91093, -1.0045, -0.91513, -0.13567, -1.317, ...
            -0.59384, 0.073656, 0.45328, -0.14185, -1.6636, 0.93376, -0.47118, -1.3936, 1.1738, -1.3643, 0.5551, 1.7849, 0.52615, 0.046027, -0.3757, -0.49072, 0.31919, -0.51303, -1.3591, -2.3936, ...
            0.35224, 0.29537, 2.8742, 0.44851, -0.52306, -0.026208, -0.45371, -2.7755, -1.7695, 0.31446, -0.1086, 0.42728, -0.16842, -0.78708, -0.24488, 0.21532, -1.9379, -0.089156, -1.093, 0.6223, ...
            -0.42421, -0.96772, -2.966, 0.080489, -2.3535, -0.36013, 0.63964, -0.24366, -0.71081, -0.32061, -0.58217, -0.60245, 0.81978, 0.97109, -0.82882, -0.44245, 0.23766, 0.61572, -1.8261, -1.4105, ...
            -2.5034, -0.53054, -0.78738, -2.476, -1.9915, 1.6085, -0.15611, 0.78795, -0.55445, 0.35747, 2.3512, 0.60302, -1.4626, 0.35058, 0.24289, 1.1021, -0.2734, 1.9568, -0.018495, 1.1002, ...
            0.53647, 1.5522, -0.80493, 2.5551, 2.7166, 0.48472, 1.4811, -0.21672, -0.013545, 0.13661, -0.23778, -2.7053, 3.3874, 3.1205, -0.33655, -0.11582, 0.44368, 0.10004, 0.62585, -3.0802, ...
            -0.2406, 1.9392, 0.1726, 0.26785, -0.43498, -0.072659, -1.4378, 1.2205, 0.18703, -0.33784, 0.32155, -3.0353, -2.2865, -0.87564, -3.2133, 0.72808, -0.67943, -0.52242, 0.90397, 0.22264, ...
            0.018669, -2.4758, -1.7567, 1.2762, 0.081375, 0.34845, -3.7213, -2.1727, -1.9997, 0.5223, -2.5983, -0.46635, 1.0299, -0.13046, -0.32028, 2.4791, -0.68071, -0.015612, 0.66817, 0.99705, ...
            0.74854, -0.17753, 1.3939, 0.41626, 0.66475, 2.7305, 0.43656, 1.5089, -1.9274, -0.90579, 0.4647, -0.023878, 2.5421, 0.23164, -2.2724, 0.13921, 3.1672, 0.56733, -0.0097312, -2.4495, ...
            -0.64061, -0.19887, 2.9435, -0.14718, 1.3457, 0.86884, -0.053789, -1.0437, 0.79602, -0.54224, 3.3423, -0.8997, 1.2456, -1.441, -0.17621, 0.43899, 0.03704, -0.91105, 0.20332, 0.28313, ...
            -0.52757, 0.7342, -0.94055, -1.3649, 0.22248, -0.27435, 0.79796, 0.78883, -1.056, 0.56099, -3.0355, 0.66062, 5.8842, 0.27749, 3.0673, 0.97255, -1.6803, 0.46262, -0.43731, -0.11003, ...
            -0.034507, 1.4273, 0.87178, 0.50407, -0.41619, 0.01201, -1.6396, -0.94961, 0.19394, 0.22359, -0.33915, -1.2758, -1.0946, -2.329, 0.26584, 3.1467, -0.39561, 1.6165, -1.3036, -0.29226, ...
            0.15488, -0.097013, -0.52905, -2.6669, -0.83932, -0.7576, -0.69778, 0.35939, 0.79719, -0.36288, 0.87665, -1.6291, 0.12428, -0.89791, -0.20342, 1.0367, 0.57518, -1.0581, 2.5257, -2.8175, ...
            -0.57162, 1.1753, 0.79517, 1.6117, 3.1487, 0.88951, 0.96311, 0.36764, -0.75483, -1.2019, 0.44149, -2.1365, 0.50799, 2.5258, -0.23386, 0.9915, 1.1578, 0.23002, -1.0787, -1.0167, ...
            1.0194, -0.61484, 1.2936, 0.36533, 0.23901, -0.062568, -1.1529, 1.7163, -1.1254, -0.93185, 1.3292, 0.55811, -0.36176, 1.2618, 1.4387, -0.36223, 0.42051, -0.53435, 0.12971, 1.2111, ...
            0.071381, -0.027951, 1.1816, -1.3917, -2.9063, 0.76554, 0.82026, -0.47042, -0.39013, 1.0787, -1.1778, -1.2048, -1.3756, 1.1512, -2.7682, -0.45383, 1.2951, 1.206, -1.6637, -1.2805, ...
            -1.3864, -0.41062, -1.5664, 1.676, 2.6358, 0.016005, 1.0198, 0.78016, -0.49292, -1.7156, -0.18123, 1.6731, 0.51141, -2.5625, 2.2196, -1.9985, 0.87777, 1.8862, -0.38198, -0.22086, ...
            1.2061, 0.77151, -0.73294, 1.4835, 0.64929, -0.38808, -1.1153, 1.4133, 0.44434, -1.2472, 1.2735, 1.33, -0.29651, -0.31954, -0.92723, -0.094324, -0.12523, 0.79075, -0.33615, -0.18484, ...
            0.5898, 0.31504, -1.6758, 0.31906, -2.3041, 0.49945, 0.064263, 1.3, 0.068846, -2.457, 0.31101, -0.42623, -1.3215, -0.0036439, 0.21348, 0.12436, -0.45786, 0.45384, -1.1339, -1.1531, ...
            1.9447, 0.68275, -0.23017, 0.43545, -0.035012, 0.25972, 0.99275, -0.38312, -0.45933, -0.64895, 1.6585, 0.36688, -0.42435, -0.79959, -0.46896, 1.0849, -0.40981, -1.2185, -0.10217, 1.1468, ...
            0.59319, -0.64078, -0.97641, -0.22195, -0.10417, 0.25546, -2.1287, -0.33038, 0.97705, 0.018309, -1.2141, -0.13895, -2.365, -0.39116, 0.89036, -0.42251, -0.95464, 0.95258, -0.89635, 0.26476, ...
            0.53459, 0.057367, -0.13614, -0.6429, 0.0057858, 0.71904, 0.36172, -0.0057756, -0.56461, 2.2591, -0.92467, 0.91378, -0.2064, 0.89899, -2.1596, -1.5804, -0.16677, -1.7038, -1.022, -0.35688, ...
            0.1378, -0.86025, 0.0040434, -1.0449, 0.76851, -0.56655, 2.3116, -0.50284, -2.0031, -0.33854, 1.1529, -0.55216, -0.80367, -1.6682, -0.81309, 0.0042223, 1.1963, 1.9102, -2.7267, 0.48614, ...
            1.022, -1.48, 2.8189, 0.46265, -1.992, 0.62225, 0.35456, -2.6306, -0.0059669, -1.9985, 0.74482, -0.77683, -0.29464, -0.094782, -0.85624, -0.0047915, 0.44591, -0.52671, -2.4469, 0.30932, ...
            -1.3031, -1.2752, 1.1036, 1.9306, -0.23545, -2.0938, -0.23827, 0.50522, -0.77517, 2.4497, 0.38985, 1.3773, -0.3827, -0.43136, 1.6953, -0.040491, -0.21753, 0.46644, -0.71226, 1.9719, ...
            1.4949, 0.021001, -1.2284, 0.54145, -1.282, 2.0831, -0.19827, 2.4919, -1.7047, -0.098966, -0.26659, 1.3544, 0.026013, -0.0049323, -2.189, 0.011932, -0.0058476, -2.1973, -1.6298, -1.156, ...
            -0.047405, 2.1271, -0.31357, -0.51543, 0.3884, 0.56998, 0.011369, -0.31635, -0.4661, -0.17245, -1.6861, -1.3759, 0.46175, -0.76132, 1.3706, -0.27942, -1.5643, 0.20852, 0.39174, -0.89957, ...
            -1.8311, 0.33586, -0.52646, 0.0046178, 0.76005, -0.79897, -0.38216, 0.28651, -0.25149, 0.70199, 0.85812, -0.042381, -0.50054, -2.1468, 1.3688, -1.3003, -1.627, 0.13261, -0.40869, -0.0047763, ...
            -0.077053, 0.12798, -0.12659, 1.0023, -0.0042579, 0.95003, -1.7034, -0.9114, -0.0058776, -0.57179, -0.14521, 0.004163, -1.744, -0.0052363, -0.76434, -0.006098, 1.8416, 0.24874, 1.9521, -0.0048536, ...
            -3.2208, -0.16309, 0.12859, 0.36589, 1.6664, -0.71534, 0.0046912, 0.35483, 0.015031, -0.31646, 0.022411, -1.6628, 0.02695, 0.15582, 0.89311, 0.13951, 0.48031, -0.84376, 0.47007, -1.8556, ...
            0.03593, 0.39782, -1.837, 1.1677, 1.374, 1.8374, -1.8636, 0.51403, 0.0048904, -0.32519, 0.71213, 1.2771, -0.12304, -0.19162, -0.0052473, 0.40204, -0.82789, -0.82592, -1.2697, 0.0063917, ...
            1.9082, -1.6693, -0.28978, 0.31496, -0.044842, 0.0041578, -1.1174, -0.0052744, 0.29959, -0.28394, 0.030325, 0.24049, 1.0578, -1.4425, 0.48059, -1.2693, -0.66998, -0.11371, -0.64591, 2.2108, ...
            -0.21476, -0.42262, -0.28871, -0.41635, 0.25289, 0.049935, 1.7405, -0.20401, -0.67011, -2.8974, -0.47533, -1.6716, -0.0046862, -0.28389, -0.17113, 1.7143, -0.067355, 0.65426, -1.2697, -0.0042635, ...
            0.70006, 2.1954, -3.6473, 0.041981, 0.072321, 0.68675, -2.719, 1.6479, -2.2305, -0.47871, 0.6114, 0.40804, 0.56403, -0.56601, -0.004912, -0.0062273, 0.085136, 0.8989, 1.1111, -0.94658, ...
            1.0044, 0.79903, 0.22017, -0.7851, -0.083347, -1.4228, -0.40534, 0.41319, 0.96627, -0.74473, 0.25817, -0.13305, 0.71495, -0.49344, 0.7288, 0.32444, -0.1008, -0.71539, -0.26024, 0.28092, ...
            0.061966, -0.66351, -2.118, -0.13034, 0.6326, 1.111, 0.0046866, 1.3766, 0.44412, -0.49146, -3.441, -3.357, 0.20631, -0.94357, -0.1122, -0.66644, 1.7257, -0.61702, 0.93473, 0.122, ...
            -0.0052404, -0.22048, -0.019502, 1.8735, -0.53641, 1.0374, 0.34403, -2.0036, 0.46154, -1.7033, 0.018082, -0.5049, -0.6324, 0.87248, 1.9271, -0.89424, -0.62973, -0.73591, -3.2718, 1.4651, ...
            -0.43601, -0.062814, -1.3003, 0.35764, 0.12422, 0.3531, 1.8353, 1.5104, 0.41357, 0.75568, -0.50633, -2.564, 0.89089, -0.56497, -2.3584, 1.2843, -0.8168, 0.25114, 1.8734, -0.67232 ];
        
    case 'Gauss150'
        F(:,1) = [-0.60004, -0.0048264, 0.24031, -1.8559, -1.2435, 0.088789, 0.0094573, -1.5432, -0.55521, 1.3937, -0.67883, -0.20923, 0.42957, -1.2483, -1.4328, -2.5983, -0.59521, -0.63665, -0.89063, 1.658, ...
            -2.8479, 0.43332, 0.0027715, -0.72042, -1.2512, 0.0053545, -1.4113, -0.19978, -0.0047717, 1.1878, 2.0424, 2.1915, -1.2485, 0.81448, -2.0877, -0.37508, -2.2559, 1.376, -1.3113, -1.2591, ...
            -0.57354, 0.60077, -0.83851, 0.45752, 0.08957, -1.4973, 0.22767, -1.5355, 0.64565, 0.21217, -1.7697, -1.98, 0.50644, -0.87352, 0.73708, -3.2946, -0.63499, -0.34658, -2.8236, 0.3879, ...
            0.068895, 0.77066, -1.7474, 1.1782, -2.4564, -0.90758, -0.59008, 0.17637, 0.57399, -1.2764, -0.064527, 0.54974, 0.66832, 0.65947, -0.29285, -0.24211, 1.2019, 1.0572, 0.63603, 1.7612, ...
            0.47083, -0.57337, 0.68654, 1.9165, -0.65995, 0.17927, -0.86479, 1.3381, 0.11198, -1.9145, -1.037, 1.1253, 1.323, 0.91349, 1.1305, -1.2069, 1.057, 0.80952, 0.54825, -0.69003, ...
            -1.1284, 1.6609, 0.022491, 0.43093, 0.14146, 0.028621, 1.2067, -1.0894, 2.0934, -1.0256, 1.2857, -0.24793, -0.078987, -0.006827, 0.4213, 0.16991, 1.9037, -0.96182, -0.65223, -0.13765, ...
            1.113, -0.58853, 0.53118, 1.3879, 0.070197, -0.83279, 0.0034333, -0.44951, -1.353, 0.83979, 0.32944, 0.50368, 0.83415, -0.38369, -0.40782, 0.64464, -0.30331, 0.52975, -1.4049, 1.6342, ...
            -1.0325, 1.8815, -0.1525, 0.50155, 0.59682, 0.27662, -2.387, -0.062646, -0.63798, -1.7003 ];
        
        F(:,2) = [1.5877, -0.0041212, -1.3413, -0.060631, 0.19886, 0.7378, -1.4353, -2.3766, 0.32126, -1.2397, 0.2921, 0.72475, 1.3335, -1.8288, -0.8782, 1.4905, -0.076383, -1.6226, -0.97079, -2.0697, ...
            0.055774, 0.76007, -2.1707, 1.7039, -1.7657, -0.0028589, -1.2933, -0.054203, 1.2708, 0.37641, 0.56013, 0.3882, 0.57396, 0.85845, -0.68433, 0.16456, 0.19536, -0.42055, 0.078744, -0.13122, ...
            -0.14377, 0.005878, -0.054029, 0.84943, 0.51762, 1.3936, -1.0128, 1.1733, -1.2388, -0.8364, -1.062, 0.22151, -0.11806, -0.259, -0.072488, -0.48017, 0.72887, 1.2241, 0.18004, 0.78233, ...
            -0.23984, 1.1481, -0.56819, 0.21533, 1.2488, 0.09885, 1.5423, 0.57048, -0.19868, 1.263, 2.3746, 0.51142, 1.7258, 2.4664, -0.75337, 0.69172, 2.009, 0.52914, 0.79323, 0.056371, ...
            -2.136, 0.86253, -1.5472, -0.96403, -0.0090359, 0.31789, 0.37611, -0.04499, 0.18526, 1.6056, 0.48265, 1.3633, -2.0483, -0.59537, -1.5246, 0.5064, 0.14904, -0.84097, -2.9653, 0.90731, ...
            -0.24371, -0.66661, -0.96222, 0.82969, 0.38803, -0.82292, 1.6266, -1.0117, -0.13222, 1.2019, 0.81262, 0.3704, 0.018576, -0.0063797, 1.2786, 0.24928, 0.16519, -0.15579, -0.79654, 0.59448, ...
            0.98635, 0.029153, -0.65036, -0.44557, 1.5898, 0.89554, -1.355, -1.9201, -0.33901, -0.72845, 0.49721, 0.19176, 0.28541, -1.2858, -0.523, 2.5314, 0.64205, -2.1181, -1.0417, -0.53688, ...
            -0.71384, 1.565, -1.7551, -0.80016, 1.5758, 0.38815, -1.6312, -0.84307, -1.1367, 0.68396 ];
        
        F(:,3) = [0.40758, -0.00097921, 1.8801, 0.96683, -2.7446, -0.45553, -1.0347, -0.061662, 1.0252, 1.2956, -1.0773, 0.28509, 1.1633, 2.1357, 0.33137, -0.55041, -0.83785, -0.42283, 1.9984, 0.30574, ...
            0.92077, -1.1355, -0.6208, 0.46044, 0.98429, 0.002096, 1.2806, -1.1706, 0.43336, -1.4429, -1.7879, 0.046184, -1.1244, -2.4852, -0.56632, 1.081, -0.4383, 0.96415, -0.25128, -1.5401, ...
            -0.54596, 0.11148, -2.2364, 0.378, 0.48297, -1.234, 0.91983, -0.48522, -1.086, -1.7, 0.15099, 0.42635, 0.35667, -1.806, -0.68143, -0.65684, 0.28005, 2.2333, -1.686, -0.50038, ...
            -1.0647, -1.0929, 1.1413, 0.38536, 0.5465, 1.6692, 0.99854, 0.99742, -1.0275, 0.38997, -0.99173, 0.92783, 1.5863, -1.3021, -0.17696, 1.3437, 0.89158, 1.0686, -1.2537, -0.16132, ...
            -1.4504, -1.3571, 0.67419, -0.27203, -0.40416, 1.6669, -0.22462, 0.69048, 2.704, -0.058548, -0.3835, 2.1649, -1.3191, -0.28127, -0.58942, -1.8036, 0.85782, 0.60867, -0.18222, 0.43022, ...
            -0.18697, -0.24871, 0.29411, 0.16043, -1.6424, 0.51162, 0.052374, 0.57157, 0.68444, -0.3333, 0.47554, 1.5831, -0.14955, -0.042862, -0.62982, 0.66867, -0.63692, 0.25203, -0.1805, -1.0497, ...
            -0.64408, -1.7183, 1.0008, -0.69351, -0.90481, -0.20933, -2.0722, -0.48398, -1.8988, 0.20102, 1.7826, -0.57634, -0.6685, 1.8601, 0.51143, -0.3106, 0.13501, -0.10457, 0.16046, 2.366, ...
            0.18948, 0.48205, 0.34543, -0.88404, -0.26936, -0.8864, 1.2625, 0.34792, -1.0813, -1.2725 ];
        
    case 'Gauss300'
        F(:,1) = [-0.78332, -1.0099, 0.81605, 1.0904, -1.2655, 1.2338, -0.0018188, 3.0837, -0.00068703, -0.18292, -1.052, 0.6063, 1.829, -0.61479, -2.3318, 0.24554, 0.99794, 0.24828, -0.98217, -1.1618, ...
            -0.27173, -0.4523, -0.67177, -2.8711, 0.61731, 0.32877, 1.2365, 0.69534, 0.83127, -0.94602, -0.9797, -0.46826, -0.71444, 2.727, -2.0263, -2.2538, -1.041, 0.28672, 2.0479, 0.62305, ...
            1.1754, 0.63056, 1.2102, -1.2333, -0.76508, -0.46505, -0.13911, -2.4363, 1.2585, -0.567, -0.38774, 0.30812, -0.58091, 1.1976, 0.30624, 1.4794, -0.27932, -0.12447, 0.72223, -0.2075, ...
            2.7966, 0.28043, 0.69669, -2.6839, 3.1725, -0.11833, 1.1681, -0.20012, -1.1158, -0.10385, -1.1206, -1.0832, -0.2179, -1.7656, 0.4683, 0.18, 0.77564, 0.87195, 0.83962, -1.9993, ...
            0.23678, -1.1615, 0.083961, -0.61929, 0.028863, -0.46771, -0.13709, 0.046524, -0.72999, -0.34772, 0.059632, -0.055213, 0.33064, 0.17377, -1.0695, 0.646, -0.67063, -0.48086, -0.57269, -0.54615, ...
            1.126, 0.26353, 0.10919, 0.8802, -0.01753, -0.43926, -0.9423, -0.47942, -1.106, -0.44156, -1.5284, 0.1068, 0.41702, -1.2115, 1.1324, 1.3521, 0.67706, 1.579, 0.72952, 0.062338, ...
            -0.161, -0.70097, -0.63026, -0.15405, -1.2886, 0.15362, -0.4846, -1.4414, 0.11038, 0.97323, -0.7231, 1.7241, -0.25256, 1.1718, -1.6135, -0.35664, -2.5378, 0.39022, 1.0914, -1.4878, ...
            -0.34046, 0.50831, 0.53282, -1.1218, 1.445, -0.43884, 1.2097, 0.35861, -1.3811, 0.23143, 0.66045, 1.1907, -0.46175, 0.44787, 0.59976, -1.6987, 0.26746, -0.5184, -0.10039, 0.49375, ...
            2.7453, 1.4963, -0.26493, 1.3242, 0.94838, -1.9633, -0.55452, -0.60664, -1.7049, -0.83923, -1.4965, 0.94177, 0.7815, -0.13845, 1.1506, 0.47856, 0.0078883, 2.0395, 1.46, 0.89119, ...
            0.34334, -1.4646, 1.7198, -2.7305, -0.76491, 0.96062, -2.5527, -1.0943, 0.91751, 1.3693, 0.22106, -1.5823, -0.18656, 0.52754, 1.5848, -0.16578, 0.32319, -0.50314, 0.56779, 0.26053, ...
            -1.1889, -1.6296, 0.86861, 0.35608, 1.774, 1.9995, 0.0014009, -0.36084, -0.15044, 0.24184, -2.2991, -1.187, -2.4817, -1.4768, 0.97977, 0.31526, 0.39708, 0.31537, 0.53553, -0.53025, ...
            1.8878, -1.7518, 0.18834, 1.8894, -0.10097, 0.22313, -2.3235, -1.0695, -0.22406, -1.5109, -0.1861, 1.9171, 0.50511, 0.83859, -0.91437, -1.7641, 0.707, 1.466, 0.33631, 1.2527, ...
            -1.1523, -1.8349, 2.0616, 0.41067, -2.5365, 0.65828, 0.80464, 0.29565, 1.683, -1.7807, -0.67038, -0.94301, 0.74443, 0.0081518, 1.0723, 0.51669, 0.3393, 1.6384, 0.98539, 1.6072, ...
            0.88771, -0.10899, -1.3129, -0.20021, 0.92133, 1.1868, -1.6848, -1.9291, -0.99552, 1.0724, -1.5188, 0.57136, -1.7306, -0.87643, -0.83999, -0.23018, -0.35992, 0.84885, 1.8717, 0.065478, ...
            1.0004, -0.47287, 0.091288, 1.0147, 0.80729, -1.1009, -0.7713, 0.27808, -0.097385, -1.01, 0.39093, 0.31085, -0.66534, -0.88462, 0.00020888, 0.67792, -0.16412, -1.7541, 1.2077, -0.44759 ];
        
        F(:,2) = [-1.5489, 0.59838, -0.9412, -0.20875, -0.30354, 1.0348, 0.0058223, 0.71924, 0.0058533, -0.1572, -0.6815, 0.38508, -0.95709, -0.24068, -0.78296, -1.5854, 0.71155, -1.0744, -1.1631, -1.5502, ...
            0.45484, -1.9723, -1.0196, 1.5118, 1.1709, 0.43742, 1.0778, -0.084953, -0.38341, -1.5901, -0.18049, 1.1646, -1.3273, -0.10288, 0.70229, -0.84517, -1.7276, 1.2292, -0.27484, 1.4997, ...
            -1.2471, -1.3081, 0.21577, 0.63233, 2.0841, -0.66049, 0.89311, 1.8449, -0.18259, 0.90278, -1.1467, -1.2093, -0.74128, 0.36011, -0.88178, -0.97671, -0.79212, -0.46407, -0.26027, 0.79306, ...
            -2.1476, -0.36135, -0.33317, 1.5013, 0.0356, -0.91915, 0.23829, 2.0256, 0.16179, -1.5125, 1.1634, 2.4253, 0.40633, -0.24039, -0.35653, -0.67213, -0.074636, -0.49195, 0.46709, -0.018686, ...
            -0.3779, 1.4105, 0.39938, 0.50317, -1.2784, -0.64077, -0.83737, -0.43213, 0.82137, 1.2016, -1.4629, -0.85998, -1.9463, 1.4947, 0.21603, -0.16399, -0.57279, 1.1819, -0.90139, 1.0143, ...
            0.11075, -0.87171, -0.35367, 0.4576, 0.9975, 0.32006, -0.506, -0.87324, -0.21021, -1.4476, -0.24395, 0.43549, 1.8519, -0.050873, 0.91656, 0.69722, -0.77389, -0.59673, 0.70723, -0.25063, ...
            0.33628, 1.3558, 1.3631, -0.89343, -1.114, -0.65102, 0.74427, 1.6718, 0.45463, -1.1151, 1.9434, 1.9775, 2.3558, 0.5538, 0.6484, -0.75237, 0.10454, 0.099604, -0.34393, -0.52569, ...
            0.14428, -0.47905, 0.13143, 1.2257, 1.2988, 0.69632, -0.5103, 0.23519, 2.8143, -0.15526, 1.4535, 0.11447, 0.51741, -0.093637, 0.6054, 0.069357, 0.27669, 0.57257, -0.17078, 0.25013, ...
            -0.70763, -0.1323, -0.40058, -0.96587, 0.47613, 1.3885, 0.7383, 0.31687, 0.80891, 0.5691, -0.43643, 0.026163, -0.57532, -0.89102, -0.73732, -1.0891, -1.9167, -0.0585, -1.0807, -0.662, ...
            0.7468, -2.4944, -0.78035, -1.8565, -2.6698, -0.30512, 0.53009, -1.7324, -1.7022, 0.62774, -1.5565, -1.0837, 0.2702, 0.046803, 0.64344, -1.0375, -0.63712, -2.4167, -0.41663, 1.6993, ...
            0.83268, -0.072068, -0.37834, -0.056667, -2.5414, 0.51172, -0.0062155, -0.19691, 2.2975, 1.9144, -1.4164, 1.4202, 1.2392, 1.9418, -1.5742, -1.2799, -1.5586, -1.3196, 0.5586, -0.76274, ...
            -0.11445, -1.2567, 0.78216, -0.99249, 0.94168, 0.79373, 1.139, 1.5335, -0.0074267, 0.3993, -0.061579, -1.5889, 0.23542, 0.18292, -1.2709, 0.0256, 0.28294, 0.95028, 0.93808, -0.45852, ...
            1.2543, -0.53748, 0.37116, 1.2304, -2.3336, 0.73121, 0.077232, -0.79872, 1.279, 0.92323, -0.60218, -0.95618, -2.6273, -0.15307, -1.365, 0.37566, -0.017587, 0.91897, -0.31001, -0.61941, ...
            -0.18757, -1.7835, -0.57513, 1.9147, -0.89435, 2.3468, 0.711, 0.13179, -1.1767, 0.2036, -1.2699, -2.4611, 0.34903, 0.40765, -1.3749, -0.66064, -1.1861, -1.5114, -1.4618, 1.4767, ...
            -0.68675, 3.1669, -0.11708, -0.38194, 0.60031, 2.3494, -1.5098, 3.0635, -2.5473, -2.2026, -1.3271, -0.73223, 1.8737, -1.5399, -0.0056259, 0.63037, -2.0868, 2.1805, -0.1873, -0.23928 ];
        
        F(:,3) = [0.84526, 1.4797, 1.4534, -1.6266, 1.9552, 0.83566, -0.0020074, -0.11276, 0.0025503, -1.6048, -0.30148, 0.7931, 0.34889, -1.9087, -1.4022, 0.99745, -0.065005, -0.84743, 0.17409, 0.47315, ...
            -0.13029, 0.47302, -1.6692, -1.4894, 0.69193, -0.98934, -1.1331, -0.31828, 0.72298, -1.7412, 1.5389, 0.46281, -0.037876, 1.6866, 0.31581, -2.1651, 0.6336, -0.8914, -0.1139, -0.14578, ...
            -0.92039, -2.2399, 1.0134, -1.4031, -1.4216, -0.21618, -0.1853, -1.0686, 0.11989, 1.4635, 0.59091, 0.15195, 0.65095, -0.21814, 0.26165, 0.93768, -1.7737, -0.55036, 0.2469, -0.38679, ...
            -0.56032, -1.3628, -2.5399, 0.42555, 1.1138, 1.0579, 2.4347, -0.75266, -0.083251, -1.47, 1.7514, 1.7307, 0.51209, 2.1116, 1.9047, 0.61663, 0.1243, 0.15152, 0.85592, -0.67187, ...
            1.0651, -0.39521, 0.37773, 1.2349, 1.8839, 0.020451, 0.23203, -0.56491, -0.4478, 0.61691, -0.63848, -2.3536, 1.9946, -1.3077, -0.073218, -0.41627, -0.26867, 0.74871, -1.2, -1.0602, ...
            -0.46151, 0.078515, 1.0756, 0.27919, 0.13255, -0.11923, 0.73115, 0.50301, -0.5866, -0.58463, 0.72618, 0.45086, 0.65011, -0.00078562, -0.71133, 1.3126, -0.40534, -1.0491, -0.23804, -1.4841, ...
            1.4459, 0.45832, -1.7015, -0.074084, 0.67032, -0.55539, 2.2625, 0.17391, 0.71533, -0.68423, 0.71575, 1.5309, 0.7617, -1.9816, -0.48918, -0.25027, -0.64117, -0.50416, 0.79215, -1.3508, ...
            0.55803, 0.87802, -0.68706, -0.16223, -0.096551, 0.11145, -1.0226, -2.0706, -0.46611, -0.62663, 0.66786, -1.7563, 1.3799, -0.61859, -0.069177, 1.1799, 0.81008, -0.36604, -2.1597, 0.70152, ...
            0.50927, -0.78219, 0.92841, 1.8449, -0.069169, -0.82558, 0.8325, 0.99258, -0.15716, 1.0837, 0.74797, -0.4177, -0.060145, -0.37729, -3.0373, -0.35727, 1.0647, 0.54736, -0.22274, 0.36451, ...
            0.83184, -0.076296, -0.84213, -0.98043, 0.72511, -0.21494, 2.3212, -0.78304, 0.48483, -1.2949, 0.9841, -0.12946, -2.4641, 1.3289, 1.0241, 1.1719, -0.48616, -1.1503, 1.1656, 1.5693, ...
            -0.95631, 0.98969, 1.2814, 1.5819, 0.26756, -0.3055, -0.00080141, -0.69379, 0.13654, 0.061941, -0.35941, 1.2246, -0.84766, -1.5213, 0.34173, 0.94513, 0.44742, -0.98745, -1.0824, -3.3678, ...
            0.093615, 1.6475, -0.72211, -1.9889, -1.3243, 1.4347, 1.2976, 0.046977, -1.8932, 0.6043, 1.465, -0.36173, 1.0202, -0.63795, -1.1403, -0.88022, -1.109, 1.1494, -1.3307, 1.6845, ...
            -0.1035, -0.44159, -0.22188, 1.0889, -0.46001, -0.24615, 1.0753, 2.0354, -0.0056986, 1.3085, 1.4661, -1.0475, -1.6116, 1.704, -0.18779, 0.71994, 0.85707, 0.41011, -3.0355, 0.50022, ...
            -0.47216, 0.84656, 1.0123, -0.14491, -0.093775, 2.4474, 0.91239, -0.36888, 1.5682, 0.5831, -0.71758, 0.16942, -2.5853, -0.2959, 0.008959, -0.47441, -2.7856, 0.77015, -2.1779, 1.1836, ...
            -0.3191, 0.16724, 0.06694, 0.6603, 2.0543, -0.77027, -0.36615, 0.78864, -2.5948, -1.5513, -1.2929, 0.43452, 0.64498, -0.75725, -0.0030891, -3.0483, -0.03915, 1.5062, 1.6819, -1.0945 ];
        
    case 'Gauss500'
        F(:,1) = [1.07, 0.94756, 0.78599, -0.046246, 0.38277, 2.4145, -0.89164, -0.89555, 1.2435, -1.1456, 0.21037, 2.8289, -0.60589, 1.1275, 1.108, 0.48307, -2.1607, 0.72805, -0.25226, -0.86573, ...
            -3.0801, 0.34723, 1.9572, 0.62279, 1.0664, 2.97, 0.85065, -1.5167, 0.70094, 0.22834, 0.93084, -2.1516, -1.3792, 0.84669, 1.0759, 1.5154, -2.1187, 2.058, 1.2688, -0.22978, ...
            1.1999, -0.1384, -0.4676, 0.76785, 0.28486, -0.32967, 2.415, -1.0923, -0.4919, 0.44349, -1.8443, -0.37481, -1.1553, 1.037, -0.49491, 1.8889, 1.8946, -0.046257, 2.7808, 0.084878, ...
            1.8984, -0.96203, -0.18355, -1.1668, 2.3658, -0.35006, -0.2259, -0.027752, 1.6857, -0.082273, -0.25168, -0.64673, -1.4395, -0.60091, 2.9151, 0.31505, -1.426, 0.50103, -0.28507, 0.95818, ...
            0.18607, -0.10208, 0.091392, -0.91037, -0.07006, 0.45656, 0.38631, 0.084219, -1.3787, -1.1983, -2.2804, 0.56017, -0.0043725, -0.84287, 0.67887, -0.61806, 0.35564, -2.3548, -0.73989, -0.039346, ...
            0.16566, -0.40828, 1.9052, 0.62211, -0.49273, 0.80279, 0.6197, -0.47216, 1.2533, 0.79367, 0.29694, 0.20838, -3.3704, -0.50506, -0.77097, -0.39849, 0.80331, -1.9254, 2.9713, 0.18858, ...
            0.13139, -0.50921, -1.6742, 0.54648, 1.9701, 0.82831, -1.3884, 1.0387, 0.77889, 0.42642, 0.15034, -1.5474, -0.90945, 0.56762, 0.84159, -1.9744, -0.020019, -0.0085364, -0.011089, 0.16116, ...
            0.91292, -0.62967, 1.1652, 0.34837, 0.14464, -0.69193, 0.5024, 0.65142, 0.82905, -0.065296, 0.45411, 1.6396, 0.82451, 0.90215, 1.2919, -0.29983, -0.72492, -0.67991, -0.70595, -0.2103, ...
            0.95839, 0.37873, 0.33214, 2.3311, 1.0164, -0.24912, 1.4024, 0.0030008, 1.2563, 2.1217, -0.10652, -0.32062, 1.2916, 0.30409, 0.65275, -0.063388, 0.2434, -0.47829, -0.88266, -1.2801, ...
            -0.010161, 7.1091e-05, -0.28628, 1.1231, 0.55913, 1.1113, -0.19349, -0.9961, -0.78703, -0.66116, 0.42232, -1.474, -1.1652, 0.52824, 1.0763, 1.0041, -0.93987, 1.413, 0.78386, -0.49099, ...
            1.1359, 0.076708, 0.71923, -1.6282, 1.4109, 0.61794, 0.56943, -0.032397, -1.2192, 0.23683, 0.54503, -1.3215, -0.23413, -0.93171, 1.0581, 1.4132, -0.11363, 1.3433, -0.40839, 1.4153, ...
            -0.64536, -1.5492, 0.10725, 1.1779, -1.0913, -1.6115, 0.63899, -1.2095, -0.35115, -0.498, 1.7202, 0.4641, -0.47529, -0.41877, -1.412, -0.84079, 0.46029, 1.4054, -0.12163, 1.1324, ...
            1.398, -1.1122, -0.84637, -0.89071, 1.4056, -0.67295, -1.0276, -1.5464, 0.53703, -1.715, 0.26781, -0.60213, -1.448, -1.3861, 0.0041461, 1.9889, -1.24, -1.221, 1.1672, 2.9903, ...
            0.49481, -0.2014, 0.18197, 1.2541, -0.42189, 0.041858, -0.43358, 1.0887, 1.0914, 1.1406, -0.39717, 1.5208, 2.6272, -1.3576, -0.58164, -0.60222, -0.10341, -0.093619, 0.21242, 1.9473, ...
            -0.57042, -1.4326, -1.3925, -1.2367, -0.78662, -0.41944, -1.3763, -0.080115, -0.15925, -0.23829, -1.1843, -0.04263, -0.50692, -0.51331, 0.16369, 1.1664, 0.5938, 0.53695, -0.049156, 0.67295, ...
            -0.49094, 0.74056, 0.69621, -0.52377, 1.2411, 1.0089, -2.2324, -0.93408, 1.4823, 1.6884, 0.55904, -0.66959, -0.49702, 0.50535, -0.19089, 0.42151, 0.33956, 2.0146, -0.36094, -0.55264, ...
            -1.2318, 0.024705, 0.77382, 2.7728, 2.0016, -0.31845, 0.42885, 0.34533, 1.0853, -0.61851, -0.81413, 1.0856, 0.96812, 1.4921, 1.2743, -0.25142, -1.8138, -0.94803, 0.11489, 0.49241, ...
            -1.1944, -0.84991, -0.47971, 1.5777, -2.0392, 0.015683, -0.73584, -0.65092, 1.3089, 2.5689, -1.1583, 1.3095, -2.0793, 0.67302, -1.1246, 0.5932, 1.4301, -0.42934, 1.1715, 0.89951, ...
            0.19707, 0.73449, -0.047041, -0.76158, 0.80015, -1.2893, -1.9163, 1.9237, 0.42204, -0.21889, -2.0791, -0.13496, -1.5538, 0.6154, -0.61404, 0.3487, -1.5176, -2.6258, 0.12921, 0.31122, ...
            -1.5503, -1.6184, 0.43443, 1.0846, 1.0707, 0.26217, -0.0071609, -0.37427, -0.53658, 0.2166, -1.823, -0.75923, -1.0266, -1.4022, -0.045945, 0.57556, 0.4612, 0.87046, -1.4641, -1.8449, ...
            0.50806, -1.4447, 0.69755, -0.98145, 2.0773, 1.4582, 1.0138, -0.46398, -1.164, -0.41581, -0.58877, -0.87734, -0.14023, 0.77865, -1.3693, -1.4525, 0.42589, 2.1173, 0.52133, -0.28187, ...
            -0.87418, 1.4403, 1.8855, -0.91899, 0.63761, 0.93325, -1.7715, -0.076825, -0.72552, -1.5427, -0.18729, 0.5866, 1.5031, 1.729, 0.55994, 1.8785, 0.2832, -1.9756, -0.75415, -0.26873, ...
            1.6534, -0.71526, -1.7087, -1.917, 0.22196, -1.0595, -2.06, -0.85299, 2.3489, 0.37467, -0.85978, 2.2792, -0.14256, -0.032569, 0.16729, 0.63102, 1.1588, 0.39506, -0.3992, 1.3756, ...
            -1.0914, 0.083695, -0.31302, -1.5305, -0.61882, -0.67848, -0.0084147, 1.1309, -0.0050702, -0.33001, -0.26506, -0.25976, -0.24948, 0.43396, -0.94424, 0.46027, 0.20514, -0.85648, -0.96328, 0.70484, ...
            0.7619, 0.34183, -0.0018082, -1.44, 0.95958, -0.78314, 0.58874, -0.0062754, -0.82075, -0.30063, 2.1801, 0.0042651, 0.2439, -0.41487, 0.45645, 0.078075, 0.70701, 0.20233, -1.9048, 2.5921 ];
        
        F(:,2) = [-1.3943, 1.4297, 1.5993, -2.1567, -2.0803, 0.90218, 0.9817, -0.5645, 0.5183, 1.3446, -0.063101, 1.2273, 0.4625, -0.45657, -0.46874, 1.4939, -0.13304, 1.7678, -0.15257, -1.659, ...
            0.36913, -0.60042, 0.58567, 0.16409, -1.8372, 1.229, -0.79431, -0.58284, 0.069858, -0.12098, -0.29109, 0.11516, 1.3987, 0.12303, 0.1417, -0.58276, -1.7119, 0.38754, 1.6656, -0.06417, ...
            1.0013, -0.75666, -0.42824, -0.8207, 0.24951, -0.038934, -2.1126, 0.40182, 1.0622, 1.9327, 1.5087, 0.61401, 1.7619, 1.054, 0.51649, -1.014, -1.0207, 1.5366, -1.7967, 0.065649, ...
            1.435, -1.3747, 1.7671, 0.34909, -1.3452, -0.039198, -0.049265, -2.1997, 0.13391, -2.0566, -2.2392, -0.014531, -1.4112, 2.2291, 0.68225, -0.70737, 0.86013, 0.38959, -2.3885, -0.63484, ...
            0.20339, 0.066422, 0.78895, 1.4467, 0.5239, 0.44802, 0.51867, 0.21554, -1.6209, 1.7009, 0.8339, -0.3481, -0.00059337, 1.3445, 1.549, 1.4695, 0.59977, -0.38987, -0.37272, 0.24302, ...
            -0.80464, -2.1652, 0.32245, 0.1999, -2.7335, -0.45022, 0.86267, -0.2203, -0.2046, 0.10974, -0.60665, 0.59069, 1.2326, -0.69173, 0.59579, 0.84204, 0.23146, 0.67933, 0.53891, 0.9343, ...
            1.2638, 0.99736, -1.4154, -0.089969, -2.6118, -0.29144, 2.2278, 0.33159, -0.09367, 1.6121, 1.4799, 0.33431, -0.64576, -1.4922, -0.44695, -0.024302, -0.078193, 0.66263, -0.19163, 1.8027, ...
            -0.076061, -0.5445, -0.31001, -0.83396, 0.88156, -1.7632, -0.50741, 0.24148, 0.15891, 1.5726, 0.54761, 2.4434, -1.4211, 0.00039321, 0.65038, -0.17174, 2.7174, -1.5261, 0.29295, 1.7712, ...
            0.56955, -0.76952, 0.65095, -2.6212, -0.59382, 0.91275, 0.25865, 0.0032358, -0.096088, 1.5207, 1.141, -0.82855, 1.0751, -3.1562, -0.89303, 0.60081, 0.72557, -0.50266, -0.55173, 1.2667, ...
            -0.12191, 0.90517, 0.9093, -0.45464, 0.099615, 0.014886, 0.32668, 1.6402, -2.2294, -0.82034, 0.42002, 0.21263, -0.54171, -0.16653, -0.25521, 2.4364, -0.41879, 0.28656, 0.21542, -0.98393, ...
            1.989, -0.58092, -0.30648, 0.26215, 2.0198, -0.47747, -0.32387, -1.0239, 0.056777, 1.7993, 0.58738, 0.014018, 0.7864, -0.51597, -1.3421, 1.0646, -0.39839, 1.8026, -1.6555, -0.95185, ...
            -2.9361, 0.72524, -0.78808, -0.76646, 3.2168, 0.68829, 1.2965, 0.60008, 0.80299, -0.38911, -0.18105, 0.66468, 1.52, 0.50592, 0.35077, 0.14507, 0.71646, 0.26641, -0.75443, 1.455, ...
            -0.38756, -2.1847, 2.4176, 2.7582, 0.33913, -0.1061, -0.49703, -0.25205, 2.0031, -2.5002, 0.81615, 0.54686, 0.14114, 1.0675, 0.00076762, -1.3134, -0.74309, -0.93258, -0.24234, -1.84, ...
            -0.51475, 0.75861, -0.15177, -1.3302, 1.0437, 0.7247, -1.256, -2.1812, -1.0383, -1.0488, 1.5193, 1.1861, 0.76947, -0.4251, -0.81139, -1.3236, 0.60832, 0.84902, 1.1949, -0.47963, ...
            -0.86245, -0.015044, -0.93697, 0.56015, -0.10879, -0.075928, -0.44137, -2.9582, 0.39715, -1.5249, 1.7531, 0.89668, -0.12951, 0.72995, -0.58664, 0.65749, -0.3044, 1.8543, -1.1348, -0.3759, ...
            -1.058, 0.26206, 0.92609, 0.52165, 2.5804, 0.10004, 0.76028, -0.030156, 2.6375, -1.5129, -0.6258, -0.92224, 0.52253, 0.40899, -0.78547, 0.50965, 1.0148, -0.5291, -0.55025, -0.39346, ...
            0.13433, -0.0094846, 0.42989, -1.2911, 0.94031, -0.3964, 0.8229, -1.4523, -0.7034, -1.5303, -0.11791, 0.21469, 1.7754, -1.8684, -0.8429, -1.1638, -0.8644, 0.38348, 1.3707, 0.013549, ...
            -1.1195, 0.41977, 2.0828, 0.72981, 0.61709, 0.56069, -0.55829, 1.4682, 1.6602, -0.25172, -0.86557, 0.92209, 0.092635, -1.0762, -0.84416, -1.3349, 0.87818, -0.99018, -0.55002, -1.1618, ...
            1.3813, -0.13559, -0.64028, 0.97714, 0.25536, 0.67, 0.65818, 0.76781, -1.3732, -0.061982, 1.2229, 0.03795, 0.089793, -2.4886, 0.76516, -0.54698, -1.4839, -1.3527, 0.60402, 1.0864, ...
            -0.053369, -0.07304, 1.0141, -1.2059, -1.8647, 0.10974, 1.2513, -0.025776, 2.0374, 2.1827, 0.64548, 1.3767, -1.0537, -0.44327, 0.50566, -0.71284, -1.4408, 0.78209, -1.4575, -0.078906, ...
            0.88736, -0.42732, 0.69423, 0.80376, 0.45099, -0.055873, -0.24394, -0.96518, -0.9556, 1.1455, 1.2914, -1.7138, 1.6506, 1.6097, 1.2579, -0.29577, 1.1777, 0.22601, 0.50457, -0.04989, ...
            -0.18672, -0.56454, 0.59836, -0.24995, -0.98476, 0.9398, 0.93802, -0.18347, 0.13474, -0.62417, -1.224, 1.3234, -1.1572, -1.9934, 0.55248, -0.97119, -2.8948, 0.37954, 0.57034, 0.43173, ...
            -0.083073, 1.2653, -0.65141, -0.53899, -1.2216, -0.18848, 0.29508, 0.013539, 0.2743, 0.37727, 0.3412, -1.1543, 1.2022, 1.3293, -0.1489, -1.7976, 0.64276, -1.8003, -1.2374, 0.92377, ...
            -1.2001, -1.4415, 0.91114, 0.57525, 1.6141, 0.32225, -1.5418, 1.1169, -0.0036669, 0.20029, 2.1873, -1.1179, 0.97547, -0.8844, 1.448, -0.98878, 2.1799, 1.9468, 0.98993, -1.0314, ...
            -0.5947, -1.6512, -0.0027868, 0.79998, 1.4655, 2.0879, 1.1144, 0.00086509, -0.6705, -1.7845, 1.0866, -5.6111e-05, -0.90585, -1.407, -0.60658, -0.41903, 1.0582, -0.49584, -0.69113, -0.11106 ];
        
        F(:,3) = [-0.48034, 0.62176, 0.56851, -0.4637, -0.62767, -1.9529, 0.32776, 0.38865, 1.2961, -0.72461, -0.45632, 0.95494, -0.19763, -1.3853, 0.089759, 2.3101, -0.14105, 0.18011, 0.39586, 0.4747, ...
            0.90176, -0.23465, 0.084182, -0.46366, 0.44457, -0.29481, -0.51849, 0.44074, -0.39258, -0.38358, 0.1315, 0.66966, -0.97644, 0.53845, -1.3906, 1.4308, -1.032, 0.22723, -0.43268, 0.39812, ...
            -0.91223, 0.86122, 1.612, 1.039, 0.33625, -0.40977, -1.0109, -0.026748, -0.63267, -0.20619, 0.4798, 0.15498, 0.52917, -1.9379, -0.14847, -0.38192, -0.48691, -1.2843, 0.10576, 0.48177, ...
            0.21112, 1.3726, -0.73325, 1.4242, -2.0487, 0.38565, 0.47755, -0.16979, 1.1343, 0.12623, -1.5383, -0.52245, 1.4206, 0.65719, 0.53761, -0.17633, 0.37956, 0.5231, 0.01722, -0.32496, ...
            0.50993, 0.56323, -1.0665, 1.2088, 0.64236, -0.52848, -0.966, 0.52343, 0.49973, 0.64116, -0.36082, 0.91724, -0.0046655, -0.022579, 1.2824, -0.34257, 0.44596, -0.65801, 0.29085, -0.82662, ...
            0.12235, -0.11602, -0.11773, 0.60769, 1.1835, -1.5394, 0.26695, 0.69601, -2.1234, 0.42384, 0.60449, 0.44685, 0.87389, 1.5886, -0.0745, -0.38087, 1.6026, -2.3732, -2.509, 1.5619, ...
            -2.2811, 2.9234, -0.13492, 1.7588, -0.83297, 0.04674, -1.5556, -0.083783, -0.41062, 0.07982, 2.7625, -1.4833, 3.1126, 2.8378, 1.5195, 1.4025, 0.90282, -0.24471, -1.8682, -1.9245, ...
            -0.86798, 0.32483, -0.29644, -0.58764, 0.65276, 0.73176, -0.26747, 0.211, -0.38965, 0.39161, 1.6465, 2.1133, 0.71745, 0.25626, -0.73942, -1.0121, 0.19701, -1.4215, -0.57853, -0.89052, ...
            -0.84037, -0.69898, 0.2176, -0.10127, -0.039931, 1.5237, 1.5455, -0.0046649, 0.36799, -0.41874, 2.19, -0.17976, -1.9297, 1.4894, 1.0226, 0.50505, -2.8242, 0.86182, 0.78393, -0.32847, ...
            0.99896, 0.047664, 0.53584, -0.63817, 0.45236, 0.86192, 1.7291, -0.81738, -0.12162, -0.22821, 0.81124, -0.12961, 0.2305, 2.0547, -0.041187, 0.11558, -0.80498, 0.64216, -1.9791, 0.12241, ...
            -2.2439, 1.1676, 1.0691, 0.31008, -0.67553, 0.50217, 0.79281, -0.66776, 1.3643, 2.6243, 0.64808, -0.72536, -0.41911, -3.1288, 0.51036, 0.36419, -1.6803, -2.19, -1.4003, -0.051485, ...
            1.9245, 1.0487, 0.13321, -1.3224, 1.1777, -1.0224, -0.26464, 0.92127, -0.070799, -0.90816, -0.11479, 0.017416, -0.31835, 1.0573, 0.68095, 2.8843, 0.46489, -0.022194, 0.089708, -1.1885, ...
            -1.2438, -0.56191, -0.061455, 0.038477, 0.34807, 0.83716, -1.4293, 0.64724, -0.6575, -0.6386, 0.33539, -0.96555, -0.65028, 0.90429, -0.0048433, -0.36008, -2.1256, -0.32005, -2.4004, 0.14823, ...
            -1.7871, -0.77982, -1.0791, 1.1699, 2.3437, -1.6805, -2.3788, 0.76814, 0.60132, 0.62131, 0.91471, -2.5713, 1.4223, 0.33862, 0.47697, -0.39133, -0.49901, -0.20337, -0.033527, 2.3767, ...
            0.41226, 0.72294, -1.3333, 1.0732, 0.85845, -1.821, -0.47374, 0.99809, 1.0074, -2.7958, -0.49295, -0.29145, 1.0022, -0.23174, 0.90828, -0.79343, 0.27564, 0.65287, -1.5691, 1.0086, ...
            -1.9296, -0.80225, -0.16374, 0.46616, -0.44049, 2.4613, -0.49748, 2.1302, 0.74021, 1.1819, -0.41804, 1.3015, 0.99712, -0.9563, -0.84507, -0.60089, -0.12352, 0.44951, 0.96596, 1.5429, ...
            -1.3683, -1.2336, 0.60325, -0.14729, -1.2136, -1.1121, 2.1681, 0.39839, -2.756, 1.3344, -2.1368, -0.22965, 2.2856, -0.20476, -0.043647, 0.3727, -0.0194, -0.12759, 0.16555, 1.1098, ...
            -0.90269, -0.92965, -1.9341, -1.2979, 0.012224, -1.1016, 2.5657, -1.4, 1.8657, 1.0386, 0.93497, 0.48646, -0.55731, -1.3519, 1.1924, 1.5232, 1.3126, -0.92962, -0.74553, 0.017323, ...
            -0.56783, 1.52, -0.97744, 0.83424, 1.6081, -0.39752, -0.52706, -0.15678, 0.50891, 0.94235, 1.2594, 1.0148, -0.69965, 0.93003, 1.0077, -1.5414, -0.0069293, 2.5009, -0.95315, -2.6648, ...
            -1.3943, 1.3462, -1.0596, 1.3579, -0.49469, 1.1592, 0.94723, 1.8323, -0.55912, -0.34878, -2.3115, 0.017503, -0.6452, -0.062046, 1.1122, 0.78489, 0.39753, -0.23506, -0.52116, 0.29155, ...
            0.37013, 0.73818, -1.161, 0.25313, -1.5175, -1.7772, 1.8919, 1.0935, 0.27308, 0.99033, 0.66768, -1.0165, -2.2399, 0.95912, 0.40585, -0.091787, 1.0336, -0.26132, 1.4516, -1.8492, ...
            1.528, -1.2575, -0.60783, 1.268, -1.4282, 0.92274, 0.087781, -1.7582, -1.3851, 1.3918, 1.3483, -0.7786, 0.84395, 0.96482, 0.87671, 1.2365, 0.97232, -0.79147, 0.57129, -1.7989, ...
            1.4034, -1.61, -1.1787, -0.95015, 1.0436, 0.029627, -0.44664, -0.90538, 1.8697, -1.5491, -0.66925, -1.5287, 1.0924, 1.0695, 1.716, -0.73962, -1.8564, -0.99286, -1.318, 0.49937, ...
            0.55429, 0.95306, -1.4459, -0.56611, -0.081593, -1.291, 1.5163, -0.58477, 0.0014435, 0.23364, 1.8608, -1.4354, 0.72997, -0.8006, -0.061668, 0.83008, -0.27249, -1.4352, 1.261, -0.18244, ...
            1.4821, -1.3024, 0.0054955, -2.3707, 0.39303, 1.4411, -0.15175, -0.0010525, 2.0779, 0.12023, -2.182, -0.0048003, 0.51288, -0.12131, 2.2872, 1.8211, -1.257, -1.7088, 0.38492, -0.60217 ];
        
    case 'Gauss1000'
        F(:,1) = [0.48929, -0.39145, -1.7491, 0.41528, 0.22344, 0.64188, -1.849, 0.27664, -1.2809, -0.872, 0.94103, 0.48933, 0.50981, -0.82597, 1.2377, -1.2731, 1.2964, -1.0103, 2.386, 0.34646, ...
            -0.98605, -0.22386, 0.85958, 0.039294, 0.40574, 0.74881, 0.25319, 0.87904, 0.10438, 1.3101, -0.71041, 2.5136, 0.35541, 0.98026, -2.0285, -1.7906, 0.11101, 0.23184, -1.8355, 0.91598, ...
            0.082215, 0.1312, 0.17354, 1.2514, -1.8696, 1.0685, -0.7804, -1.7513, 0.064759, 1.5212, -2.5183, -1.1321, 0.24285, 0.041215, -1.0154, -2.1037, 0.79856, 0.16238, -1.1782, 0.32593, ...
            1.4128, 0.35955, -1.4465, -0.31057, -1.2048, -0.67475, -0.23639, -1.0648, -1.2845, 1.4132, -2.7513, -3.1506, 1.5464, 1.1438, 1.4034, -1.5618, 0.51946, -2.63, 1.1859, -2.0676, ...
            0.15692, 0.71284, 0.14006, 0.88031, 1.2628, -1.7264, -0.66833, -0.056281, -0.955, 1.2253, 0.59294, -1.6668, 0.79497, 1.2956, 1.5582, -2.128, -0.7278, 2.47, -0.68313, 0.517, ...
            -2.9263, -2.7844, 1.3001, -0.58498, -0.41134, 0.86356, 2.1154, 1.0887, -1.5991, -0.3937, 0.8557, 0.5614, -0.95962, 0.78862, -2.7441, 1.7905, 1.1501, 0.43902, 0.15205, -0.054352, ...
            0.65996, -2.0611, -0.54425, -0.7419, -0.50247, -0.5721, 0.79281, 0.16571, -0.37504, -1.3478, 0.050325, 0.32637, 0.16011, 0.13931, 1.2869, 0.19382, -1.759, -1.2611, 1.2115, -0.36826, ...
            1.4047, -1.0708, -0.34734, -1.7339, -0.37573, 0.92153, 0.45206, 0.18396, -1.3032, 0.8635, -0.26685, -1.2167, -0.84402, -0.14214, -0.88352, -3.2561, 0.082585, 0.93666, -0.044393, 0.12891, ...
            -0.73908, 0.49659, 1.3586, 2.4684, 0.86996, -1.0926, 0.32041, -0.86509, 2.1299, -1.8029, -3.5542, -0.55868, 0.044833, -0.27218, -0.32061, 0.18963, -1.2818, 0.73238, 0.10337, 1.1629, ...
            -0.74357, -0.56491, -1.1281, 2.2426, -1.2975, 1.435, -0.97052, -0.19732, 0.26887, 0.2581, 0.28913, 0.17798, 0.082277, -2.7057, 0.23946, -0.1781, -0.11091, -0.92437, -2.5878, 0.24746, ...
            0.036536, -0.9572, -1.4409, -2.8644, 0.33758, -1.17, 2.9572, -2.2655, 2.0991, 0.66275, -0.73474, 0.28025, -0.22507, 0.25575, -1.2963, -1.9203, 0.006037, -2.8054, 0.96579, 1.8013, ...
            -2.7598, -0.85512, -0.068196, -1.2109, 0.58672, -0.98729, 1.1165, -1.6631, 0.13908, -0.30321, 2.5453, 1.532, -1.4305, -0.13052, -0.38198, 0.28733, 0.4102, 0.26287, 0.43882, -1.1672, ...
            -0.31234, -0.51139, 0.29255, 0.59977, -1.2638, 1.0866, -0.1237, -0.11469, 1.7277, -0.71051, -0.39019, -1.2581, -0.34817, 0.19466, -1.3069, -0.71508, -1.7591, 0.67339, -0.73214, -2.725, ...
            -1.3007, 0.21934, -1.2571, -0.94234, -0.43088, -1.6371, -1.0474, -0.013019, 0.2922, -3.4985, 0.17734, 0.0061714, 0.27946, -1.0022, -0.38042, -1.8966, -0.48161, 0.27272, -0.24689, 2.0668, ...
            0.37713, -1.0397, -0.35139, 0.30533, 1.7951, -0.46651, 0.88617, 0.76678, 0.23911, 1.366, 1.6415, 0.37571, -0.89674, 0.22926, -1.1215, 0.7887, 0.21926, 0.27724, -0.036724, 0.5836, ...
            -0.031899, 1.5621, 0.077609, 0.54762, -0.33038, 2.0164, 0.23664, 0.7885, 0.058136, 1.5211, -0.23867, -0.67627, 1.0582, -1.1672, -0.24075, 2.3445, 0.4967, -0.19427, -2.9776, 3.4473, ...
            -0.84834, 0.74366, -0.34309, -1.708, 0.47523, -0.39118, -0.15828, -0.43443, -0.32289, 0.022514, 1.5646, 1.0663, 0.048457, 0.4233, 0.73857, 0.10552, -0.11566, 0.51873, -0.3739, -0.30579, ...
            -1.6555, 0.33465, -0.12541, 1.724, -0.12909, -1.5687, -1.6286, -0.073459, 1.0977, -1.2735, -0.30079, -0.28483, -0.81173, 0.51012, 1.4845, -0.58455, -1.8504, -1.673, 0.8025, 0.092237, ...
            0.36989, 0.38341, -0.35623, 1.7982, 0.59536, -1.1545, -0.033271, -0.092615, 0.37893, -1.6222, -0.082425, -1.5901, -2.2743, -0.74745, 0.32109, -1.1868, -0.16809, -0.47266, -0.34941, 0.090388, ...
            1.4659, 0.54203, 1.1182, -0.84404, 0.87617, -1.6341, -0.75495, 0.27134, -0.37461, 0.3754, 2.8396, 2.2149, 2.0505, 0.59266, -0.50486, 0.29961, -0.53347, -0.51317, 0.37753, -2.6317, ...
            -0.19561, -1.2515, -0.82226, 0.34215, 0.64201, -1.1162, -0.41466, 0.70037, 0.035462, 0.12017, -1.7679, -1.0213, 0.3486, 0.67826, -1.8105, 0.030431, -0.73668, -0.16091, -0.34868, -0.26706, ...
            0.048747, 1.1871, 1.6671, 2.1777, -0.15046, -0.26854, -0.91009, -0.16259, -0.49634, 1.1522, -1.1528, -0.64127, 1.3998, -0.039608, -0.63032, -0.76876, 1.3866, 1.8453, 1.356, -1.5397, ...
            1.7506, 0.15515, -0.85083, -0.35068, 1.2163, 0.064734, -0.83544, -0.21707, 0.52481, 2.1486, 0.90213, -0.81809, 0.8031, -0.020992, 0.45914, -1.3744, -0.69054, -0.67893, 0.81825, -0.69622, ...
            -0.85021, -0.49541, 0.31449, -1.7843, 0.22269, 0.092957, 0.12759, 0.39235, -0.28555, 0.067813, 1.0754, 2.0344, -0.47514, -1.8693, 0.88314, 1.7785, 0.3042, 0.41052, -1.0369, 0.48148, ...
            -0.32679, 0.29325, -2.0004, -0.095238, 0.82457, -0.8729, -1.2944, 1.1081, 0.057866, 0.75387, 1.1434, -0.88656, 0.42319, -0.37297, 2.2492, -0.7234, -0.26041, 0.84523, -1.4511, 0.61981, ...
            -1.0713, -1.7109, -2.6351, 0.92743, -0.32884, -2.2494, 0.064693, -0.96321, 1.2645, -1.0634, 0.99764, 0.51096, -2.3718, -1.0609, 0.86393, 0.10949, 0.55805, 1.5727, 0.77153, 2.3806, ...
            -1.2876, 1.7978, -0.55003, 0.74463, 0.38926, 1.6328, 1.0074, 0.76172, 1.5754, -0.26518, 0.96784, 0.74512, -0.77777, 0.019069, -0.34399, 0.29829, 0.66308, -0.33115, 0.31635, -0.87473, ...
            1.8277, -0.79778, 0.70786, 0.2934, 0.92177, 0.094154, 0.32405, 0.53301, 0.33179, -0.21892, 0.10838, 0.53942, -2.2951, -1.4895, -0.28068, -0.90682, -0.44775, 1.0249, 0.3829, 1.1039, ...
            0.3239, -2.2469, 0.8164, -1.8557, 0.36872, 0.49546, 0.69805, -0.059408, 2.0113, -0.2187, 1.442, -0.23926, 1.9602, 0.49667, -0.17057, 0.78379, -0.47632, -0.67089, -0.37922, -1.2641, ...
            0.75124, 0.2456, -0.42958, -1.4822, 0.0098059, 0.36256, 3.4192, 2.278, -0.28923, 1.6002, -1.6167, -1.3954, -2.5956, -1.8997, -1.914, 1.0604, -1.4501, -1.12, 0.037921, -0.12539, ...
            -1.8686, -0.86068, 0.70268, 0.6921, 1.7709, -1.0843, -1.8207, -1.4691, 0.92422, -0.39357, -0.13966, -0.55851, -0.22096, -1.2793, -1.1752, 0.57099, 1.3845, 0.46139, -0.25037, 0.51413, ...
            1.2759, 0.5559, 0.11798, 0.54251, -0.3858, -1.1279, 0.78197, -0.78658, -1.9057, 0.23479, -0.20681, -1.3706, -1.3817, -0.4989, -1.0666, 0.40619, -1.1829, -0.45209, -0.18492, 0.35341, ...
            0.41914, 0.71887, 0.70858, -1.1416, -0.65035, -0.67875, 1.0324, -0.56662, -1.6667, -0.85473, -0.8823, 0.029308, -2.0419, -0.49842, -0.48629, 0.58701, -0.93604, 0.77166, -0.7694, 0.12981, ...
            1.3043, -0.048301, -0.75858, 0.9527, 0.98986, -2.126, -2.2342, -0.76922, 0.10127, -0.81012, -0.44714, -0.40864, -0.59832, 0.81005, -1.0813, 0.8758, -1.5384, 0.81643, 0.98387, -0.53146, ...
            0.6111, 1.7106, 0.60475, 2.6382, -0.30705, 2.2079, -1.3557, 1.0944, -0.52728, 0.24368, -0.35675, -0.39639, 1.616, -0.5881, 0.23094, 1.1571, 1.9264, -1.4547, -0.17335, 2.5914, ...
            -0.96705, -0.71137, -0.049474, -0.59548, 0.25725, -0.77483, -1.2727, 1.5951, -0.33073, 0.57049, 0.020467, 0.98355, -2.1939, -1.6219, -0.2772, -1.5616, -0.41571, -0.11741, -1.5413, 0.67953, ...
            0.51098, 1.1945, 0.82192, -1.0421, 0.024663, 1.2953, -1.1127, 0.81247, -1.9114, -1.0703, 1.1366, -0.35987, -0.92409, -0.19407, -1.7421, 1.0794, -0.55441, 0.5907, 0.17306, 0.67073, ...
            0.29007, -0.37742, 0.57258, -2.7599, 1.0339, 2.3473, 0.25743, -1.7821, 1.046, -0.49004, -0.86881, -0.28963, -1.6807, 0.24243, -1.572, 0.87939, -1.3205, 1.6978, 2.4438, 0.64662, ...
            0.030156, 1.7645, -1.6673, 0.17698, -1.4498, 1.0251, 0.10302, -1.5758, -0.17523, 0.46286, -1.6058, -1.9523, -0.0028886, 0.61566, 1.2069, 0.68539, -2.4204, -2.2416, -1.6575, -0.95152, ...
            0.83323, 0.29228, 1.3524, -0.97213, -0.62298, -0.38218, 0.032442, -0.016938, 2.1263, 0.95842, -0.0076153, -0.65331, -0.49992, 0.96686, 1.7885, -0.81156, -0.41444, 1.2407, 0.78681, -0.51763, ...
            0.013677, 0.25991, 0.18623, -0.47641, 0.11703, -0.62355, 1.2, 0.90036, -0.68295, -0.68649, 1.6156, 1.1164, 0.067335, -1.2519, -0.77811, 1.1635, -0.28339, 0.98797, 0.32214, 0.083533, ...
            0.52982, -0.44278, 0.80068, -0.66298, 0.98405, -0.22888, 0.39516, -1.575, 0.35232, -0.40061, -1.8447, -0.40992, -1.4354, -0.66766, -0.83195, 0.10045, 1.814, 0.56257, -1.4124, 0.56455, ...
            0.74453, 0.5693, 0.63783, 0.80903, -0.75856, 0.14883, 0.94514, -0.81487, -0.49192, -0.81099, 1.0356, 0.86964, -0.67249, -1.7694, 1.6351, 0.0017309, 1.1795, -1.3193, -0.25019, 2.0353, ...
            -1.5058, -0.29344, 0.51107, 0.38168, -2.1777, -1.8725, -1.3472, 1.0437, 0.83464, -0.83549, -1.6153, -1.2977, -1.1443, -1.2973, 0.5192, 0.15545, 0.14119, -1.0639, 0.29572, -1.6296, ...
            0.26639, 0.17633, -0.84838, 1.5199, 1.0636, 1.3189, 0.26683, -2.2205, -0.74714, 0.99936, -1.6375, -0.84237, 0.32066, 0.85932, -0.70949, -1.6449, 1.6415, 0.44839, 0.30304, -0.58395, ...
            0.85704, 1.2059, 0.99262, 1.2945, 0.56696, -0.944, -1.7228, 1.1779, -1.0234, 0.8229, -0.97787, 0.71174, 1.2501, -1.545, 1.2433, -0.82853, -1.8753, 1.3474, -0.69266, -1.7005, ...
            0.66536, 1.2744, 0.62874, 1.3141, 1.0273, -0.47771, -0.92757, 0.76368, 1.4188, 0.53419, -1.7643, 1.3635, 0.32273, 1.1519, 1.2625, 1.1738, -0.36944, 0.18915, -0.96572, 0.83788, ...
            0.95331, -0.011521, 1.2948, -1.1833, 0.95522, -1.0615, 0.27581, -0.16089, 1.4197, -2.3108, 0.06685, -1.159, -0.37378, -0.45237, -0.04944, 1.4777, -0.67055, 1.3118, -0.61715, 3.0722, ...
            0.46614, 0.93888, 2.2858, 0.18929, -1.433, -0.14912, 1.8718, -0.83876, -0.32489, 1.2431, -0.11701, -0.49004, 0.028432, 0.31362, 0.90343, 1.6758, -0.25971, 0.97006, -0.66148, 1.0197, ...
            -0.56067, 0.98495, -1.4343, 1.0229, -0.11765, -0.5181, -1.1962, -0.28144, 0.45514, -0.78445, -0.59254, -2.0656, -1.0271, 0.094102, 0.84438, 0.66015, -0.048798, 0.057238, -0.79317, -2.0037 ];
        
        F(:,2) = [1.2609, 2.0004, -1.1494, -0.79024, -0.45842, -0.038711, -0.86764, 0.23407, -0.34326, -1.8129, 0.2539, -0.99444, 0.79447, -1.9631, 1.6631, -0.29407, -0.10138, -1.7365, -1.0161, 0.86076, ...
            1.1896, -0.072916, -0.12355, -1.392, 2.0808, 1.8887, -0.38665, -0.31133, 0.63255, 0.055154, -0.0015976, 0.69056, -1.4882, -0.95458, 0.95031, 0.60056, 1.5302, 1.4872, -0.79365, -0.97428, ...
            -0.044207, 1.479, -0.47656, -0.3465, -0.87197, -0.037884, -0.48751, 1.0935, 0.95552, 0.045175, 0.039628, 0.99312, 1.701, -0.058669, -0.95021, -0.17934, 0.44666, -2.736, 0.45479, -1.5521, ...
            -0.50881, 1.0059, 0.59096, -0.93298, -0.39931, -1.4722, -0.02856, -0.91025, 0.63436, -0.0033689, 1.9476, 1.49, 2.0307, -0.12706, -0.6465, -1.8836, -1.2241, -0.21479, 1.4425, 0.028059, ...
            -0.69871, 0.39313, 1.8192, 1.4266, 1.1474, 0.2356, -0.81666, 1.2607, -1.3846, -0.7015, 0.10265, 0.44665, 1.5684, 0.15969, -0.21786, 0.96275, -1.2253, 2.0102, -1.52, -0.49451, ...
            0.073002, -2.1306, -0.32454, 1.1641, 2.2602, 1.2835, -0.99373, 1.0962, 1.2343, 0.14015, 0.67528, 0.1885, 0.15069, -0.098499, -0.16228, 0.43759, -1.2966, -1.9994, 0.85259, -0.75484, ...
            -0.67637, -1.0694, -0.18704, -2.4741, 1.7334, 1.6031, -0.48343, 1.6921, -1.5481, 1.1088, 1.2033, -0.071295, 0.49725, 0.82251, 1.0402, 1.5437, -0.33562, 1.0692, -1.8786, -0.026596, ...
            0.0040394, -1.034, 0.59503, -0.37165, 0.060847, 0.66606, -0.90331, -0.86493, -0.21425, -0.41535, -1.8331, -1.3913, 0.55812, 0.054483, 0.073586, 1.0893, 0.37584, 0.57692, 0.88955, -1.2458, ...
            1.4667, 0.058205, -0.5964, -1.2354, -2.0373, 1.125, -1.5688, -0.7847, -0.00097435, -1.1721, -0.5994, 0.66045, 1.5899, 0.027369, -0.28514, -0.051395, -1.5636, -1.9203, 0.02868, -0.0053388, ...
            1.3908, -0.58981, 2.255, 1.5718, -1.5619, -0.064234, 0.74886, -0.70196, 1.6448, -0.61592, -0.67004, -0.075873, 0.75836, 0.41532, 0.47526, -0.1945, -0.83263, -1.3926, -0.24907, -0.62391, ...
            -0.69451, -1.545, -0.88591, -0.038607, 0.11389, 2.3706, -0.61885, -1.0451, 0.29779, -1.8808, 1.2676, -0.052383, -1.4737, 0.7241, 0.71506, 0.065742, 0.00021107, -1.7167, -0.56888, 1.6325, ...
            0.43513, -0.78887, -0.94722, 0.84478, -0.79427, 0.49131, 0.51134, -1.142, 0.22524, -0.26738, -1.5081, -0.85561, -0.74073, -0.70878, -0.082594, -1.5444, -0.011773, 0.12229, 0.31513, -0.90309, ...
            -2.4436, 0.59551, -1.3658, 1.4538, -1.3152, 0.088133, -0.16939, -1.7149, 2.088, -0.78113, -0.712, -0.75496, -0.66924, 0.70732, 0.97372, -1.4428, 0.57785, -0.2607, -0.78933, -0.82503, ...
            0.53774, 0.21606, -0.16629, -0.95555, -0.17601, 0.3333, 1.2542, 1.7024, -0.7623, -0.67331, 0.78902, 0.00079679, -0.27549, -0.58085, -0.061316, -2.8671, -0.428, 0.70432, 0.3704, 0.41423, ...
            -0.86827, 1.0953, -1.2171, 1.5201, -2.269, 1.7994, -0.64417, 1.316, -2.4861, -1.2057, 0.5245, 1.9137, 0.17577, 0.50394, -0.91223, -0.58128, -0.77418, -0.0054514, 0.57795, -0.15969, ...
            -2.1139, -0.14239, 0.57567, -0.57396, -1.709, 0.0030632, -0.004096, -0.36977, -0.58373, -0.6696, 0.18051, 0.47374, 0.49057, 2.8689, -1.206, 1.1322, -0.53573, -0.061184, -1.3487, 1.0393, ...
            -2.1939, 0.34541, -1.057, -0.44251, -0.64708, 0.51074, 0.011566, 0.56766, -1.3738, 0.82986, 1.2059, 0.52448, 0.84124, 0.2425, 0.22947, -0.0070005, -0.030778, -0.57357, -0.2085, -0.34333, ...
            0.56698, 0.04312, -0.46753, -0.1886, 0.80727, -0.41091, -0.6313, 0.4883, 1.0983, -1.364, -0.79906, 0.6213, 1.3179, -0.59617, 1.4618, 0.8368, -0.64033, -1.3046, -0.49046, -0.27553, ...
            0.78751, 2.5547, -0.39779, 0.63122, 0.47497, -0.63518, -0.5356, 0.41379, 0.13864, -0.53737, -0.18625, 0.37697, 0.2188, -0.8335, 0.14462, -0.18881, -2.0978, 0.86635, 0.097905, 0.23528, ...
            0.22965, -0.16673, -0.030343, -2.399, -1.0395, 0.67052, 0.44985, 1.6878, -0.14084, 0.74428, 0.29826, -0.49445, -2.175, 0.069763, 0.0051556, 0.0027198, 2.4419, 0.77999, -1.8009, -1.1477, ...
            0.52848, -0.41121, 0.042405, -0.53345, -0.14615, 1.0433, 1.78, 0.63615, 1.6873, 0.40142, -0.92537, 1.5139, -0.76126, -0.40216, -1.3358, 0.12891, 0.0088123, 0.67161, -0.86859, 0.097866, ...
            -1.9643, -1.2929, -1.2028, 1.6704, 0.83787, 0.7908, 0.14259, 0.60322, 0.55253, 0.84813, -0.11041, 0.3851, 0.18483, -0.57123, -0.68674, 1.4808, -0.085472, -0.65937, 1.9069, 0.31647, ...
            -1.426, -0.0080593, -1.5601, -0.60826, -0.67494, 0.73948, 0.30111, -0.70576, -1.3538, -0.79404, 0.24339, 1.352, -0.69676, 0.76825, -0.46529, -0.36163, 0.070858, 0.49223, -0.73066, 1.912, ...
            -0.15598, -0.10301, 2.3027, -1.3008, 1.3768, 2.685, -0.86825, 0.77302, -0.11235, 0.20528, -0.017484, -0.11428, 1.0693, -0.31036, -2.8823, -0.57969, -1.2586, 2.9864, 0.22747, 1.8462, ...
            -0.84648, 0.20391, -0.26822, -0.019761, -0.13437, -0.15619, 0.5381, -1.9388, -0.35657, -0.59335, 0.58306, -0.37604, 0.041937, 0.70921, -0.30709, -1.378, -0.17013, 0.023866, 0.56265, 1.1289, ...
            -2.1727, 1.2935, -0.77456, -1.1173, -0.25266, 0.34372, 0.6711, 0.66091, 0.043185, 1.8062, 0.65495, 0.79409, -1.0382, -1.1417, -0.13111, -0.88351, 1.3016, -1.4206, 1.1062, -0.75694, ...
            -0.33288, -0.35487, -0.5623, -1.3925, -0.050057, -0.066658, -1.3013, -0.24043, -1.4369, -0.099814, 0.30495, -0.068659, -0.24012, 1.6305, 1.7879, -0.0041763, 0.62283, 0.5851, 2.0823, 1.5123, ...
            0.41687, 0.037549, 0.050356, 1.6539, -1.5298, -1.12, -0.66406, 0.71894, 0.068637, 0.63909, 0.34534, -0.094636, -1.1531, 1.7379, -1.0069, -2.0147, 1.7929, -1.4808, 0.060121, 0.5017, ...
            -0.75581, 0.89631, 1.7881, -2.0066, 1.4065, -0.50046, -0.52246, -0.85731, 0.79592, -0.95522, -0.6704, 0.44082, -0.14946, -0.35075, -2.0509, -0.14939, -0.45657, 0.64914, 1.1482, -0.86311, ...
            -0.70756, -0.90938, 0.85882, -2.7386, -0.9611, 0.88204, -1.0989, 0.14287, -0.12113, -1.4288, 2.2368, -1.2534, 1.5159, 0.014232, -0.37639, 1.3323, 0.15875, -0.46337, -0.67037, 0.92632, ...
            -1.0887, -1.958, -0.34987, -0.29831, -1.1752, -0.49018, 1.6224, -0.6891, 0.66416, 1.886, 1.6851, 2.6626, 1.1522, -0.43185, 0.51272, -0.86314, -0.70592, -0.0027674, -0.60942, 0.78055, ...
            -1.3494, 0.088743, -0.38986, 0.07865, 1.9066, 0.53836, 1.0567, -2.5632, 0.79593, -0.9683, 1.3123, -1.7954, 2.4222, -0.36458, -0.16784, -0.86573, 0.56537, 1.1543, -0.54958, -0.79518, ...
            -1.3412, 1.6183, 0.40988, 2.1219, -0.566, 1.5265, 1.6542, -1.8361, 0.40824, 0.45678, 0.36479, 1.0243, 0.92961, 0.68334, -1.7243, 0.82826, -0.24542, -0.42192, 1.7162, -1.0984, ...
            -0.17965, 0.017245, -0.015013, 0.24118, -0.88336, -0.77047, 1.1822, 1.1484, -0.79632, -0.48915, 0.0277, 2.2252, -0.33641, -1.4438, -2.5448, 0.15708, 1.8717, 1.2452, 0.0017311, 0.73045, ...
            0.58213, -0.20466, -0.5791, -0.094564, -0.86835, 0.64469, -2.4491, -0.45924, -1.0769, 0.39347, 1.0786, -0.058435, -1.2145, -0.11967, -0.51635, 1.4018, -0.74335, -0.53624, 1.1566, 0.39903, ...
            -0.41648, -2.0459, -1.8232, -1.1484, -2.6113, -0.87451, 1.0594, 1.2492, 1.4833, 2.6174, 0.85242, 1.174, 0.38428, 1.4954, -1.154, -0.48558, -0.12908, -0.83265, 1.3472, -0.94125, ...
            -1.0396, 1.6936, -1.7185, -0.97247, -1.0495, 0.72024, -1.5049, 0.84509, -0.41663, -0.95988, -0.75458, -0.051227, 0.40793, -1.0792, 1.3168, -2.261, -0.12876, -1.368, -1.0999, -1.368, ...
            0.57328, -0.88303, 0.44027, 0.23435, 0.74595, 0.41749, 0.47504, 1.4436, -0.43198, -0.49196, 0.95197, -1.8087, 0.12645, 2.1, -0.69849, -1.3194, 0.36675, 0.81439, -1.5442, -0.53399, ...
            1.254, 0.063816, 0.55218, -1.2865, -1.3449, 0.21271, -0.042613, 1.7063, 0.33257, 1.4719, 2.2483, -0.87679, 1.2218, -0.93045, -0.50102, 1.1543, -0.076784, -0.53718, -1.5021, 0.6072, ...
            0.4511, -1.0213, -1.1352, -0.16263, -1.1146, -0.7805, -0.60106, 0.51786, 0.95282, 0.26954, -0.85315, -0.46707, 2.3857, -0.49381, -1.4349, 1.5264, -2.9426, 0.081314, -0.98969, 0.44884, ...
            1.6318, 1.1347, 1.3191, 1.2495, -0.072315, -0.94078, -1.7327, 1.0321, -1.0315, -0.53458, 0.42734, 0.84209, -1.2034, -1.0866, -0.054531, 0.49018, 1.2609, 0.22052, 1.8779, -0.5805, ...
            -2.5793, -0.15818, 0.80609, 1.6461, -1.5057, 2.7549, 0.75968, -0.29318, 1.3611, 1.7949, 0.49963, -0.82682, -0.71709, -0.13373, -1.134, 0.26532, -0.10586, 1.1973, -0.093497, -0.12902, ...
            1.1924, -0.094074, -0.50296, -0.028507, 0.75487, -0.61262, -1.0397, 1.1714, -2.5924, 0.27222, 0.94647, -0.086664, -0.058841, -1.385, 0.36566, 0.60344, -0.84338, -1.8725, 1.0864, 0.73021, ...
            0.66075, 1.0185, 0.13525, 1.2104, 0.42228, -0.29569, -1.332, 0.65957, 0.92058, 2.3197, -0.87848, -0.96455, 0.45761, 0.34531, -0.79793, -2.0616, -1.2026, -0.47025, 1.0052, 0.1343, ...
            1.0494, -1.251, -1.2272, -0.56923, 1.6138, 0.012555, 1.9394, 0.30903, -0.85705, 2.8096, 0.74093, 0.64727, 1.2672, 0.047452, -0.10045, 0.41786, 0.022044, 2.3265, 1.3398, 1.4079, ...
            -0.17622, -0.86206, 0.40009, 0.00081304, 1.4679, -0.69352, 1.3463, -0.90853, 1.4885, -1.0489, -0.17639, -1.5492, 0.37961, 0.12622, 0.17396, -0.10497, 1.2692, 2.4766, 1.2194, -1.0685, ...
            0.66461, -0.20925, -1.8766, 0.69231, 0.74627, 0.4509, -0.42323, 1.443, 0.18736, 0.24402, -0.16302, -0.11695, 0.58654, -0.83197, 0.14394, -1.7035, -0.78696, 1.4342, -0.43874, -0.53132, ...
            3.3987, -1.3982, 0.2561, 1.3764, 0.052192, 1.3296, 0.55486, 0.53087, 0.24814, -0.046769, -0.56885, 0.82319, 2.0596, 0.071589, -2.5755, 0.61377, 0.1573, 0.39211, -0.89362, -0.63771, ...
            -1.158, 2.0931, -0.45137, -0.013725, -1.6319, -0.05829, 0.90039, 0.57899, -1.0942, 0.92623, 0.38602, 0.76113, 1.6908, 0.20317, -1.0039, 1.8578, -1.4511, -0.4819, -1.7688, -0.59634, ...
            -0.71635, -0.88256, 1.8716, 1.2487, 2.051, 2.0079, 0.55411, 1.0013, 0.34699, -0.99544, -0.59699, -0.0084384, 0.85033, -0.0059792, 0.79105, -1.5101, -0.25186, 2.3532, 0.45735, -1.5821 ];
        
        F(:,3) = [0.25973, 0.35745, -0.39845, 0.90249, 2.1715, -1.3623, -0.89115, -1.2418, -0.1611, -0.65883, -0.62224, -0.9648, -1.1574, 1.462, -0.22685, 0.57539, -0.5011, -0.68586, -0.68539, 0.30813, ...
            -2.2028, -0.031258, 1.2152, 3.2519, 1.4913, -0.15383, -1.5385, -0.73247, -1.4337, 0.29473, 1.3469, 1.1972, 0.038954, -0.05646, 0.41435, -0.13307, 0.32772, 0.34978, 1.0893, 2.353, ...
            -1.5097, -0.33417, -1.5431, -0.514, 1.0314, 1.0866, -0.40768, -0.82353, -1.1461, -0.72355, -0.093683, -0.036405, -0.55742, -1.5678, -1.9739, 0.59502, -1.1805, -1.1091, -0.93172, 1.4215, ...
            1.0593, 1.0732, 0.15835, -1.1242, -0.14939, -0.14259, 0.0062516, -0.8021, 0.84361, 0.90475, 0.57933, 0.52007, 1.255, -1.7483, 0.9445, -0.54313, 1.0108, 1.4332, -0.33295, 1.0611, ...
            -1.438, -0.56951, 0.62816, -0.25228, -0.45733, -0.47663, -1.057, -1.1295, -0.19774, 0.042411, -1.4064, 0.40076, 0.63471, -0.051288, 1.6385, -0.39844, -0.95256, 1.4175, 0.30201, -1.6381, ...
            -1.8707, -0.058105, -1.8963, 1.1378, 0.92815, -1.2778, -0.37781, -0.76041, 0.90661, -1.5761, -0.56568, -1.7066, 1.3577, 1.2889, 0.19034, 1.0301, 1.1354, 1.0892, -1.2413, 1.5294, ...
            1.6682, 0.059227, -1.7123, -0.90526, -0.78838, 0.2764, -0.020037, 0.28638, -1.3939, -0.41345, -2.1783, -2.2447, 2.2974, 0.30745, -0.69185, -0.74365, -0.10063, -0.73549, -1.1445, -1.5356, ...
            -0.069785, 1.0006, -0.39377, 0.42203, 0.14249, 1.0098, -1.1002, -0.05207, 1.2718, -1.4129, 0.67124, -0.65974, 0.76653, -0.090684, -1.202, -0.98226, 1.6793, -1.0103, 0.16834, 1.8242, ...
            -2.2982, -1.6478, 1.0588, -1.0354, 0.67464, 0.74803, -0.65654, 1.2264, 2.8944, 2.9008, 0.12051, 1.4799, 1.525, 0.045956, 1.7112, -0.077693, -0.019032, 0.24221, 0.097964, -0.67632, ...
            -1.3028, 1.5282, 0.455, -2.3268, 0.016581, -1.2891, 2.4129, 0.39772, 1.6219, 0.45219, 2.975, 0.78287, -0.30844, -1.074, -1.5422, -0.72282, -0.094429, -1.0865, 1.0684, 0.44701, ...
            1.6103, 0.57553, 0.35915, 1.227, 0.45411, 1.1519, -0.44011, 0.45438, 1.8238, 2.9895, 0.29266, -0.0010976, -3.3009, 0.27258, -0.37878, 0.18825, 0.0021787, 1.178, 1.394, 1.2044, ...
            -2.0974, 0.25376, 0.68401, 0.22573, 0.83561, -0.98656, -1.0608, -0.61777, -0.65319, -0.52486, -1.0345, -0.08194, 0.1028, 0.41614, 0.66278, 1.258, -0.72212, 0.51796, -2.2662, 2.0726, ...
            0.72799, 0.18484, -1.8528, 1.6694, 0.65435, -1.4848, -0.69894, 1.0804, -1.055, -1.0563, 0.11722, 0.65, 0.29301, 0.33485, 0.5685, -1.2392, -0.32164, -1.435, 1.0352, 0.3654, ...
            0.87007, 0.52245, -0.98619, 1.2293, 0.60515, 0.15644, -1.974, 0.48414, -1.3404, -0.46969, 2.5828, 0.0015863, 0.03021, -0.16414, -0.44124, 0.64084, -1.9535, 0.32646, -1.7676, 0.61042, ...
            -0.88161, 1.576, 1.2697, -1.6901, -0.042836, -0.70313, -1.4201, -1.7148, 1.7403, -0.88246, 0.15906, 0.50337, 0.71298, -0.60868, -1.8181, -0.42863, -1.9372, -0.023682, -0.51097, -0.040785, ...
            -2.066, -1.8863, -0.61253, -0.02115, 0.97183, 2.1698, -0.80372, 1.4712, 0.59869, -0.74807, 0.82284, -0.089904, 0.33732, -0.090028, 1.9797, -2.2826, 0.30993, -0.03354, 1.1627, 0.20531, ...
            0.28919, -0.12335, -1.0737, -1.0051, -0.05494, -0.52534, 0.83987, -0.44807, -0.84639, 0.026146, 1.5551, 0.10714, 0.066678, -0.59379, 1.5211, -0.78979, -0.052202, 0.14869, -0.37558, 2.2839, ...
            1.5262, 0.0075095, -0.69708, 1.5216, 0.99156, -1.9046, 1.5786, -0.69964, 0.11037, -2.0113, 0.039908, -0.53872, 1.1251, 1.5328, 0.31065, 0.15591, -0.93005, -0.45739, -1.4424, 0.81943, ...
            0.019438, 0.34642, 0.68612, -0.49713, -0.33138, -0.83685, -0.49765, 1.1052, 0.093501, -3.289, 0.84523, 1.3035, -0.82681, -0.98862, 1.4895, -0.9506, -0.090064, 2.1268, -0.18304, 0.78065, ...
            0.61203, 0.25542, 0.69058, -0.060731, 0.41193, -1.7881, 2.1928, -0.45662, -0.45561, 0.18735, 0.18852, 0.0075339, 0.68042, -0.17528, -0.039454, -0.81443, 0.18646, 1.1921, -1.9199, 0.29143, ...
            0.66542, -0.95168, -2.4984, 0.60226, 0.3146, -1.3236, -0.40641, 0.129, 1.5058, 0.80376, -0.96912, 2.423, 0.2408, -0.02925, 0.53612, -0.89669, 0.24748, 0.52815, -0.96056, 0.83242, ...
            -0.48746, -0.28428, -0.96136, 2.5231, -1.2897, -0.2417, 0.35367, -0.69966, 0.44168, -2.225, 0.16868, -1.3895, 0.60442, 1.5101, -0.44913, -0.59157, 0.48211, -1.6046, -0.23611, -1.7826, ...
            1.7117, -0.87539, -0.026404, 0.51277, 0.64489, 0.44951, 2.1807, 0.46303, 1.2155, 0.39106, 0.08043, 0.81507, -1.101, 0.40299, -0.62793, 2.1414, 0.0042865, 0.22947, -1.6875, -0.066938, ...
            -0.22343, 0.52618, -0.16104, 0.76489, 1.6502, 0.80249, -0.12046, 0.14735, -0.2782, -0.7231, 1.6796, -0.4078, 0.79591, -1.2386, 0.65375, -2.1216, -0.1682, 0.62508, -0.13647, -1.3249, ...
            -1.9956, 2.2567, 0.93135, -0.81704, 0.036525, -0.18776, 0.55155, 1.548, 0.81731, -1.1447, 0.82502, 0.73287, -0.938, 0.4237, -0.27914, 1.3903, 0.58463, -0.9572, 0.30417, -2.2243, ...
            -0.85638, -0.42592, -1.3659, 1.4759, -0.48294, -0.24876, -0.57956, -0.054472, 0.73353, 0.99369, -1.2898, -0.22685, 2.6859, -0.59801, -0.025553, -0.076834, -1.4167, 0.79216, 2.3108, 0.68707, ...
            -1.947, 0.52572, 0.42399, -0.87731, -0.83426, -0.34154, 0.70183, 0.49145, 0.0085707, -1.4119, -0.017315, 0.4427, -0.32253, -2.1265, -0.66329, 1.7671, 0.60754, -0.64341, 0.032203, -3.3674, ...
            -1.1929, 0.30446, 0.56391, 0.87422, 0.56725, 2.2673, -0.54866, -0.23154, -1.3438, -0.69528, -2.4593, -0.79159, 0.63876, 1.3757, -2.1187, 1.9006, 0.55594, -0.14109, 0.44143, -3.4633, ...
            -0.48513, 1.2591, 0.13973, -2.1157, 1.3777, -0.7362, 1.0368, -0.40163, 0.45908, 1.0988, 3.0867, 0.85247, -0.81711, 0.83759, -1.7758, 1.2869, -1.01, -0.024227, 2.0095, -0.34163, ...
            0.16306, -0.24748, 0.12046, 0.13658, -0.12635, 0.13009, 0.12352, -0.22545, -0.65125, -0.27251, -1.2969, -1.3833, -0.040059, -1.2156, -1.6298, 1.8927, -0.43682, -0.5837, -0.44663, 0.24427, ...
            0.47537, 1.59, -0.49982, -0.30809, 0.13574, -0.85454, -0.49428, 1.9933, -0.82942, -0.61363, 1.0096, 0.9509, -0.60387, -0.44651, 1.6018, -0.88842, -1.9894, 0.43991, 2.5085, -1.165, ...
            -0.99406, 0.31346, 1.5539, 0.44557, 0.49153, 1.9671, 1.0036, -0.13162, -0.90885, 0.14199, 0.76444, 1.4629, -1.0687, -1.1368, -0.54442, 0.31066, -1.5393, 0.18373, 1.2139, 0.56501, ...
            1.3197, -0.71389, -0.56312, -0.71496, 0.35023, 0.88528, 0.47273, 0.531, 0.43182, 1.1847, -0.3814, -0.35699, -0.5349, -1.8896, -0.34297, -0.38523, 0.39088, -1.2326, 0.30378, -0.028067, ...
            -1.1949, -1.5297, 0.70763, -0.074955, 0.85812, -0.27446, 0.34058, 0.2424, -1.3586, 0.42614, -0.6989, -1.9432, -1.4228, -0.7205, 0.70939, 0.54673, -0.69447, 1.6511, 0.21547, -0.63888, ...
            0.74793, -0.40369, 0.1088, 0.61252, -0.6542, 0.1703, 0.5207, 1.7583, 0.20503, 1.2888, 2.3315, 0.79184, 2.215, 0.95714, -1.0656, 0.58424, 1.0918, 1.4964, -1.0113, -0.36469, ...
            -1.8968, 0.10754, 1.0382, -0.57653, 0.4779, 0.066367, 1.3801, -1.8391, -1.6742, 1.1611, 0.82874, 1.6864, 0.39757, -0.37018, 0.038509, 0.42272, -0.50778, 2.1736, -1.9353, 0.27732, ...
            0.39685, -0.47629, -0.5878, 1.8285, 0.62705, 0.44083, -0.94186, -0.42001, -0.53504, 2.7241, 0.12676, -0.51526, -0.68614, 0.63624, 0.7421, -0.17335, -0.553, -0.45673, 0.67715, -0.18216, ...
            -1.0006, 0.90144, 2.0713, -1.3724, -1.2211, 1.9788, 1.2173, 0.02312, -0.034155, 2.2402, -0.12989, -0.48002, 1.705, 0.033422, -0.0094897, -1.5324, -1.9502, 1.2426, 0.65791, 1.2359, ...
            -0.4173, 1.4846, -2.5214, 0.2897, 1.2193, 1.094, -1.1884, -0.58712, 2.3001, -0.74032, 0.83532, 1.0366, -0.66207, 0.85169, 0.045362, 2.631, 1.3544, 0.079144, -1.9998, -0.046901, ...
            -0.6294, -1.0499, -1.352, -1.8837, 1.7383, 0.061435, -1.0126, 2.8307, 1.0988, 2.1339, 3.014, 0.92382, -0.87803, -0.39133, -0.054385, -0.95275, 0.1266, 0.40103, 1.4163, -2.0352, ...
            1.1452, -0.56408, 2.6565, 0.019929, 1.4872, 0.70467, -1.3166, 0.041581, -0.58841, 1.9786, -2.6082, 0.59916, -1.879, 1.5968, 0.57563, 1.326, -0.50474, -1.1243, -1.249, -1.1258, ...
            -1.3727, -1.2051, 1.5849, -0.58365, 0.50921, 0.14521, 1.3152, 0.80639, -0.036791, 0.76959, -1.2912, 1.3899, 0.90494, -0.96061, 0.003062, -1.3244, 0.13297, 1.2721, 2.2459, 1.5645, ...
            0.20316, 2.9192, 0.97552, 0.9688, -0.58794, -0.52874, 0.22143, -0.076808, -1.3126, -0.93903, -0.44511, 2.3719, 0.54789, -0.25886, 1.2248, 1.4888, 0.36052, -0.73722, 0.89653, -0.54403, ...
            0.33454, -2.4337, 3.0417, -0.65839, 0.12864, 0.61696, 0.87449, 0.16238, 0.73805, -1.2515, 1.322, 1.1006, -0.3043, 1.4801, 0.95052, -0.4035, -0.76184, -0.51045, 1.0644, -1.1499, ...
            -0.99468, -0.82047, -0.20838, 0.14802, -0.48885, -0.87659, -1.4754, 0.35785, -1.013, 0.84719, -0.41604, -0.018905, -0.76566, 1.6621, -0.99738, 0.81141, -0.84045, -1.6396, 0.66038, -0.15655, ...
            1.0534, -0.276, -0.68868, 0.17625, -0.18179, 0.9728, -0.36922, 0.99349, -0.0082787, 0.67342, -0.50406, 0.6607, -0.12598, 1.4062, 0.30509, 0.89765, 0.43815, -0.89712, 0.63022, -1.5697, ...
            -0.43817, 0.66963, -0.23988, 0.46708, -0.64318, -1.4336, -2.468, 0.14946, -0.18855, 1.1526, -0.11252, -0.049571, 1.1081, 1.9668, -0.30195, -0.78068, -1.0681, 0.48073, 0.74043, 2.8884, ...
            -0.16092, -0.52517, -0.53564, 2.1449, 0.92224, -1.2825, 1.2447, -1.4764, -0.43825, -0.93766, 0.6015, 0.20201, 0.035599, -0.064325, 1.7869, 0.85096, 1.6567, -0.055886, 0.90316, 0.033524, ...
            0.92324, -0.047969, -1.6731, 0.038127, 0.26461, -0.22327, -1.4261, 1.1419, -0.93772, -0.89335, -1.8389, -1.1582, 1.6138, 0.72194, 1.3463, -1.6663, 0.069766, -0.23331, -2.2535, -0.62735, ...
            -1.5607, 0.02826, -0.55439, -1.1916, 0.46537, -0.089272, 1.9397, 1.4856, -1.6998, -0.93842, 1.5841, -0.63998, -0.85376, 0.090331, 0.96466, 1.0654, -1.3206, -0.91724, 0.50233, -1.0887 ];
end

h_sos = qd_sos([]);
h_sos.name              = acf_type;
h_sos.Pdist_decorr      = 10;
h_sos.dist              = single( D );
h_sos.acf               = single( R );
h_sos.sos_freq          = single( F ./ D(end) );
h_sos.sos_amp           = sqrt( single( 2 /  size(F,1)) );
h_sos.init;

if exist( 'distribution','var' ) && ~isempty( distribution )
    h_sos.distribution = distribution;
end

if exist( 'dist_decorr','var' ) && ~isempty( dist_decorr ) && any( dist_decorr ~= 10 )
    h_sos.dist_decorr = dist_decorr;
end

end
