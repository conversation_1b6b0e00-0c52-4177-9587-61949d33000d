function result = multi()

no_snapshots = 100;
bs_num = 3;
ue_num = 2;
isd=100;
floder = '/data/setsumi/beamforming/Azhangyu/test2/';
myseed = 1215;
BW = 48960e3; %10M频带 BW=15KHZ*12sc*16prb*17sub
nSub = 17; %子频带个数

addpath(rmpath('/data/setsumi/beamforming/setsumi_env/quadriga_src'));
addpath(rmpath('/data/setsumi/beamforming/setsumi_env/quadriga_src/config'));
addpath(genpath('/data/setsumi/beamforming/setsumi_env/quadriga_src'));
addpath(genpath('/data/setsumi/beamforming/setsumi_env/quadriga_src/config'));

my_rng = rng;
if myseed > 0
    seed = int64(myseed);
else
    seed = my_rng.State(1,1);
end
rng(seed);
check_my_rng = rng;

%仿真参数配置
s = qd_simulation_parameters;
s.center_frequency = 28e9;
s.use_3GPP_baseline = 0; %增强3GPP，能够支持移动
s.show_progress_bars = 1; %显示进度条

%天线面板配置
downtilt = 0;
H_ants = 8;
V_ants = 4;
V_d = 0.5;
UE_H_ants = 1;
UE_V_ants = 2;
UE_port = UE_H_ants*UE_V_ants*2;
%模型选择，垂直天线数，水平天线数，中心频率，极化方式，下倾，天线间的间距，垂直面板个数，水平面板个数，垂直面板间距，水平面板间距
BS_array  = qd_arrayant( '3gpp-mmw', 1, 1, s.center_frequency, 3, downtilt, 0.5, V_ants, H_ants, V_d, 0.5 ); 
UE_array = qd_arrayant('3gpp-mmw', 1, 1, s.center_frequency, 3, 0, 0.5, UE_V_ants, UE_H_ants, 0.5, 0.5);

%基站和用户
no_bs = bs_num; %bs个数 
no_rx = ue_num; %用户数量
no_go_dist = 10; 
bs_height = 10; 

% 设置移动参数
speed = 3/3.6; %3km/h->m/s
time_unit = 0.02; %s
time = time_unit*(no_snapshots - 1);
move_len = time*speed;
if no_snapshots > 1
    move_enable = true;
else
    move_enable = false;
end

scene = qd_layout;
scene.no_rx = no_rx;
scene.no_tx = no_bs;
scene.rx_array = UE_array;
scene.tx_array = BS_array;
scene.simpar = s;

scene.tx_position = [0, 173.2051, 0; 0, 100, 200; 10, 10, 10];
for ue_idx = 1: no_rx
    dist = 0.93*isd*rand(1);
    angle = pi/3*(2*rand(1)-1);
    scene.rx_position(1,ue_idx) = dist*cos(angle);
    scene.rx_position(2,ue_idx) = dist*sin(angle);
    scene.rx_position(3,ue_idx) = 1.0;
end

if move_enable
    for n = 1 : scene.no_rx
        % t = qd_track('linear',move_len,0);
        % t.initial_position = scene.rx_position(:,n);
        % t.movement_profile = [ 0,time;0,move_len]; 
        % t.interpolate('time',0.02);
        % t.name = scene.rx_name{1,n};
        
        % % t.scenario{1} = '3GPP_38.901_UMi_LOS';
        % % pos1 = t.positions(:,1)+t.initial_position;
        % % t.add_segment(pos1, '3GPP_38.901_UMi_LOS');

        % % pos1 = t.positions(:,51)+t.initial_position;
        % % t.add_segment(pos1, '3GPP_38.901_Indoor_LOS');

        % % pos1 = t.positions(:,51)+t.initial_position;
        % % t.add_segment(pos1, '3GPP_38.901_Indoor_NLOS');
        % t.segment_index = [1,51];
        % t.scenario = { '3GPP_38.901_UMi_LOS', '3GPP_38.901_Indoor_LOS' };

        % disp(t.no_snapshots);
        % disp(t.no_segments);
        % scene.rx_track(1,n) = t;

        t = qd_track('linear',10,0);
        t.initial_position = [5;0;0];
        t.interpolate_positions( 100 );
        t.segment_index = [1,40,90];
        t.scenario = {'3GPP_38.901_UMi_LOS', '3GPP_38.901_Indoor_LOS', '3GPP_38.901_Indoor_NLOS' };
        t.name = scene.rx_name{1,n};

        disp(t.no_snapshots);
        disp(t.no_segments);
        scene.rx_track(1,n) = t;
    end
end

b = scene.init_builder;
sic = size(b);
for ib = 1 : numel(b)
    [ i1,i2 ] = qf.qind2sub( sic, ib );
    scenpar = b(i1,i2).scenpar;                 % Read scenario parameters
    % scenpar.SC_lambda = 0;                      % Disable spatial consistency of SSF
    b(i1,i2).scenpar_nocheck = scenpar;         % Save parameters without check (faster)
end

b_ue = b;
b_ue = split_multi_freq( b_ue );                      % Split t he builders for multiple frequencies
gen_parameters( b_ue );                            % Generate LSF and SSF parameters (uncorrelated)
cm = get_channels( b_ue );                         % Generate channels

cm_merge = merge(cm, [], 0);

file_log = fopen(strcat(floder,'log.txt'),'w');
for cm_idx = 1:numel(cm)
    fprintf(file_log,"sim cm %d name is %s,npath is %d,no_snap is %d\n",cm_idx,cm(1,cm_idx).name,cm(1,cm_idx).no_path,cm(1,cm_idx).no_snap);
end
for cm_idx = 1:numel(cm_merge)
    fprintf(file_log,"sim cm_merge %d name is %s,npath is %d,no_snap is %d\n",cm_idx,cm_merge(1,cm_idx).name,cm_merge(1,cm_idx).no_path,cm_merge(1,cm_idx).no_snap);
end

fclose(file_log);

n_tr = numel(cm);
for tr_idx = 1 : n_tr
    tr_builder = cm(1,tr_idx);
    H_time = tr_builder.coeff; 
    disp(size(H_time));
end

for tr_idx = 1 : n_tr
    tr_builder = cm(1,tr_idx);
    H_freq = tr_builder.fr(BW,nSub);
    disp(size(H_freq));
end

result = 'setsumi';