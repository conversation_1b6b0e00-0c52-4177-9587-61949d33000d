
import os

scenario_path = '../Data/test/'
os.makedirs(scenario_path,exist_ok=True)

floder = scenario_path
file_H_time = os.open(floder+'H_time.txt',os.O_RDWR|os.O_CREAT)
file_H_freq = os.open(floder+'H_freq.txt',os.O_RDWR|os.O_CREAT)
# file_power = os.open(floder+'power.txt',os.O_RDWR|os.O_CREAT)
# file_gain = os.open(floder+'gain.txt',os.O_RDWR|os.O_CREAT)
file_log = os.open(floder+'log.txt',os.O_RDWR|os.O_CREAT)
file_loc = os.open(floder+'loc.txt',os.O_RDWR|os.O_CREAT)
# file_sangles = os.open(floder+'sangles.txt',os.O_RDWR|os.O_CREAT)
# file_angles = os.open(floder+'angles.txt',os.O_RDWR|os.O_CREAT)
file_pg = os.open(floder+'pg.txt',os.O_RDWR|os.O_CREAT)
# file_angles_ae = os.open(floder+'anglesae.txt',os.O_RDWR|os.O_CREAT)

file_pg_all = os.open(floder+'pg_all.txt',os.O_RDWR|os.O_CREAT)


os.close(file_loc)
os.close(file_H_time)
# os.close(file_power)
# os.close(file_gain)
os.close(file_pg)
os.close(file_H_freq)
os.close(file_log)
# os.close(file_angles)
# os.close(file_sangles)
os.close(file_pg_all)



# 修改文件权限为777