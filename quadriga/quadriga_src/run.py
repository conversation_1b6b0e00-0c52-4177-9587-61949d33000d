import matlab.engine
import os

def rewrite_conf(KF_mu):

    dir_path = '/data/setsumi/zhangyucode/env2023/env/quadriga_src/config/'

    confs = ['3GPP_38.901_InF_LOS.conf']

    for conf in confs:
        conf_path = dir_path + conf
        with open(conf_path, 'r') as infile:
            lines = infile.readlines()
        for i, line in enumerate(lines):
            if line.startswith('KF_mu ='):
                lines[i] = f'KF_mu =                {KF_mu:d}            % Ricean K-factor [dB]\n'
        with open(conf_path, 'w') as outfile:
            outfile.writelines(lines)

            
KF_list = [-100,-50,0,7,50,100]
for i in range(len(KF_list)):
    KF_mu = KF_list[i]
    scenario_path = f'/data/setsumi/zhangyucode/env2023/env/Data/kf_los_speed3/{KF_mu}/'
    rewrite_conf(KF_mu)
    eng = matlab.engine.start_matlab()
    result = eng.run(scenario_path)