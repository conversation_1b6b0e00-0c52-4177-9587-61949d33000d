% Config file for scenario "3GPP_38.901_RMa_NLOS"
% See: 3GPP TR 38.901 v16.1.0 (2019-12)

% Valid frequency range:        0.5 to 7 GHz
% Valid distance range:         10 to 10000 m
% Valid BS antenna height:      10 to 150 m
% Valid MT antenna height:      1 to 10 m

% ==================================================================================================
% Large scale distributions
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p46 Table 7.5-6 
% ==================================================================================================

% DS  = -7.43 + Xds * ( 0.48 )
% KF  = -100 + Xkf * ( 0 )
% SF  = Xsf * ( 8 )
% ASD = 0.95 + Xasd * ( 0.45 )
% ASA = 1.52 + Xasa * ( 0.13 )
% ESD = 0.28 + Xesd * ( 0.3 )
% ESA = 0.58 + Xesa * ( 0.37 )
% XPR = 7 + Xxpr * ( 3 )

DS_mu =               -7.43         % delay spread [log10(s)] 
DS_sigma =             0.48         % delay spread STD [log10(s)] 

KF_mu =               -100          % Ricean K-factor [dB] 
KF_sigma =             0            % Ricean K-factor STD [dB] 

SF_sigma =             8            % Shadow Fading STD [dB] 

AS_D_mu =              0.95         % azimuth of departure angle spread [log10(deg)] 
AS_D_sigma =           0.45         % azimuth of departure angle spread STD [log10(deg)] 

AS_A_mu =              1.52         % azimuth of arrival angle spread [log10(deg)] 
AS_A_sigma =           0.13         % azimuth of arrival angle spread STD [log10(deg)] 

ES_D_mu =              0.28         % elevation of departure angle spread [log10(deg)] 
ES_D_sigma =           0.3          % elevation of departure angle spread STD [log10(deg)] 
ES_D_mu_min =         -1            % minimum ESD reference value [log10(deg)]
ES_D_mu_A =           -0.19         % TX-RX 2D dist.-dep. of ESD [log10(deg)/km]

ES_A_mu =              0.58         % elevation of arrival angle spread [log10(deg)] 
ES_A_sigma =           0.37         % elevation of arrival angle spread STD [log10(deg)] 

XPR_mu =               7            % cross-polarization ratio [dB] 
XPR_sigma =            3            % cross-polarization ratio STD [dB] 

% ==================================================================================================
% Large-Scale fading decorrelation distances
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p46 Table 7.5-6 
% ==================================================================================================

DS_lambda =            36           % DS decorrelation distance [m]
KF_lambda =            40           % KF decorrelation distance [m]
SF_lambda =            120          % SF decorrelation distance [m]
AS_D_lambda =          30           % ASD decorrelation distance [m]
AS_A_lambda =          40           % ASA decorrelation distance [m]
ES_D_lambda =          50           % ESD decorrelation distance [m]
ES_A_lambda =          50           % ESA decorrelation distance [m]
XPR_lambda =           40           % XPR decorrelation distance [m]

% ==================================================================================================
% Inter-parameter correlations
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p46 Table 7.5-6 
% ==================================================================================================

%         DS     KF     SF     ASD    ASA    ESD    ESA    XPR
%     |   1      0     -0.5   -0.4    0     -0.1   -0.4    0     | DS 
%     |   0      1      0      0      0      0      0      0     | KF 
%     |  -0.5    0      1      0.6    0     -0.04  -0.25   0     | SF 
% R = |  -0.4    0      0.6    1      0      0.42  -0.27   0     | ASD 
%     |   0      0      0      0      1     -0.18   0.26   0     | ASA 
%     |  -0.1    0     -0.04   0.42  -0.18   1     -0.27   0     | ESD 
%     |  -0.4    0     -0.25  -0.27   0.26  -0.27   1      0     | ESA 
%     |   0      0      0      0      0      0      0      1     | XPR 

ds_sf =               -0.5          % DS vs. SF
asD_ds =              -0.4          % DS vs. ASD
esD_ds =              -0.1          % DS vs. ESD
esA_ds =              -0.4          % DS vs. ESA
asD_sf =               0.6          % SF vs. ASD
esD_sf =              -0.04         % SF vs. ESD
esA_sf =              -0.25         % SF vs. ESA
esD_asD =              0.42         % ASD vs. ESD
esA_asD =             -0.27         % ASD vs. ESA
esD_asA =             -0.18         % ASA vs. ESD
esA_asA =              0.26         % ASA vs. ESA
esD_esA =             -0.27         % ESD vs. ESA

% ==================================================================================================
% Model parameters
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p46 Table 7.5-6 
% ==================================================================================================

NumClusters =          11           % number of clusters
NumSubPaths =          20           % number of paths per (NLOS) cluster
SubpathMethod =        legacy       % subpath mapping method (legacy, Laplacian or mmMAGIC)

r_DS =                 1.7          % delay scaling factor
LNS_ksi =              3            % per cluster shadowing STD [dB]

PerClusterAS_D =       2            % cluster azimuth of departure angle spread [deg]
PerClusterAS_A =       3            % cluster azimuth of arrival angle spread [deg]
PerClusterES_D =       3            % cluster elevation of departure angle spread [deg]
PerClusterES_A =       3            % cluster elevation of arrival angle spread [deg]

% ==================================================================================================
% Decorrelation distance for the small-scale fading spatial consistency
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p54 Table 7.6.3.1-2 
% ==================================================================================================

SC_lambda =            60           % decorrelation distance [m]; 0 = disabled

% ==================================================================================================
% Path-loss model
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p27 Table 7.4.1-1 
% ==================================================================================================

% Formula for dual-slope (LOS) pathloss model:
% (Distance in meters, frequency in GHz)
%
%     PL = PL1 for d2D <= dBP | PL2 for d2D > dBP
%    PL1 = A1 * log10( d3D ) + B + C * log10( fGHz ) + D * d3D
%    PL2 = PL1( dBP ) + A2 * log10( d3D / dBP )
%    dBP = E * ( hBS-hE ) * ( hMS-hE ) * fGHz

% Formula for 3GPP NLOS pathloss model:
% (Distances and heights in meters, frequency in GHz)
%
%    PLn =  An * log10( d3D )
%        +  Bn
%        +  Cn * log10( fGHz )
%        +  Dn * log10( hBS )
%        + D1n * log10( hBS ) / hBS
%        + D2n * log10( hBS ) / hBS^2
%        + D3n * hBS
%        +  En * log10( hUT )
%        + E1n * log10( hUT ) / hUT
%        + E2n * log10( hUT ) / hUT^2
%        + E3n * hUT
%        +  Fn * log10( hBS ) * log10( d3d )
%        + G1n * log10^2( G2n * hUT )
%
%     PL = max( PL_LOS, PLn ) 

PL_model =             nlos
PL_A1 =                20.478       % TX-RX 3D dist.-dep. of the LOS-PL before break-point [dB/log10(m)]
PL_A2 =                40           % TX-RX 3D dist.-dep. of the LOS-PL after break-point [dB/log10(m)]
PL_B =                 31.741       % Reference LOS-PL in [dB] @ 1 GHz, 1 m TX-RX dist.
PL_C =                 20           % Freq.-dep. of the LOS-PL in [dB/log10(GHz)]
PL_D =                 0.0014       % TX-RX 3D dist.-dep. of LOS-PL [dB/m]
PL_E =                 13.34        % Breakpoint scaling factor
PL_hE =                1            % Environment height in [m]
PL_An =                43.42        % TX-RX 3D dist.-dep. of NLOS-PL [dB/log10(m)]
PL_Bn =                31.755       % Reference NLOS-PL in [dB] @ 1 GHz, 1 m TX-RX dist.
PL_Cn =                20           % Freq.-dep. of the NLOS-PL in [dB/log10(GHz)]
PL_Dn =               -15.07        % TX height-dep. of the NLOS-PL in [dB/log10(m)]
PL_D2n =               92.5         % TX height-dep. of the NLOS-PL in [dB/log10(m)/m^2]
PL_Fn =               -3.1          % Combined TX-height and freq.-dep. of the NLOS-PL in [dB/(log10(m)*log10(GHz))]
PL_G1n =              -3.2          % RX height-dep. of the NLOS-PL on the square of the MT height [dB/(log10(m))^2]
PL_G2n =               11.75        % RX height-dep. factor

