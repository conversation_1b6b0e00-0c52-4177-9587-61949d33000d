% Config file for scenario "3GPP_38.901_UMa_NLOS"
% See: 3GPP TR 38.901 v16.1.0 (2019-12)

% Valid frequency range:        0.5 to 100 GHz
% Valid distance range:         10 to 5000 m
% Valid BS antenna height:      25 m
% Valid MT antenna height:      1.5 to 22.5 m

% ==================================================================================================
% Large scale distributions
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p43 Table 7.5-6 
% ==================================================================================================

% DS  = -6.28 - 0.204 * log10( fGHz ) + Xds * ( 0.39 )
% KF  = -100 + Xkf * ( 0 )
% SF  = Xsf * ( 6 )
% ASD = 1.5 - 0.1144 * log10( fGHz ) + Xasd * ( 0.28 )
% ASA = 2.08 - 0.27 * log10( fGHz ) + Xasa * ( 0.11 )
% ESD = 0.9 + Xesd * ( 0.49 )
% ESA = 1.512 - 0.3236 * log10( fGHz ) + Xesa * ( 0.16 )
% XPR = 7 + Xxpr * ( 3 )

DS_mu =               -6.28         % delay spread [log10(s)] @ 1 GHz
DS_sigma =             0.39         % delay spread STD [log10(s)] 
DS_gamma =            -0.204        % freq.-dep. of DS [log10(s)/log10(GHz)]

KF_mu =               -100          % Ricean K-factor [dB] 
KF_sigma =             0            % Ricean K-factor STD [dB] 

SF_sigma =             6            % Shadow Fading STD [dB] 

AS_D_mu =              1.5          % azimuth of departure angle spread [log10(deg)] @ 1 GHz
AS_D_sigma =           0.28         % azimuth of departure angle spread STD [log10(deg)] 
AS_D_gamma =          -0.1144       % freq.-dep. of ASD [log10(deg)/log10(GHz)]

AS_A_mu =              2.08         % azimuth of arrival angle spread [log10(deg)] @ 1 GHz
AS_A_sigma =           0.11         % azimuth of arrival angle spread STD [log10(deg)] 
AS_A_gamma =          -0.27         % freq.-dep. of ASA [log10(deg)/log10(GHz)]

ES_D_mu =              0.9          % elevation of departure angle spread [log10(deg)] 
ES_D_sigma =           0.49         % elevation of departure angle spread STD [log10(deg)] 
ES_D_mu_min =         -0.5          % minimum ESD reference value [log10(deg)]
ES_D_mu_A =           -2.1          % TX-RX 2D dist.-dep. of ESD [log10(deg)/km]

ES_A_mu =              1.512        % elevation of arrival angle spread [log10(deg)] @ 1 GHz
ES_A_sigma =           0.16         % elevation of arrival angle spread STD [log10(deg)] 
ES_A_gamma =          -0.3236       % freq.-dep. of ESA [log10(deg)/log10(GHz)]

XPR_mu =               7            % cross-polarization ratio [dB] 
XPR_sigma =            3            % cross-polarization ratio STD [dB] 

% ==================================================================================================
% Large-Scale fading decorrelation distances
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p43 Table 7.5-6 
% ==================================================================================================

DS_lambda =            40           % DS decorrelation distance [m]
KF_lambda =            50           % KF decorrelation distance [m]
SF_lambda =            50           % SF decorrelation distance [m]
AS_D_lambda =          50           % ASD decorrelation distance [m]
AS_A_lambda =          50           % ASA decorrelation distance [m]
ES_D_lambda =          50           % ESD decorrelation distance [m]
ES_A_lambda =          50           % ESA decorrelation distance [m]
XPR_lambda =           50           % XPR decorrelation distance [m]

% ==================================================================================================
% Inter-parameter correlations
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p43 Table 7.5-6 
% ==================================================================================================

%         DS     KF     SF     ASD    ASA    ESD    ESA    XPR
%     |   1      0     -0.4    0.4    0.6   -0.5    0      0     | DS 
%     |   0      1      0      0      0      0      0      0     | KF 
%     |  -0.4    0      1     -0.6    0      0     -0.4    0     | SF 
% R = |   0.4    0     -0.6    1      0.4    0.5   -0.1    0     | ASD 
%     |   0.6    0      0      0.4    1      0      0      0     | ASA 
%     |  -0.5    0      0      0.5    0      1      0      0     | ESD 
%     |   0      0     -0.4   -0.1    0      0      1      0     | ESA 
%     |   0      0      0      0      0      0      0      1     | XPR 

ds_sf =               -0.4          % DS vs. SF
asD_ds =               0.4          % DS vs. ASD
asA_ds =               0.6          % DS vs. ASA
esD_ds =              -0.5          % DS vs. ESD
asD_sf =              -0.6          % SF vs. ASD
esA_sf =              -0.4          % SF vs. ESA
asD_asA =              0.4          % ASD vs. ASA
esD_asD =              0.5          % ASD vs. ESD
esA_asD =             -0.1          % ASD vs. ESA

% ==================================================================================================
% Model parameters
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p43 Table 7.5-6 
% ==================================================================================================

NumClusters =          21           % number of clusters
NumSubPaths =          20           % number of paths per (NLOS) cluster
SubpathMethod =        legacy       % subpath mapping method (legacy, Laplacian or mmMAGIC)

r_DS =                 2.3          % delay scaling factor
LNS_ksi =              3            % per cluster shadowing STD [dB]

PerClusterDS =         6.5622       % cluster delay spread [ns]
PerClusterDS_gamma =  -3.4084       % freq.-dep. of cluster delay spread [ns/log10(GHz)]
PerClusterDS_min =     0.25         % minumum cluster delay spread [ns]
PerClusterAS_D =       2            % cluster azimuth of departure angle spread [deg]
PerClusterAS_A =       15           % cluster azimuth of arrival angle spread [deg]
PerClusterES_D =       1.5          % cluster elevation of departure angle spread [deg]
PerClusterES_A =       7            % cluster elevation of arrival angle spread [deg]

% ==================================================================================================
% Decorrelation distance for the small-scale fading spatial consistency
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p54 Table 7.6.3.1-2 
% ==================================================================================================

SC_lambda =            50           % decorrelation distance [m]; 0 = disabled

% ==================================================================================================
% Path-loss model
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p27 Table 7.4.1-1 
% ==================================================================================================

% Formula for dual-slope (LOS) pathloss model:
% (Distance in meters, frequency in GHz)
%
%     PL = PL1 for d2D <= dBP | PL2 for d2D > dBP
%    PL1 = A1 * log10( d3D ) + B + C * log10( fGHz ) + D * d3D
%    PL2 = PL1( dBP ) + A2 * log10( d3D / dBP )
%    dBP = E * ( hBS-hE ) * ( hMS-hE ) * fGHz

% Formula for 3GPP NLOS pathloss model:
% (Distances and heights in meters, frequency in GHz)
%
%    PLn =  An * log10( d3D )
%        +  Bn
%        +  Cn * log10( fGHz )
%        +  Dn * log10( hBS )
%        + D1n * log10( hBS ) / hBS
%        + D2n * log10( hBS ) / hBS^2
%        + D3n * hBS
%        +  En * log10( hUT )
%        + E1n * log10( hUT ) / hUT
%        + E2n * log10( hUT ) / hUT^2
%        + E3n * hUT
%        +  Fn * log10( hBS ) * log10( d3d )
%        + G1n * log10^2( G2n * hUT )
%
%     PL = max( PL_LOS, PLn ) 

PL_model =             nlos
PL_A1 =                22           % TX-RX 3D dist.-dep. of the LOS-PL before break-point [dB/log10(m)]
PL_A2 =                40           % TX-RX 3D dist.-dep. of the LOS-PL after break-point [dB/log10(m)]
PL_B =                 28           % Reference LOS-PL in [dB] @ 1 GHz, 1 m TX-RX dist.
PL_C =                 20           % Freq.-dep. of the LOS-PL in [dB/log10(GHz)]
PL_E =                 13.34        % Breakpoint scaling factor
PL_hE =                1            % Environment height in [m]
PL_An =                39.08        % TX-RX 3D dist.-dep. of NLOS-PL [dB/log10(m)]
PL_Bn =                14.44        % Reference NLOS-PL in [dB] @ 1 GHz, 1 m TX-RX dist.
PL_Cn =                20           % Freq.-dep. of the NLOS-PL in [dB/log10(GHz)]
PL_E3n =              -0.6          % RX height-dep. of the NLOS-PL in [dB/m]

