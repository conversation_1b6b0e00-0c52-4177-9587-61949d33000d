% Config file for scenario "3GPP_38.901_UMa_LOS"
% See: 3GPP TR 38.901 v16.1.0 (2019-12)

% Valid frequency range:        0.5 to 100 GHz
% Valid distance range:         10 to 5000 m
% Valid BS antenna height:      25 m
% Valid MT antenna height:      1.5 to 22.5 m

% ==================================================================================================
% Large scale distributions
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p43 Table 7.5-6 
% ==================================================================================================

% DS  = -6.955 - 0.0963 * log10( fGHz ) + Xds * ( 0.66 )
% KF  = 9 + Xkf * ( 3.5 )
% SF  = Xsf * ( 4 )
% ASD = 1.06 + 0.1114 * log10( fGHz ) + Xasd * ( 0.28 )
% ASA = 1.81 + Xasa * ( 0.2 )
% ESD = 0.75 + Xesd * ( 0.4 )
% ESA = 0.95 + Xesa * ( 0.16 )
% XPR = 8 + Xxpr * ( 4 )

DS_mu =               -6.955        % delay spread [log10(s)] @ 1 GHz
DS_sigma =             0.66         % delay spread STD [log10(s)] 
DS_gamma =            -0.0963       % freq.-dep. of DS [log10(s)/log10(GHz)]

KF_mu =                9            % Ricean K-factor [dB] 
KF_sigma =             3.5          % Ricean K-factor STD [dB] 

SF_sigma =             4            % Shadow Fading STD [dB] 

AS_D_mu =              1.06         % azimuth of departure angle spread [log10(deg)] @ 1 GHz
AS_D_sigma =           0.28         % azimuth of departure angle spread STD [log10(deg)] 
AS_D_gamma =           0.1114       % freq.-dep. of ASD [log10(deg)/log10(GHz)]

AS_A_mu =              1.81         % azimuth of arrival angle spread [log10(deg)] 
AS_A_sigma =           0.2          % azimuth of arrival angle spread STD [log10(deg)] 

ES_D_mu =              0.75         % elevation of departure angle spread [log10(deg)] 
ES_D_sigma =           0.4          % elevation of departure angle spread STD [log10(deg)] 
ES_D_mu_min =         -0.5          % minimum ESD reference value [log10(deg)]
ES_D_mu_A =           -2.1          % TX-RX 2D dist.-dep. of ESD [log10(deg)/km]

ES_A_mu =              0.95         % elevation of arrival angle spread [log10(deg)] 
ES_A_sigma =           0.16         % elevation of arrival angle spread STD [log10(deg)] 

XPR_mu =               8            % cross-polarization ratio [dB] 
XPR_sigma =            4            % cross-polarization ratio STD [dB] 

% ==================================================================================================
% Large-Scale fading decorrelation distances
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p43 Table 7.5-6 
% ==================================================================================================

DS_lambda =            30           % DS decorrelation distance [m]
KF_lambda =            12           % KF decorrelation distance [m]
SF_lambda =            37           % SF decorrelation distance [m]
AS_D_lambda =          18           % ASD decorrelation distance [m]
AS_A_lambda =          15           % ASA decorrelation distance [m]
ES_D_lambda =          15           % ESD decorrelation distance [m]
ES_A_lambda =          15           % ESA decorrelation distance [m]
XPR_lambda =           15           % XPR decorrelation distance [m]

% ==================================================================================================
% Inter-parameter correlations
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p43 Table 7.5-6 
% ==================================================================================================

%         DS     KF     SF     ASD    ASA    ESD    ESA    XPR
%     |   1     -0.4   -0.4    0.4    0.8   -0.2    0      0     | DS 
%     |  -0.4    1      0      0     -0.2    0      0      0     | KF 
%     |  -0.4    0      1     -0.5   -0.5    0     -0.8    0     | SF 
% R = |   0.4    0     -0.5    1      0      0.5    0      0     | ASD 
%     |   0.8   -0.2   -0.5    0      1     -0.3    0.4    0     | ASA 
%     |  -0.2    0      0      0.5   -0.3    1      0      0     | ESD 
%     |   0      0     -0.8    0      0.4    0      1      0     | ESA 
%     |   0      0      0      0      0      0      0      1     | XPR 

ds_kf =               -0.4          % DS vs. KF
ds_sf =               -0.4          % DS vs. SF
asD_ds =               0.4          % DS vs. ASD
asA_ds =               0.8          % DS vs. ASA
esD_ds =              -0.2          % DS vs. ESD
asA_kf =              -0.2          % KF vs. ASA
asD_sf =              -0.5          % SF vs. ASD
asA_sf =              -0.5          % SF vs. ASA
esA_sf =              -0.8          % SF vs. ESA
esD_asD =              0.5          % ASD vs. ESD
esD_asA =             -0.3          % ASA vs. ESD
esA_asA =              0.4          % ASA vs. ESA

% ==================================================================================================
% Model parameters
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p43 Table 7.5-6 
% ==================================================================================================

NumClusters =          12           % number of clusters
NumSubPaths =          20           % number of paths per (NLOS) cluster
SubpathMethod =        legacy       % subpath mapping method (legacy, Laplacian or mmMAGIC)

r_DS =                 2.5          % delay scaling factor
LNS_ksi =              3            % per cluster shadowing STD [dB]

PerClusterDS =         6.5622       % cluster delay spread [ns]
PerClusterDS_gamma =  -3.4084       % freq.-dep. of cluster delay spread [ns/log10(GHz)]
PerClusterDS_min =     0.25         % minumum cluster delay spread [ns]
PerClusterAS_D =       5            % cluster azimuth of departure angle spread [deg]
PerClusterAS_A =       11           % cluster azimuth of arrival angle spread [deg]
PerClusterES_D =       5            % cluster elevation of departure angle spread [deg]
PerClusterES_A =       7            % cluster elevation of arrival angle spread [deg]

% ==================================================================================================
% Decorrelation distance for the small-scale fading spatial consistency
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p54 Table 7.6.3.1-2 
% ==================================================================================================

SC_lambda =            40           % decorrelation distance [m]; 0 = disabled

% ==================================================================================================
% Path-loss model
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p27 Table 7.4.1-1 
% ==================================================================================================

% Formula for dual-slope (LOS) pathloss model:
% (Distance in meters, frequency in GHz)
%
%     PL = PL1 for d2D <= dBP | PL2 for d2D > dBP
%    PL1 = A1 * log10( d3D ) + B + C * log10( fGHz ) + D * d3D
%    PL2 = PL1( dBP ) + A2 * log10( d3D / dBP )
%    dBP = E * ( hBS-hE ) * ( hMS-hE ) * fGHz

PL_model =             dual_slope
PL_A1 =                22           % TX-RX 3D dist.-dep. of the PL before break-point [dB/log10(m)]
PL_A2 =                40           % TX-RX 3D dist.-dep. of the PL after break-point [dB/log10(m)]
PL_B =                 28           % Reference PL in [dB] @ 1 GHz, 1 m TX-RX dist.
PL_C =                 20           % Freq.-dep. of the PL in [dB/log10(GHz)]
PL_E =                 13.34        % Breakpoint scaling factor
PL_hE =                1            % Environment height in [m]

