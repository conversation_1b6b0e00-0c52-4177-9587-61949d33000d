% Config file for scenario "3GPP_38.901_Indoor_NLOS"
% See: 3GPP TR 38.901 v16.1.0 (2019-12)

% Valid frequency range:        0.5 to 100 GHz
% Valid distance range:         1 to 150 m
% Valid BS antenna height:      3 m
% Valid MT antenna height:      1 m

% ==================================================================================================
% Large scale distributions
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p46 Table 7.5-6 
% ==================================================================================================

% DS  = -7.173 - 0.28 * log10( 1 + fGHz ) + Xds * ( 0.055 + 0.1 * log10( 1 + fGHz ) )
% KF  = -100 + Xkf * ( 0 )
% SF  = Xsf * ( 8.03 )
% ASD = 1.62 + Xasd * ( 0.25 )
% ASA = 1.863 - 0.11 * log10( 1 + fGHz ) + Xasa * ( 0.059 + 0.12 * log10( 1 + fGHz ) )
% ESD = 1.08 + Xesd * ( 0.36 )
% ESA = 1.387 - 0.15 * log10( 1 + fGHz ) + Xesa * ( 0.746 - 0.09 * log10( 1 + fGHz ) )
% XPR = 10 + Xxpr * ( 4 )

DS_mu =               -7.173        % delay spread [log10(s)] @ 0 GHz
DS_sigma =             0.055        % delay spread STD [log10(s)] @ 0 GHz
DS_omega =             1            % reference frequency offset for the DS [GHz]
DS_gamma =            -0.28         % freq.-dep. of DS [log10(s)/log10(GHz)]
DS_delta =             0.1          % freq.-dep. of DS STD [log10(s)/log10(GHz)]

KF_mu =               -100          % Ricean K-factor [dB] 
KF_sigma =             0            % Ricean K-factor STD [dB] 

SF_sigma =             8.03         % Shadow Fading STD [dB] 

AS_D_mu =              1.62         % azimuth of departure angle spread [log10(deg)] 
AS_D_sigma =           0.25         % azimuth of departure angle spread STD [log10(deg)] 

AS_A_mu =              1.863        % azimuth of arrival angle spread [log10(deg)] @ 0 GHz
AS_A_sigma =           0.059        % azimuth of arrival angle spread STD [log10(deg)] @ 0 GHz
AS_A_omega =           1            % reference frequency offset for the ASA [GHz]
AS_A_gamma =          -0.11         % freq.-dep. of ASA [log10(deg)/log10(GHz)]
AS_A_delta =           0.12         % freq.-dep. of ASA STD [log10(deg)/log10(GHz)]

ES_D_mu =              1.08         % elevation of departure angle spread [log10(deg)] 
ES_D_sigma =           0.36         % elevation of departure angle spread STD [log10(deg)] 

ES_A_mu =              1.387        % elevation of arrival angle spread [log10(deg)] @ 0 GHz
ES_A_sigma =           0.746        % elevation of arrival angle spread STD [log10(deg)] @ 0 GHz
ES_A_omega =           1            % reference frequency offset for the ESA [GHz]
ES_A_gamma =          -0.15         % freq.-dep. of ESA [log10(deg)/log10(GHz)]
ES_A_delta =          -0.09         % freq.-dep. of ESA STD [log10(deg)/log10(GHz)]

XPR_mu =               10           % cross-polarization ratio [dB] 
XPR_sigma =            4            % cross-polarization ratio STD [dB] 

% ==================================================================================================
% Large-Scale fading decorrelation distances
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p46 Table 7.5-6 
% ==================================================================================================

DS_lambda =            5            % DS decorrelation distance [m]
KF_lambda =            4            % KF decorrelation distance [m]
SF_lambda =            6            % SF decorrelation distance [m]
AS_D_lambda =          3            % ASD decorrelation distance [m]
AS_A_lambda =          3            % ASA decorrelation distance [m]
ES_D_lambda =          4            % ESD decorrelation distance [m]
ES_A_lambda =          4            % ESA decorrelation distance [m]
XPR_lambda =           4            % XPR decorrelation distance [m]

% ==================================================================================================
% Inter-parameter correlations
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p46 Table 7.5-6 
% ==================================================================================================

%         DS     KF     SF     ASD    ASA    ESD    ESA    XPR
%     |   1      0     -0.5    0.4    0     -0.27  -0.06   0     | DS 
%     |   0      1      0      0      0      0      0      0     | KF 
%     |  -0.5    0      1      0     -0.4    0      0      0     | SF 
% R = |   0.4    0      0      1      0      0.35   0.23   0     | ASD 
%     |   0      0     -0.4    0      1     -0.08   0.43   0     | ASA 
%     |  -0.27   0      0      0.35  -0.08   1      0.42   0     | ESD 
%     |  -0.06   0      0      0.23   0.43   0.42   1      0     | ESA 
%     |   0      0      0      0      0      0      0      1     | XPR 

ds_sf =               -0.5          % DS vs. SF
asD_ds =               0.4          % DS vs. ASD
esD_ds =              -0.27         % DS vs. ESD
esA_ds =              -0.06         % DS vs. ESA
asA_sf =              -0.4          % SF vs. ASA
esD_asD =              0.35         % ASD vs. ESD
esA_asD =              0.23         % ASD vs. ESA
esD_asA =             -0.08         % ASA vs. ESD
esA_asA =              0.43         % ASA vs. ESA
esD_esA =              0.42         % ESD vs. ESA

% ==================================================================================================
% Model parameters
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p46 Table 7.5-6 
% ==================================================================================================

NumClusters =          20           % number of clusters
NumSubPaths =          20           % number of paths per (NLOS) cluster
SubpathMethod =        legacy       % subpath mapping method (legacy, Laplacian or mmMAGIC)

r_DS =                 3            % delay scaling factor
LNS_ksi =              3            % per cluster shadowing STD [dB]

PerClusterAS_D =       5            % cluster azimuth of departure angle spread [deg]
PerClusterAS_A =       11           % cluster azimuth of arrival angle spread [deg]
PerClusterES_D =       8            % cluster elevation of departure angle spread [deg]
PerClusterES_A =       9            % cluster elevation of arrival angle spread [deg]

% ==================================================================================================
% Decorrelation distance for the small-scale fading spatial consistency
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p54 Table 7.6.3.1-2 
% ==================================================================================================

SC_lambda =            10           % decorrelation distance [m]; 0 = disabled

% ==================================================================================================
% Path-loss model
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p29 Table 7.4.1-1 
% ==================================================================================================

% Formula for dual-slope (LOS) pathloss model:
% (Distance in meters, frequency in GHz)
%
%     PL = PL1 for d2D <= dBP | PL2 for d2D > dBP
%    PL1 = A1 * log10( d3D ) + B + C * log10( fGHz ) + D * d3D
%    PL2 = PL1( dBP ) + A2 * log10( d3D / dBP )
%    dBP = E * ( hBS-hE ) * ( hMS-hE ) * fGHz

% Formula for 3GPP NLOS pathloss model:
% (Distances and heights in meters, frequency in GHz)
%
%    PLn =  An * log10( d3D )
%        +  Bn
%        +  Cn * log10( fGHz )
%        +  Dn * log10( hBS )
%        + D1n * log10( hBS ) / hBS
%        + D2n * log10( hBS ) / hBS^2
%        + D3n * hBS
%        +  En * log10( hUT )
%        + E1n * log10( hUT ) / hUT
%        + E2n * log10( hUT ) / hUT^2
%        + E3n * hUT
%        +  Fn * log10( hBS ) * log10( d3d )
%        + G1n * log10^2( G2n * hUT )
%
%     PL = max( PL_LOS, PLn ) 

PL_model =             nlos
PL_A1 =                17.3         % TX-RX 3D dist.-dep. of the LOS-PL before break-point [dB/log10(m)]
PL_A2 =                17.3         % TX-RX 3D dist.-dep. of the LOS-PL after break-point [dB/log10(m)]
PL_B =                 32.4         % Reference LOS-PL in [dB] @ 1 GHz, 1 m TX-RX dist.
PL_C =                 20           % Freq.-dep. of the LOS-PL in [dB/log10(GHz)]
PL_E =                 13.34        % Breakpoint scaling factor
PL_hE =                0            % Environment height in [m]
PL_An =                38.3         % TX-RX 3D dist.-dep. of NLOS-PL [dB/log10(m)]
PL_Bn =                17.3         % Reference NLOS-PL in [dB] @ 1 GHz, 1 m TX-RX dist.
PL_Cn =                24.9         % Freq.-dep. of the NLOS-PL in [dB/log10(GHz)]

