% Config file for scenario "3GPP_38.901_UMi_LOS"
% See: 3GPP TR 38.901 v16.1.0 (2019-12)

% Valid frequency range:        0.5 to 100 GHz
% Valid distance range:         10 to 5000 m
% Valid BS antenna height:      10 m
% Valid MT antenna height:      1.5 to 22.5 m

% ==================================================================================================
% Large scale distributions
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p43 Table 7.5-6 
% ==================================================================================================

% DS  = -7.14 - 0.24 * log10( 1 + fGHz ) + Xds * ( 0.38 )
% KF  = 9 + Xkf * ( 5 )
% SF  = Xsf * ( 4 )
% ASD = 1.21 - 0.05 * log10( 1 + fGHz ) + Xasd * ( 0.41 )
% ASA = 1.73 - 0.08 * log10( 1 + fGHz ) + Xasa * ( 0.28 + 0.014 * log10( 1 + fGHz ) )
% ESD = 0.83 + Xesd * ( 0.35 )
% ESA = 0.73 - 0.1 * log10( 1 + fGHz ) + Xesa * ( 0.34 - 0.04 * log10( 1 + fGHz ) )
% XPR = 9 + Xxpr * ( 3 )

DS_mu =               -7.14         % delay spread [log10(s)] @ 0 GHz
DS_sigma =             0.38         % delay spread STD [log10(s)] 
DS_omega =             1            % reference frequency offset for the DS [GHz]
DS_gamma =            -0.24         % freq.-dep. of DS [log10(s)/log10(GHz)]

# KF_mu =                100            % Ricean K-factor [dB] 
# KF_sigma =             0            % Ricean K-factor STD [dB] 
KF_mu =                9            % Ricean K-factor [dB] 
KF_sigma =             5            % Ricean K-factor STD [dB] 

SF_sigma =             4            % Shadow Fading STD [dB] 

AS_D_mu =              1.21         % azimuth of departure angle spread [log10(deg)] @ 0 GHz
AS_D_sigma =           0.41         % azimuth of departure angle spread STD [log10(deg)] 
AS_D_omega =           1            % reference frequency offset for the ASD [GHz]
AS_D_gamma =          -0.05         % freq.-dep. of ASD [log10(deg)/log10(GHz)]

AS_A_mu =              1.73         % azimuth of arrival angle spread [log10(deg)] @ 0 GHz
AS_A_sigma =           0.28         % azimuth of arrival angle spread STD [log10(deg)] @ 0 GHz
AS_A_omega =           1            % reference frequency offset for the ASA [GHz]
AS_A_gamma =          -0.08         % freq.-dep. of ASA [log10(deg)/log10(GHz)]
AS_A_delta =           0.014        % freq.-dep. of ASA STD [log10(deg)/log10(GHz)]

ES_D_mu =              0.83         % elevation of departure angle spread [log10(deg)] 
ES_D_sigma =           0.35         % elevation of departure angle spread STD [log10(deg)] 
ES_D_mu_min =         -0.21         % minimum ESD reference value [log10(deg)]
ES_D_mu_A =           -14.8         % TX-RX 2D dist.-dep. of ESD [log10(deg)/km]

ES_A_mu =              0.73         % elevation of arrival angle spread [log10(deg)] @ 0 GHz
ES_A_sigma =           0.34         % elevation of arrival angle spread STD [log10(deg)] @ 0 GHz
ES_A_omega =           1            % reference frequency offset for the ESA [GHz]
ES_A_gamma =          -0.1          % freq.-dep. of ESA [log10(deg)/log10(GHz)]
ES_A_delta =          -0.04         % freq.-dep. of ESA STD [log10(deg)/log10(GHz)]

XPR_mu =               9            % cross-polarization ratio [dB] 
XPR_sigma =            3            % cross-polarization ratio STD [dB] 

% ==================================================================================================
% Large-Scale fading decorrelation distances
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p43 Table 7.5-6 
% ==================================================================================================

DS_lambda =            7            % DS decorrelation distance [m]
KF_lambda =            15           % KF decorrelation distance [m]
SF_lambda =            10           % SF decorrelation distance [m]
AS_D_lambda =          8            % ASD decorrelation distance [m]
AS_A_lambda =          8            % ASA decorrelation distance [m]
ES_D_lambda =          12           % ESD decorrelation distance [m]
ES_A_lambda =          12           % ESA decorrelation distance [m]
XPR_lambda =           15           % XPR decorrelation distance [m]

% ==================================================================================================
% Inter-parameter correlations
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p43 Table 7.5-6 
% ==================================================================================================

%         DS     KF     SF     ASD    ASA    ESD    ESA    XPR
%     |   1     -0.7   -0.4    0.5    0.8    0      0.2    0     | DS 
%     |  -0.7    1      0.5   -0.2   -0.3    0      0      0     | KF 
%     |  -0.4    0.5    1     -0.5   -0.4    0      0      0     | SF 
% R = |   0.5   -0.2   -0.5    1      0.4    0.5    0.3    0     | ASD 
%     |   0.8   -0.3   -0.4    0.4    1      0      0      0     | ASA 
%     |   0      0      0      0.5    0      1      0      0     | ESD 
%     |   0.2    0      0      0.3    0      0      1      0     | ESA 
%     |   0      0      0      0      0      0      0      1     | XPR 

ds_kf =               -0.7          % DS vs. KF
ds_sf =               -0.4          % DS vs. SF
asD_ds =               0.5          % DS vs. ASD
asA_ds =               0.8          % DS vs. ASA
esA_ds =               0.2          % DS vs. ESA
sf_kf =                0.5          % KF vs. SF
asD_kf =              -0.2          % KF vs. ASD
asA_kf =              -0.3          % KF vs. ASA
asD_sf =              -0.5          % SF vs. ASD
asA_sf =              -0.4          % SF vs. ASA
asD_asA =              0.4          % ASD vs. ASA
esD_asD =              0.5          % ASD vs. ESD
esA_asD =              0.3          % ASD vs. ESA

% ==================================================================================================
% Model parameters
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p43 Table 7.5-6 
% ==================================================================================================

NumClusters =          12           % number of clusters
NumSubPaths =          20           % number of paths per (NLOS) cluster
SubpathMethod =        legacy       % subpath mapping method (legacy, Laplacian or mmMAGIC)

r_DS =                 3            % delay scaling factor
LNS_ksi =              3            % per cluster shadowing STD [dB]

PerClusterDS =         5            % cluster delay spread [ns]
PerClusterAS_D =       3            % cluster azimuth of departure angle spread [deg]
PerClusterAS_A =       17           % cluster azimuth of arrival angle spread [deg]
PerClusterES_D =       4            % cluster elevation of departure angle spread [deg]
PerClusterES_A =       7            % cluster elevation of arrival angle spread [deg]

% ==================================================================================================
% Decorrelation distance for the small-scale fading spatial consistency
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p54 Table 7.6.3.1-2 
% ==================================================================================================

SC_lambda =            12           % decorrelation distance [m]; 0 = disabled

% ==================================================================================================
% Path-loss model
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p27 Table 7.4.1-1 
% ==================================================================================================

% Formula for dual-slope (LOS) pathloss model:
% (Distance in meters, frequency in GHz)
%
%     PL = PL1 for d2D <= dBP | PL2 for d2D > dBP
%    PL1 = A1 * log10( d3D ) + B + C * log10( fGHz ) + D * d3D
%    PL2 = PL1( dBP ) + A2 * log10( d3D / dBP )
%    dBP = E * ( hBS-hE ) * ( hMS-hE ) * fGHz

PL_model =             dual_slope
PL_A1 =                21           % TX-RX 3D dist.-dep. of the PL before break-point [dB/log10(m)]
PL_A2 =                40           % TX-RX 3D dist.-dep. of the PL after break-point [dB/log10(m)]
PL_B =                 32.4         % Reference PL in [dB] @ 1 GHz, 1 m TX-RX dist.
PL_C =                 20           % Freq.-dep. of the PL in [dB/log10(GHz)]
PL_E =                 13.34        % Breakpoint scaling factor
PL_hE =                1            % Environment height in [m]

