% Config file for scenario "3GPP_38.901_RMa_LOS"
% See: 3GPP TR 38.901 v16.1.0 (2019-12)

% Valid frequency range:        0.5 to 7 GHz
% Valid distance range:         10 to 10000 m
% Valid BS antenna height:      10 to 150 m
% Valid MT antenna height:      1 to 10 m

% ==================================================================================================
% Large scale distributions
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p46 Table 7.5-6 
% ==================================================================================================

% DS  = -7.49 + Xds * ( 0.55 )
% KF  = 7 + Xkf * ( 4 )
% SF  = Xsf * ( 5 )
% ASD = 0.9 + Xasd * ( 0.38 )
% ASA = 1.52 + Xasa * ( 0.24 )
% ESD = 0.22 + Xesd * ( 0.34 )
% ESA = 0.47 + Xesa * ( 0.4 )
% XPR = 12 + Xxpr * ( 4 )

DS_mu =               -7.49         % delay spread [log10(s)] 
DS_sigma =             0.55         % delay spread STD [log10(s)] 

KF_mu =                7            % Ricean K-factor [dB] 
KF_sigma =             4            % Ricean K-factor STD [dB] 

SF_sigma =             5            % Shadow Fading STD [dB] 

AS_D_mu =              0.9          % azimuth of departure angle spread [log10(deg)] 
AS_D_sigma =           0.38         % azimuth of departure angle spread STD [log10(deg)] 

AS_A_mu =              1.52         % azimuth of arrival angle spread [log10(deg)] 
AS_A_sigma =           0.24         % azimuth of arrival angle spread STD [log10(deg)] 

ES_D_mu =              0.22         % elevation of departure angle spread [log10(deg)] 
ES_D_sigma =           0.34         % elevation of departure angle spread STD [log10(deg)] 
ES_D_mu_min =         -1            % minimum ESD reference value [log10(deg)]
ES_D_mu_A =           -0.17         % TX-RX 2D dist.-dep. of ESD [log10(deg)/km]

ES_A_mu =              0.47         % elevation of arrival angle spread [log10(deg)] 
ES_A_sigma =           0.4          % elevation of arrival angle spread STD [log10(deg)] 

XPR_mu =               12           % cross-polarization ratio [dB] 
XPR_sigma =            4            % cross-polarization ratio STD [dB] 

% ==================================================================================================
% Large-Scale fading decorrelation distances
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p46 Table 7.5-6 
% ==================================================================================================

DS_lambda =            50           % DS decorrelation distance [m]
KF_lambda =            40           % KF decorrelation distance [m]
SF_lambda =            37           % SF decorrelation distance [m]
AS_D_lambda =          25           % ASD decorrelation distance [m]
AS_A_lambda =          35           % ASA decorrelation distance [m]
ES_D_lambda =          15           % ESD decorrelation distance [m]
ES_A_lambda =          15           % ESA decorrelation distance [m]
XPR_lambda =           20           % XPR decorrelation distance [m]

% ==================================================================================================
% Inter-parameter correlations
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p46 Table 7.5-6 
% ==================================================================================================

%         DS     KF     SF     ASD    ASA    ESD    ESA    XPR
%     |   1      0     -0.5    0      0     -0.05   0.27   0     | DS 
%     |   0      1      0      0      0      0     -0.02   0     | KF 
%     |  -0.5    0      1      0      0      0.01  -0.17   0     | SF 
% R = |   0      0      0      1      0      0.73  -0.14   0     | ASD 
%     |   0      0      0      0      1     -0.2    0.24   0     | ASA 
%     |  -0.05   0      0.01   0.73  -0.2    1     -0.07   0     | ESD 
%     |   0.27  -0.02  -0.17  -0.14   0.24  -0.07   1      0     | ESA 
%     |   0      0      0      0      0      0      0      1     | XPR 

ds_sf =               -0.5          % DS vs. SF
esD_ds =              -0.05         % DS vs. ESD
esA_ds =               0.27         % DS vs. ESA
esA_kf =              -0.02         % KF vs. ESA
esD_sf =               0.01         % SF vs. ESD
esA_sf =              -0.17         % SF vs. ESA
esD_asD =              0.73         % ASD vs. ESD
esA_asD =             -0.14         % ASD vs. ESA
esD_asA =             -0.2          % ASA vs. ESD
esA_asA =              0.24         % ASA vs. ESA
esD_esA =             -0.07         % ESD vs. ESA

% ==================================================================================================
% Model parameters
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p46 Table 7.5-6 
% ==================================================================================================

NumClusters =          11           % number of clusters
NumSubPaths =          20           % number of paths per (NLOS) cluster
SubpathMethod =        legacy       % subpath mapping method (legacy, Laplacian or mmMAGIC)

r_DS =                 3.8          % delay scaling factor
LNS_ksi =              3            % per cluster shadowing STD [dB]

PerClusterAS_D =       2            % cluster azimuth of departure angle spread [deg]
PerClusterAS_A =       3            % cluster azimuth of arrival angle spread [deg]
PerClusterES_D =       3            % cluster elevation of departure angle spread [deg]
PerClusterES_A =       3            % cluster elevation of arrival angle spread [deg]

% ==================================================================================================
% Decorrelation distance for the small-scale fading spatial consistency
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p54 Table 7.6.3.1-2 
% ==================================================================================================

SC_lambda =            50           % decorrelation distance [m]; 0 = disabled

% ==================================================================================================
% Path-loss model
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p27 Table 7.4.1-1 
% ==================================================================================================

% Formula for dual-slope (LOS) pathloss model:
% (Distance in meters, frequency in GHz)
%
%     PL = PL1 for d2D <= dBP | PL2 for d2D > dBP
%    PL1 = A1 * log10( d3D ) + B + C * log10( fGHz ) + D * d3D
%    PL2 = PL1( dBP ) + A2 * log10( d3D / dBP )
%    dBP = E * ( hBS-hE ) * ( hMS-hE ) * fGHz

PL_model =             dual_slope
PL_A1 =                20.478       % TX-RX 3D dist.-dep. of the PL before break-point [dB/log10(m)]
PL_A2 =                40           % TX-RX 3D dist.-dep. of the PL after break-point [dB/log10(m)]
PL_B =                 31.741       % Reference PL in [dB] @ 1 GHz, 1 m TX-RX dist.
PL_C =                 20           % Freq.-dep. of the PL in [dB/log10(GHz)]
PL_D =                 0.0014       % TX-RX 3D dist.-dep. of PL [dB/m]
PL_E =                 13.34        % Breakpoint scaling factor
PL_hE =                1            % Environment height in [m]
PL_sig1 =              4            % Shadow Fading STD before breakpoint [dB]
PL_sig2 =              6            % Shadow Fading STD after breakpoint [dB]

