% Config file for scenario "3GPP_38.901_InF_NLOS_SL"
% 3GPP Indoor Factory Non Line Of Sight SL (Sparse clutter, Low BS)
% See: 3GPP TR 38.901 v16.1.0

% Updated on 09.june.2020  <PERSON> (<EMAIL>) / 10.june 2020
% ==================================================================================================
% Large scale distributions
% See: 3GPP TR 38.901 v16.1.0 Table 7.5-6 Part-3
% ==================================================================================================
% The DS depends on the hall size. log10(26*(V/S)+14)-9.35 
% w = 120 m, l = 60 m, h = 10 m
% hall volume = 72000 m3 and surface area s = 18000 m2 (small hall)

DS_mu =               -7.2582       % delay spread [log10(s)]   Depends on hall volume
DS_sigma =             0.19         % delay spread STD [log10(s)] 

KF_mu =               -100          % Ricean K-factor [dB] 
KF_sigma =             0            % Ricean K-factor STD [dB] 

SF_sigma =             5.7          % Shadow Fading STD [dB] 

AS_D_mu =              1.57         % azimuth of departure angle spread [log10(deg)] 
AS_D_sigma =           0.20         % azimuth of departure angle spread STD [log10(deg)] 

AS_A_mu =              1.72         % azimuth of arrival angle spread [log10(deg)] 
AS_A_sigma =           0.30         % azimuth of arrival angle spread STD [log10(deg)]
AS_A_omega =           0            % reference frequency offset for the ASA [GHz]
AS_A_gamma =           0            % freq.-dep. of ASA [log10(deg)/log10(GHz)]
AS_A_delta =           0            % freq.-dep. of ASA STD [log10(deg)/log10(GHz)]

ES_D_mu =              1.2          % elevation of departure angle spread [log10(deg)] 
ES_D_sigma =           0.55         % elevation of departure angle spread STD [log10(deg)] 
ES_D_omega =           0            % reference frequency offset for the ESD [GHz]
ES_D_gamma =           0            % freq.-dep. of ESD [log10(deg)/log10(GHz)]
ES_D_delta =           0            % freq.-dep. of ESD STD [log10(deg)/log10(GHz)]

ES_A_mu =              1.45         % elevation of arrival angle spread [log10(deg)] @ 3.5 GHz
ES_A_sigma =           0.45         % elevation of arrival angle spread STD [log10(deg)] @ 3.5 GHz
ES_A_omega =           1            % reference frequency offset for the ESA [GHz]
ES_A_gamma =          -0.13         % freq.-dep. of ESA [log10(deg)/log10(GHz)]
ES_A_delta =           0            % freq.-dep. of ESA STD [log10(deg)/log10(GHz)]

XPR_mu =               11           % cross-polarization ratio [dB] 
XPR_sigma =            6            % cross-polarization ratio STD [dB] 

% ==================================================================================================
% Model parameters
% See: 3GPP TR 38.901 v16.1.0 Table 7.5-6 Part-3
% ==================================================================================================

NumClusters =          26           % number of clusters  (25 NLOS cluster + 1 dummy LOS cluster)
NumSubPaths =          20           % number of paths per (NLOS) cluster
SubpathMethod =        legacy       % subpath mapping method (legacy or mmMAGIC)

r_DS =                 3            % delay scaling factor
LNS_ksi =              3            % per cluster shadowing STD [dB]

PerClusterAS_D =       5            % cluster azimuth of departure angle spread [deg]
PerClusterAS_A =       8            % cluster azimuth of arrival angle spread [deg]
PerClusterES_D =       7            % cluster elevation of departure angle spread [deg]
PerClusterES_A =       9            % cluster elevation of arrival angle spread [deg]

% ==================================================================================================
% Absolute time of arrival model parameters (optional feature)
% See: 3GPP TR 38.901 Section 7.6.9
% ==================================================================================================

absTOA_mu =           -7.5          % absolute time of arrival offset reference value [log10(s)]
absTOA_sigma =         0.4          % absolute time of arrival offset referenece STD [log10(s)]
absTOA_lambda =        6           % absolute time of arrival offset decorrelation distance [m]

% ==================================================================================================
% Large-Scale fading decorrelation distances
% See: 3GPP TR 38.901 v16.1.0 Table 7.5-6 Part-3
% ==================================================================================================

DS_lambda =            10           % DS decorrelation distance [m]
KF_lambda =            10           % KF decorrelation distance [m]
SF_lambda =            10           % SF decorrelation distance [m]
AS_D_lambda =          10           % ASD decorrelation distance [m]
AS_A_lambda =          10           % ASD decorrelation distance [m]
ES_D_lambda =          10           % ESD decorrelation distance [m]
ES_A_lambda =          10           % ESD decorrelation distance [m]
XPR_lambda =           5            % XPR decorrelation distance [m]

% ==================================================================================================
% Decorrelation distance for the small-scale fading spatial consistency
% See: 3GPP TR 38.901 v15.0.0 (2018-06) p47 Table 7.6.3.1-2
% ==================================================================================================

SC_lambda =            10           % decorrelation distance [m]; 0 = disabled

% ==================================================================================================
% Path-loss model
% See: 3GPP TR 38.901 v16.1.0 Table 7.4.1-1
% ==================================================================================================
% Formula for dual-slope (LOS) pathloss model:
% (Distance in meters, frequency in GHz)
%
%     PL = PL1 for d2D <= dBP | PL2 for d2D > dBP
%    PL1 = A1 * log10( d3D ) + B + C * log10( fGHz ) + D * d3D
%    PL2 = PL1( dBP ) + A2 * log10( d3D / dBP )
%    dBP = E * ( hBS-hE ) * ( hMS-hE ) * fGHz
%
% PL2 can be also written as  
%    PL2 = A1* log10(dBP) + A2 * log10(d3D) - A2 *log10(dBP) + B + C * log10(fGHz) + D * d3D
% If Model "logdist" is used for LOS  set A1 and A2 to identical values => dBP is not relevant 

% Formula for 3GPP NLOS pathloss model:
% (Distances and heights in meters, frequency in GHz)
%
%    PLn =  An * log10( d3D )
%        +  Bn
%        +  Cn * log10( fGHz )
%        +  Dn * log10( hBS )
%        + D1n * log10( hBS ) / hBS
%        + D2n * log10( hBS ) / hBS^2
%        + D3n * hBS
%        +  En * log10( hUT )
%        + E1n * log10( hUT ) / hUT
%        + E2n * log10( hUT ) / hUT^2
%        + E3n * hUT
%        +  Fn * log10( hBS ) * log10( d3d )
%        + G1n * log10^2( G2 * hUT )
%
%%    PL = max( PL_LOS, PLn )

PL_model =             nlos
PL_A1 =                21.5         % TX-RX 3D dist.-dep. of LOS-PL before breakpoint [dB/log10(m)]
PL_A2 =                21.5         % TX-RX 3D dist.-dep. of LOS-PL after breakpoint [dB/log10(m)]  => for InF "logdist"  ==> second slope identical
PL_B =                 31.84        % reference LOS-PL in [dB] @ 1 GHz, 1 m TX-RX dist.
PL_C =                 19           % freq.-dep. of the LOS-PL in [dB/log10(GHz)]
PL_E =                 13.34        % breakpoint scaling factor (4e9 / c = 13.34)  ==> only relevant for LOS dualslope model   
PL_hE =                0            % environment height in [m] (not relevant), only relevant for LOS dualslope model 
PL_An =                25.5         % TX-RX 3D dist.-dep. of NLOS-PL [dB/log10(m)]
PL_Bn =                33           % reference NLOS-PL in [dB]
PL_Cn =                20           % freq.-dep. of the NLOS-PL in [dB/log10(GHz)]

