% Config file for scenario "3GPP_38.901_InF_LOS"
% 3GPP Indoor Factory Line Of Sight
% See: 3GPP TR 38.901 v16.1.0

% ==================================================================================================
% Large scale distributions
% See: 3GPP TR 38.901 v16.1.0 Table 7.5-6 Part-3
% ==================================================================================================
% Updated on 09.june.2020  <PERSON> (<EMAIL>) / 10.june 2020  eberleet (ernst.e<PERSON><EMAIL>)

% The DS depends on the hall size. log10(26*(V/S)+14)-9.35 
% Two halls are defined by TR38.901
% w = 120 m, l = 60 m, h = 10 m   => hall volume = 72000 m3 and surface area s = 18000 m2   ==> DS_mu = -7.2781
% w = 300 m, l = 150 m, h = 10 m  => hall volume = 450000 m3 and surface area s = 99000 m2  ==> DS_mu = -7.2288
% average: (-7.2781 - 7.2288)/2 = -7.2535  

DS_mu =               -7.2535       % delay spread [log10(s)] -> avg. of two hall size since LOS doesn't have a hall size specified
DS_sigma =             0.15         % delay spread STD [log10(s)] 

KF_mu =                7            % Ricean K-factor [dB] 
KF_sigma =             8            % Ricean K-factor STD [dB] 

SF_sigma =             4            % Shadow Fading STD [dB] 

AS_D_mu =              1.56         % azimuth of departure angle spread [log10(deg)] 
AS_D_sigma =           0.25         % azimuth of departure angle spread STD [log10(deg)] 

AS_A_mu =              1.78         % azimuth of arrival angle spread [log10(deg)]
AS_A_sigma =           0.2          % azimuth of arrival angle spread STD [log10(deg)] 
AS_A_omega =           1            % reference frequency offset for the ASA [GHz]
AS_A_gamma =          -0.18         % freq.-dep. of ASA [log10(deg)/log10(GHz)]
AS_A_delta =           0.12         % freq.-dep. of ASA STD [log10(deg)/log10(GHz)]

ES_D_mu =              1.35         % elevation of departure angle spread [log10(deg)] 
ES_D_sigma =           0.35         % elevation of departure angle spread STD [log10(deg)] 

ES_A_mu =              1.5          % elevation of arrival angle spread [log10(deg)] 
ES_A_sigma =           0.35         % elevation of arrival angle spread STD [log10(deg)] 
ES_A_omega =           1            % reference frequency offset for the ESA [GHz]
ES_A_gamma =          -0.2          % freq.-dep. of ESA [log10(deg)/log10(GHz)]
ES_A_delta =           0            % freq.-dep. of ESA STD [log10(deg)/log10(GHz)]

XPR_mu =               12           % cross-polarization ratio [dB] 
XPR_sigma =            6            % cross-polarization ratio STD [dB] 

% ==================================================================================================
% Model parameters
% See: 3GPP TR 38.901 v16.1.0 Table 7.5-6 Part-3
% ==================================================================================================

NumClusters =          25           % number of clusters
NumSubPaths =          20           % number of paths per (NLOS) cluster
SubpathMethod =        legacy       % subpath mapping method (legacy or mmMAGIC)

r_DS =                 2.7          % delay scaling factor
LNS_ksi =              4            % per cluster shadowing STD [dB]

PerClusterAS_D =       5            % cluster azimuth of departure angle spread [deg]
PerClusterAS_A =       8            % cluster azimuth of arrival angle spread [deg]
PerClusterES_D =       7            % cluster elevation of departure angle spread [deg]
PerClusterES_A =       9            % cluster elevation of arrival angle spread [deg]

% ==================================================================================================
% Large-Scale fading decorrelation distances
% See: 3GPP TR 38.901 v16.1.0 Table 7.5-6 Part-3
% ==================================================================================================

DS_lambda =            10           % DS decorrelation distance [m]
KF_lambda =            10           % KF decorrelation distance [m]
SF_lambda =            10           % SF decorrelation distance [m]
AS_D_lambda =          10           % ASD decorrelation distance [m]
AS_A_lambda =          10           % ASD decorrelation distance [m]
ES_D_lambda =          10           % ESD decorrelation distance [m]
ES_A_lambda =          10           % ESD decorrelation distance [m]
XPR_lambda =           5            % XPR decorrelation distance [m]

% ==================================================================================================
% Decorrelation distance for the small-scale fading spatial consistency
% See: 3GPP TR 38.901 v16.0.0 Table 7.6.3.1-2
% ==================================================================================================

SC_lambda =            10           % decorrelation distance [m]; 0 = disabled

% ==================================================================================================
% Inter-parameter correlations
% See: 3GPP TR 38.901 v16.1.0 p41 Table 7.5-6 Part-3
% ==================================================================================================

asD_kf =              -0.5          % ASD vs. KF
ds_kf =               -0.7          % DS vs. KF

% ==================================================================================================
% Path-loss model
% See: 3GPP TR 38.901 v16.0.0 Table 7.4.1-1
% ==================================================================================================
% Formula for Hata pathloss model model:
% (Distances are in meters, frequencies in GHz)
%
%    PL = A * log10( d3D ) + B + C * log10( fGHz )

PL_model =             logdist
PL_A =                 21.50        % TX-RX 3D dist.-dep. of PL [dB/log10(m)]
PL_B =                 31.84        % reference PL in [dB] @ 1 GHz, 1 m TX-RX dist.
PL_C =                 19           % freq.-dep. of the PL in [dB/log10(GHz)]

