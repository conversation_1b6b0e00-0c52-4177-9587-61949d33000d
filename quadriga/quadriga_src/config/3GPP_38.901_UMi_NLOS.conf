% Config file for scenario "3GPP_38.901_UMi_NLOS"
% See: 3GPP TR 38.901 v16.1.0 (2019-12)

% Valid frequency range:        0.5 to 100 GHz
% Valid distance range:         10 to 5000 m
% Valid BS antenna height:      10 m
% Valid MT antenna height:      1.5 to 22.5 m

% ==================================================================================================
% Large scale distributions
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p43 Table 7.5-6 
% ==================================================================================================

% DS  = -6.83 - 0.24 * log10( 1 + fGHz ) + Xds * ( 0.28 + 0.16 * log10( 1 + fGHz ) )
% KF  = -100 + Xkf * ( 0 )
% SF  = Xsf * ( 7.82 )
% ASD = 1.53 - 0.23 * log10( 1 + fGHz ) + Xasd * ( 0.33 + 0.11 * log10( 1 + fGHz ) )
% ASA = 1.81 - 0.08 * log10( 1 + fGHz ) + Xasa * ( 0.3 + 0.05 * log10( 1 + fGHz ) )
% ESD = 0.2 + Xesd * ( 0.35 )
% ESA = 0.92 - 0.04 * log10( 1 + fGHz ) + Xesa * ( 0.41 - 0.07 * log10( 1 + fGHz ) )
% XPR = 8 + Xxpr * ( 3 )

DS_mu =               -6.83         % delay spread [log10(s)] @ 0 GHz
DS_sigma =             0.28         % delay spread STD [log10(s)] @ 0 GHz
DS_omega =             1            % reference frequency offset for the DS [GHz]
DS_gamma =            -0.24         % freq.-dep. of DS [log10(s)/log10(GHz)]
DS_delta =             0.16         % freq.-dep. of DS STD [log10(s)/log10(GHz)]

KF_mu =               -100          % Ricean K-factor [dB] 
KF_sigma =             0            % Ricean K-factor STD [dB] 

SF_sigma =             7.82         % Shadow Fading STD [dB] 

AS_D_mu =              1.53         % azimuth of departure angle spread [log10(deg)] @ 0 GHz
AS_D_sigma =           0.33         % azimuth of departure angle spread STD [log10(deg)] @ 0 GHz
AS_D_omega =           1            % reference frequency offset for the ASD [GHz]
AS_D_gamma =          -0.23         % freq.-dep. of ASD [log10(deg)/log10(GHz)]
AS_D_delta =           0.11         % freq.-dep. of ASD STD [log10(deg)/log10(GHz)]

AS_A_mu =              1.81         % azimuth of arrival angle spread [log10(deg)] @ 0 GHz
AS_A_sigma =           0.3          % azimuth of arrival angle spread STD [log10(deg)] @ 0 GHz
AS_A_omega =           1            % reference frequency offset for the ASA [GHz]
AS_A_gamma =          -0.08         % freq.-dep. of ASA [log10(deg)/log10(GHz)]
AS_A_delta =           0.05         % freq.-dep. of ASA STD [log10(deg)/log10(GHz)]

ES_D_mu =              0.2          % elevation of departure angle spread [log10(deg)] 
ES_D_sigma =           0.35         % elevation of departure angle spread STD [log10(deg)] 
ES_D_mu_min =         -0.5          % minimum ESD reference value [log10(deg)]
ES_D_mu_A =           -3.1          % TX-RX 2D dist.-dep. of ESD [log10(deg)/km]

ES_A_mu =              0.92         % elevation of arrival angle spread [log10(deg)] @ 0 GHz
ES_A_sigma =           0.41         % elevation of arrival angle spread STD [log10(deg)] @ 0 GHz
ES_A_omega =           1            % reference frequency offset for the ESA [GHz]
ES_A_gamma =          -0.04         % freq.-dep. of ESA [log10(deg)/log10(GHz)]
ES_A_delta =          -0.07         % freq.-dep. of ESA STD [log10(deg)/log10(GHz)]

XPR_mu =               8            % cross-polarization ratio [dB] 
XPR_sigma =            3            % cross-polarization ratio STD [dB] 

% ==================================================================================================
% Large-Scale fading decorrelation distances
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p43 Table 7.5-6 
% ==================================================================================================

DS_lambda =            10           % DS decorrelation distance [m]
KF_lambda =            10           % KF decorrelation distance [m]
SF_lambda =            13           % SF decorrelation distance [m]
AS_D_lambda =          10           % ASD decorrelation distance [m]
AS_A_lambda =          9            % ASA decorrelation distance [m]
ES_D_lambda =          10           % ESD decorrelation distance [m]
ES_A_lambda =          10           % ESA decorrelation distance [m]
XPR_lambda =           15           % XPR decorrelation distance [m]

% ==================================================================================================
% Inter-parameter correlations
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p43 Table 7.5-6 
% ==================================================================================================

%         DS     KF     SF     ASD    ASA    ESD    ESA    XPR
%     |   1      0     -0.7    0      0.4   -0.5    0      0     | DS 
%     |   0      1      0      0      0      0      0      0     | KF 
%     |  -0.7    0      1      0     -0.4    0      0      0     | SF 
% R = |   0      0      0      1      0      0.5    0.5    0     | ASD 
%     |   0.4    0     -0.4    0      1      0      0.2    0     | ASA 
%     |  -0.5    0      0      0.5    0      1      0      0     | ESD 
%     |   0      0      0      0.5    0.2    0      1      0     | ESA 
%     |   0      0      0      0      0      0      0      1     | XPR 

ds_sf =               -0.7          % DS vs. SF
asA_ds =               0.4          % DS vs. ASA
esD_ds =              -0.5          % DS vs. ESD
asA_sf =              -0.4          % SF vs. ASA
esD_asD =              0.5          % ASD vs. ESD
esA_asD =              0.5          % ASD vs. ESA
esA_asA =              0.2          % ASA vs. ESA

% ==================================================================================================
% Model parameters
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p43 Table 7.5-6 
% ==================================================================================================

NumClusters =          20           % number of clusters
NumSubPaths =          20           % number of paths per (NLOS) cluster
SubpathMethod =        legacy       % subpath mapping method (legacy, Laplacian or mmMAGIC)

r_DS =                 2.1          % delay scaling factor
LNS_ksi =              3            % per cluster shadowing STD [dB]

PerClusterDS =         11           % cluster delay spread [ns]
PerClusterAS_D =       10           % cluster azimuth of departure angle spread [deg]
PerClusterAS_A =       22           % cluster azimuth of arrival angle spread [deg]
PerClusterES_D =       4            % cluster elevation of departure angle spread [deg]
PerClusterES_A =       7            % cluster elevation of arrival angle spread [deg]

% ==================================================================================================
% Decorrelation distance for the small-scale fading spatial consistency
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p54 Table 7.6.3.1-2 
% ==================================================================================================

SC_lambda =            15           % decorrelation distance [m]; 0 = disabled

% ==================================================================================================
% Path-loss model
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p27 Table 7.4.1-1 
% ==================================================================================================

% Formula for dual-slope (LOS) pathloss model:
% (Distance in meters, frequency in GHz)
%
%     PL = PL1 for d2D <= dBP | PL2 for d2D > dBP
%    PL1 = A1 * log10( d3D ) + B + C * log10( fGHz ) + D * d3D
%    PL2 = PL1( dBP ) + A2 * log10( d3D / dBP )
%    dBP = E * ( hBS-hE ) * ( hMS-hE ) * fGHz

% Formula for 3GPP NLOS pathloss model:
% (Distances and heights in meters, frequency in GHz)
%
%    PLn =  An * log10( d3D )
%        +  Bn
%        +  Cn * log10( fGHz )
%        +  Dn * log10( hBS )
%        + D1n * log10( hBS ) / hBS
%        + D2n * log10( hBS ) / hBS^2
%        + D3n * hBS
%        +  En * log10( hUT )
%        + E1n * log10( hUT ) / hUT
%        + E2n * log10( hUT ) / hUT^2
%        + E3n * hUT
%        +  Fn * log10( hBS ) * log10( d3d )
%        + G1n * log10^2( G2n * hUT )
%
%     PL = max( PL_LOS, PLn ) 

PL_model =             nlos
PL_A1 =                21           % TX-RX 3D dist.-dep. of the LOS-PL before break-point [dB/log10(m)]
PL_A2 =                40           % TX-RX 3D dist.-dep. of the LOS-PL after break-point [dB/log10(m)]
PL_B =                 32.4         % Reference LOS-PL in [dB] @ 1 GHz, 1 m TX-RX dist.
PL_C =                 20           % Freq.-dep. of the LOS-PL in [dB/log10(GHz)]
PL_E =                 13.34        % Breakpoint scaling factor
PL_hE =                1            % Environment height in [m]
PL_An =                35.3         % TX-RX 3D dist.-dep. of NLOS-PL [dB/log10(m)]
PL_Bn =                22.85        % Reference NLOS-PL in [dB] @ 1 GHz, 1 m TX-RX dist.
PL_Cn =                21.3         % Freq.-dep. of the NLOS-PL in [dB/log10(GHz)]
PL_E3n =              -0.3          % RX height-dep. of the NLOS-PL in [dB/m]

