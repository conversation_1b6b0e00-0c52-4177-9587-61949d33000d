% Config file for scenario "3GPP_38.901_UMi_LOS_O2I"
% See: 3GPP TR 38.901 v16.1.0 (2019-12)

% Valid frequency range:        0.5 to 100 GHz
% Valid distance range:         10 to 5000 m
% Valid BS antenna height:      10 m
% Valid MT antenna height:      1.5 to 22.5 m

% ==================================================================================================
% Large scale distributions
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p43 Table 7.5-6 
% ==================================================================================================

% DS  = -6.62 + Xds * ( 0.32 )
% KF  = -100 + Xkf * ( 0 )
% SF  = Xsf * ( 7 )
% ASD = 1.25 + Xasd * ( 0.42 )
% ASA = 1.76 + Xasa * ( 0.16 )
% ESD = 0.83 + Xesd * ( 0.35 )
% ESA = 1.01 + Xesa * ( 0.43 )
% XPR = 9 + Xxpr * ( 5 )

DS_mu =               -6.62         % delay spread [log10(s)] 
DS_sigma =             0.32         % delay spread STD [log10(s)] 

KF_mu =               -100          % Ricean K-factor [dB] 
KF_sigma =             0            % Ricean K-factor STD [dB] 

SF_sigma =             7            % Shadow Fading STD [dB] 

AS_D_mu =              1.25         % azimuth of departure angle spread [log10(deg)] 
AS_D_sigma =           0.42         % azimuth of departure angle spread STD [log10(deg)] 

AS_A_mu =              1.76         % azimuth of arrival angle spread [log10(deg)] 
AS_A_sigma =           0.16         % azimuth of arrival angle spread STD [log10(deg)] 

ES_D_mu =              0.83         % elevation of departure angle spread [log10(deg)] 
ES_D_sigma =           0.35         % elevation of departure angle spread STD [log10(deg)] 
ES_D_mu_min =         -0.21         % minimum ESD reference value [log10(deg)]
ES_D_mu_A =           -14.8         % TX-RX 2D dist.-dep. of ESD [log10(deg)/km]

ES_A_mu =              1.01         % elevation of arrival angle spread [log10(deg)] 
ES_A_sigma =           0.43         % elevation of arrival angle spread STD [log10(deg)] 

XPR_mu =               9            % cross-polarization ratio [dB] 
XPR_sigma =            5            % cross-polarization ratio STD [dB] 

% ==================================================================================================
% Large-Scale fading decorrelation distances
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p43 Table 7.5-6 
% ==================================================================================================

DS_lambda =            10           % DS decorrelation distance [m]
KF_lambda =            10           % KF decorrelation distance [m]
SF_lambda =            7            % SF decorrelation distance [m]
AS_D_lambda =          11           % ASD decorrelation distance [m]
AS_A_lambda =          17           % ASA decorrelation distance [m]
ES_D_lambda =          25           % ESD decorrelation distance [m]
ES_A_lambda =          25           % ESA decorrelation distance [m]
XPR_lambda =           15           % XPR decorrelation distance [m]

% ==================================================================================================
% Inter-parameter correlations
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p43 Table 7.5-6 
% ==================================================================================================

%         DS     KF     SF     ASD    ASA    ESD    ESA    XPR
%     |   1      0     -0.5    0.4    0.4   -0.6   -0.2    0     | DS 
%     |   0      1      0      0      0      0      0      0     | KF 
%     |  -0.5    0      1      0.2    0      0      0      0     | SF 
% R = |   0.4    0      0.2    1      0     -0.2    0      0     | ASD 
%     |   0.4    0      0      0      1      0      0.5    0     | ASA 
%     |  -0.6    0      0     -0.2    0      1      0.5    0     | ESD 
%     |  -0.2    0      0      0      0.5    0.5    1      0     | ESA 
%     |   0      0      0      0      0      0      0      1     | XPR 

ds_sf =               -0.5          % DS vs. SF
asD_ds =               0.4          % DS vs. ASD
asA_ds =               0.4          % DS vs. ASA
esD_ds =              -0.6          % DS vs. ESD
esA_ds =              -0.2          % DS vs. ESA
asD_sf =               0.2          % SF vs. ASD
esD_asD =             -0.2          % ASD vs. ESD
esA_asA =              0.5          % ASA vs. ESA
esD_esA =              0.5          % ESD vs. ESA

% ==================================================================================================
% Model parameters
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p43 Table 7.5-6 
% ==================================================================================================

NumClusters =          13           % number of clusters
NumSubPaths =          20           % number of paths per (NLOS) cluster
SubpathMethod =        legacy       % subpath mapping method (legacy, Laplacian or mmMAGIC)

r_DS =                 2.2          % delay scaling factor
LNS_ksi =              4            % per cluster shadowing STD [dB]

PerClusterDS =         11           % cluster delay spread [ns]
PerClusterAS_D =       5            % cluster azimuth of departure angle spread [deg]
PerClusterAS_A =       8            % cluster azimuth of arrival angle spread [deg]
PerClusterES_D =       4            % cluster elevation of departure angle spread [deg]
PerClusterES_A =       3            % cluster elevation of arrival angle spread [deg]

% ==================================================================================================
% Decorrelation distance for the small-scale fading spatial consistency
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p54 Table 7.6.3.1-2 
% ==================================================================================================

SC_lambda =            15           % decorrelation distance [m]; 0 = disabled

% ==================================================================================================
% Path-loss model
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p27 Table 7.4.1-1 
% ==================================================================================================

% Formula for dual-slope (LOS) pathloss model:
% (Distance in meters, frequency in GHz)
%
%     PL = PL1 for d2D <= dBP | PL2 for d2D > dBP
%    PL1 = A1 * log10( d3D ) + B + C * log10( fGHz ) + D * d3D
%    PL2 = PL1( dBP ) + A2 * log10( d3D / dBP )
%    dBP = E * ( hBS-hE ) * ( hMS-hE ) * fGHz

PL_model =             dual_slope
PL_A1 =                21           % TX-RX 3D dist.-dep. of the PL before break-point [dB/log10(m)]
PL_A2 =                40           % TX-RX 3D dist.-dep. of the PL after break-point [dB/log10(m)]
PL_B =                 32.4         % Reference PL in [dB] @ 1 GHz, 1 m TX-RX dist.
PL_C =                 20           % Freq.-dep. of the PL in [dB/log10(GHz)]
PL_E =                 13.34        % Breakpoint scaling factor
PL_hE =                1            % Environment height in [m]

