% Config file for scenario "3GPP_38.901_Indoor_LOS"
% See: 3GPP TR 38.901 v16.1.0 (2019-12)

% Valid frequency range:        0.5 to 100 GHz
% Valid distance range:         1 to 150 m
% Valid BS antenna height:      3 m
% Valid MT antenna height:      1 m

% ==================================================================================================
% Large scale distributions
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p46 Table 7.5-6 
% ==================================================================================================

% DS  = -7.692 - 0.01 * log10( 1 + fGHz ) + Xds * ( 0.18 )
% KF  = 7 + Xkf * ( 4 )
% SF  = Xsf * ( 3 )
% ASD = 1.6 + Xasd * ( 0.18 )
% ASA = 1.781 - 0.19 * log10( 1 + fGHz ) + Xasa * ( 0.119 + 0.12 * log10( 1 + fGHz ) )
% ESD = 2.228 - 1.43 * log10( 1 + fGHz ) + Xesd * ( 0.3 + 0.13 * log10( 1 + fGHz ) )
% ESA = 1.44 - 0.26 * log10( 1 + fGHz ) + Xesa * ( 0.264 - 0.04 * log10( 1 + fGHz ) )
% XPR = 11 + Xxpr * ( 4 )

DS_mu =               -7.692        % delay spread [log10(s)] @ 0 GHz
DS_sigma =             0.18         % delay spread STD [log10(s)] 
DS_omega =             1            % reference frequency offset for the DS [GHz]
DS_gamma =            -0.01         % freq.-dep. of DS [log10(s)/log10(GHz)]

KF_mu =                7            % Ricean K-factor [dB] 
KF_sigma =             4            % Ricean K-factor STD [dB] 

SF_sigma =             3            % Shadow Fading STD [dB] 

AS_D_mu =              1.6          % azimuth of departure angle spread [log10(deg)] 
AS_D_sigma =           0.18         % azimuth of departure angle spread STD [log10(deg)] 

AS_A_mu =              1.781        % azimuth of arrival angle spread [log10(deg)] @ 0 GHz
AS_A_sigma =           0.119        % azimuth of arrival angle spread STD [log10(deg)] @ 0 GHz
AS_A_omega =           1            % reference frequency offset for the ASA [GHz]
AS_A_gamma =          -0.19         % freq.-dep. of ASA [log10(deg)/log10(GHz)]
AS_A_delta =           0.12         % freq.-dep. of ASA STD [log10(deg)/log10(GHz)]

ES_D_mu =              2.228        % elevation of departure angle spread [log10(deg)] @ 0 GHz
ES_D_sigma =           0.3          % elevation of departure angle spread STD [log10(deg)] @ 0 GHz
ES_D_omega =           1            % reference frequency offset for the ESD [GHz]
ES_D_gamma =          -1.43         % freq.-dep. of ESD [log10(deg)/log10(GHz)]
ES_D_delta =           0.13         % freq.-dep. of ESD STD [log10(deg)/log10(GHz)]

ES_A_mu =              1.44         % elevation of arrival angle spread [log10(deg)] @ 0 GHz
ES_A_sigma =           0.264        % elevation of arrival angle spread STD [log10(deg)] @ 0 GHz
ES_A_omega =           1            % reference frequency offset for the ESA [GHz]
ES_A_gamma =          -0.26         % freq.-dep. of ESA [log10(deg)/log10(GHz)]
ES_A_delta =          -0.04         % freq.-dep. of ESA STD [log10(deg)/log10(GHz)]

XPR_mu =               11           % cross-polarization ratio [dB] 
XPR_sigma =            4            % cross-polarization ratio STD [dB] 

% ==================================================================================================
% Large-Scale fading decorrelation distances
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p46 Table 7.5-6 
% ==================================================================================================

DS_lambda =            8            % DS decorrelation distance [m]
KF_lambda =            4            % KF decorrelation distance [m]
SF_lambda =            10           % SF decorrelation distance [m]
AS_D_lambda =          7            % ASD decorrelation distance [m]
AS_A_lambda =          5            % ASA decorrelation distance [m]
ES_D_lambda =          4            % ESD decorrelation distance [m]
ES_A_lambda =          4            % ESA decorrelation distance [m]
XPR_lambda =           5            % XPR decorrelation distance [m]

% ==================================================================================================
% Inter-parameter correlations
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p46 Table 7.5-6 
% ==================================================================================================

%         DS     KF     SF     ASD    ASA    ESD    ESA    XPR
%     |   1     -0.5   -0.8    0.6    0.8    0.1    0.2    0     | DS 
%     |  -0.5    1      0.5    0      0      0      0.1    0     | KF 
%     |  -0.8    0.5    1     -0.4   -0.5    0.2    0.3    0     | SF 
% R = |   0.6    0     -0.4    1      0.4    0.5    0      0     | ASD 
%     |   0.8    0     -0.5    0.4    1      0      0.5    0     | ASA 
%     |   0.1    0      0.2    0.5    0      1      0      0     | ESD 
%     |   0.2    0.1    0.3    0      0.5    0      1      0     | ESA 
%     |   0      0      0      0      0      0      0      1     | XPR 

ds_kf =               -0.5          % DS vs. KF
ds_sf =               -0.8          % DS vs. SF
asD_ds =               0.6          % DS vs. ASD
asA_ds =               0.8          % DS vs. ASA
esD_ds =               0.1          % DS vs. ESD
esA_ds =               0.2          % DS vs. ESA
sf_kf =                0.5          % KF vs. SF
esA_kf =               0.1          % KF vs. ESA
asD_sf =              -0.4          % SF vs. ASD
asA_sf =              -0.5          % SF vs. ASA
esD_sf =               0.2          % SF vs. ESD
esA_sf =               0.3          % SF vs. ESA
asD_asA =              0.4          % ASD vs. ASA
esD_asD =              0.5          % ASD vs. ESD
esA_asA =              0.5          % ASA vs. ESA

% ==================================================================================================
% Model parameters
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p46 Table 7.5-6 
% ==================================================================================================

NumClusters =          15           % number of clusters
NumSubPaths =          20           % number of paths per (NLOS) cluster
SubpathMethod =        legacy       % subpath mapping method (legacy, Laplacian or mmMAGIC)

r_DS =                 3.6          % delay scaling factor
LNS_ksi =              6            % per cluster shadowing STD [dB]

PerClusterAS_D =       5            % cluster azimuth of departure angle spread [deg]
PerClusterAS_A =       8            % cluster azimuth of arrival angle spread [deg]
PerClusterES_D =       7            % cluster elevation of departure angle spread [deg]
PerClusterES_A =       9            % cluster elevation of arrival angle spread [deg]

% ==================================================================================================
% Decorrelation distance for the small-scale fading spatial consistency
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p54 Table 7.6.3.1-2 
% ==================================================================================================

SC_lambda =            10           % decorrelation distance [m]; 0 = disabled

% ==================================================================================================
% Path-loss model
% See: 3GPP TR 38.901 v16.1.0 (2019-12); p29 Table 7.4.1-1 
% ==================================================================================================

% Formula for Hata pathloss model:
% (Distance in meters, frequency in GHz)
%
%    PL = A * log10( d3D ) + B + C * log10( fGHz )

PL_model =             logdist
PL_A =                 17.3         % TX-RX 3D dist.-dep. of the PL [dB/log10(m)]
PL_B =                 32.4         % Reference PL in [dB] @ 1 GHz, 1 m TX-RX dist.
PL_C =                 20           % Freq.-dep. of the PL in [dB/log10(GHz)]

