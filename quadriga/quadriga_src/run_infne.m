warning('off','all');

scenario_path = "/home/<USER>/quadriga/ne_v0/";
scenario_name = '3GPP_38.901_InF_SH'; % 3GPP_38.901_UMi_NLOS, 3GPP_38.901_UMi_LOS, 3GPP_38.901_UMa_NLOS, 3GPP_38.901_UMa_LOS, O2I
bs_num = int64(3);
ue_num = int64(1000);
spilt_ue = int64(1);
spilt_ue_num = int64(10);
spilt_ue_seg = int64(ue_num/spilt_ue_num);
disp("start run");
disp(datetime);

speed = 0/3.6; %3km/h->m/s
time_unit = 0.02; %s
angle_base_array = (rand(1,spilt_ue_num)*2-1)*pi/6;
dist_base_array = round(rand(1,spilt_ue_num)*10)+15;

angle_speed = -pi/3;

indoor = 1; % indoor=1, outdoor=0

no_snapshots = 1;
myseed = 0723;
set_los = false;
%schedule = true; %设置是否选择最佳cell
schedule=false;
addpath(rmpath('./config'));
addpath(genpath('./config'));

my_rng = rng;
if myseed > 0
    seed = int64(myseed);
else
    seed = my_rng.State(1,1);
end
rng(seed);
% check_my_rng = rng;
floder = scenario_path;

%仿真参数配置
s = qd_simulation_parameters;
s.center_frequency = 28e9;
s.use_3GPP_baseline = 0; %增强3GPP，能够支持移动
s.show_progress_bars = 0;%显示进度条
scenario = scenario_name;
scenario_base = scenario(1:15);
num_spilt = ue_num/spilt_ue;%spilt ue

%通信参数
BW = 48960e3*4; %10M频带 BW=60KHZ*12sc*16prb*17sub
nSub = 17; %子频带个数
indoor_percent = indoor; %室内用户比例

%基站和用户
no_bs = bs_num; %bs个数 
no_rx = ue_num; %用户数量

no_go_dist = 1; 
bs_height = 8; 
bs_isd = 50; % uma=500, umi=200

isd = bs_isd; %最大范围,uma=500,umi=200

%天线面板配置
downtilt = 0;
H_ants = 8;
V_ants = 4;
V_d = 0.5;
UE_H_ants = 1;
UE_V_ants = 2;
UE_port = UE_H_ants*UE_V_ants*2;
%模型选择，垂直天线数，水平天线数，中心频率，极化方式，下倾，天线间的间距，垂直面板个数，水平面板个数，垂直面板间距，水平面板间距
BS_array  = qd_arrayant( '3gpp-mmw', 1, 1, s.center_frequency, 3, downtilt, 0.5, V_ants, H_ants, V_d, 0.5 ); 
% rotate_pattern( BS_array,90,'x',1 );
UE_array = qd_arrayant('3gpp-mmw', 1, 1, s.center_frequency, 3, 0, 0.5, UE_V_ants, UE_H_ants, 0.5, 0.5);

% ========== 新增：计算天线增益 ==========
% 计算BS_array的增益
bs_element_indices = 1:BS_array.no_elements;  % 获取所有天线元素的索引
[bs_gain_dBi, bs_pow_max] = BS_array.calc_gain(bs_element_indices);

% 计算UE_array的增益
ue_element_indices = 1:UE_array.no_elements;  % 获取所有天线元素的索引
[ue_gain_dBi, ue_pow_max] = UE_array.calc_gain(ue_element_indices);

% 输出天线增益信息
fprintf('BS天线阵列增益信息:\n');
fprintf('  天线元素数量: %d\n', BS_array.no_elements);
fprintf('  最大增益: %.2f dBi\n', bs_pow_max);
fprintf('  归一化增益范围: %.2f dBi 到 %.2f dBi\n', min(bs_gain_dBi), max(bs_gain_dBi));

fprintf('UE天线阵列增益信息:\n');
fprintf('  天线元素数量: %d\n', UE_array.no_elements);
fprintf('  最大增益: %.2f dBi\n', ue_pow_max);
fprintf('  归一化增益范围: %.2f dBi 到 %.2f dBi\n', min(ue_gain_dBi), max(ue_gain_dBi));

cell_type = 'indoor'; %选择基站产生的小区类型，regular对应一个基站按照120度生成三个小区,0/120/240 hexagonal一个基站一个小区

% 如果使用regular，每个基站会生成3个扇区(0°, 120°, 240°)

if strcmp(cell_type, 'regular')
    sectors_per_bs = 3; % 每个基站3个扇区
    cell_number = no_bs * sectors_per_bs;
else
    sectors_per_bs = 1; % 每个基站1个小区
    cell_number = no_bs;
end


% 设置移动参数
spilt_ue_speed = speed;
spilt_ue_time_unit = time_unit;
time = time_unit*(no_snapshots - 1);
move_len = time*speed;
if no_snapshots > 1
    move_enable = true;
else
    move_enable = false;
end

% ========== 新增：创建天线增益文件 ==========
file_H_time = fopen(strcat(floder,'H_time.txt'),'w');
file_H_freq = fopen(strcat(floder,'H_freq.txt'),'w');
file_log = fopen(strcat(floder,'log.txt'),'w');
file_loc = fopen(strcat(floder,'loc.txt'),'w');
file_pg = fopen(strcat(floder,'pg.txt'),'w');
file_pg_all = fopen(strcat(floder,'pg_all.txt'),'w');
file_bs_gain = fopen(strcat(floder,'bs_gain.txt'),'w');  % 新增：BS增益文件
file_ue_gain = fopen(strcat(floder,'ue_gain.txt'),'w');  % 新增：UE增益文件
% 新增：邻区相关文件
file_neighbor = fopen(strcat(floder,'neighbor_info.txt'),'w');
file_rsrp = fopen(strcat(floder,'rsrp_matrix.txt'),'w');
file_serving_H_freq = fopen(strcat(floder,'serving_H_freq.txt'),'w');
file_neighbor_H_freq = fopen(strcat(floder,'neighbor_H_freq.txt'),'w');
file_cell_association = fopen(strcat(floder,'cell_association.txt'),'w');

fprintf(file_log,scenario_base);
fprintf(file_log,"\n");
fprintf(file_log,"random seed is %d\n",seed);
fprintf(file_log,"BS cell number is %d\n",cell_number);
fprintf(file_log,"UE number is %d\n",no_rx);

% ========== 新增：写入天线增益信息到日志 ==========
fprintf(file_log,"BS antenna elements: %d\n",BS_array.no_elements);
fprintf(file_log,"BS max gain: %.2f dBi\n",bs_pow_max);
fprintf(file_log,"UE antenna elements: %d\n",UE_array.no_elements);
fprintf(file_log,"UE max gain: %.2f dBi\n",ue_pow_max);

if schedule == true
    fprintf(file_log,"BS schedule is true\n");
else
    fprintf(file_log,"BS schedule is false - generating neighbor cell info\n");
end
fprintf(file_log,"H_ants is %d\n",H_ants);
fprintf(file_log,"V_ants is %d\n",V_ants);
fprintf(file_log,"UE Port is %d\n",UE_port);
fprintf(file_log,"nSub is %d\n",nSub);
fprintf(file_log,"sim time unit is %d\n",time_unit*1000);
fprintf(file_log,"sim time number is %d\n",no_snapshots);
fprintf(file_log,"sim par of cm\n");
fprintf(file_log,"UE are divided into %d parts\n",int64(num_spilt));
fprintf(file_log,"parts are divided into %d groups\n",int64(spilt_ue_num));
fprintf(file_log,"UE are divided by %d\n",int64(spilt_ue));

% ========== 新增：写入天线增益数据到文件 ==========
% 写入BS增益数据
fprintf(file_bs_gain,'%.6f ',bs_gain_dBi);
fprintf(file_bs_gain,"\n");
fprintf(file_bs_gain,'%.6f',bs_pow_max);
fprintf(file_bs_gain,"\n");

% 写入UE增益数据
fprintf(file_ue_gain,'%.6f ',ue_gain_dBi);
fprintf(file_ue_gain,"\n");
fprintf(file_ue_gain,'%.6f',ue_pow_max);
fprintf(file_ue_gain,"\n");

if strcmp(cell_type, 'regular')
    scene = qd_layout.generate('regular', no_bs, isd, BS_array);
else
    scene = qd_layout.generate('hexagonal', no_bs, isd, BS_array);
end
scene = qd_layout.generate('indoor', no_bs, isd, BS_array);
scene.simpar = s;
scene.tx_position(:,1) = [0,0,bs_height];
scene.tx_position(:,2) = [-isd,0,bs_height];
scene.tx_position(:,3) = [isd,0,bs_height];
scene.name = 'setsumi';
scene.rx_array = UE_array;
for bs_idx = 1:no_bs
    fprintf(file_log,"bs location: x: %.4f y: %.4f z: %.4f\n",scene.tx_position(1,bs_idx),scene.tx_position(2,bs_idx),scene.tx_position(3,bs_idx));
end

% dist_all = 0.45*isd*rand(spilt_ue);
% angle_all = pi/6*(2*rand(spilt_ue)-1);
% 新增：邻区信息计算函数
function [serving_cells, neighbor_cells_list, rsrp_matrix] = calculate_neighbor_info(cs_all, no_rx, no_bs, sectors_per_bs, rsrp_threshold)
    total_cells = no_bs * sectors_per_bs;
    rsrp_matrix = zeros(no_rx, total_cells);

    % 计算RSRP矩阵
    link_idx = 1;
    for bs_idx = 1:no_bs
        for sector_idx = 1:sectors_per_bs
            cell_idx = (bs_idx - 1) * sectors_per_bs + sector_idx;
            for ue_idx = 1:no_rx
                if link_idx <= numel(cs_all)
                    % 获取路径增益并转换为RSRP
                    path_gain_db = cs_all(1, link_idx).par.pg;
                    rsrp_matrix(ue_idx, cell_idx) = path_gain_db;
                    link_idx = link_idx + 1;
                end
            end
        end
    end

    % 确定服务小区和邻区
    serving_cells = zeros(1, no_rx);
    neighbor_cells_list = cell(1, no_rx);

    for ue_idx = 1:no_rx
        % 找到RSRP最高的小区作为服务小区
        [~, serving_cells(ue_idx)] = max(rsrp_matrix(ue_idx, :));

        % 按RSRP降序排列获取邻区
        [sorted_rsrp, sorted_cells] = sort(rsrp_matrix(ue_idx, :), 'descend');

        % 邻区为除服务小区外RSRP高于阈值的小区
        valid_neighbors = sorted_cells(2:end); % 排除服务小区
        valid_neighbor_rsrp = sorted_rsrp(2:end);
        valid_neighbors = valid_neighbors(valid_neighbor_rsrp > rsrp_threshold);

        neighbor_cells_list{ue_idx} = valid_neighbors;
    end
end

for spilt_idx = 1:num_spilt
    no_rx = spilt_ue;
    scene.no_rx = no_rx;

    %default move away from center
    move_len = rem(spilt_idx-1,spilt_ue_seg);
    move_idx = floor((double(spilt_idx)-1)/double(spilt_ue_seg));
    move_dist = spilt_ue_speed*spilt_ue_time_unit*(double(move_len));
    angle_base = angle_base_array(move_idx+1);
    dist_base = dist_base_array(move_idx+1);

    res_idx = rem( spilt_idx , 100 );
    if res_idx == 0
        fprintf("run %d datas\n",spilt_idx);
        disp(datetime);
    end

    % 随机撒UE的位置
    for ue_idx = 1: no_rx
        scene.rx_position(1,ue_idx) = dist_base*cos(angle_base) + move_dist*cos(angle_speed);
        scene.rx_position(2,ue_idx) = dist_base*sin(angle_base) + move_dist*sin(angle_speed);
        scene.rx_position(3,ue_idx) = 1.5;
    end

    for n = 1 : no_bs
        dist_r = sqrt((scene.rx_position(1,:) - scene.tx_position(1,n)).^2 + (scene.rx_position(2,:) - scene.tx_position(2,n)).^2);
        change_location = dist_r < no_go_dist;
        x_scale = scene.rx_position(1,:) - scene.tx_position(1,n);
        y_scale = scene.rx_position(2,:) - scene.tx_position(2,n);
        scene.rx_position(1,:) = scene.rx_position(1,:)+x_scale./dist_r.*(no_go_dist-dist_r).*change_location;
        scene.rx_position(2,:) = scene.rx_position(2,:)+y_scale./dist_r.*(no_go_dist-dist_r).*change_location;
    end

    % 设置移动轨迹
    if move_enable
        for n = 1 : scene.no_rx
            t = qd_track('linear',move_len,-pi/3);
            t.initial_position = scene.rx_position(:,n);
            t.movement_profile = [ 0,time;0,move_len]; 
            t.interpolate('time',0.02);
            t.name = scene.rx_name{1,n};
            scene.rx_track(1,n) = t;
        end
    end

    % 配置楼内楼外
    scene.set_scenario(scenario,[],[],indoor_percent);

    b = scene.init_builder;
    sic = size(b);
    for ib = 1 : numel(b)
        [ i1,i2 ] = qf.qind2sub( sic, ib );
        scenpar = b(i1,i2).scenpar;
        b(i1,i2).scenpar_nocheck = scenpar;
    end

    b_ue = b;
    b_ue = split_multi_freq( b_ue );
    gen_parameters( b_ue );
    cm = get_channels( b_ue );

    % reorder cm
    pattern = '\S+Tx(\d+)_Rx(\d+)';
    idx = zeros(numel(cm),1);
    for cm_idx = 1:numel(cm)
        cm_name = cm(1,cm_idx).name;    
        cm_result = regexp(cm_name, pattern, 'tokens');
        tx_idx = str2num(cm_result{1,1}{1,1});
        rx_idx = str2num(cm_result{1,1}{1,2});
        txrx_idx = (tx_idx-1)*no_rx + rx_idx;
        idx(cm_idx) = txrx_idx;
    end
    cm(1,idx) = cm(1,:);

    Num_ants_2pol = H_ants*V_ants*2;

    cs = cm;
    cs_origin = cs; % 保存所有小区的原始信道信息

    % 修改：计算邻区信息
    rsrp_threshold = -120; % dBm阈值，可调整
    [serving_cells, neighbor_cells_list, rsrp_matrix] = calculate_neighbor_info(cs_origin, no_rx, no_bs, sectors_per_bs, rsrp_threshold);

    % 修改：根据schedule参数决定输出哪些信道
    if schedule == true
        % 只输出服务小区信道
        best_indices = zeros(1, no_rx);
        for ue_idx = 1:no_rx
            serving_cell = serving_cells(ue_idx);
            best_indices(ue_idx) = (serving_cell - 1) * no_rx + ue_idx;
        end
        cm = cm(1, best_indices);
        cs = cs(1, best_indices);
    else
        % 保留所有小区信道用于邻区分析
        % cm和cs保持不变
    end

    for cm_idx = 1:numel(cm)
        fprintf(file_log,"sim cm %d name is %s,npath is %d,no_snap is %d\n",cm_idx,cm(1,cm_idx).name,cm(1,cm_idx).no_path,cm(1,cm_idx).no_snap);
    end

    for cs_idx = 1:numel(cs)
        fprintf(file_log,"sim cs %d name is %s,npath is %d,no_snap is %d\n",cs_idx,cs(1,cs_idx).name,cs(1,cs_idx).no_path,cs(1,cs_idx).no_snap);
    end

    % 新增：写入邻区信息
    for ue_idx = 1:no_rx
        fprintf(file_neighbor, 'UE%d: Serving_Cell=%d, Neighbors=[', ue_idx, serving_cells(ue_idx));
        neighbors = neighbor_cells_list{ue_idx};
        for i = 1:length(neighbors)
            if i < length(neighbors)
                fprintf(file_neighbor, '%d,', neighbors(i));
            else
                fprintf(file_neighbor, '%d', neighbors(i));
            end
        end
        fprintf(file_neighbor, ']\n');

        % 写入小区关联信息
        fprintf(file_cell_association, '%d %d\n', ue_idx, serving_cells(ue_idx));
    end

    % 新增：写入RSRP矩阵
    for ue_idx = 1:no_rx
        fprintf(file_rsrp, '%.2f ', rsrp_matrix(ue_idx, :));
        fprintf(file_rsrp, '\n');
    end

    %写入UE位置数据
    for ue_idx = 1 : no_rx
        no_snap = scene.rx_track(1,ue_idx).no_snapshots;
        for snap_idx = 1 : no_snap
            fprintf(file_loc,"%.4f ",scene.rx_track(1,ue_idx).positions(:,snap_idx)+scene.rx_track(1,ue_idx).initial_position);
            fprintf(file_loc,"\n");
        end
    end

    %写入能量数据和时域数据
    n_tr = numel(cs);
    for tr_idx = 1 : n_tr
        tr_builder = cs(1,tr_idx);
        H_time = tr_builder.coeff; 
        temp_H = [real(reshape(H_time,1,[]));imag(reshape(H_time,1,[]))];
        data = reshape(temp_H,1,[]);
        fprintf(file_H_time,'%.6g ',data);
        fprintf(file_H_time,"\n");
        fprintf(file_pg,'%.6f ',cs(1,tr_idx).par.pg);
        fprintf(file_pg,"\n");
    end

    % 修改：分别写入服务小区和邻区的频域信道信息
    for ue_idx = 1:no_rx
        % 服务小区信道
        serving_cell_idx = serving_cells(ue_idx);
        channel_idx = (serving_cell_idx - 1) * no_rx + ue_idx;

        if channel_idx <= numel(cs_origin)
            H_freq = cs_origin(1, channel_idx).fr(BW, nSub);
            temp_H = [real(reshape(H_freq,1,[])); imag(reshape(H_freq,1,[]))];
            data = reshape(temp_H,1,[]);
            fprintf(file_serving_H_freq,'%.6g ',data);
            fprintf(file_serving_H_freq,"\n");
        end

        % 邻区信道
        neighbors = neighbor_cells_list{ue_idx};
        for neighbor_cell_idx = neighbors
            channel_idx = (neighbor_cell_idx - 1) * no_rx + ue_idx;

            if channel_idx <= numel(cs_origin)
                H_freq = cs_origin(1, channel_idx).fr(BW, nSub);
                temp_H = [real(reshape(H_freq,1,[])); imag(reshape(H_freq,1,[]))];
                data = reshape(temp_H,1,[]);
                fprintf(file_neighbor_H_freq,'%.6g ',data);
                fprintf(file_neighbor_H_freq,"\n");
            end
        end
    end

    % 原有的频域数据写入（兼容性保持）
    for tr_idx = 1 : n_tr
        tr_builder = cs(1,tr_idx);
        H_freq = tr_builder.fr(BW,nSub);
        temp_H = [real(reshape(H_freq,1,[]));imag(reshape(H_freq,1,[]))];
        data = reshape(temp_H,1,[]);
        fprintf(file_H_freq,'%.6g ',data);
        fprintf(file_H_freq,"\n");
    end

    %写入所有pg数据
    n_tr = numel(cs_origin);
    for tr_idx = 1 : n_tr
        fprintf(file_pg_all,'%.6f ',cs_origin(1,tr_idx).par.pg);
        fprintf(file_pg_all,"\n");
    end

end

% ========== 修改：关闭所有文件 ==========
fclose(file_loc);
fclose(file_H_time);
fclose(file_pg);
fclose(file_H_freq);
fclose(file_log);
fclose(file_pg_all);
fclose(file_bs_gain);
fclose(file_ue_gain);
% 关闭新增的邻区相关文件
fclose(file_neighbor);
fclose(file_rsrp);
fclose(file_serving_H_freq);
fclose(file_neighbor_H_freq);
fclose(file_cell_association);

disp("end run");
disp(datetime);
result = "setsumi";