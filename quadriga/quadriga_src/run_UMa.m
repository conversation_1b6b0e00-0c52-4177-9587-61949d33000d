% function result = run(scenario_path,scenario_name,ue_num,spilt_ue,indoor,set_los,nfloor,bs_isd,myseed,schedule,no_snapshots)

warning('off','all');
    
scenario_path ="/home/<USER>/quadriga/UMa_v3_1000/";
scenario_name = '3GPP_38.901_UMa_NLOS'; % 3GPP_38.901_UMi_NLOS, 3GPP_38.901_UMi_LOS, 3GPP_38.901_UMa_NLOS, 3GPP_38.901_UMa_LOS, O2I
bs_num = int64(1);
ue_num = int64(10000);
spilt_ue = int64(1);
spilt_ue_num = int64(10);
spilt_ue_seg = int64(ue_num/spilt_ue_num);
disp("start run");
disp(datetime);

speed = 3/3.6; %3km/h->m/s
time_unit = 0.02; %s
angle_base_array = (rand(1,spilt_ue_num)*2-1)*pi/6;
dist_base_array = round(rand(1,spilt_ue_num)*10)+15;
% angle_base_array = [-pi, -4*pi/5, -3*pi/5, -2*pi/5, -1*pi/5, 0, 1*pi/5, 2*pi/5, 3*pi/5, 4*pi/5];
% dist_base_array = [5, 10, 15, 20, 25, 30, 35, 40, 45, 50];

% angle_base_array = [-pi/6,-pi/9,-pi/12,0,pi/12,pi/9,pi/6,-pi/6,-pi/9,-pi/12,0,pi/12,pi/9,pi/6,-pi/6,-pi/9,-pi/12,0,pi/12,pi/9,pi/6,-pi/6,-pi/9,-pi/12,0,pi/12,pi/9,pi/6];
% dist_base_array = [5, 5, 5, 5, 5, 5, 5, 10, 10, 10 ,10 ,10 ,10 ,10 ,15, 15 ,15, 15, 15, 15, 15, 20, 20 ,20, 20, 20, 20, 20];

% angle_speed = (rand(1,spilt_ue_num)*2-1)*pi/6;

indoor = 0.8; % indoor=1, outdoor=0

no_snapshots = 1;
myseed = 0723;
set_los = false;
schedule = true; %璁剧疆鏄惁閫夋嫨鏈?浣砪ell

% addpath(rmpath('/data/setsumi/beamforming/setsumi_env/quadriga_src'));
addpath(rmpath('./config'));
% addpath(genpath('/data/setsumi/beamforming/setsumi_env/quadriga_src'));
addpath(genpath('./config'));

my_rng = rng;
if myseed > 0
    seed = int64(myseed);
else
    seed = my_rng.State(1,1);
end
rng(seed);
% check_my_rng = rng;
floder = scenario_path;

%浠跨湡鍙傛暟閰嶇疆
s = qd_simulation_parameters;
s.center_frequency = 3.5e9;
s.use_3GPP_baseline = 0; %澧炲己3GPP锛岃兘澶熸敮鎸佺Щ鍔?
s.show_progress_bars = 0;%鏄剧ず杩涘害鏉?
scenario = scenario_name;
scenario_base = scenario(1:15);
num_spilt = ue_num/spilt_ue;%spilt ue

%閫氫俊鍙傛暟
BW = 48960e3*4; %10M棰戝甫 BW=60KHZ*12sc*16prb*17sub
nSub = 17; %瀛愰甯︿釜鏁?
indoor_percent = indoor; %瀹ゅ唴鐢ㄦ埛姣斾緥

%鍩虹珯鍜岀敤鎴?
no_bs = bs_num; %bs涓暟 
no_rx = ue_num; %鐢ㄦ埛鏁伴噺


no_go_dist = 35; %inf=0,umi=10,uma=35
bs_height = 25; %umi=10,uma=25,inf=3
bs_isd = 500; % uma=500, umi=200,inf=20

isd = bs_isd; %鏈?澶ц寖鍥?,uma=500,umi=200

%澶╃嚎闈㈡澘閰嶇疆
downtilt = 0;
H_ants = 8;
V_ants = 4;
V_d = 0.5;
UE_H_ants = 1;
UE_V_ants = 2;
UE_port = UE_H_ants*UE_V_ants*2;
%妯″瀷閫夋嫨锛屽瀭鐩村ぉ绾挎暟锛屾按骞冲ぉ绾挎暟锛屼腑蹇冮鐜囷紝鏋佸寲鏂瑰紡锛屼笅鍊撅紝澶╃嚎闂寸殑闂磋窛锛屽瀭鐩撮潰鏉夸釜鏁帮紝姘村钩闈㈡澘涓暟锛屽瀭鐩撮潰鏉块棿璺濓紝姘村钩闈㈡澘闂磋窛
BS_array  = qd_arrayant( '3gpp-mmw', 1, 1, s.center_frequency, 3, downtilt, 0.5, V_ants, H_ants, V_d, 0.5 ); 
% rotate_pattern( BS_array,90,'x',1 );
UE_array = qd_arrayant('3gpp-mmw', 1, 1, s.center_frequency, 3, 0, 0.5, UE_V_ants, UE_H_ants, 0.5, 0.5);

cell_type = 'indoor'; %閫夋嫨鍩虹珯浜х敓鐨勫皬鍖虹被鍨嬶紝regular瀵瑰簲涓?涓熀绔欐寜鐓?120搴︾敓鎴愪笁涓皬鍖?,0/120/240 hexagonal涓?涓熀绔欎竴涓皬鍖?

cell_number = no_bs;

% 璁剧疆绉诲姩鍙傛暟
spilt_ue_speed = speed;
spilt_ue_time_unit = time_unit;
time = time_unit*(no_snapshots - 1);
move_len = time*speed;
if no_snapshots > 1
    move_enable = true;
else
    move_enable = false;
end

file_H_time = fopen(strcat(floder,'H_time.txt'),'w');
file_H_freq = fopen(strcat(floder,'H_freq.txt'),'w');
file_log = fopen(strcat(floder,'log.txt'),'w');
file_loc = fopen(strcat(floder,'loc.txt'),'w');
file_pg = fopen(strcat(floder,'pg.txt'),'w');
file_pg_all = fopen(strcat(floder,'pg_all.txt'),'w');

fprintf(file_log,scenario_base);
fprintf(file_log,"\n");
fprintf(file_log,"random seed is %d\n",seed);
fprintf(file_log,"BS cell number is %d\n",cell_number);
fprintf(file_log,"UE number is %d\n",no_rx);

if schedule == true
    fprintf(file_log,"BS schedule is true\n");
end
fprintf(file_log,"H_ants is %d\n",H_ants);
fprintf(file_log,"V_ants is %d\n",V_ants);
fprintf(file_log,"UE Port is %d\n",UE_port);
fprintf(file_log,"nSub is %d\n",nSub);
fprintf(file_log,"sim time unit is %d\n",time_unit*1000);
fprintf(file_log,"sim time number is %d\n",no_snapshots);
fprintf(file_log,"sim par of cm\n");
fprintf(file_log,"UE are divided into %d parts\n",int64(num_spilt));
fprintf(file_log,"parts are divided into %d groups\n",int64(spilt_ue_num));
fprintf(file_log,"UE are divided by %d\n",int64(spilt_ue));

scene = qd_layout.generate('indoor', no_bs, isd, BS_array);
scene.simpar = s;
scene.tx_position(:,1) = [0,0,bs_height];
scene.tx_position(:,2) = [-isd,0,bs_height];
scene.tx_position(:,3) = [isd,0,bs_height];
scene.name = 'setsumi';
scene.rx_array = UE_array;
for bs_idx = 1:no_bs
    fprintf(file_log,"bs location: x: %.4f y: %.4f z: %.4f\n",scene.tx_position(1,bs_idx),scene.tx_position(2,bs_idx),scene.tx_position(3,bs_idx));
end

% dist_all = 0.45*isd*rand(spilt_ue);
% angle_all = pi/6*(2*rand(spilt_ue)-1);

for spilt_idx = 1:num_spilt
    no_rx = spilt_ue;
    scene.no_rx = no_rx;

    %default move away from center
    move_len = rem(spilt_idx-1,spilt_ue_seg);
    move_idx = floor((double(spilt_idx)-1)/double(spilt_ue_seg));
    move_dist = spilt_ue_speed*spilt_ue_time_unit*(double(move_len));
    angle_base = angle_base_array(move_idx+1);
    dist_base = dist_base_array(move_idx+1);
    
    res_idx = rem( spilt_idx , 100 );
    if res_idx == 0
        fprintf("run %d datas\n",spilt_idx);
        disp(datetime);
    end

    % 闅忔満鎾扷E鐨勪綅缃?
    for ue_idx = 1: no_rx
        % dist = 0.45*isd*rand(1);
        % angle = pi/3*(2*rand(1)-1);

        % dist = dist_base + move_dist;
        % angle = angle_base;
        
        % dist = dist_all(ue_idx);
        % angle = angle_all(ue_idx);
        % scene.rx_position(1,ue_idx) = dist*cos(angle);
        % scene.rx_position(2,ue_idx) = dist*sin(angle);
        % scene.rx_position(3,ue_idx) = 1.5;

        % scene.rx_position(1,ue_idx) = 0;
        % scene.rx_position(2,ue_idx) = 25;
        % scene.rx_position(3,ue_idx) = 1.5;

        scene.rx_position(1,ue_idx) = dist_base*cos(angle_base) + move_dist*cos(angle_base);
        scene.rx_position(2,ue_idx) = dist_base*sin(angle_base) + move_dist*sin(angle_base);
        scene.rx_position(3,ue_idx) = 1.5;
    end

    for n = 1 : no_bs
        dist_r = sqrt((scene.rx_position(1,:) - scene.tx_position(1,n)).^2 + (scene.rx_position(2,:) - scene.tx_position(2,n)).^2);
        change_location = dist_r < no_go_dist;
        x_scale = scene.rx_position(1,:) - scene.tx_position(1,n);
        y_scale = scene.rx_position(2,:) - scene.tx_position(2,n);
        scene.rx_position(1,:) = scene.rx_position(1,:)+x_scale./dist_r.*(no_go_dist-dist_r).*change_location;
        scene.rx_position(2,:) = scene.rx_position(2,:)+y_scale./dist_r.*(no_go_dist-dist_r).*change_location;
    end


    % 璁剧疆绉诲姩杞ㄨ抗
    if move_enable
        for n = 1 : scene.no_rx
            user_angle_speed = (rand(1)*2-1)*pi/6;  % 每个用户独立的角速度
            t = qd_track('linear',move_len,user_angle_speed);
            t.initial_position = scene.rx_position(:,n);
            t.movement_profile = [ 0,time;0,move_len]; 
            t.interpolate('time',0.02);
            t.name = scene.rx_name{1,n};
            scene.rx_track(1,n) = t;
        end
    end

    % 閰嶇疆妤煎唴妤煎
    % indoor_rx = scene.set_scenario(scenario,[],[],indoor_percent);
    scene.set_scenario(scenario,[],[],indoor_percent);

    % scene.visualize ( [] , [], 2 , 1);
    % view(-33,66)

    b = scene.init_builder;
    sic = size(b);
    for ib = 1 : numel(b)
        [ i1,i2 ] = qf.qind2sub( sic, ib );
        scenpar = b(i1,i2).scenpar;                 % Read scenario parameters
        % scenpar.SC_lambda = 0;                      % Disable spatial consistency of SSF
        b(i1,i2).scenpar_nocheck = scenpar;         % Save parameters without check (faster)
    end

    b_ue = b;
    b_ue = split_multi_freq( b_ue );                      % Split t he builders for multiple frequencies
    gen_parameters( b_ue );                            % Generate LSF and SSF parameters (uncorrelated)
    cm = get_channels( b_ue );                         % Generate channels

    % reorder cm
    pattern = '\S+Tx(\d+)_Rx(\d+)';
    idx = zeros(numel(cm),1);
    for cm_idx = 1:numel(cm)
        cm_name = cm(1,cm_idx).name;    
        cm_result = regexp(cm_name, pattern, 'tokens');
        tx_idx = str2num(cm_result{1,1}{1,1});
        rx_idx = str2num(cm_result{1,1}{1,2});
        txrx_idx = (tx_idx-1)*no_rx + rx_idx;
        idx(cm_idx) = txrx_idx;
    end
    cm(1,idx) = cm(1,:);

    Num_ants_2pol = H_ants*V_ants*2;
    % if cell_type == 'regular'
    %     cs = split_tx( cm, {1:Num_ants_2pol,Num_ants_2pol+1:Num_ants_2pol*2,Num_ants_2pol*2+1:Num_ants_2pol*3} );
    %     pattern = 'Tx(\d+)s(\d+)_Rx(\d+)';
    %     idx = zeros(numel(cs),1);
    %     for cs_idx = 1:numel(cs)
    %         cs_name = cs(1,cs_idx).name;    
    %         cs_result = regexp(cs_name, pattern, 'tokens');
    %         bx_idx = str2num(cs_result{1,1}{1,1});
    %         sector_idx = str2num(cs_result{1,1}{1,2});
    %         rx_idx = str2num(cs_result{1,1}{1,3});
    %         tx_idx = (bx_idx-1)*3 + sector_idx;
    %         txrx_idx = (tx_idx-1)*no_rx + rx_idx;
    %         idx(cs_idx) = txrx_idx;
    %     end
    %     cs(1,idx) = cs(1,:);
    % else
    %     cs = cm;
    % end
    cs = cm;



    % rsrp_all = zeros(no_bs*3, no_rx);
    % for ue_idx = 1:no_rx
    %     for bs_idx = 1:no_bs*3
    %         cs_idx = (bs_idx - 1)*no_rx + ue_idx;
    %         cs_data = cs(1,cs_idx).coeff;
    %         if(move_enable)
    %             tmp = cs_data(:,1,:,1);
    %         else
    %             tmp = cs_data(:,1,:);
    %         end
    %         rsrp = sum(abs(tmp(:).^2)/2);
    %         rsrp_all(bs_idx,ue_idx) = rsrp;
    %     end
    % end

    % [max_data,max_cell] = max(rsrp_all);
    % max_bs = ceil(max_cell/3);
    % best_bs = int64(max_bs-1)*no_rx + (1:no_rx);
    % best_cell = int64(max_cell-1)*no_rx + (1:no_rx);
    % cm_redo = cm(1,best_bs);
    % cs_redo = cs(1,best_cell); 

    max_cell = ones(1, no_rx);
    max_bs = ones(1, no_rx);
    best_bs = int64(max_bs-1)*no_rx + (1:no_rx);
    best_cell = int64(max_cell-1)*no_rx + (1:no_rx);
    cm_redo = cm(1,best_bs);
    cs_redo = cs(1,best_cell); 

    cs_origin = cs;
    if schedule == true
        cm = cm_redo;
        cs = cs_redo;
    end

    for cm_idx = 1:numel(cm)
        fprintf(file_log,"sim cm %d name is %s,npath is %d,no_snap is %d\n",cm_idx,cm(1,cm_idx).name,cm(1,cm_idx).no_path,cm(1,cm_idx).no_snap);
    end

    for cs_idx = 1:numel(cs)
        fprintf(file_log,"sim cs %d name is %s,npath is %d,no_snap is %d\n",cs_idx,cs(1,cs_idx).name,cs(1,cs_idx).no_path,cs(1,cs_idx).no_snap);
    end

    %鍐欏叆UE浣嶇疆鏁版嵁
    for ue_idx = 1 : no_rx
        no_snap = scene.rx_track(1,ue_idx).no_snapshots;
        for snap_idx = 1 : no_snap
            fprintf(file_loc,"%.4f ",scene.rx_track(1,ue_idx).positions(:,snap_idx)+scene.rx_track(1,ue_idx).initial_position);
            fprintf(file_loc,"\n");
        end
    end


    %鍐欏叆鑳介噺鏁版嵁
    %鍐欏叆鏃跺煙鏁版嵁
    n_tr = numel(cs);
    for tr_idx = 1 : n_tr
        tr_builder = cs(1,tr_idx);
        H_time = tr_builder.coeff; 
        temp_H = [real(reshape(H_time,1,[]));imag(reshape(H_time,1,[]))];
        data = reshape(temp_H,1,[]);
        fprintf(file_H_time,'%.6g ',data);
        fprintf(file_H_time,"\n");
        fprintf(file_pg,'%.6f ',cs(1,tr_idx).par.pg);
        fprintf(file_pg,"\n");
    end

    %鍐欏叆棰戝煙鏁版嵁
    
    for tr_idx = 1 : n_tr
        tr_builder = cs(1,tr_idx);
        H_freq = tr_builder.fr(BW,nSub);
        temp_H = [real(reshape(H_freq,1,[]));imag(reshape(H_freq,1,[]))];
        data = reshape(temp_H,1,[]);
        fprintf(file_H_freq,'%.6g ',data);
        fprintf(file_H_freq,"\n");
    end

    %鍐欏叆鎵?鏈塸g鏁版嵁
    n_tr = numel(cs_origin);
    for tr_idx = 1 : n_tr
        fprintf(file_pg_all,'%.6f ',cs_origin(1,tr_idx).par.pg);
        fprintf(file_pg_all,"\n");
    end

end

fclose(file_loc);
fclose(file_H_time);
fclose(file_pg);
fclose(file_H_freq);
fclose(file_log);
fclose(file_pg_all);

disp("end run");
disp(datetime);
result = "setsumi";