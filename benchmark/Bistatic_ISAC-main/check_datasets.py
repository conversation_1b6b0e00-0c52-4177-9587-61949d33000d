# 检查datasets.py是否正确提取了q字段
import os
from datasets import create_dataset

snr_val = [-10, -5, 5, 15]

# 检查每个 SNR 值对应的数据路径
for snr in snr_val:
    # 使用正确的字符串格式化方法
    data_path = f'/home/<USER>/benchmark/Bistatic_ISAC-main/data/Bistatic_data_with_ToA_Nrtargets_classif/Bistatic_data_with_ToA_Nrtargets_classif_{snr}_dB'
    
    # 添加检查代码
    if not os.path.exists(data_path):
        print(f"原始数据文件不存在: {data_path}")
    else:
        print(f"找到数据文件: {data_path}")
