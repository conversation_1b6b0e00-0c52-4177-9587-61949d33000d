import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np

# ====== 你的模型定义（只放 CNN 版本） ======
from complexPyTorch.complexLayers import ComplexBatchNorm2d, ComplexConv2d, ComplexLinear
from complexPyTorch.complexFunctions import complex_relu


class Flatten(nn.Module):
    def forward(self, input):
        return input.view(input.size(0), -1)


class HH2ComplexCONV_1(nn.Module):
    def __init__(self, input_size, device, n_outputs=1):
        super().__init__()
        self.flatten = Flatten()
        self.conv_1 = ComplexConv2d(1, 1, kernel_size=(3, 3), stride=1, padding=2)
        self.conv_2 = ComplexConv2d(1, 1, kernel_size=(3, 3), stride=1, padding=2)
        self.bn_1 = ComplexBatchNorm2d(1)
        self.bn_2 = ComplexBatchNorm2d(1)
        self.mlp_1 = ComplexLinear(input_size, input_size // 2)
        self.mlp_2 = ComplexLinear(input_size // 2, input_size // 4)
        self.mlp_3 = ComplexLinear(input_size // 4, input_size // 8)
        self.mlp_4 = ComplexLinear(input_size // 8, n_outputs)

    def forward(self, x):
        x = self.conv_1(x)
        x = self.bn_1(x)
        x = complex_relu(x)

        x = self.conv_2(x)
        x = self.bn_2(x)
        x = complex_relu(x)

        x = x.view(x.size(0), -1)
        x = self.mlp_1(x)
        x = complex_relu(x)
        x = self.mlp_2(x)
        x = complex_relu(x)
        x = self.mlp_3(x)
        x = complex_relu(x)
        x = self.mlp_4(x)

        # 转换成角度输出
        output = (180 / np.pi) * x.angle()
        return output


# ====== 1. 生成假数据 ======
def generate_fake_data(batch_size, H, W):
    real = np.random.randn(batch_size, 1, H, W)
    imag = np.random.randn(batch_size, 1, H, W)

    # 转成复数张量
    x = torch.tensor(real, dtype=torch.float32) + 1j * torch.tensor(imag, dtype=torch.float32)
    x = x.to(torch.complex64)  # (B,1,H,W)

    labels = np.random.uniform(-180, 180, size=(batch_size, 1))
    y = torch.tensor(labels, dtype=torch.float32)

    return x, y



# ====== 2. 训练 Demo ======
device = "cuda" if torch.cuda.is_available() else "cpu"
H, W = 16, 16  # 输入矩阵大小
input_size = H * W  # 展平后的大小

model = HH2ComplexCONV_1(input_size=input_size, device=device, n_outputs=1).to(device)
criterion = nn.MSELoss()
optimizer = optim.Adam(model.parameters(), lr=1e-3)

# 简单训练：2 epoch × 5 batch
for epoch in range(2):
    for batch in range(5):
        x, y = generate_fake_data(batch_size=8, H=H, W=W)
        x, y = x.to(device), y.to(device)

        optimizer.zero_grad()
        output = model(x)  # (B,1)
        loss = criterion(output, y)
        loss.backward()
        optimizer.step()

        print(f"Epoch {epoch+1}, Batch {batch+1}, Loss = {loss.item():.4f}")

# ====== 3. 推理 Demo ======
x_test, y_test = generate_fake_data(batch_size=2, H=H, W=W)
x_test = x_test.to(device)
pred = model(x_test)
print("真实角度:", y_test)
print("预测角度:", pred.detach().cpu())
