{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os,sys\n", "sys.path.append(os.getcwd())\n", "from func.BM_functions import *\n", "from func.system_functions import *\n", "from func.process import *\n", "from func.setsumi_env1 import setsumi_env1\n", "from utils import *\n", "import torch\n", "import pickle\n", "from train_td3 import seed_torch,parse_args,train,infer"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["setsumi load sim log OK\n", "Load begin\n", "H_pg shape: (1000, 1)\n", "bs_loc: [[0.]\n", " [0.]\n", " [8.]]\n", "使用 pg_all.txt 数据，rsrp shape: (1000, 1, 1)\n", "pg_all rsrp 元素总数: 1000\n", "Load Finish\n", "cut_length 1000\n", "切片后 rsrp 数组形状: (1000, 1, 1)\n", "切片后 rsrp 元素总数: 1000\n", "处理 pg_all 数据，原始维度参数: 10, 100, 1, 1, 1\n", "pg_all 处理后 rsrp 形状: (10, 100, 1)\n", "处理后参数: nUE=10, max_time=100, nBS=1\n", "expand H: (10, 4000, 17, 4, 64)\n", "expand ue loc: (10, 4000, 3)\n", "expand rsrp: (10, 4000, 1)\n", "srs_choose_all:(10, 4000, 17, 4)\n", "simulate srs:(10, 4000, 17, 4, 64)\n", "simulate coma:(10, 4000, 1, 32, 32)\n", "simulate rsrp:(10, 4000, 1)\n", "end load_H\n"]}], "source": ["path = os.getcwd()\n", "\n", "path ='/home/<USER>/quadriga/inf_v0/'\n", "seed = 777\n", "seed_torch(seed)\n", "env = setsumi_env1(path=path, seed=seed, max_length=4000,mu=0.9,gNB_tti=1,select_subs=[1],select_ports=1,test_real=False,rsrp_source=\"pg_all\")  \n"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["setsumi load sim log OK\n", "1000\n", "10\n"]}], "source": ["sim_par = Sim_Log(path)\n", "nUE = sim_par.nUE\n", "print(nUE)\n", "UE=env.nUE\n", "print(UE)"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["rsrp=env.rsrp"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# 假设已经获得了rsrp数据：rsrp = env.rsrp\n", "# rsrp的维度是[10, 100, 3]，其中10是用户个数，100是时间点，3是rsrp值\n", "\n", "# 对第三个维度进行平均\n", "rsrp_avg = np.mean(rsrp, axis=2)  # 维度变为[10, 100]\n", "\n", "# 再对所有用户进行平均，得到每个时间点的平均RSRP值\n", "rsrp_time_avg = np.mean(rsrp_avg, axis=0)  # 维度变为[100]\n", "\n", "# 创建时间点数组\n", "time_points = np.arange(100)\n", "\n", "# 绘制折线图\n", "plt.figure(figsize=(12, 6))\n", "plt.plot(time_points, rsrp_time_avg, linewidth=2, marker='o', markersize=3)\n", "plt.xlabel('time')\n", "plt.ylabel('rsrp')\n", "plt.grid(True, alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()\n", "plt.savefig(\"rsrp1.png\")\n", "# 如果需要为每个用户单独绘制折线图\n", "plt.figure(figsize=(12, 8))\n", "for user_id in range(10):\n", "    plt.plot(time_points, rsrp_avg[user_id], label=f'user{user_id+1}', alpha=0.7)\n", "\n", "plt.xlabel('time')\n", "plt.ylabel('rsrp')\n", "plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')\n", "plt.grid(True, alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()\n", "plt.savefig(\"rsrp2.png\")"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(10, 100, 1)\n"]}, {"ename": "ValueError", "evalue": "cannot reshape array of size 1000 into shape (10,10,10,3)", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[26], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28mprint\u001b[39m(rsrp\u001b[38;5;241m.\u001b[39mshape)\n\u001b[0;32m----> 2\u001b[0m grouped \u001b[38;5;241m=\u001b[39m \u001b[43mrsrp\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m<PERSON><PERSON>e\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m10\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m10\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m10\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m3\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m      4\u001b[0m \u001b[38;5;66;03m# 2. 直接计算算术平均（不加权）\u001b[39;00m\n\u001b[1;32m      5\u001b[0m result \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39mmean(grouped, axis\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m2\u001b[39m)  \u001b[38;5;66;03m# 沿时间点维度求平均\u001b[39;00m\n", "\u001b[0;31mValueError\u001b[0m: cannot reshape array of size 1000 into shape (10,10,10,3)"]}], "source": ["print(rsrp.shape)\n", "grouped = rsrp.reshape(10, 10, 10, 3)\n", "\n", "# 2. 直接计算算术平均（不加权）\n", "result = np.mean(grouped, axis=2)  # 沿时间点维度求平均\n", "\n", "# 结果形状: (10, 10, 1)\n", "print(result.shape)  # 输出: (10, 10, 1)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "def plot_rsrp_vs_time(rsrp_data, time_interval=2):\n", "    \"\"\"\n", "    绘制每个用户的RSRP随时间变化图\n", "    \n", "    Parameters:\n", "    rsrp_data: shape (n_users, n_time, 1) - RSRP数据\n", "    time_interval: 时间间隔，单位秒\n", "    \"\"\"\n", "    n_users, n_time, _ = rsrp_data.shape\n", "    \n", "    # 创建时间轴 (秒)\n", "    time_axis = np.arange(n_time) * time_interval\n", "    \n", "    # 提取RSRP值 (去掉最后一个维度)\n", "    rsrp_values = rsrp_data.squeeze(axis=-1)  # shape: (n_users, n_time)\n", "    \n", "    # 创建子图 - 5行2列布局\n", "    fig, axes = plt.subplots(5, 2, figsize=(15, 20))\n", "    axes = axes.flatten()  # 将2D数组展平为1D\n", "    \n", "    # 生成不同颜色\n", "    colors = plt.cm.tab10(np.linspace(0, 1, n_users))\n", "    \n", "    # 为每个用户绘制RSRP曲线\n", "    for user_idx in range(n_users):\n", "        ax = axes[user_idx]\n", "        \n", "        # 绘制RSRP曲线\n", "        ax.plot(time_axis, rsrp_values[user_idx, :], \n", "               color=colors[user_idx], linewidth=2, marker='o', markersize=4,\n", "               label=f'User {user_idx + 1}')\n", "        \n", "        # 设置标题和标签\n", "        ax.set_title(f'User {user_idx + 1} RSRP vs Time', fontsize=12, fontweight='bold')\n", "        ax.set_xlabel('Time (seconds)', fontsize=10)\n", "        ax.set_ylabel('RSRP (dBm)', fontsize=10)\n", "        ax.grid(True, alpha=0.3)\n", "        ax.legend()\n", "        \n", "        # 添加统计信息到图上\n", "        mean_rsrp = np.mean(rsrp_values[user_idx, :])\n", "        min_rsrp = np.min(rsrp_values[user_idx, :])\n", "        max_rsrp = np.max(rsrp_values[user_idx, :])\n", "        \n", "        # 在图上显示统计信息\n", "        stats_text = f'Mean: {mean_rsrp:.1f} dBm\\nMin: {min_rsrp:.1f} dBm\\nMax: {max_rsrp:.1f} dBm'\n", "        ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, \n", "               verticalalignment='top', fontsize=8,\n", "               bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))\n", "    \n", "    plt.tight_layout()\n", "    plt.suptitle('RSRP vs Time for All Users', fontsize=16, fontweight='bold', y=0.995)\n", "    return fig, axes\n", "\n", "def plot_all_users_combined(rsrp_data, time_interval=2):\n", "    \"\"\"\n", "    在一张图上显示所有用户的RSRP曲线\n", "    \"\"\"\n", "    n_users, n_time, _ = rsrp_data.shape\n", "    \n", "    # 创建时间轴\n", "    time_axis = np.arange(n_time) * time_interval\n", "    rsrp_values = rsrp_data.squeeze(axis=-1)\n", "    \n", "    # 创建图形\n", "    fig, ax = plt.subplots(figsize=(12, 8))\n", "    \n", "    # 生成不同颜色和线型\n", "    colors = plt.cm.tab10(np.linspace(0, 1, n_users))\n", "    line_styles = ['-', '--', '-.', ':', '-', '--', '-.', ':', '-', '--']\n", "    \n", "    # 绘制所有用户的曲线\n", "    for user_idx in range(n_users):\n", "        ax.plot(time_axis, rsrp_values[user_idx, :], \n", "               color=colors[user_idx], \n", "               linestyle=line_styles[user_idx % len(line_styles)],\n", "               linewidth=2, marker='o', markersize=3, alpha=0.8,\n", "               label=f'User {user_idx + 1}')\n", "    \n", "    # 设置图形属性\n", "    ax.set_title('RSRP vs Time - All Users Combined', fontsize=14, fontweight='bold')\n", "    ax.set_xlabel('Time (seconds)', fontsize=12)\n", "    ax.set_ylabel('RSRP (dBm)', fontsize=12)\n", "    ax.grid(True, alpha=0.3)\n", "    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')\n", "    \n", "    plt.tight_layout()\n", "    return fig, ax\n", "\n", "def plot_rsrp_statistics(rsrp_data, time_interval=2):\n", "    \"\"\"\n", "    绘制RSRP统计图：平均值、最大值、最小值随时间变化\n", "    \"\"\"\n", "    n_users, n_time, _ = rsrp_data.shape\n", "    time_axis = np.arange(n_time) * time_interval\n", "    rsrp_values = rsrp_data.squeeze(axis=-1)\n", "    \n", "    # 计算统计量\n", "    mean_rsrp = np.mean(rsrp_values, axis=0)  # 所有用户的平均值\n", "    max_rsrp = np.max(rsrp_values, axis=0)    # 所有用户的最大值\n", "    min_rsrp = np.min(rsrp_values, axis=0)    # 所有用户的最小值\n", "    std_rsrp = np.std(rsrp_values, axis=0)    # 标准差\n", "    \n", "    # 创建图形\n", "    fig, ax = plt.subplots(figsize=(12, 8))\n", "    \n", "    # 绘制统计曲线\n", "    ax.plot(time_axis, mean_rsrp, 'b-', linewidth=3, label='Mean RSRP', marker='o')\n", "    ax.plot(time_axis, max_rsrp, 'g-', linewidth=2, label='Max RSRP', marker='^')\n", "    ax.plot(time_axis, min_rsrp, 'r-', linewidth=2, label='Min RSRP', marker='v')\n", "    \n", "    # 添加标准差阴影区域\n", "    ax.fill_between(time_axis, \n", "                   mean_rsrp - std_rsrp, mean_rsrp + std_rsrp,\n", "                   alpha=0.3, color='blue', label='±1 Std Dev')\n", "    \n", "    # 设置图形属性\n", "    ax.set_title('RSRP Statistics Over Time', fontsize=14, fontweight='bold')\n", "    ax.set_xlabel('Time (seconds)', fontsize=12)\n", "    ax.set_ylabel('RSRP (dBm)', fontsize=12)\n", "    ax.grid(True, alpha=0.3)\n", "    ax.legend()\n", "    \n", "    plt.tight_layout()\n", "    return fig, ax\n", "\n", "def print_rsrp_summary(rsrp_data, time_interval=2):\n", "    \"\"\"\n", "    打印RSRP数据的详细统计信息\n", "    \"\"\"\n", "    n_users, n_time, _ = rsrp_data.shape\n", "    rsrp_values = rsrp_data.squeeze(axis=-1)\n", "    \n", "    print(\"=\"*60)\n", "    print(\"RSRP数据统计摘要\")\n", "    print(\"=\"*60)\n", "    print(f\"用户数量: {n_users}\")\n", "    print(f\"时间步数: {n_time}\")\n", "    print(f\"时间间隔: {time_interval} 秒\")\n", "    print(f\"总时长: {(n_time-1) * time_interval} 秒\")\n", "    \n", "    print(\"\\n各用户RSRP统计:\")\n", "    print(\"-\"*60)\n", "    print(\"用户\\t平均值\\t最小值\\t最大值\\t标准差\\t范围\")\n", "    print(\"-\"*60)\n", "    \n", "    for user_idx in range(n_users):\n", "        user_rsrp = rsrp_values[user_idx, :]\n", "        mean_val = np.mean(user_rsrp)\n", "        min_val = np.min(user_rsrp)\n", "        max_val = np.max(user_rsrp)\n", "        std_val = np.std(user_rsrp)\n", "        range_val = max_val - min_val\n", "        \n", "        print(f\"User{user_idx+1:2d}\\t{mean_val:6.1f}\\t{min_val:6.1f}\\t{max_val:6.1f}\\t{std_val:6.1f}\\t{range_val:6.1f}\")\n", "    \n", "    # 整体统计\n", "    overall_mean = np.mean(rsrp_values)\n", "    overall_min = np.min(rsrp_values)\n", "    overall_max = np.max(rsrp_values)\n", "    overall_std = np.std(rsrp_values)\n", "    \n", "    print(\"-\"*60)\n", "    print(f\"整体\\t{overall_mean:6.1f}\\t{overall_min:6.1f}\\t{overall_max:6.1f}\\t{overall_std:6.1f}\\t{overall_max-overall_min:6.1f}\")\n", "    print(\"=\"*60)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "cannot select an axis to squeeze out which has size not equal to one", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[13], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m fig2, ax2 \u001b[38;5;241m=\u001b[39m \u001b[43mplot_all_users_combined\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrsrp\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtime_interval\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m2\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m      2\u001b[0m plt\u001b[38;5;241m.\u001b[39mshow()\n\u001b[1;32m      3\u001b[0m plt\u001b[38;5;241m.\u001b[39msavefig(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mrsrp2.png\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "Cell \u001b[0;32mIn[12], line 66\u001b[0m, in \u001b[0;36mplot_all_users_combined\u001b[0;34m(rsrp_data, time_interval)\u001b[0m\n\u001b[1;32m     64\u001b[0m \u001b[38;5;66;03m# 创建时间轴\u001b[39;00m\n\u001b[1;32m     65\u001b[0m time_axis \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39marange(n_time) \u001b[38;5;241m*\u001b[39m time_interval\n\u001b[0;32m---> 66\u001b[0m rsrp_values \u001b[38;5;241m=\u001b[39m \u001b[43mrsrp_data\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msqueeze\u001b[49m\u001b[43m(\u001b[49m\u001b[43maxis\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m-\u001b[39;49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m     68\u001b[0m \u001b[38;5;66;03m# 创建图形\u001b[39;00m\n\u001b[1;32m     69\u001b[0m fig, ax \u001b[38;5;241m=\u001b[39m plt\u001b[38;5;241m.\u001b[39msubplots(figsize\u001b[38;5;241m=\u001b[39m(\u001b[38;5;241m12\u001b[39m, \u001b[38;5;241m8\u001b[39m))\n", "\u001b[0;31mValueError\u001b[0m: cannot select an axis to squeeze out which has size not equal to one"]}], "source": ["fig2, ax2 = plot_all_users_combined(rsrp, time_interval=2)\n", "plt.show()\n", "plt.savefig(\"rsrp2.png\")\n", "\n", "# 4. 打印统计信息\n", "print_rsrp_summary(rsrp, time_interval=2)"]}], "metadata": {"kernelspec": {"display_name": "meiziy", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 2}