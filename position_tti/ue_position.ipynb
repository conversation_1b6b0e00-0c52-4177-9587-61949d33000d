{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/root/miniconda3/envs/setsumi/lib/python3.10/site-packages/scipy/__init__.py:155: UserWarning: A NumPy version >=1.18.5 and <1.25.0 is required for this version of SciPy (detected version 1.26.0\n", "  warnings.warn(f\"A NumPy version >={np_minversion} and <{np_maxversion}\"\n"]}], "source": ["import os,sys\n", "sys.path.append(os.getcwd())\n", "from func.BM_functions import *\n", "from func.system_functions import *\n", "from func.process import *\n", "from func.setsumi_env import setsumi_env\n", "from utils import *\n", "import torch\n", "import pickle\n", "from train_td3 import seed_torch,parse_args,train,infer\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def cal_pos(real_aod,real_eod,real_dist):\n", "    \n", "    realx = real_dist*np.cos(real_eod/180*np.pi)*np.cos(real_aod/180*np.pi) + 0\n", "    realy = real_dist*np.cos(real_eod/180*np.pi)*np.sin(real_aod/180*np.pi) + 0\n", "    realz = real_dist*np.sin(real_eod/180*np.pi) + 8\n", "    \n", "    ue_real_pos = [realx,realy,realz]\n", "    \n", "    return ue_real_pos"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "10 100 1\n", "expand H: (10, 1, 17, 4, 64)\n", "expand ue loc: (10, 1, 3)\n", "expand rsrp: (10, 1, 1)\n", "srs_choose_all:(10, 1, 17, 4)\n", "simulate srs:(10, 1, 17, 4, 64)\n", "simulate coma:(10, 1, 1, 32, 32)\n", "simulate rsrp:(10, 1, 1)\n", "end load_H\n"]}], "source": ["path = os.getcwd()\n", "\n", "path = './uma_v3/'\n", "\n", "seed = 777\n", "seed_torch(seed)\n", "env = setsumi_env(path=path, seed=seed, max_length=1,mu=0.99,gNB_tti=1,select_subs=[1],select_ports=1,test_real=False)  \n"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(3, 10, 1)\n", "(1, 3)\n"]}], "source": ["real_aod=env.azimuths\n", "real_eod=env.elevation\n", "real_dist=env.Distance\n", "ue_real_pos=np.array(cal_pos(real_aod,real_eod,real_dist))\n", "print(ue_real_pos.shape)\n", "bs_loc=np.array(env.bs_loc)\n", "print(bs_loc.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(3, 1)\n"]}, {"ename": "TypeError", "evalue": "'numpy.float64' object is not iterable", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[26], line 9\u001b[0m\n\u001b[1;32m      7\u001b[0m x\u001b[38;5;241m=\u001b[39mbs_loc[\u001b[38;5;241m0\u001b[39m,\u001b[38;5;241m0\u001b[39m]\n\u001b[1;32m      8\u001b[0m y\u001b[38;5;241m=\u001b[39mbs_loc[\u001b[38;5;241m0\u001b[39m,\u001b[38;5;241m1\u001b[39m]\n\u001b[0;32m----> 9\u001b[0m \u001b[43mfigr\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43madd_line\u001b[49m\u001b[43m(\u001b[49m\u001b[43mx\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mx\u001b[49m\u001b[43m,\u001b[49m\u001b[43my\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43my\u001b[49m\u001b[43m,\u001b[49m\u001b[43mname\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43mf\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mue1_trace\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m     10\u001b[0m figr\u001b[38;5;241m.\u001b[39madd_line(x\u001b[38;5;241m=\u001b[39mx,y\u001b[38;5;241m=\u001b[39my,name\u001b[38;5;241m=\u001b[39m\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mBS\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m     11\u001b[0m figr\u001b[38;5;241m.\u001b[39mfig\u001b[38;5;241m.\u001b[39mupdate_layout(legend\u001b[38;5;241m=\u001b[39m{\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mx\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;241m0.1\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124my\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;241m0.98\u001b[39m}) \n", "File \u001b[0;32m/data/setsumi/zhangyucode/position_tti/utils/draw.py:366\u001b[0m, in \u001b[0;36mdraw_line.add_line\u001b[0;34m(self, x, y, name, mode, line_style_change, marker_symbols_change)\u001b[0m\n\u001b[1;32m    365\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21madd_line\u001b[39m(\u001b[38;5;28mself\u001b[39m, x, y, name,mode\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mlines+markers\u001b[39m\u001b[38;5;124m'\u001b[39m,line_style_change\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m,marker_symbols_change\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[0;32m--> 366\u001b[0m     x \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mlist\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mx\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    367\u001b[0m     y \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlist\u001b[39m(y)\n\u001b[1;32m    369\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m line_style_change \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "\u001b[0;31mTypeError\u001b[0m: 'numpy.float64' object is not iterable"]}], "source": ["figr = draw_line(\"UE real trace\",\"x\",\"y\")\n", "\n", "ue_1=ue_real_pos[:,0,:]\n", "print(ue_1.shape)\n", "x=ue_1[0,:]\n", "y=ue_1[1,:]\n", "figr.add_line(x=x,y=y,name=f'ue1_trace')\n", "figr.fig.update_layout(legend={'x': 0.1, 'y': 0.98}) \n", "figr.fig.update_xaxes(range=[22,24],dtick=1)\n", "figr.fig.update_yaxes(range=[1,4],dtick=1)\n", "\n", "figr.fig.show()\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "setsumi", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 2}