import argparse
import os
import torch
import torch.nn as nn
from torch.nn.utils import weight_norm
import datetime
import logging
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
import random


class Chomp1d(nn.Module):
    def __init__(self, chomp_size):
        super(Chomp1d, self).__init__()
        self.chomp_size = chomp_size

    def forward(self, x):
        return x[:, :, :-self.chomp_size].contiguous()


class TemporalBlock(nn.Module):
    def __init__(self, n_inputs, n_outputs, kernel_size, stride, dilation, padding, dropout=0.2):
        super(TemporalBlock, self).__init__()
        self.conv1 = weight_norm(nn.Conv1d(n_inputs, n_outputs, kernel_size,
                                           stride=stride, padding=padding, dilation=dilation))
        self.chomp1 = Chomp1d(padding)
        self.relu1 = nn.ReLU()
        self.dropout1 = nn.Dropout(dropout)

        self.conv2 = weight_norm(nn.Conv1d(n_outputs, n_outputs, kernel_size,
                                           stride=stride, padding=padding, dilation=dilation))
        self.chomp2 = Chomp1d(padding)
        self.relu2 = nn.ReLU()
        self.dropout2 = nn.Dropout(dropout)

        self.net = nn.Sequential(self.conv1, self.chomp1, self.relu1, self.dropout1,
                                 self.conv2, self.chomp2, self.relu2, self.dropout2)
        self.downsample = nn.Conv1d(n_inputs, n_outputs, 1) if n_inputs != n_outputs else None
        self.relu = nn.ReLU()
        self.init_weights()

    def init_weights(self):
        self.conv1.weight.data.normal_(0, 0.01)
        self.conv2.weight.data.normal_(0, 0.01)
        if self.downsample is not None:
            self.downsample.weight.data.normal_(0, 0.01)

    def forward(self, x):
        out = self.net(x)
        res = x if self.downsample is None else self.downsample(x)
        return self.relu(out + res)


class TemporalConvNet(nn.Module):
    def __init__(self, num_inputs, num_channels, kernel_size=2, dropout=0.2):
        super(TemporalConvNet, self).__init__()
        layers = []
        num_levels = len(num_channels)
        for i in range(num_levels):
            dilation_size = 2 ** i
            in_channels = num_inputs if i == 0 else num_channels[i-1]
            out_channels = num_channels[i]
            layers += [TemporalBlock(in_channels, out_channels, kernel_size, stride=1, dilation=dilation_size,
                                     padding=(kernel_size-1) * dilation_size, dropout=dropout)]

        self.network = nn.Sequential(*layers)

    def forward(self, x):
        return self.network(x)
    
class TCN(nn.Module):
    def __init__(self, input_size, output_size, num_channels, kernel_size, dropout):
        super(TCN, self).__init__()
        self.tcn = TemporalConvNet(input_size, num_channels, kernel_size=kernel_size, dropout=dropout)
        self.linear = nn.Linear(num_channels[-1], output_size)
        self.init_weights()

    def init_weights(self):
        self.linear.weight.data.normal_(0, 0.01)

    def forward(self, x):
        y1 = self.tcn(x)
        # return self.linear(y1[:, :, -1])
        y1 = y1.permute(0, 2, 1)
        y1 = self.linear(y1)
        return y1
 
def seed_torch(seed):
    np.random.seed(seed)
    random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
    if torch.cuda.is_available():
        torch.backends.cudnn.enabled = True
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False
    torch.use_deterministic_algorithms(True, warn_only=True)
    os.environ['CUBLAS_WORKSPACE_CONFIG']=':4096:8'
   
def parse_args(args=None):
    # ++ Basic Configs ++
    parser = argparse.ArgumentParser(description='Sequence Modeling - The Adding Problem')
    parser.add_argument('--cuda', action='store_false',
                        help='use CUDA (default: True)')
    parser.add_argument('--log-interval', type=int, default=100, metavar='N',
                        help='report interval (default: 100')

    # tcn para
    parser.add_argument('--input_channels', type=int, default=1,
                        help='input_channels size (default: 1)')
    parser.add_argument('--n_classes', type=int, default=1,
                        help='n_classes size (default: 1)')
    parser.add_argument('--ksize', type=int, default=7,
                        help='kernel size (default: 7)')
    parser.add_argument('--levels', type=int, default=8,
                        help='# of levels (default: 8)')
    parser.add_argument('--nhid', type=int, default=30,
                        help='number of hidden units per layer (default: 30)')
    
    # optim para
    parser.add_argument('--seed', type=int, default=1111,
                        help='random seed (default: 1111)') 
    parser.add_argument('--epochs', type=int, default=10,
                        help='upper epoch limit (default: 10)')
    parser.add_argument('--batch_size', type=int, default=32, metavar='N',
                        help='batch size (default: 32)')
    parser.add_argument('--lr', type=float, default=4e-3,
                        help='initial learning rate (default: 4e-3)')
    parser.add_argument('--optim', type=str, default='Adam',
                        help='optimizer to use (default: Adam)')
    parser.add_argument('--dropout', type=float, default=0.0,
                        help='dropout applied to layers (default: 0.0)')
    parser.add_argument('--clip', type=float, default=-1,
                        help='gradient clip, -1 means no clip (default: -1)')
    
    if args is None:
        # 从命令行读取参数
        parsed_args = parser.parse_args()
    else:
        # 使用 parse_known_args 传递参数列表
        parsed_args, _ = parser.parse_known_args(args)
    
    return parsed_args

def train(args, modelpath, train_x, train_y, test_x, test_y):
    # 存储
    args.daytime = datetime.datetime.now().strftime('%Y-%m-%d-%H-%M-%S')
    if not os.path.exists(modelpath):
        os.makedirs(modelpath)
    logging.basicConfig(format='%(asctime)s - %(levelname)s - %(message)s',
                    datefmt='%Y-%m-%d %H:%M:%S',
                    level=logging.INFO,
                    filename=f'{modelpath}/{args.daytime}.log',
                    filemode='w')
    logger = logging.getLogger(__name__)
    logger.info("parameters: %s", args)
    
    # 数据
    X_train, Y_train = torch.FloatTensor(train_x), torch.FloatTensor(train_y)
    X_test, Y_test = torch.FloatTensor(test_x),torch.FloatTensor(test_y)

    # 模型
    model = TCN(
        input_size=args.input_channels,
        output_size=args.n_classes,
        num_channels=[args.nhid]*args.levels, 
        kernel_size=args.ksize, 
        dropout=args.dropout)
    
    epochs = args.epochs
    batch_size = args.batch_size
    lr = args.lr
    optimizer = torch.optim.Adam(model.parameters(), lr=lr)

    log_interval = args.log_interval
    clip = args.clip

    # 训练
    maxscore = -5
    for epoch in range(1, epochs+1):
        model.train()
        batch_idx = 1
        total_loss = 0
        for i in range(0, X_train.size(0), batch_size):
            if i + batch_size > X_train.size(0):
                x, y = X_train[i:], Y_train[i:]
            else:
                x, y = X_train[i:(i+batch_size)], Y_train[i:(i+batch_size)]
            optimizer.zero_grad()
            output = model(x)
            # print(i, 'avg dist err:', torch.mean(torch.abs((output-y))))

            loss = F.mse_loss(output, y)
            loss.backward()
            if clip > 0:
                torch.nn.utils.clip_grad_norm_(model.parameters(), clip)
            optimizer.step()
            batch_idx += 1
            total_loss += loss.item()

            if batch_idx % log_interval == 0:
                cur_loss = total_loss / log_interval
                processed = min(i+batch_size, X_train.size(0))
                print('Train Epoch: {:2d} [{:6d}/{:6d} ({:.0f}%)]\tLearning rate: {:.4f}\tLoss: {:.6f}'.format(
                    epoch, processed, X_train.size(0), 100.*processed/X_train.size(0), lr, cur_loss))
                total_loss = 0
        
        # 模型结果
        output = model(X_train)
        score = - torch.mean(torch.abs(output - Y_train))
        score = score.detach().cpu().numpy() * 50
        if score > maxscore:
            torch.save(model.state_dict(), modelpath+f'{args.daytime}_epoch{epoch}_score{score}')
            maxscore = score
        
        # 推理
        model.eval()
        with torch.no_grad():
            output = model(X_test)
            test_loss = F.mse_loss(output, Y_test)
            print('\nTest set: Average loss: {:.6f}\n'.format(test_loss.item())) 
            score = - torch.mean(torch.abs(output - Y_test))
            score = score.detach().cpu().numpy() * 50
            print('score:', score)

    
    return maxscore
        
def infer(args, modelpath, test_x, test_y):
    # 数据
    X_test, Y_test = torch.FloatTensor(test_x),torch.FloatTensor(test_y)

    # 模型
    model = TCN(
        input_size=args.input_channels,
        output_size=args.n_classes,
        num_channels=[args.nhid]*args.levels, 
        kernel_size=args.ksize, 
        dropout=args.dropout)
    model.load_state_dict(torch.load(modelpath))
    
    # 推理
    model.eval()
    with torch.no_grad():
        output = model(X_test)
        test_loss = F.mse_loss(output, Y_test)
        print('\nTest set: Average loss: {:.6f}\n'.format(test_loss.item()))
        score = - torch.mean(torch.abs(output - Y_test))
        score = score.detach().cpu().numpy() * 50
        print('score:', score)
    
    return output.detach().cpu().numpy()
            