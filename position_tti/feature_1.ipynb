{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os,sys\n", "sys.path.append(os.getcwd())\n", "from func.BM_functions import *\n", "from func.system_functions import *\n", "from func.process import *\n", "from func.setsumi_env import setsumi_env\n", "from utils import *\n", "import torch\n", "import pickle\n", "from train_td3 import seed_torch,parse_args,train,infer"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def cal_pos_error(real_aod,real_eod,real_dist):\n", "    realx = real_dist*np.cos(real_eod/180*np.pi)*np.cos(real_aod/180*np.pi) + 0\n", "    realy = real_dist*np.cos(real_eod/180*np.pi)*np.sin(real_aod/180*np.pi) + 0\n", "    realz = real_dist*np.sin(real_eod/180*np.pi) + 8\n", "    \n", "    ue_real_pos = [realx,realy,realz]\n", "    \n", "    return ue_real_pos"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["setsumi load sim log OK\n", "Load begin\n", "(1000, 1)\n", "文件提取的rsrp (1000, 1, 3)\n", "初始 rsrp 形状: (1000, 1, 3)\n", "初始 rsrp 元素总数: 3000\n", "Load Finish\n", "处理后 rsrp 形状: (1000, 1, 3)\n", "处理后 rsrp 元素总数: 3000\n", "cut_length 10\n", "切片后数组形状: (10, 1, 3)\n", "切片后元素总数: 30\n", "10 1 1 1 3\n", "10 1 3\n", "expand H: (10, 100, 17, 4, 64)\n", "expand ue loc: (10, 100, 3)\n", "expand rsrp: (10, 100, 3)\n", "srs_choose_all:(10, 100, 17, 4)\n", "simulate srs:(10, 100, 17, 4, 64)\n", "simulate coma:(10, 100, 1, 32, 32)\n", "simulate rsrp:(10, 100, 3)\n", "end load_H\n"]}], "source": ["path = os.getcwd()\n", "\n", "path = '/home/<USER>/quadriga/UMi_v30/'\n", "\n", "seed = 777\n", "seed_torch(seed)\n", "env = setsumi_env(path=path, seed=seed, max_length=100,mu=0.99,gNB_tti=1,select_subs=[1],select_ports=1,test_real=False)  "]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(10, 1, 3)\n", "(3, 10, 100)\n", "(3, 3)\n"]}], "source": ["real_aod=env.azimuths\n", "real_eod=env.elevation\n", "real_dist=env.Distance\n", "rsrp=env.rsrp\n", "print(np.array(rsrp).shape)\n", "ue_real_pos=np.array(cal_pos_error(real_aod,real_eod,real_dist))\n", "print(np.array(ue_real_pos).shape)\n", "bs_loc=np.array(env.bs_loc)\n", "print(bs_loc.shape)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["UE坐标点数: 1000\n", "RSRP数据点数: 10\n"]}, {"ename": "ValueError", "evalue": "operands could not be broadcast together with shapes (1000,) (10,) ", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[7], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;66;03m# 绘制完整的系统图 (显示第一个基站的RSRP)\u001b[39;00m\n\u001b[0;32m----> 2\u001b[0m fig, ax \u001b[38;5;241m=\u001b[39m \u001b[43mplot_complete_cartesian_system\u001b[49m\u001b[43m(\u001b[49m\u001b[43mue_real_pos\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbs_loc\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrsrp\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbase_station_idx\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m      3\u001b[0m plt\u001b[38;5;241m.\u001b[39mshow()\n\u001b[1;32m      4\u001b[0m plt\u001b[38;5;241m.\u001b[39msavefig(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mrsrp_fe.png\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "Cell \u001b[0;32mIn[6], line 38\u001b[0m, in \u001b[0;36mplot_complete_cartesian_system\u001b[0;34m(ue_real_pos, bs_loc, rsrp, base_station_idx)\u001b[0m\n\u001b[1;32m     35\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mRSRP数据点数: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mrsrp_all\u001b[38;5;241m.\u001b[39mshape[\u001b[38;5;241m0\u001b[39m]\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     37\u001b[0m \u001b[38;5;66;03m# 去除无效点（如 inf 或 nan）\u001b[39;00m\n\u001b[0;32m---> 38\u001b[0m valid_mask \u001b[38;5;241m=\u001b[39m \u001b[43mnp\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43misfinite\u001b[49m\u001b[43m(\u001b[49m\u001b[43mue_x_all\u001b[49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m&\u001b[39;49m\u001b[43m \u001b[49m\u001b[43mnp\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43misfinite\u001b[49m\u001b[43m(\u001b[49m\u001b[43mue_y_all\u001b[49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m&\u001b[39;49m\u001b[43m \u001b[49m\u001b[43mnp\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43misfinite\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrsrp_all\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     39\u001b[0m ue_x_all \u001b[38;5;241m=\u001b[39m ue_x_all[valid_mask]\n\u001b[1;32m     40\u001b[0m ue_y_all \u001b[38;5;241m=\u001b[39m ue_y_all[valid_mask]\n", "\u001b[0;31mValueError\u001b[0m: operands could not be broadcast together with shapes (1000,) (10,) "]}], "source": ["# 绘制完整的系统图 (显示第一个基站的RSRP)\n", "fig, ax = plot_complete_cartesian_system(ue_real_pos, bs_loc, rsrp, base_station_idx=0)\n", "plt.show()\n", "plt.savefig(\"rsrp_fe.png\")\n", "# 也可以显示其他基站的RSRP\n", "fig2, ax2 = plot_complete_cartesian_system(ue_real_pos, bs_loc, rsrp, base_station_idx=1)\n", "plt.show()\n", "plt.savefig(\"rsrp_fe1.png\")"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["def plot_complete_polar_system(ue_real_pos, bs_loc, rsrp, base_station_idx=0):\n", "    \n", "    \n", "    # 创建极坐标图\n", "    fig, ax = plt.subplots(figsize=(14, 12), subplot_kw=dict(projection='polar'))\n", "    \n", "    # ==================== 1. 准备数据 ====================\n", "    # 提取UE的x,y坐标 (忽略z坐标，只看2D投影)\n", "    ue_x = ue_real_pos[0, :, :]  # shape: (10, 100)\n", "    ue_y = ue_real_pos[1, :, :]  # shape: (10, 100)\n", "    \n", "    # 提取基站的x,y坐标\n", "    bs_x = bs_loc[:, 0]  # shape: (3,)\n", "    bs_y = bs_loc[:, 1]  # shape: (3,)\n", "    \n", "    # 转换基站坐标为极坐标\n", "    bs_r = np.sqrt(bs_x**2 + bs_y**2)\n", "    bs_theta = np.arctan2(bs_y, bs_x)\n", "    \n", "    # ==================== 2. 绘制RSRP等高线 ====================\n", "    # 提取指定基站的RSRP数据并计算时间平均\n", "    rsrp_data = rsrp[:, :, base_station_idx]  # shape: (10, 100)\n", "    rsrp_avg = np.mean(rsrp_data, axis=1)  # shape: (10,) - 每个UE的平均RSRP\n", "    \n", "    # 计算UE的平均位置\n", "    ue_x_avg = np.mean(ue_x, axis=1)  # shape: (10,)\n", "    ue_y_avg = np.mean(ue_y, axis=1)  # shape: (10,)\n", "    \n", "    # 转换为极坐标\n", "    ue_r_avg = np.sqrt(ue_x_avg**2 + ue_y_avg**2)\n", "    ue_theta_avg = np.arctan2(ue_y_avg, ue_x_avg)\n", "    \n", "    # 创建极坐标网格进行插值\n", "    theta_grid = np.linspace(0, 2*np.pi, 120)\n", "    r_max = max(np.max(np.sqrt(ue_x**2 + ue_y**2)), np.max(bs_r)) * 1.1\n", "    r_grid = np.linspace(0, r_max, 60)\n", "    THETA, R = np.meshgrid(theta_grid, r_grid)\n", "    \n", "    # 将极坐标转换为笛卡尔坐标进行插值\n", "    X_grid = R * np.cos(THETA)\n", "    Y_grid = R * np.sin(THETA)\n", "    \n", "    ue_x_cart = ue_r_avg * np.cos(ue_theta_avg)\n", "    ue_y_cart = ue_r_avg * np.sin(ue_theta_avg)\n", "    \n", "    # 使用griddata进行插值\n", "    try:\n", "        rsrp_grid = griddata(\n", "            (ue_x_cart, ue_y_cart), rsrp_avg,\n", "            (X_grid, Y_grid), method='nearest', fill_value=np.nan\n", "        )\n", "        \n", "        # 绘制RSRP等高图 (填充)\n", "        contour_filled = ax.contourf(THETA, R, rsrp_grid, \n", "                                   levels=12, cmap='viridis', alpha=0.4, extend='both')\n", "        \n", "        # 绘制RSRP等高线\n", "        contour_lines = ax.contour(THETA, R, rsrp_grid, \n", "                                 levels=8, colors='darkblue', alpha=0.7, linewidths=1.2)\n", "        \n", "        # 添加等高线标签 - 修改字体设置方式\n", "        labels = ax.clabel(contour_lines, inline=True, fontsize=8, fmt='%.0f dBm', colors='darkblue')\n", "        # 单独设置字体粗细\n", "        for label in labels:\n", "            label.set_fontweight('bold')\n", "        \n", "        # 添加颜色条\n", "        cbar = plt.colorbar(contour_filled, ax=ax, shrink=0.7, pad=0.15)\n", "        cbar.set_label(f'RSRP from BS{base_station_idx+1} (dBm)', fontsize=12, fontweight='bold')\n", "        \n", "        print(f\"成功绘制BS{base_station_idx+1}的RSRP等高线\")\n", "        \n", "    except Exception as e:\n", "        print(f\"RSRP插值失败: {e}\")\n", "# ==================== 3. 绘制所有UE的运动轨迹 ====================\n", "    # 生成不同颜色用于UE轨迹\n", "    colors = plt.cm.Set1(np.linspace(0, 1, min(10, ue_real_pos.shape[1])))\n", "    \n", "    # 绘制每个UE的轨迹\n", "    for ue_idx in range(ue_real_pos.shape[1]):  # 遍历所有UE\n", "        # 获取该UE的轨迹\n", "        x_traj = ue_x[ue_idx, :]  # shape: (100,)\n", "        y_traj = ue_y[ue_idx, :]  # shape: (100,)\n", "        \n", "        # 转换为极坐标\n", "        r_traj = np.sqrt(x_traj**2 + y_traj**2)\n", "        theta_traj = np.arctan2(y_traj, x_traj)\n", "        \n", "        # 绘制轨迹线\n", "        ax.plot(theta_traj, r_traj, \n", "               color=colors[ue_idx % len(colors)], alpha=0.8, linewidth=2,\n", "               label=f'UE{ue_idx+1}' if ue_idx < 6 else \"\")  # 只显示前6个UE的标签\n", "        \n", "        # 标记起始点 (时间=0)\n", "        ax.scatter(theta_traj[0], r_traj[0], \n", "                  color=colors[ue_idx % len(colors)], s=80, marker='o', \n", "                  edgecolor='black', linewidth=1.5, zorder=10, alpha=0.9)\n", "        \n", "        # 标记结束点 (时间=99)\n", "        ax.scatter(theta_traj[-1], r_traj[-1], \n", "                  color=colors[ue_idx % len(colors)], s=80, marker='s', \n", "                  edgecolor='black', linewidth=1.5, zorder=10, alpha=0.9)\n", "        \n", "        # 为前几个UE添加起始位置标签\n", "        if ue_idx < 5:\n", "            ax.annotate(f'UE{ue_idx+1}', (theta_traj[0], r_traj[0]), \n", "                       xytext=(5, 5), textcoords='offset points',\n", "                       fontsize=9, color=colors[ue_idx % len(colors)], \n", "                       fontweight='bold',\n", "                       bbox=dict(boxstyle=\"round,pad=0.2\", facecolor='white', alpha=0.8))\n", "    \n", "    # ==================== 4. 绘制所有基站位置 ====================\n", "    # 绘制所有基站\n", "    ax.scatter(bs_theta, bs_r, c='red', s=300, alpha=1.0, \n", "              marker='^', edgecolor='darkred', linewidth=2,\n", "              label='Base Stations', zorder=15)\n", "    \n", "    # 高亮显示当前分析RSRP的基站\n", "    ax.scatter(bs_theta[base_station_idx], bs_r[base_station_idx], \n", "              c='gold', s=400, alpha=1.0, \n", "              marker='^', edgecolor='orange', linewidth=3,\n", "              label=f'BS{base_station_idx+1} (RSRP Source)', zorder=16)\n", "    \n", "    # 为每个基站添加编号和位置信息\n", "    for i, (theta, r) in enumerate(zip(bs_theta, bs_r)):\n", "        if i == base_station_idx:\n", "            color = 'orange'\n", "            weight = 'bold'\n", "            bbox_color = 'gold'\n", "            alpha = 1.0\n", "        else:\n", "            color = 'darkred'\n", "            weight = 'normal'\n", "            bbox_color = 'white'\n", "            alpha = 0.9\n", "            \n", "        ax.annotate(f'BS{i+1}\\n({bs_x[i]:.0f},{bs_y[i]:.0f})', \n", "                   (theta, r), \n", "                   xytext=(10, 10), textcoords='offset points',\n", "                   fontsize=10, fontweight=weight, color=color,\n", "                   bbox=dict(boxstyle=\"round,pad=0.3\", \n", "                           facecolor=bbox_color, alpha=alpha, \n", "                           edgecolor=color, linewidth=1),\n", "                   ha='left', va='bottom')\n", "    \n", "    # ==================== 5. 设置图形属性 ====================\n", "    # 设置标题\n", "    title = f'UE Trajectories + All Base Stations + RSRP Contours (BS{base_station_idx+1})\\nPolar Coordinate System'\n", "    ax.set_title(title, pad=35, fontsize=16, fontweight='bold')\n", "    \n", "    # 创建详细的图例\n", "    legend_elements = []\n", "    \n", "    # UE轨迹相关\n", "    legend_elements.append(plt.Line2D([0], [0], color='blue', linewidth=2,\n", "                                    label='UE Trajectories'))\n", "    legend_elements.append(plt.Line2D([0], [0], marker='o', color='blue', \n", "                                    markersize=8, linestyle='None', \n", "                                    markeredgecolor='black', markeredgewidth=1,\n", "                                    label='UE Start Position'))\n", "    legend_elements.append(plt.Line2D([0], [0], marker='s', color='blue', \n", "                                    markersize=8, linestyle='None', \n", "                                    markeredgecolor='black', markeredgewidth=1,\n", "                                    label='UE End Position'))\n", "    \n", "    # 基站相关\n", "    legend_elements.append(plt.Line2D([0], [0], marker='^', color='red', \n", "                                    markersize=12, linestyle='None', \n", "                                    markeredgecolor='darkred', markeredgewidth=2,\n", "                                    label='Base Stations'))\n", "    legend_elements.append(plt.Line2D([0], [0], marker='^', color='gold', \n", "                                    markersize=14, linestyle='None', \n", "                                    markeredgecolor='orange', markeredgewidth=2,\n", "                                    label=f'BS{base_station_idx+1} (RSRP Source)'))\n", "    \n", "    # RSRP等高线\n", "    legend_elements.append(plt.Line2D([0], [0], color='darkblue', linewidth=1.2,\n", "                                    label='RSRP Contour Lines'))\n", "    \n", "    ax.legend(handles=legend_elements, loc='upper left', bbox_to_anchor=(0.02, 1.0),\n", "             fontsize=10, framealpha=0.9)\n", "    \n", "    # 设置网格和坐标系\n", "    ax.grid(True, alpha=0.4, linestyle='-', linewidth=0.5)\n", "    ax.set_theta_zero_location('E')  # 0度指向东方\n", "    ax.set_theta_direction(1)  # 逆时针方向\n", "    \n", "    # 设置径向范围\n", "    ax.set_ylim(0, r_max)\n", "    \n", "    # 添加径向标签\n", "    ax.set_rlabel_position(45)\n", "    ax.set_ylabel('Distance (m)', labelpad=35, fontsize=12)\n", "    \n", "    plt.tight_layout()\n", "    \n", "    # 打印统计信息\n", "    print_system_statistics(ue_real_pos, bs_loc, rsrp, base_station_idx)\n", "    \n", "    return fig, ax\n", "\n", "def print_system_statistics(ue_real_pos, bs_loc, rsrp, base_station_idx):\n", "    \"\"\"\n", "    打印系统统计信息\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*50)\n", "    print(\"系统统计信息\")\n", "    print(\"=\"*50)\n", "    \n", "    print(f\"UE数量: {ue_real_pos.shape[1]}\")\n", "    print(f\"时间步数: {ue_real_pos.shape[2]}\")\n", "    print(f\"基站数量: {bs_loc.shape[1]}\")\n", "    print(f\"当前分析基站: BS{base_station_idx+1}\")\n", "    \n", "    # RSRP统计\n", "    rsrp_bs = rsrp[:, :, base_station_idx]\n", "    print(f\"\\nBS{base_station_idx+1}的RSRP统计:\")\n", "    print(f\"  最小值: {rsrp_bs.min():.1f} dBm\")\n", "    print(f\"  最大值: {rsrp_bs.max():.1f} dBm\")\n", "    print(f\"  平均值: {rsrp_bs.mean():.1f} dBm\")\n", "    print(f\"  标准差: {rsrp_bs.std():.1f} dBm\")\n", "    \n", "    # 基站位置\n", "    print(f\"\\n基站位置:\")\n", "    for i in range(bs_loc.shape[1]):\n", "        x, y, z = bs_loc[i, 0], bs_loc[i, 1], bs_loc[i, 2]\n", "        r = np.sqrt(x**2 + y**2)\n", "        theta_deg = np.degrees(np.arctan2(y, x))\n", "        marker = \" ← RSRP源\" if i == base_station_idx else \"\"\n", "        print(f\"  BS{i+1}: 直角坐标({x:.0f}, {y:.0f}, {z:.0f}), 极坐标(r={r:.0f}m, θ={theta_deg:.0f}°){marker}\")"]}], "metadata": {"kernelspec": {"display_name": "meiziy", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 2}