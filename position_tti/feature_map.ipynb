{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os,sys\n", "sys.path.append(os.getcwd())\n", "from func.BM_functions import *\n", "from func.system_functions import *\n", "from func.process import *\n", "from func.setsumi_env import setsumi_env\n", "from utils import *\n", "import torch\n", "import pickle\n", "from train_td3 import seed_torch,parse_args,train,infer"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def cal_pos_error(real_aod,real_eod,real_dist):\n", "    realx = real_dist*np.cos(real_eod/180*np.pi)*np.cos(real_aod/180*np.pi) + 0\n", "    realy = real_dist*np.cos(real_eod/180*np.pi)*np.sin(real_aod/180*np.pi) + 0\n", "    realz = real_dist*np.sin(real_eod/180*np.pi) + 3\n", "    \n", "    ue_real_pos = [realx,realy,realz]\n", "    \n", "    return ue_real_pos"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["setsumi load sim log OK\n", "Load begin\n", "(1000, 1)\n", "[[  0.     173.2051   0.    ]\n", " [  0.     100.     200.    ]\n", " [ 10.      10.      10.    ]]\n", "文件提取的rsrp (1000, 1, 3)\n", "初始 rsrp 形状: (1000, 1, 3)\n", "初始 rsrp 元素总数: 3000\n", "Load Finish\n", "处理后 rsrp 形状: (1000, 1, 3)\n", "处理后 rsrp 元素总数: 3000\n", "cut_length 10\n", "切片后数组形状: (10, 1, 3)\n", "切片后元素总数: 30\n", "10 1 1 1 3\n", "10 1 3\n", "expand H: (10, 100, 17, 4, 64)\n", "expand ue loc: (10, 100, 3)\n", "expand rsrp: (10, 100, 3)\n", "srs_choose_all:(10, 100, 17, 4)\n", "simulate srs:(10, 100, 17, 4, 64)\n", "simulate coma:(10, 100, 1, 32, 32)\n", "simulate rsrp:(10, 100, 3)\n", "end load_H\n"]}], "source": ["path = os.getcwd()\n", "\n", "path = '/home/<USER>/quadriga/UMi_v30/'\n", "\n", "seed = 777\n", "seed_torch(seed)\n", "env = setsumi_env(path=path, seed=seed, max_length=100,mu=0.99,gNB_tti=1,select_subs=[1],select_ports=1,test_real=False)  "]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["real_aod=env.azimuths\n", "real_eod=env.elevation\n", "real_dist=env.Distance\n", "rsrp=env.rsrp_srs\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(10, 100)\n", "(10, 100)\n", "(10, 100, 3)\n"]}], "source": ["print(np.array(real_aod).shape)\n", "print(np.array(real_dist).shape)\n", "print(np.array(rsrp).shape)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5\n", " 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5\n", " 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5\n", " 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5\n", " 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5\n", " 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5 4.5]\n", "(3, 10, 100)\n", "[[  0.     173.2051   0.    ]\n", " [  0.     100.     200.    ]\n", " [ 10.      10.      10.    ]]\n"]}], "source": ["ue_real_pos=np.array(cal_pos_error(real_aod,real_eod,real_dist))\n", "print(ue_real_pos[2,0,:])\n", "print(np.array(ue_real_pos).shape)\n", "bs_loc=np.array(env.bs_loc)\n", "print(bs_loc)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import matplotlib.colors as mcolors\n", "from scipy.interpolate import griddata\n", "from scipy.interpolate import RBFInterpolator\n", "\n", "def plot_complete_cartesian_system(ue_real_pos, bs_loc, rsrp, base_station_idx=0):\n", "    \"\"\"\n", "    在笛卡尔坐标系中绘制完整的系统图：UE轨迹 + 所有基站 + 指定基站的RSRP等高线\n", "    \n", "    Parameters:\n", "    ue_real_pos: shape (3, 10, 100) - UE位置数据 (x,y,z, n_ue, n_time)\n", "    bs_loc: shape (3, 3) - 基站位置数据 (n_bs, x,y,z)\n", "    rsrp: shape (10, 100, 3) - RSRP数据 (n_ue, n_time, n_bs)\n", "    base_station_idx: int - 要显示RSRP等高线的基站索引 (默认0，即第一个基站)\n", "    \"\"\"\n", "    \n", "    # 创建笛卡尔坐标图\n", "    fig, ax = plt.subplots(figsize=(14, 12))\n", "    \n", "    # ==================== 1. 准备数据 ====================\n", "    # 提取UE的x,y坐标 (忽略z坐标，只看2D投影)\n", "    ue_x = ue_real_pos[0, :, :]  # shape: (10, 100)\n", "    ue_y = ue_real_pos[1, :, :]  # shape: (10, 100)\n", "    \n", "    # 提取基站的x,y坐标\n", "    bs_x = bs_loc[:, 0]  # shape: (3,)\n", "    bs_y = bs_loc[:, 1]  # shape: (3,)\n", "    \n", "    # 将所有UE位置和RSRP数据展平\n", "    ue_x_all = ue_x.flatten()  # shape: (10*100,)\n", "    ue_y_all = ue_y.flatten()\n", "    rsrp_all = rsrp[:, :, base_station_idx].flatten()\n", "    \n", "    print(f\"UE位置数据形状: {ue_x_all.shape}\")\n", "    print(f\"RSRP数据形状: {rsrp_all.shape}\")\n", "    \n", "    # 去除无效点（如 inf 或 nan）\n", "    valid_mask = np.isfinite(ue_x_all) & np.isfinite(ue_y_all) & np.isfinite(rsrp_all)\n", "    ue_x_all = ue_x_all[valid_mask]\n", "    ue_y_all = ue_y_all[valid_mask]\n", "    rsrp_all = rsrp_all[valid_mask]\n", "    \n", "    print(f\"有效数据点数量: {len(ue_x_all)}\")\n", "    \n", "    # ==================== 2. 创建插值网格和RSRP等高线 ====================\n", "    # 确定坐标范围\n", "    x_min = min(np.min(ue_x_all), np.min(bs_x)) - 50\n", "    x_max = max(np.max(ue_x_all), np.max(bs_x)) + 50\n", "    y_min = min(np.min(ue_y_all), np.min(bs_y)) - 50\n", "    y_max = max(np.max(ue_y_all), np.max(bs_y)) + 50\n", "    \n", "    # 创建插值网格\n", "    x_grid = np.linspace(x_min, x_max, 120)\n", "    y_grid = np.linspace(y_min, y_max, 120)\n", "    X_grid, Y_grid = np.meshgrid(x_grid, y_grid)\n", "    \n", "    # ========== 构建插值器（多种方法备选）==========\n", "    rsrp_interpolated = False\n", "    \n", "    # 数据预处理：检查数据质量\n", "    print(f\"插值前数据检查:\")\n", "    print(f\"  有效数据点: {len(ue_x_all)}\")\n", "    print(f\"  X范围: [{ue_x_all.min():.1f}, {ue_x_all.max():.1f}]\")\n", "    print(f\"  Y范围: [{ue_y_all.min():.1f}, {ue_y_all.max():.1f}]\")\n", "    print(f\"  RSRP范围: [{rsrp_all.min():.1f}, {rsrp_all.max():.1f}]\")\n", "    \n", "    # 检查是否有重复点或数据不足\n", "    unique_points = np.unique(np.column_stack((ue_x_all, ue_y_all)), axis=0)\n", "    print(f\"  唯一坐标点数: {len(unique_points)}\")\n", "    \n", "    if len(unique_points) < 10:\n", "        print(\"警告: 唯一数据点太少，可能导致插值失败\")\n", "    \n", "    # 方法1: 尝试RBF插值（较少邻居和更多平滑）\n", "    if not rsrp_interpolated:\n", "        try:\n", "            # 减少邻居数量，增加平滑度\n", "            rbf_interp = RBFInterpolator(\n", "                np.column_stack((ue_x_all, ue_y_all)),\n", "                rsrp_all,\n", "                neighbors=min(15, len(ue_x_all)//2),  # 动态调整邻居数\n", "                smoothing=5.0,                        # 增加平滑度\n", "                kernel='thin_plate_spline'            # 更稳定的核函数\n", "            )\n", "            \n", "            rsrp_grid = rbf_interp(np.column_stack((X_grid.flatten(), Y_grid.flatten())))\n", "            rsrp_grid = rsrp_grid.reshape(X_grid.shape)\n", "            rsrp_interpolated = True\n", "            print(\"成功使用 RBF 插值（thin_plate_spline）\")\n", "            \n", "        except Exception as e:\n", "            print(f\"RBF 插值失败: {e}\")\n", "    \n", "    # 方法2: 尝试线性插值\n", "    if not rsrp_interpolated:\n", "        try:\n", "            rsrp_grid = griddata(\n", "                np.column_stack((ue_x_all, ue_y_all)),\n", "                rsrp_all,\n", "                (X_grid, Y_grid),\n", "                method='linear',\n", "                fill_value=np.nan\n", "            )\n", "            rsrp_interpolated = True\n", "            print(\"成功使用 griddata 线性插值\")\n", "            \n", "        except Exception as e:\n", "            print(f\"线性插值失败: {e}\")\n", "    \n", "    # 方法3: 尝试三次插值\n", "    if not rsrp_interpolated:\n", "        try:\n", "            rsrp_grid = griddata(\n", "                np.column_stack((ue_x_all, ue_y_all)),\n", "                rsrp_all,\n", "                (X_grid, Y_grid),\n", "                method='cubic',\n", "                fill_value=np.nan\n", "            )\n", "            rsrp_interpolated = True\n", "            print(\"成功使用 griddata 三次插值\")\n", "            \n", "        except Exception as e:\n", "            print(f\"三次插值失败: {e}\")\n", "    \n", "    # 方法4: 最近邻插值（最稳定的备选方案）\n", "    if not rsrp_interpolated:\n", "        try:\n", "            rsrp_grid = griddata(\n", "                np.column_stack((ue_x_all, ue_y_all)),\n", "                rsrp_all,\n", "                (X_grid, Y_grid),\n", "                method='nearest'\n", "            )\n", "            rsrp_interpolated = True\n", "            print(\"使用 griddata 最近邻插值\")\n", "            \n", "        except Exception as e:\n", "            print(f\"最近邻插值失败: {e}\")\n", "    \n", "    # 如果插值成功，绘制等高线\n", "    if rsrp_interpolated and not np.all(np.isnan(rsrp_grid)):\n", "        try:\n", "            # 处理NaN值\n", "            if np.any(np.isnan(rsrp_grid)):\n", "                # 用最近邻填充NaN\n", "                mask = np.isnan(rsrp_grid)\n", "                rsrp_grid[mask] = griddata(\n", "                    np.column_stack((ue_x_all, ue_y_all)),\n", "                    rsrp_all,\n", "                    (X_grid[mask], Y_grid[mask]),\n", "                    method='nearest'\n", "                )\n", "            \n", "            # 绘制RSRP等高图 (填充)\n", "            contour_filled = ax.contourf(X_grid, Y_grid, rsrp_grid,\n", "                                        levels=12, cmap='viridis', alpha=0.4, extend='both')\n", "\n", "            # 绘制RSRP等高线\n", "            contour_lines = ax.contour(X_grid, Y_grid, rsrp_grid,\n", "                                    levels=8, colors='darkblue', alpha=0.7, linewidths=1.2)\n", "\n", "            # 添加等高线标签\n", "            try:\n", "                labels = ax.clabel(contour_lines, inline=True, fontsize=8, fmt='%.0f dBm', colors='darkblue')\n", "                for label in labels:\n", "                    label.set_fontweight('bold')\n", "            except:\n", "                print(\"等高线标签添加失败，跳过\")\n", "\n", "            # 添加颜色条\n", "            cbar = plt.colorbar(contour_filled, ax=ax, shrink=0.7, pad=0.02)\n", "            cbar.set_label(f'RSRP from BS{base_station_idx+1} (dBm)', fontsize=12, fontweight='bold')\n", "\n", "            print(f\"成功绘制 BS{base_station_idx+1} 的 RSRP 等高线\")\n", "\n", "        except Exception as e:\n", "            print(f\"等高线绘制失败: {e}\")\n", "            rsrp_interpolated = False\n", "    \n", "    # 如果所有插值方法都失败，添加散点图显示原始数据\n", "    if not rsrp_interpolated:\n", "        print(\"所有插值方法失败，使用散点图显示原始RSRP数据\")\n", "        scatter = ax.scatter(ue_x_all, ue_y_all, c=rsrp_all, cmap='viridis', \n", "                           s=30, alpha=0.6, edgecolors='black', linewidth=0.5)\n", "        cbar = plt.colorbar(scatter, ax=ax, shrink=0.7, pad=0.02)\n", "        cbar.set_label(f'RSRP from BS{base_station_idx+1} (dBm)', fontsize=12, fontweight='bold')\n", "    \n", "    # ==================== 3. 绘制所有UE的运动轨迹 ====================\n", "    # 获取UE总数\n", "    n_ues = ue_real_pos.shape[1]\n", "    print(f\"绘制 {n_ues} 个UE的轨迹\")\n", "    \n", "    # 生成足够多的不同颜色用于UE轨迹\n", "    if n_ues <= 10:\n", "        colors = plt.cm.Set1(np.linspace(0, 1, n_ues))\n", "    else:\n", "        # 对于超过10个UE，使用多个颜色映射组合\n", "        colors1 = plt.cm.Set1(np.linspace(0, 1, 9))  # Set1最多9种颜色\n", "        colors2 = plt.cm.Set2(np.linspace(0, 1, 8))  # Set2最多8种颜色\n", "        colors3 = plt.cm.Dark2(np.linspace(0, 1, 8)) # Dark2最多8种颜色\n", "        colors = np.vstack([colors1, colors2, colors3])\n", "    \n", "    # 绘制每个UE的轨迹\n", "    for ue_idx in range(n_ues):  # 遍历所有UE\n", "        # 获取该UE的轨迹\n", "        x_traj = ue_x[ue_idx, :]  # shape: (100,)\n", "        y_traj = ue_y[ue_idx, :]  # shape: (100,)\n", "        print(x_traj,y_traj)\n", "        # 获取当前UE的颜色\n", "        color = colors[ue_idx % len(colors)]\n", "        \n", "        print(f\"绘制 UE{ue_idx+1}: 起点({x_traj[0]:.1f}, {y_traj[0]:.1f}), 终点({x_traj[-1]:.1f}, {y_traj[-1]:.1f})\")\n", "        \n", "        # 绘制轨迹线\n", "        ax.plot(x_traj, y_traj, \n", "               color=color, alpha=0.8, linewidth=2,\n", "               label=f'UE{ue_idx+1}' if ue_idx < 8 else \"\")  # 只显示前8个UE的标签避免图例过挤\n", "        \n", "        # 标记起始点 (时间=0)\n", "        ax.scatter(x_traj[0], y_traj[0], \n", "                  color=color, s=80, marker='o', \n", "                  edgecolor='black', linewidth=1.5, zorder=10+ue_idx, alpha=0.9)\n", "        \n", "        # 标记结束点 (时间=99)  \n", "        ax.scatter(x_traj[-1], y_traj[-1], \n", "                  color=color, s=80, marker='s', \n", "                  edgecolor='black', linewidth=1.5, zorder=10+ue_idx, alpha=0.9)\n", "        \n", "        # 为所有UE添加起始位置标签，但用不同的偏移避免重叠\n", "        offset_x = 5 + (ue_idx % 3) * 15  # 水平偏移\n", "        offset_y = 5 + (ue_idx % 3) * 10  # 垂直偏移\n", "        \n", "        ax.annotate(f'UE{ue_idx+1}', (x_traj[0], y_traj[0]), \n", "                   xytext=(offset_x, offset_y), textcoords='offset points',\n", "                   fontsize=8, color=color, \n", "                   fontweight='bold',\n", "                   bbox=dict(boxstyle=\"round,pad=0.2\", facecolor='white', alpha=0.8, edgecolor=color),\n", "                   zorder=20+ue_idx)\n", "    \n", "    # ==================== 4. 绘制所有基站位置 ====================\n", "    # 绘制所有基站\n", "    ax.scatter(bs_x, bs_y, c='red', s=300, alpha=1.0, \n", "              marker='^', edgecolor='darkred', linewidth=2,\n", "              label='Base Stations', zorder=15)\n", "    \n", "    # 高亮显示当前分析RSRP的基站\n", "    ax.scatter(bs_x[base_station_idx], bs_y[base_station_idx], \n", "              c='gold', s=400, alpha=1.0, \n", "              marker='^', edgecolor='orange', linewidth=3,\n", "              label=f'BS{base_station_idx+1} (RSRP Source)', zorder=16)\n", "    \n", "    # 为每个基站添加编号和位置信息\n", "    for i, (x, y) in enumerate(zip(bs_x, bs_y)):\n", "        if i == base_station_idx:\n", "            color = 'orange'\n", "            weight = 'bold'\n", "            bbox_color = 'gold'\n", "            alpha = 1.0\n", "        else:\n", "            color = 'darkred'\n", "            weight = 'normal'\n", "            bbox_color = 'white'\n", "            alpha = 0.9\n", "            \n", "        ax.annotate(f'BS{i+1}\\n({bs_x[i]:.0f},{bs_y[i]:.0f})', \n", "                   (x, y), \n", "                   xytext=(10, 10), textcoords='offset points',\n", "                   fontsize=10, fontweight=weight, color=color,\n", "                   bbox=dict(boxstyle=\"round,pad=0.3\", \n", "                           facecolor=bbox_color, alpha=alpha, \n", "                           edgecolor=color, linewidth=1),\n", "                   ha='left', va='bottom')\n", "    \n", "    # ==================== 5. 设置图形属性 ====================\n", "    # 设置标题\n", "    title = f'UE Trajectories + All Base Stations + RSRP Contours (BS{base_station_idx+1})\\nCartesian Coordinate System'\n", "    ax.set_title(title, pad=20, fontsize=16, fontweight='bold')\n", "    \n", "    # 创建详细的图例\n", "    legend_elements = []\n", "    \n", "    # UE轨迹相关\n", "    legend_elements.append(plt.Line2D([0], [0], color='blue', linewidth=2,\n", "                                    label='UE Trajectories'))\n", "    legend_elements.append(plt.Line2D([0], [0], marker='o', color='blue', \n", "                                    markersize=8, linestyle='None', \n", "                                    markeredgecolor='black', markeredgewidth=1,\n", "                                    label='UE Start Position'))\n", "    legend_elements.append(plt.Line2D([0], [0], marker='s', color='blue', \n", "                                    markersize=8, linestyle='None', \n", "                                    markeredgecolor='black', markeredgewidth=1,\n", "                                    label='UE End Position'))\n", "    \n", "    # 基站相关\n", "    legend_elements.append(plt.Line2D([0], [0], marker='^', color='red', \n", "                                    markersize=12, linestyle='None', \n", "                                    markeredgecolor='darkred', markeredgewidth=2,\n", "                                    label='Base Stations'))\n", "    legend_elements.append(plt.Line2D([0], [0], marker='^', color='gold', \n", "                                    markersize=14, linestyle='None', \n", "                                    markeredgecolor='orange', markeredgewidth=2,\n", "                                    label=f'BS{base_station_idx+1} (RSRP Source)'))\n", "    \n", "    # RSRP等高线\n", "    legend_elements.append(plt.Line2D([0], [0], color='darkblue', linewidth=1.2,\n", "                                    label='RSRP Contour Lines'))\n", "    \n", "    ax.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(0.98, 0.98),\n", "             fontsize=10, framealpha=0.9)\n", "    \n", "    # 设置网格和坐标轴\n", "    ax.grid(True, alpha=0.4, linestyle='-', linewidth=0.5)\n", "    ax.set_xlabel('X (m)', fontsize=12, fontweight='bold')\n", "    ax.set_ylabel('Y (m)', fontsize=12, fontweight='bold')\n", "    \n", "    # 设置坐标轴范围\n", "    ax.set_xlim(x_min, x_max)\n", "    ax.set_ylim(y_min, y_max)\n", "    \n", "    # 设置等比例坐标轴\n", "    ax.set_aspect('equal', adjustable='box')\n", "    \n", "    plt.tight_layout()\n", "    \n", "    # 打印统计信息\n", "    print_system_statistics(ue_real_pos, bs_loc, rsrp, base_station_idx)\n", "    \n", "    return fig, ax\n", "\n", "def print_system_statistics(ue_real_pos, bs_loc, rsrp, base_station_idx):\n", "    \"\"\"\n", "    打印系统统计信息\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*50)\n", "    print(\"系统统计信息\")\n", "    print(\"=\"*50)\n", "    \n", "    print(f\"UE数量: {ue_real_pos.shape[1]}\")\n", "    print(f\"时间步数: {ue_real_pos.shape[2]}\")\n", "    print(f\"基站数量: {bs_loc.shape[0]}\")\n", "    print(f\"当前分析基站: BS{base_station_idx+1}\")\n", "    \n", "    # RSRP统计\n", "    rsrp_bs = rsrp[:, :, base_station_idx]\n", "    print(f\"\\nBS{base_station_idx+1}的RSRP统计:\")\n", "    print(f\"  最小值: {rsrp_bs.min():.1f} dBm\")\n", "    print(f\"  最大值: {rsrp_bs.max():.1f} dBm\")\n", "    print(f\"  平均值: {rsrp_bs.mean():.1f} dBm\")\n", "    print(f\"  标准差: {rsrp_bs.std():.1f} dBm\")\n", "    \n", "    # 基站位置\n", "    print(f\"\\n基站位置:\")\n", "    for i in range(bs_loc.shape[0]):\n", "        x, y, z = bs_loc[i, 0], bs_loc[i, 1], bs_loc[i, 2]\n", "        distance_from_origin = np.sqrt(x**2 + y**2)\n", "        marker = \" ← RSRP源\" if i == base_station_idx else \"\"\n", "        print(f\"  BS{i+1}: 坐标({x:.0f}, {y:.0f}, {z:.0f}), 距原点距离={distance_from_origin:.0f}m{marker}\")\n", "\n", "def plot_individual_ue_trajectory(ue_real_pos, bs_loc, rsrp, ue_idx=0, base_station_idx=0):\n", "    \"\"\"\n", "    绘制单个UE的轨迹和对应的RSRP变化\n", "    \n", "    Parameters:\n", "    ue_real_pos: shape (3, 10, 100) - UE位置数据\n", "    bs_loc: shape (3, 3) - 基站位置数据\n", "    rsrp: shape (10, 100, 3) - RSRP数据\n", "    ue_idx: int - 要显示的UE索引\n", "    base_station_idx: int - 要显示RSRP的基站索引\n", "    \"\"\"\n", "    \n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))\n", "    \n", "    # 提取数据\n", "    ue_x = ue_real_pos[0, ue_idx, :]\n", "    ue_y = ue_real_pos[1, ue_idx, :]\n", "    bs_x = bs_loc[:, 0]\n", "    bs_y = bs_loc[:, 1]\n", "    rsrp_values = rsrp[ue_idx, :, base_station_idx]\n", "    \n", "    # 左图：轨迹图\n", "    ax1.plot(ue_x, ue_y, 'b-', linewidth=2, alpha=0.8, label=f'UE{ue_idx+1} Trajectory')\n", "    ax1.scatter(ue_x[0], ue_y[0], color='green', s=100, marker='o', \n", "               edgecolor='darkgreen', linewidth=2, label='Start', zorder=10)\n", "    ax1.scatter(ue_x[-1], ue_y[-1], color='red', s=100, marker='s', \n", "               edgecolor='darkred', linewidth=2, label='End', zorder=10)\n", "    \n", "    # 绘制基站\n", "    ax1.scatter(bs_x, bs_y, c='red', s=200, marker='^', \n", "               edgecolor='darkred', linewidth=2, label='Base Stations', zorder=15)\n", "    ax1.scatter(bs_x[base_station_idx], bs_y[base_station_idx], \n", "               c='gold', s=300, marker='^', edgecolor='orange', linewidth=3,\n", "               label=f'BS{base_station_idx+1} (RSRP Source)', zorder=16)\n", "    \n", "    # 为基站添加标签\n", "    for i, (x, y) in enumerate(zip(bs_x, bs_y)):\n", "        ax1.annotate(f'BS{i+1}', (x, y), xytext=(5, 5), textcoords='offset points',\n", "                    fontsize=10, fontweight='bold')\n", "    \n", "    ax1.set_xlabel('X (m)', fontsize=12)\n", "    ax1.set_ylabel('Y (m)', fontsize=12)\n", "    ax1.set_title(f'UE{ue_idx+1} Trajectory', fontsize=14, fontweight='bold')\n", "    ax1.legend()\n", "    ax1.grid(True, alpha=0.3)\n", "    ax1.set_aspect('equal', adjustable='box')\n", "    \n", "    # 右图：RSRP随时间变化\n", "    time_steps = np.arange(len(rsrp_values))\n", "    ax2.plot(time_steps, rsrp_values, 'r-', linewidth=2, marker='o', markersize=4)\n", "    ax2.set_xlabel('Time Step', fontsize=12)\n", "    ax2.set_ylabel('RSRP (dBm)', fontsize=12)\n", "    ax2.set_title(f'RSRP from BS{base_station_idx+1} for UE{ue_idx+1}', fontsize=14, fontweight='bold')\n", "    ax2.grid(True, alpha=0.3)\n", "    \n", "    # 添加统计信息\n", "    ax2.text(0.02, 0.98, f'Max: {rsrp_values.max():.1f} dBm\\n'\n", "                         f'Min: {rsrp_values.min():.1f} dBm\\n'\n", "                         f'Mean: {rsrp_values.mean():.1f} dBm\\n'\n", "                         f'Std: {rsrp_values.std():.1f} dBm',\n", "            transform=ax2.transAxes, fontsize=10,\n", "            verticalalignment='top',\n", "            bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))\n", "    \n", "    plt.tight_layout()\n", "    return fig, (ax1, ax2)\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["UE位置数据形状: (1000,)\n", "RSRP数据形状: (1000,)\n", "有效数据点数量: 1000\n", "插值前数据检查:\n", "  有效数据点: 1000\n", "  X范围: [12.7, 47.8]\n", "  Y范围: [-30.4, 38.7]\n", "  RSRP范围: [-108.2, -79.8]\n", "  唯一坐标点数: 1000\n", "成功使用 RBF 插值（thin_plate_spline）\n", "成功绘制 BS1 的 RSRP 等高线\n", "绘制 10 个UE的轨迹\n", "[27.5966 27.736  27.8753 28.0147 28.1541 28.2935 28.4328 28.5722 28.7116\n", " 28.851  28.9903 29.1297 29.2691 29.4085 29.5479 29.6872 29.8266 29.966\n", " 30.1054 30.2447 30.3841 30.5235 30.6629 30.8022 30.9416 31.081  31.2204\n", " 31.3597 31.4991 31.6385 31.7779 31.9173 32.0566 32.196  32.3354 32.4748\n", " 32.6141 32.7535 32.8929 33.0323 33.1716 33.311  33.4504 33.5898 33.7292\n", " 33.8685 34.0079 34.1473 34.2867 34.426  34.5654 34.7048 34.8442 34.9835\n", " 35.1229 35.2623 35.4017 35.541  35.6804 35.8198 35.9592 36.0986 36.2379\n", " 36.3773 36.5167 36.6561 36.7954 36.9348 37.0742 37.2136 37.3529 37.4923\n", " 37.6317 37.7711 37.9105 38.0498 38.1892 38.3286 38.468  38.6073 38.7467\n", " 38.8861 39.0255 39.1648 39.3042 39.4436 39.583  39.7223 39.8617 40.0011\n", " 40.1405 40.2799 40.4192 40.5586 40.698  40.8374 40.9767 41.1161 41.2555\n", " 41.3949] [-18.095  -18.1864 -18.2778 -18.3692 -18.4606 -18.5519 -18.6433 -18.7347\n", " -18.8261 -18.9175 -19.0089 -19.1003 -19.1917 -19.2831 -19.3744 -19.4658\n", " -19.5572 -19.6486 -19.74   -19.8314 -19.9228 -20.0142 -20.1056 -20.1969\n", " -20.2883 -20.3797 -20.4711 -20.5625 -20.6539 -20.7453 -20.8367 -20.9281\n", " -21.0194 -21.1108 -21.2022 -21.2936 -21.385  -21.4764 -21.5678 -21.6592\n", " -21.7506 -21.8419 -21.9333 -22.0247 -22.1161 -22.2075 -22.2989 -22.3903\n", " -22.4817 -22.5731 -22.6644 -22.7558 -22.8472 -22.9386 -23.03   -23.1214\n", " -23.2128 -23.3042 -23.3956 -23.4869 -23.5783 -23.6697 -23.7611 -23.8525\n", " -23.9439 -24.0353 -24.1267 -24.2181 -24.3094 -24.4008 -24.4922 -24.5836\n", " -24.675  -24.7664 -24.8578 -24.9492 -25.0406 -25.1319 -25.2233 -25.3147\n", " -25.4061 -25.4975 -25.5889 -25.6803 -25.7717 -25.8631 -25.9544 -26.0458\n", " -26.1372 -26.2286 -26.32   -26.4114 -26.5028 -26.5942 -26.6856 -26.7769\n", " -26.8683 -26.9597 -27.0511 -27.1425]\n", "绘制 UE1: 起点(27.6, -18.1), 终点(41.4, -27.1)\n", "[15.9973 16.1639 16.3305 16.4972 16.6638 16.8304 16.9971 17.1637 17.3304\n", " 17.497  17.6636 17.8303 17.9969 18.1636 18.3302 18.4968 18.6635 18.8301\n", " 18.9967 19.1634 19.33   19.4967 19.6633 19.8299 19.9966 20.1632 20.3298\n", " 20.4965 20.6631 20.8298 20.9964 21.163  21.3297 21.4963 21.663  21.8296\n", " 21.9962 22.1629 22.3295 22.4961 22.6628 22.8294 22.9961 23.1627 23.3293\n", " 23.496  23.6626 23.8292 23.9959 24.1625 24.3292 24.4958 24.6624 24.8291\n", " 24.9957 25.1624 25.329  25.4956 25.6623 25.8289 25.9955 26.1622 26.3288\n", " 26.4955 26.6621 26.8287 26.9954 27.162  27.3286 27.4953 27.6619 27.8286\n", " 27.9952 28.1618 28.3285 28.4951 28.6618 28.8284 28.995  29.1617 29.3283\n", " 29.4949 29.6616 29.8282 29.9949 30.1615 30.3281 30.4948 30.6614 30.828\n", " 30.9947 31.1613 31.328  31.4946 31.6612 31.8279 31.9945 32.1612 32.3278\n", " 32.4944] [-0.2963 -0.2994 -0.3025 -0.3055 -0.3086 -0.3117 -0.3148 -0.3179 -0.321\n", " -0.3241 -0.3272 -0.3302 -0.3333 -0.3364 -0.3395 -0.3426 -0.3457 -0.3488\n", " -0.3518 -0.3549 -0.358  -0.3611 -0.3642 -0.3673 -0.3704 -0.3734 -0.3765\n", " -0.3796 -0.3827 -0.3858 -0.3889 -0.392  -0.3951 -0.3981 -0.4012 -0.4043\n", " -0.4074 -0.4105 -0.4136 -0.4167 -0.4197 -0.4228 -0.4259 -0.429  -0.4321\n", " -0.4352 -0.4383 -0.4413 -0.4444 -0.4475 -0.4506 -0.4537 -0.4568 -0.4599\n", " -0.4629 -0.466  -0.4691 -0.4722 -0.4753 -0.4784 -0.4815 -0.4846 -0.4876\n", " -0.4907 -0.4938 -0.4969 -0.5    -0.5031 -0.5062 -0.5092 -0.5123 -0.5154\n", " -0.5185 -0.5216 -0.5247 -0.5278 -0.5308 -0.5339 -0.537  -0.5401 -0.5432\n", " -0.5463 -0.5494 -0.5525 -0.5555 -0.5586 -0.5617 -0.5648 -0.5679 -0.571\n", " -0.5741 -0.5771 -0.5802 -0.5833 -0.5864 -0.5895 -0.5926 -0.5957 -0.5987\n", " -0.6018]\n", "绘制 UE2: 起点(16.0, -0.3), 终点(32.5, -0.6)\n", "[32.1628 32.3204 32.4781 32.6357 32.7934 32.9511 33.1087 33.2664 33.424\n", " 33.5817 33.7394 33.897  34.0547 34.2123 34.37   34.5277 34.6853 34.843\n", " 35.0007 35.1583 35.316  35.4736 35.6313 35.789  35.9466 36.1043 36.2619\n", " 36.4196 36.5773 36.7349 36.8926 37.0502 37.2079 37.3656 37.5232 37.6809\n", " 37.8385 37.9962 38.1539 38.3115 38.4692 38.6268 38.7845 38.9422 39.0998\n", " 39.2575 39.4151 39.5728 39.7305 39.8881 40.0458 40.2035 40.3611 40.5188\n", " 40.6764 40.8341 40.9918 41.1494 41.3071 41.4647 41.6224 41.7801 41.9377\n", " 42.0954 42.253  42.4107 42.5684 42.726  42.8837 43.0413 43.199  43.3567\n", " 43.5143 43.672  43.8296 43.9873 44.145  44.3026 44.4603 44.6179 44.7756\n", " 44.9333 45.0909 45.2486 45.4063 45.5639 45.7216 45.8792 46.0369 46.1946\n", " 46.3522 46.5099 46.6675 46.8252 46.9829 47.1405 47.2982 47.4558 47.6135\n", " 47.7712] [11.0253 11.0793 11.1334 11.1874 11.2415 11.2955 11.3496 11.4036 11.4576\n", " 11.5117 11.5657 11.6198 11.6738 11.7279 11.7819 11.836  11.89   11.9441\n", " 11.9981 12.0521 12.1062 12.1602 12.2143 12.2683 12.3224 12.3764 12.4305\n", " 12.4845 12.5386 12.5926 12.6466 12.7007 12.7547 12.8088 12.8628 12.9169\n", " 12.9709 13.025  13.079  13.1331 13.1871 13.2411 13.2952 13.3492 13.4033\n", " 13.4573 13.5114 13.5654 13.6195 13.6735 13.7276 13.7816 13.8356 13.8897\n", " 13.9437 13.9978 14.0518 14.1059 14.1599 14.214  14.268  14.3221 14.3761\n", " 14.4301 14.4842 14.5382 14.5923 14.6463 14.7004 14.7544 14.8085 14.8625\n", " 14.9166 14.9706 15.0246 15.0787 15.1327 15.1868 15.2408 15.2949 15.3489\n", " 15.403  15.457  15.5111 15.5651 15.6191 15.6732 15.7272 15.7813 15.8353\n", " 15.8894 15.9434 15.9975 16.0515 16.1056 16.1596 16.2136 16.2677 16.3217\n", " 16.3758]\n", "绘制 UE3: 起点(32.2, 11.0), 终点(47.8, 16.4)\n", "[12.7106 12.8069 12.9032 12.9995 13.0958 13.1921 13.2883 13.3846 13.4809\n", " 13.5772 13.6735 13.7698 13.8661 13.9624 14.0587 14.155  14.2513 14.3476\n", " 14.4439 14.5401 14.6364 14.7327 14.829  14.9253 15.0216 15.1179 15.2142\n", " 15.3105 15.4068 15.5031 15.5994 15.6957 15.7919 15.8882 15.9845 16.0808\n", " 16.1771 16.2734 16.3697 16.466  16.5623 16.6586 16.7549 16.8512 16.9475\n", " 17.0437 17.14   17.2363 17.3326 17.4289 17.5252 17.6215 17.7178 17.8141\n", " 17.9104 18.0067 18.103  18.1993 18.2955 18.3918 18.4881 18.5844 18.6807\n", " 18.777  18.8733 18.9696 19.0659 19.1622 19.2585 19.3548 19.4511 19.5473\n", " 19.6436 19.7399 19.8362 19.9325 20.0288 20.1251 20.2214 20.3177 20.414\n", " 20.5103 20.6066 20.7029 20.7991 20.8954 20.9917 21.088  21.1843 21.2806\n", " 21.3769 21.4732 21.5695 21.6658 21.7621 21.8584 21.9547 22.0509 22.1472\n", " 22.2435] [17.9566 18.0927 18.2287 18.3647 18.5008 18.6368 18.7729 18.9089 19.0449\n", " 19.181  19.317  19.453  19.5891 19.7251 19.8611 19.9972 20.1332 20.2692\n", " 20.4053 20.5413 20.6773 20.8134 20.9494 21.0854 21.2215 21.3575 21.4936\n", " 21.6296 21.7656 21.9017 22.0377 22.1737 22.3098 22.4458 22.5818 22.7179\n", " 22.8539 22.9899 23.126  23.262  23.398  23.5341 23.6701 23.8062 23.9422\n", " 24.0782 24.2143 24.3503 24.4863 24.6224 24.7584 24.8944 25.0305 25.1665\n", " 25.3025 25.4386 25.5746 25.7106 25.8467 25.9827 26.1187 26.2548 26.3908\n", " 26.5269 26.6629 26.7989 26.935  27.071  27.207  27.3431 27.4791 27.6151\n", " 27.7512 27.8872 28.0232 28.1593 28.2953 28.4313 28.5674 28.7034 28.8395\n", " 28.9755 29.1115 29.2476 29.3836 29.5196 29.6557 29.7917 29.9277 30.0638\n", " 30.1998 30.3358 30.4719 30.6079 30.7439 30.88   31.016  31.152  31.2881\n", " 31.4241]\n", "绘制 UE4: 起点(12.7, 18.0), 终点(22.2, 31.4)\n", "[25.9945 26.1343 26.274  26.4138 26.5535 26.6933 26.8331 26.9728 27.1126\n", " 27.2523 27.3921 27.5318 27.6716 27.8113 27.9511 28.0909 28.2306 28.3704\n", " 28.5101 28.6499 28.7896 28.9294 29.0691 29.2089 29.3487 29.4884 29.6282\n", " 29.7679 29.9077 30.0474 30.1872 30.3269 30.4667 30.6065 30.7462 30.886\n", " 31.0257 31.1655 31.3052 31.445  31.5847 31.7245 31.8643 32.004  32.1438\n", " 32.2835 32.4233 32.563  32.7028 32.8425 32.9823 33.1221 33.2618 33.4016\n", " 33.5413 33.6811 33.8208 33.9606 34.1003 34.2401 34.3799 34.5196 34.6594\n", " 34.7991 34.9389 35.0786 35.2184 35.3581 35.4979 35.6377 35.7774 35.9172\n", " 36.0569 36.1967 36.3364 36.4762 36.6159 36.7557 36.8955 37.0352 37.175\n", " 37.3147 37.4545 37.5942 37.734  37.8737 38.0135 38.1533 38.293  38.4328\n", " 38.5725 38.7123 38.852  38.9918 39.1315 39.2713 39.4111 39.5508 39.6906\n", " 39.8303] [16.8904 16.9812 17.072  17.1628 17.2536 17.3444 17.4352 17.526  17.6168\n", " 17.7076 17.7985 17.8893 17.9801 18.0709 18.1617 18.2525 18.3433 18.4341\n", " 18.5249 18.6157 18.7065 18.7973 18.8882 18.979  19.0698 19.1606 19.2514\n", " 19.3422 19.433  19.5238 19.6146 19.7054 19.7962 19.887  19.9779 20.0687\n", " 20.1595 20.2503 20.3411 20.4319 20.5227 20.6135 20.7043 20.7951 20.8859\n", " 20.9767 21.0676 21.1584 21.2492 21.34   21.4308 21.5216 21.6124 21.7032\n", " 21.794  21.8848 21.9756 22.0664 22.1573 22.2481 22.3389 22.4297 22.5205\n", " 22.6113 22.7021 22.7929 22.8837 22.9745 23.0653 23.1561 23.247  23.3378\n", " 23.4286 23.5194 23.6102 23.701  23.7918 23.8826 23.9734 24.0642 24.155\n", " 24.2459 24.3367 24.4275 24.5183 24.6091 24.6999 24.7907 24.8815 24.9723\n", " 25.0631 25.1539 25.2447 25.3356 25.4264 25.5172 25.608  25.6988 25.7896\n", " 25.8804]\n", "绘制 UE5: 起点(26.0, 16.9), 终点(39.8, 25.9)\n", "[22.872  22.9991 23.1262 23.2532 23.3803 23.5074 23.6344 23.7615 23.8886\n", " 24.0156 24.1427 24.2698 24.3968 24.5239 24.651  24.778  24.9051 25.0322\n", " 25.1592 25.2863 25.4134 25.5404 25.6675 25.7946 25.9216 26.0487 26.1758\n", " 26.3028 26.4299 26.557  26.684  26.8111 26.9382 27.0652 27.1923 27.3194\n", " 27.4464 27.5735 27.7006 27.8276 27.9547 28.0818 28.2088 28.3359 28.463\n", " 28.59   28.7171 28.8442 28.9712 29.0983 29.2254 29.3525 29.4795 29.6066\n", " 29.7337 29.8607 29.9878 30.1149 30.2419 30.369  30.4961 30.6231 30.7502\n", " 30.8773 31.0043 31.1314 31.2585 31.3855 31.5126 31.6397 31.7667 31.8938\n", " 32.0209 32.1479 32.275  32.4021 32.5291 32.6562 32.7833 32.9103 33.0374\n", " 33.1645 33.2915 33.4186 33.5457 33.6727 33.7998 33.9269 34.0539 34.181\n", " 34.3081 34.4351 34.5622 34.6893 34.8163 34.9434 35.0705 35.1975 35.3246\n", " 35.4517] [-19.4131 -19.521  -19.6288 -19.7367 -19.8445 -19.9524 -20.0602 -20.1681\n", " -20.2759 -20.3838 -20.4916 -20.5995 -20.7073 -20.8152 -20.923  -21.0309\n", " -21.1387 -21.2466 -21.3544 -21.4623 -21.5702 -21.678  -21.7859 -21.8937\n", " -22.0016 -22.1094 -22.2173 -22.3251 -22.433  -22.5408 -22.6487 -22.7565\n", " -22.8644 -22.9722 -23.0801 -23.1879 -23.2958 -23.4036 -23.5115 -23.6193\n", " -23.7272 -23.835  -23.9429 -24.0507 -24.1586 -24.2664 -24.3743 -24.4821\n", " -24.59   -24.6978 -24.8057 -24.9135 -25.0214 -25.1292 -25.2371 -25.3449\n", " -25.4528 -25.5606 -25.6685 -25.7763 -25.8842 -25.992  -26.0999 -26.2077\n", " -26.3156 -26.4234 -26.5313 -26.6391 -26.747  -26.8548 -26.9627 -27.0705\n", " -27.1784 -27.2862 -27.3941 -27.5019 -27.6098 -27.7176 -27.8255 -27.9333\n", " -28.0412 -28.149  -28.2569 -28.3647 -28.4726 -28.5804 -28.6883 -28.7962\n", " -28.904  -29.0119 -29.1197 -29.2276 -29.3354 -29.4433 -29.5511 -29.659\n", " -29.7668 -29.8747 -29.9825 -30.0904]\n", "绘制 UE6: 起点(22.9, -19.4), 终点(35.5, -30.1)\n", "[17.0863 17.2287 17.3711 17.5135 17.6558 17.7982 17.9406 18.083  18.2254\n", " 18.3678 18.5102 18.6526 18.7949 18.9373 19.0797 19.2221 19.3645 19.5069\n", " 19.6493 19.7916 19.934  20.0764 20.2188 20.3612 20.5036 20.646  20.7883\n", " 20.9307 21.0731 21.2155 21.3579 21.5003 21.6427 21.785  21.9274 22.0698\n", " 22.2122 22.3546 22.497  22.6394 22.7817 22.9241 23.0665 23.2089 23.3513\n", " 23.4937 23.6361 23.7784 23.9208 24.0632 24.2056 24.348  24.4904 24.6328\n", " 24.7751 24.9175 25.0599 25.2023 25.3447 25.4871 25.6295 25.7718 25.9142\n", " 26.0566 26.199  26.3414 26.4838 26.6262 26.7685 26.9109 27.0533 27.1957\n", " 27.3381 27.4805 27.6229 27.7652 27.9076 28.05   28.1924 28.3348 28.4772\n", " 28.6196 28.7619 28.9043 29.0467 29.1891 29.3315 29.4739 29.6163 29.7586\n", " 29.901  30.0434 30.1858 30.3282 30.4706 30.613  30.7554 30.8977 31.0401\n", " 31.1825] [10.3951 10.4817 10.5684 10.655  10.7416 10.8282 10.9149 11.0015 11.0881\n", " 11.1747 11.2614 11.348  11.4346 11.5212 11.6079 11.6945 11.7811 11.8677\n", " 11.9544 12.041  12.1276 12.2142 12.3009 12.3875 12.4741 12.5607 12.6474\n", " 12.734  12.8206 12.9073 12.9939 13.0805 13.1671 13.2538 13.3404 13.427\n", " 13.5136 13.6003 13.6869 13.7735 13.8601 13.9468 14.0334 14.12   14.2066\n", " 14.2933 14.3799 14.4665 14.5531 14.6398 14.7264 14.813  14.8996 14.9863\n", " 15.0729 15.1595 15.2462 15.3328 15.4194 15.506  15.5927 15.6793 15.7659\n", " 15.8525 15.9392 16.0258 16.1124 16.199  16.2857 16.3723 16.4589 16.5455\n", " 16.6322 16.7188 16.8054 16.892  16.9787 17.0653 17.1519 17.2385 17.3252\n", " 17.4118 17.4984 17.585  17.6717 17.7583 17.8449 17.9316 18.0182 18.1048\n", " 18.1914 18.2781 18.3647 18.4513 18.5379 18.6246 18.7112 18.7978 18.8844\n", " 18.9711]\n", "绘制 UE7: 起点(17.1, 10.4), 终点(31.2, 19.0)\n", "[22.7104 22.8366 22.9627 23.0889 23.2151 23.3412 23.4674 23.5936 23.7197\n", " 23.8459 23.9721 24.0982 24.2244 24.3506 24.4767 24.6029 24.7291 24.8553\n", " 24.9814 25.1076 25.2338 25.3599 25.4861 25.6123 25.7384 25.8646 25.9908\n", " 26.1169 26.2431 26.3693 26.4954 26.6216 26.7478 26.874  27.0001 27.1263\n", " 27.2525 27.3786 27.5048 27.631  27.7571 27.8833 28.0095 28.1356 28.2618\n", " 28.388  28.5141 28.6403 28.7665 28.8927 29.0188 29.145  29.2712 29.3973\n", " 29.5235 29.6497 29.7758 29.902  30.0282 30.1543 30.2805 30.4067 30.5329\n", " 30.659  30.7852 30.9114 31.0375 31.1637 31.2899 31.416  31.5422 31.6684\n", " 31.7945 31.9207 32.0469 32.173  32.2992 32.4254 32.5516 32.6777 32.8039\n", " 32.9301 33.0562 33.1824 33.3086 33.4347 33.5609 33.6871 33.8132 33.9394\n", " 34.0656 34.1917 34.3179 34.4441 34.5703 34.6964 34.8226 34.9488 35.0749\n", " 35.2011] [-19.602  -19.7109 -19.8198 -19.9287 -20.0376 -20.1465 -20.2554 -20.3643\n", " -20.4732 -20.5821 -20.691  -20.7999 -20.9088 -21.0177 -21.1266 -21.2355\n", " -21.3444 -21.4533 -21.5622 -21.6711 -21.78   -21.8889 -21.9978 -22.1067\n", " -22.2156 -22.3245 -22.4334 -22.5423 -22.6512 -22.7601 -22.869  -22.9779\n", " -23.0868 -23.1957 -23.3046 -23.4135 -23.5224 -23.6313 -23.7402 -23.8491\n", " -23.958  -24.0669 -24.1758 -24.2847 -24.3936 -24.5025 -24.6114 -24.7203\n", " -24.8292 -24.9381 -25.047  -25.1559 -25.2648 -25.3737 -25.4826 -25.5915\n", " -25.7004 -25.8093 -25.9182 -26.0271 -26.136  -26.2449 -26.3538 -26.4627\n", " -26.5716 -26.6805 -26.7894 -26.8983 -27.0072 -27.1161 -27.225  -27.3339\n", " -27.4428 -27.5517 -27.6606 -27.7695 -27.8784 -27.9873 -28.0962 -28.2051\n", " -28.314  -28.4229 -28.5318 -28.6407 -28.7496 -28.8585 -28.9674 -29.0763\n", " -29.1852 -29.2941 -29.403  -29.5119 -29.6208 -29.7297 -29.8386 -29.9475\n", " -30.0564 -30.1653 -30.2742 -30.3831]\n", "绘制 UE8: 起点(22.7, -19.6), 终点(35.2, -30.4)\n", "[24.987  25.1536 25.3202 25.4868 25.6533 25.8199 25.9865 26.1531 26.3197\n", " 26.4862 26.6528 26.8194 26.986  27.1526 27.3191 27.4857 27.6523 27.8189\n", " 27.9855 28.152  28.3186 28.4852 28.6518 28.8184 28.9849 29.1515 29.3181\n", " 29.4847 29.6513 29.8178 29.9844 30.151  30.3176 30.4842 30.6507 30.8173\n", " 30.9839 31.1505 31.3171 31.4836 31.6502 31.8168 31.9834 32.15   32.3165\n", " 32.4831 32.6497 32.8163 32.9829 33.1494 33.316  33.4826 33.6492 33.8158\n", " 33.9823 34.1489 34.3155 34.4821 34.6487 34.8152 34.9818 35.1484 35.315\n", " 35.4816 35.6481 35.8147 35.9813 36.1479 36.3145 36.481  36.6476 36.8142\n", " 36.9808 37.1474 37.3139 37.4805 37.6471 37.8137 37.9803 38.1468 38.3134\n", " 38.48   38.6466 38.8132 38.9797 39.1463 39.3129 39.4795 39.6461 39.8126\n", " 39.9792 40.1458 40.3124 40.479  40.6455 40.8121 40.9787 41.1453 41.3119\n", " 41.4784] [-0.8058 -0.8112 -0.8165 -0.8219 -0.8273 -0.8326 -0.838  -0.8434 -0.8488\n", " -0.8541 -0.8595 -0.8649 -0.8702 -0.8756 -0.881  -0.8864 -0.8917 -0.8971\n", " -0.9025 -0.9078 -0.9132 -0.9186 -0.924  -0.9293 -0.9347 -0.9401 -0.9455\n", " -0.9508 -0.9562 -0.9616 -0.9669 -0.9723 -0.9777 -0.9831 -0.9884 -0.9938\n", " -0.9992 -1.0045 -1.0099 -1.0153 -1.0207 -1.026  -1.0314 -1.0368 -1.0421\n", " -1.0475 -1.0529 -1.0583 -1.0636 -1.069  -1.0744 -1.0797 -1.0851 -1.0905\n", " -1.0959 -1.1012 -1.1066 -1.112  -1.1174 -1.1227 -1.1281 -1.1335 -1.1388\n", " -1.1442 -1.1496 -1.155  -1.1603 -1.1657 -1.1711 -1.1764 -1.1818 -1.1872\n", " -1.1926 -1.1979 -1.2033 -1.2087 -1.214  -1.2194 -1.2248 -1.2302 -1.2355\n", " -1.2409 -1.2463 -1.2517 -1.257  -1.2624 -1.2678 -1.2731 -1.2785 -1.2839\n", " -1.2893 -1.2946 -1.3    -1.3054 -1.3107 -1.3161 -1.3215 -1.3269 -1.3322\n", " -1.3376]\n", "绘制 UE9: 起点(25.0, -0.8), 终点(41.5, -1.3)\n", "[20.5467 20.6504 20.7542 20.858  20.9618 21.0655 21.1693 21.2731 21.3768\n", " 21.4806 21.5844 21.6882 21.7919 21.8957 21.9995 22.1032 22.207  22.3108\n", " 22.4146 22.5183 22.6221 22.7259 22.8296 22.9334 23.0372 23.141  23.2447\n", " 23.3485 23.4523 23.556  23.6598 23.7636 23.8674 23.9711 24.0749 24.1787\n", " 24.2824 24.3862 24.49   24.5938 24.6975 24.8013 24.9051 25.0088 25.1126\n", " 25.2164 25.3201 25.4239 25.5277 25.6315 25.7352 25.839  25.9428 26.0465\n", " 26.1503 26.2541 26.3579 26.4616 26.5654 26.6692 26.7729 26.8767 26.9805\n", " 27.0843 27.188  27.2918 27.3956 27.4993 27.6031 27.7069 27.8107 27.9144\n", " 28.0182 28.122  28.2257 28.3295 28.4333 28.5371 28.6408 28.7446 28.8484\n", " 28.9521 29.0559 29.1597 29.2635 29.3672 29.471  29.5748 29.6785 29.7823\n", " 29.8861 29.9898 30.0936 30.1974 30.3012 30.4049 30.5087 30.6125 30.7162\n", " 30.82  ] [25.8231 25.9535 26.084  26.2144 26.3448 26.4752 26.6056 26.7361 26.8665\n", " 26.9969 27.1273 27.2577 27.3882 27.5186 27.649  27.7794 27.9098 28.0403\n", " 28.1707 28.3011 28.4315 28.5619 28.6924 28.8228 28.9532 29.0836 29.214\n", " 29.3445 29.4749 29.6053 29.7357 29.8661 29.9966 30.127  30.2574 30.3878\n", " 30.5182 30.6487 30.7791 30.9095 31.0399 31.1703 31.3008 31.4312 31.5616\n", " 31.692  31.8224 31.9529 32.0833 32.2137 32.3441 32.4745 32.605  32.7354\n", " 32.8658 32.9962 33.1266 33.2571 33.3875 33.5179 33.6483 33.7787 33.9092\n", " 34.0396 34.17   34.3004 34.4308 34.5613 34.6917 34.8221 34.9525 35.0829\n", " 35.2134 35.3438 35.4742 35.6046 35.735  35.8655 35.9959 36.1263 36.2567\n", " 36.3871 36.5176 36.648  36.7784 36.9088 37.0392 37.1697 37.3001 37.4305\n", " 37.5609 37.6913 37.8218 37.9522 38.0826 38.213  38.3434 38.4739 38.6043\n", " 38.7347]\n", "绘制 UE10: 起点(20.5, 25.8), 终点(30.8, 38.7)\n", "\n", "==================================================\n", "系统统计信息\n", "==================================================\n", "UE数量: 10\n", "时间步数: 100\n", "基站数量: 3\n", "当前分析基站: BS1\n", "\n", "BS1的RSRP统计:\n", "  最小值: -108.2 dBm\n", "  最大值: -79.8 dBm\n", "  平均值: -94.5 dBm\n", "  标准差: 4.5 dBm\n", "\n", "基站位置:\n", "  BS1: 坐标(0, 0, 3), 距原点距离=0m ← RSRP源\n", "  BS2: 坐标(-20, 0, 3), 距原点距离=20m\n", "  BS3: 坐标(20, 0, 3), 距原点距离=20m\n"]}], "source": ["# 绘制完整的系统图 (显示第一个基站的RSRP)\n", "fig, ax = plot_complete_cartesian_system(ue_real_pos, bs_loc, rsrp, base_station_idx=0)\n", "plt.show()\n", "plt.savefig(\"rsrp_fe.png\")\n", "# 也可以显示其他基站的RSRP\n", "fig2, ax2 = plot_individual_ue_trajectory(ue_real_pos, bs_loc, rsrp, base_station_idx=1)\n", "plt.show()\n", "plt.savefig(\"rsrp_fe1.png\")"]}], "metadata": {"kernelspec": {"display_name": "meiziy", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 2}