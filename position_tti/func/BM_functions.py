from math import pi
import os,sys
import numpy as np
import pandas as pd
import re
from utils import *

from utils import PMI, sinr2mib
from utils.cqi2sinr import Sinr2Cqi
from utils.mcs2qam import Mcs2Qam
from utils.mib2bler import Mib2Bler

import matplotlib.pyplot as plt
import scipy.linalg as LA
import scipy.signal as ss 

#global data
PMI_class = PMI()
sinr2mi = sinr2mib.Sinr2Mib()
mi2sinr = sinr2mib.Mib2Sinr()
mcs2qam = Mcs2Qam()
mi2bler = Mib2Bler()
sinr2cqi = Sinr2Cqi()
sinr2mcs_table = np.array([-5.23,-4.7,-3.7,-3.18,-2.45,-1.6,-0.88,-0.12,
                                        0.79,1.38,1.88,2.7,3.65,4.51,5.24,5.88,
                                        6.75,7.29,8.23,9.14,9.92,11.09,11.86,12.93,
                                        14.14,14.98,15.91,17.15])

# Functions
def array_response_vector(array,theta):
    N = array.shape
    v = np.exp(1j*2*np.pi*array*np.sin(theta))
    return v/np.sqrt(N)

def music(CovMat,L,N,array,Angles):
    # CovMat is the signal covariance matrix, L is the number of sources, N is the number of antennas
    # array holds the positions of antenna elements
    # Angles are the grid of directions in the azimuth angular domain
    _,V = LA.eig(CovMat)
    Qn  = V[:,N-L:N]
    numAngles = Angles.size
    pspectrum = np.zeros(numAngles)
    for i in range(numAngles):
        av = array_response_vector(array,Angles[i])
        pspectrum[i] = 1/LA.norm((Qn.conj().transpose()@av))
    psindB    = np.log10(10*pspectrum/pspectrum.min())
    DoAsMUSIC,_= ss.find_peaks(psindB,height=1.35, distance=1.5)
    return DoAsMUSIC,pspectrum

def esprit(CovMat,L,N):
    # CovMat is the signal covariance matrix, L is the number of sources, N is the number of antennas
    _,U = LA.eig(CovMat)
    S = U[:,0:L]
    Phi = LA.pinv(S[0:N-1]) @ S[1:N] # the original array is divided into two subarrays [0,1,...,N-2] and [1,2,...,N-1]
    eigs,_ = LA.eig(Phi)
    DoAsESPRIT = np.arcsin(np.angle(eigs)/np.pi)
    return DoAsESPRIT

def generate_beams(H_angles=[], V_angles=[], numHant=8, numVant=4, HSpacing=0.5, VSpacing=0.5,downtilt=0):
    HAnt_pos = HSpacing * np.repeat(np.expand_dims(np.arange(0, numHant), 0), numVant, 0)
    VAnt_pos = VSpacing * np.repeat(np.expand_dims(np.arange(0, numVant), 1), numHant, 1)
        
    HAnt_pos = np.reshape(HAnt_pos,(1,-1))
    VAnt_pos = np.reshape(VAnt_pos,(1,-1))

    H_channel_angles = np.repeat(np.expand_dims(H_angles, 1), V_angles.size, 1)
    V_channel_angles = np.repeat(np.expand_dims(V_angles, 0), H_angles.size, 0)
    
    H_channel_angles = 90 - H_channel_angles


    H_channel_angles = np.reshape(H_channel_angles,(-1,1))
    V_channel_angles = np.reshape(V_channel_angles,(1,-1))
    V_channel_angles = V_channel_angles.transpose(1,0)
    
    # H_channel_angles = H_angles
    # V_channel_angles = V_angles
    
    channel = np.exp(-1j*2*pi*(np.cos(H_channel_angles/180*pi)*np.cos(V_channel_angles/180*pi)@HAnt_pos+np.sin(V_channel_angles/180*pi)@VAnt_pos))

    return channel 

def generate_beams_multiple(H_angles=[], V_angles=[], numHant=8, numVant=4, HSpacing=0.5, VSpacing=0.5,downtilt=0):
    HAnt_pos = HSpacing * np.repeat(np.expand_dims(np.arange(0, numHant), 0), numVant, 0)
    VAnt_pos = VSpacing * np.repeat(np.expand_dims(np.arange(0, numVant), 1), numHant, 1)
        
    HAnt_pos = np.reshape(HAnt_pos,(1,-1))
    VAnt_pos = np.reshape(VAnt_pos,(1,-1))

    H_channel_angles = H_angles.reshape(-1,1)
    V_channel_angles = V_angles.reshape(-1,1)
    
    H_channel_angles = 90 - H_channel_angles
    
    channel = np.exp(-1j*2*pi*(np.cos(H_channel_angles/180*pi)*np.cos(V_channel_angles/180*pi)@HAnt_pos+np.sin(V_channel_angles/180*pi)@VAnt_pos))

    return channel 

def calculate_AOD_EOD(ueloc, bsloc):
    # 计算相对位置向量
    relative_pos = np.array(ueloc) - np.array(bsloc)
    dis = np.sqrt(np.sum(np.power(relative_pos,2)))

    # 计算水平角度 AOD
    horizontal_angle = np.arctan2(relative_pos[1], relative_pos[0]) * 180 / np.pi

    # 计算垂直角度 EOD
    vertical_angle = np.arctan2(relative_pos[2], np.linalg.norm(relative_pos[:2])) * 180 / np.pi

    return horizontal_angle, vertical_angle, dis

def cal_coma(srs_p1,srs_p2): # (nUE, nAnts)
    srs_p1 = np.expand_dims(srs_p1,-2)
    srs_p2 = np.expand_dims(srs_p2,-2)
    #!coma srs.H@srs 
    # srs_p1.H@srs_p1
    # Coma (nUE, nAnts ,nAnts)
    Coma_p1 = np.conj(srs_p1.transpose(0,2,1))@srs_p1     #srs.H@srs
    Coma_p2 = np.conj(srs_p2.transpose(0,2,1))@srs_p2     #srs.H@srs

    Tra_p1 = np.trace(Coma_p1,axis1=-2,axis2=-1)
    Tra_p2 = np.trace(Coma_p2,axis1=-2,axis2=-1)
    a1 = np.divide(Tra_p1,Tra_p1+Tra_p2+1e-50)
    a2 = np.divide(Tra_p2,Tra_p1+Tra_p2+1e-50)
    Coma = Coma_p1*np.expand_dims(a1,axis=(-1,-2)) + Coma_p2*np.expand_dims(a2,axis=(-1,-2))
    return Coma    

def update_svd_Beam(Coma,n=None):
    s1,v1,d1 = np.linalg.svd(Coma)
    s1 = s1.T
    if n is not None:
        beam = np.transpose(s1[0:n+1],(1,0))
    else:
        beam = np.transpose(s1,(1,0))
    return beam
    
def update_Beam(Coma,Beams):
    """
    Coma nSub*Ants*Ants
    Coma_Long和全部可选的Beams计算全部可选的Beams的能量，从这些Beams选择能量最大和第二大的Beam返回(idx1, power1, idx2, power2)
    """
    Coma = Coma
    Beams = np.expand_dims(Beams,2) #nBeam*n_ant*1\

    Beam_ = np.expand_dims(Beams,0)
    Coma_ = np.expand_dims(Coma,1)
    Beam_power = np.conj(Beam_.transpose(0,1,3,2))@Coma_@Beam_ #Beam.H@Coma@Beam
    Beam_power = Beam_power.squeeze((-1,-2)) # 为每个UE从所有Beam中选择最大能量的一个
    
    Coma_norm = np.linalg.norm(Coma,axis=(-1,-2)) #nUE
    Beams_norm = np.linalg.norm(Beams,axis=(-1,-2)) #nBeam
    Beam_power = np.abs(Beam_power/np.expand_dims(Coma_norm,1)/np.expand_dims(Beams_norm,0)) #

    idx = np.argsort(Beam_power,kind='mergesort',axis=1)
    # power = np.sort(Beam_power,kind='mergesort',axis=1)
    idx = np.flip(idx,axis=1) #反转
    # power = np.flip(power,axis=1)
    power = Beam_power
    return idx,power

def get_PMI(H,N1,N2,layer): #ue_port*gnb_port
    O1 = (N1>1)*3+1
    O2 = (N2>1)*3+1
    assert(layer <= N1*N2*2)   
    codebook_origin = PMI_class.get_pmi_codebook(N1,N2,O1,O2,1,layer)
    H = np.expand_dims(H,(0,1,2,3)) #0,1,2,3,nSub,ue_port,max_gNB_port
    codebook = np.expand_dims(codebook_origin, axis=4)
    HP = H@codebook #0,1,2,3,ue_port,layer
    Layer_power = np.conj(HP.transpose(0,1,2,3,4,6,5))@HP
    HP_power = np.trace(np.abs(Layer_power),axis1=-1,axis2=-2)
    HP_power = np.sum(HP_power,axis = -1)

    idx = np.unravel_index(np.argmax(HP_power),HP_power.shape)
    P = codebook_origin[idx]
    max_power = HP_power[idx]
    lp = Layer_power[idx]
    return idx,P,max_power,lp   

def cal_analog_matrix(Beam,V_ants,H_ants,V_split,H_split,Port):
    nBeam = int(Port/H_split/V_split/2) 
    Beam = Beam[0:nBeam]
    # Ports_vector (Ants_num，Nrf/2) Nrf数对应动态射频链路链接  矩阵中1或0对应动态开关
    Ports_vector = generate_beam_pattern(V_ants,H_ants,V_split,H_split)
    if len(Beam.shape) == 1:
        Beam = np.expand_dims(Beam,0)
    Beam = Beam.transpose(1,0) #16*n
    Beam_Port = Beam*Ports_vector
    FRF = np.kron(np.eye(2),Beam_Port)
    FRF = FRF/np.sqrt(V_ants*H_ants*2)/np.sqrt(nBeam)*np.sqrt(Port)
    return FRF
    
def cal_digital_matrix_PMI(H,FRF,N1,N2,RI):
    """cal digital matrix based on codebook 
    http://www.sharetechnote.com/html/5G/5G_CSI_RS_Codebook.html

    Parameters
    ----------
    H : 3-D array of shape (nSub* UE_ants* Ants_all_2pol )
        Channel
    FRF : 2-D array of shape (Ants_all_2pol* nport )
        Analog precode matrix of BS
    N1 : int
        The number of antenna in horizontal direction
    N2 : int
        The number of antenna in vertical direction
    RI : int
        The number of layers

    Returns
    -------
    P : 2-D array of shape (nport* nstream )
        Digital precode matrix based on codebook of BS
    PMI : 1-D array of shape (4)
        The idx of P in codebook (i11, i12, i13, i2) 
    
    """
    # get equality channel of port
    UE_H = H@FRF
    # get digital matrix
    PMI,P,max_power,layer_power = get_PMI(UE_H,N1,N2,RI)
    P = P*np.sqrt(RI)
    return P,PMI,max_power,layer_power

def cal_report(H,F,SNR,nSub = 17,sinr_min = 0,sinr_max_diff = 15,tp_sub = False):
    #TODO compare different sinr_min
    """cal report form Channel and Precode matrix

    Parameters
    ----------
    H : 3-D array of shape (nSub* UE_ants* Ants_all_2pol)
        Channel
    F : 2-D array of shape (Ants_all_2pol* nStreams)
        Precode matrix at BS
    SNR : float
        SNR (dB)
    nSub : int, optional
        nSub, by default 17
    sinr_min : float, optional
        Set sinr to minimum value(such as -50) if sinr is small than sinr_min, by default 0
    sinr_max_diff : float, optional
        Cal the sinr diff between different stream, if the diff is large than sinr_max_diff, than set the smaller to minimum value, by default 15

    Returns
    -------
    TP : float
        Throughput
    MCS : 1-D array of shape (nSub)
        Modulation and Coding Schemes
    CQI : 1-D array of shape (nSub)
        Channel Quality Indicator,quantification of sinr
        0 for worst, 15 for bests
    sinr : 1-D array of shape (nSub)
        Sinr with optimization, optimization to 
    sinr_origin : 2-D array of shape (nSub* nStream)
        Sinr without optimization, just use for analyze 
        
    Detials:
    -------
    1. cal HF from H and F\n
    2. cal sinr by mmse\n
    3. optimize sinr to avoid unfairness with param sinr_min and sinr_max_diff, cal useful layer num\n
    4. find mcs from sinr2mcs_table with sinr\n
    5. cal TP and CQI\n
    
    """
    
    HF = H@F
    R = np.power(10,(-SNR/10))
    sinr,layer,sinr_origin = cal_sinr(HF,R,sinr_min,sinr_max_diff)
    mcs = np.searchsorted(sinr2mcs_table,sinr)
    TP = get_tp(mcs,layer,nSub,tp_sub)
    CQI = sinr2cqi(sinr)
    
    return TP,mcs,CQI,sinr,sinr_origin

def cal_se(H,SNR,F,W = None):
    SNR = 10**(SNR/10)
    if(W is None):
        Ns = H.shape[0]
        SE = np.log2(np.linalg.det(np.eye(Ns)+SNR/Ns*np.real(H@F@(F.conj().T)@(H.conj().T))))
    else:
        Heff1 = W.T@H@F
        Ns = W.shape[-1]
        # SE = np.log2(np.linalg.det(np.eye(Ns)+SNR/Ns*np.real(np.linalg.pinv(W)@H@<EMAIL>().<EMAIL>().T@W)))
        SE = np.log2(np.linalg.det(np.eye(Ns)+SNR/Ns*np.linalg.pinv(np.real(W.conj().T@W))*np.real(<EMAIL>().T)))
    return SE

def cal_se_sub(H,SNR,F,W):
    nSub = H.shape[0]
    SE_all = np.zeros(nSub)
    for sub in range(0,nSub):
        H_sub = H[sub]
        SE_all[sub] = cal_se(H_sub,SNR,F,W)
    return SE_all

def cal_se_auto(H,SNRs,F,W = None): 
    SNRs = 10**(SNRs/10)
    ntime = H.shape[0]
    nSub = H.shape[1]
    nSNR = SNRs.shape[0]
    SE_all = np.zeros((ntime,nSub,nSNR))
    for i in range(0,ntime):
        for j in range(0,nSub):
            H_sub = H[i,j]
            if(len(F.shape) == 3): #has suv
                F_sub = F[j]
            else:
                F_sub = F
            if(W is None):
                Ns = H_sub.shape[0]
                cal_matrix = np.real(H_sub@F_sub@(F_sub.conj().T)@(H_sub.conj().T))
            else:
                if(len(W.shape) == 3):
                    W_sub = W[j]
                else:
                    W_sub = W
                Heff1 = W_sub.T@H_sub@F_sub
                Ns = W.shape[-1]
                cal_matrix = np.linalg.pinv(np.real(W.conj().T@W))*np.real(<EMAIL>().T)
            for snr_idx in range(0,nSNR):
                SNR = SNRs[snr_idx]
                SE = np.log2(np.linalg.det(np.eye(Ns)+SNR/Ns*cal_matrix))
                SE_all[i,j,snr_idx] = SE
    return SE_all

def cal_long_se(H,coma,beam_tti=2,time=4000,gNB_tti=2):
    max_beam = int(time/beam_tti)
    beam_idxs = [i for i in range(0,max_beam)]
    se_long = []
    for beam_idx in beam_idxs:
        time_tti = beam_tti*beam_idx//gNB_tti
        F = update_svd_Beam(coma[time_tti][0],n=1) #update_from_coma
        F_2pol = np.kron(np.eye(2),F)

        actual_time = beam_tti*beam_idx
        se = cal_se_auto(H[actual_time:actual_time+beam_tti],np.arange(-30,35,5),F_2pol)
        se_long.append(se)
        # se_long1 = np.array(se_long1)
        # se
    se_long = np.array(se_long)
    return se_long

def cal_sinr(HP,R,sinr_min = 0,sinr_max_diff = 15):
    sinr = get_sinr(HP.transpose(0,2,1),R)
    sinr_origin = np.copy(sinr)
    # sinr[sinr < sinr_min] = -50
    # if sinr.shape[1] > 1:
    #     sinr[(sinr[:,0] - sinr[:,1] > sinr_max_diff),1] = -50
    #     sinr[(sinr[:,1] - sinr[:,0] > sinr_max_diff),0] = -50
    # if sinr.shape[1] > 3:
    #     sinr[(sinr[:,2] - sinr[:,3] > sinr_max_diff),3] = -50
    #     sinr[(sinr[:,3] - sinr[:,2] > sinr_max_diff),2] = -50
    layer = np.sum(sinr > -40,axis = 1)
    layer = np.clip(layer,1,4)
    # mi = sinr2mi(sinr)
    # mi[mi<0.0014] = 0 #min_mi = 0.0013
    # avg_mi = np.sum(mi,axis=1)/layer
    # sinr_avg = mi2sinr(avg_mi)
    sinr_avg = sinr
    return sinr_avg,layer,sinr_origin

def get_tp_par():
    # Init PRB config
    nPRB = 272
    nRBG = 17 
    nBS_RF_CHAIN = 16
    PRBpreSB = [nBS_RF_CHAIN for i in range(nRBG-1)] + [nPRB-nBS_RF_CHAIN*(nRBG-1)]  # PRB of each subband
    nPRB_PDSCH = 156
    nREAvg = min(156,  nPRB_PDSCH)
    return PRBpreSB,nREAvg

def get_tp(mcs,layer,nSub,sub=False):
    '''
        get a list of UE throughput
    '''
    PRBpreSB,nREAvg = get_tp_par()
    assert len(PRBpreSB) == nSub
    if sub: TP = []
    else:   TP = 0.
    for band in range(nSub):
        if(len(mcs) == nSub):
            mcs_sub = mcs[band]
            layer_sub = layer[band]
        else:
            mcs_sub = mcs[0]
            layer_sub = layer[0]
        if sub: TP.append(TbCalc(mcs_sub, layer_sub, PRBpreSB[band], nREAvg))
        else:   TP += TbCalc(mcs_sub, layer_sub, PRBpreSB[band], nREAvg)
    return TP

if __name__ == '__main__':
    pass