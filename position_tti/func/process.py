import os,sys
import numpy as np
import re
import pandas as pd
from collections import Counter
import plotly.graph_objects as go
from plotly.subplots import make_subplots

from utils.load_data import pre_process
from func.BM_functions import *

def get_data(sim_par,clear_time=False):
    if not os.path.exists(sim_par.Data_dir+"data_redo.npz"):
        pre_process(sim_par.Data_dir,~clear_time)
    data_redo = np.load(sim_par.Data_dir+"data_redo.npz",allow_pickle=True)['arr_0'].item()
    sim_par.data = data_redo
    if(clear_time):
        sim_par.data['H_time'] = list()


if __name__ == '__main__':
    pass