import os,sys
import re
import copy
import pickle
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
import plotly.graph_objects as go
from collections import Counter
from plotly.subplots import make_subplots
from scipy import optimize, stats
from typing import Tuple, List 
from IPython.display import Image
from matplotlib.gridspec import GridSpec

from func.BM_functions import *
from func.system_functions import *
from func.process import *
from utils import *

class setsumi_env:
    def __init__(self,logger=print,path="/data/setsumi/Data/test/test_add/",seed=777,max_time = 4000,max_length=1000,mu=0.9,gNB_tti=1,select_subs=1,select_ports=1,test_real=False,use_pg_all=True) -> None:
        self.logger = logger

        sim_par = Sim_Log(path)
        self.path = path
        self.sim_par = sim_par
        self.max_time = max_time
        self.max_time = min(self.max_time,sim_par.time)
        self.nSub = sim_par.nSub
        self.H_ants = sim_par.H_ants
        self.V_ants = sim_par.V_ants
        self.Ants_num = sim_par.Ants_all
        self.Ants_all_2pol = sim_par.Ants_all_2pol
        self.UE_port = sim_par.UE_Port
        self.nCELL = sim_par.nCell
        self.nUE = sim_par.nUE
        self.nBS = sim_par.nBS
        self.seed = seed
        self.use_pg_all = use_pg_all
        # config
        self.mu = mu
        self.max_length = max(max_length, self.max_time)
        self.select_subs = select_subs
        self.select_ports = select_ports
        self.gNB_tti = gNB_tti
        self.test_real = test_real
        self.rsrp_data_type = None 
        # ants config
        self.H_split = 2
        self.V_split = 1
        self.RI = 1
        self.N1 = max(self.V_split,self.H_split)*int(self.UE_port/self.H_split/self.V_split/2)
        self.N2 = min(self.V_split,self.H_split)
        self.nBeam = 1
        self.gNB_port = self.N1*self.N2*self.nBeam*2
        self.default_pattern = generate_beam_pattern(self.V_ants,self.H_ants,self.V_split,self.H_split)
        
        self.codebook_origin = PMI_class.get_pmi_codebook(self.N1,self.N2,(self.N1>1)*3+1,(self.N2>1)*3+1,1,self.RI)*np.sqrt(self.RI)
        self.codebook_origin = self.codebook_origin.astype(np.complex64)
        
        self.draw = draw(numHant=sim_par.H_ants,numVant=sim_par.V_ants,HSpacing=sim_par.HSpacing,VSpacing=sim_par.VSpacing)        
        
        # normalized boundary values
        self.hangle_bound = 60
        self.vangle_bound = 60
        
        self.load_H()
        self.cal_aod_eod_dist_real()
        self.cal_svd_angle() 
            
    def load_H(self):
        if self.logger is not None:
            self.logger("Load begin")
        get_data(self.sim_par,clear_time=True)
        self.H_freq = np.array(self.sim_par.data['H_freq'],dtype=np.complex64)
        self.H_pg = np.array(self.sim_par.data['H_pg'],dtype=np.float64)
        print(self.H_pg.shape)
        self.loc = np.array(self.sim_par.data['loc'],dtype=np.float64)
        self.bs_loc = np.array(self.sim_par.data['bs_loc'],dtype=np.float64).T
        print(self.bs_loc)
        if self.use_pg_all and os.path.exists(self.path+"pg_all.txt"):
            rsrp = np.array(self.sim_par.data['pg_all'], dtype=np.float64)
            self.rsrp = rsrp
            print("使用 pg_all.txt 文件")
        else:
            rsrp = self.H_pg
            rsrp = np.expand_dims(rsrp, axis=-1)
            self.rsrp = rsrp
            print("使用 pg.txt 或 H_pg 数据")
        print("初始 rsrp 形状:", self.rsrp.shape)
        print("初始 rsrp 元素总数:", self.rsrp.size)
        if self.logger is not None:
            self.logger("Load Finish")
        print("处理后 rsrp 形状:", self.rsrp.shape)
        print("处理后 rsrp 元素总数:", self.rsrp.size)
        
        self.norm_H()
        self.process()
        self.expand_time()            
      
        self.sequential_simulate()
        self.sequential_srs()
        if self.logger is not None:
            self.logger(f"srs_choose_all:{self.srs_choose_all.shape}")
        if self.logger is not None:
            self.logger(f"simulate srs:{self.H_srs.shape}")
        
        self.sequential_coma()
        if self.logger is not None:
            self.logger(f"simulate coma:{self.coma_all.shape}")
        
        self.sequential_rsrp()
        if self.logger is not None:
            self.logger(f"simulate rsrp:{self.rsrp_srs.shape}")
        
        if self.logger is not None:
            self.logger("end load_H")
    
    # The normalized channel H
    def norm_H(self):
        for i in range(0,self.nUE):
            pg = self.H_pg[i]
            pg_data = np.sqrt(np.power(10,(pg/10)))
            pg_data = np.expand_dims(pg_data,(1,2,3))
            self.H_freq[i] = self.H_freq[i]/pg_data/np.sqrt(2)
    
    """"
    Due to the quadriga, reprocess dataset. If dataset is normal(nUE*times), no need to call this function.
    quadriga dataset: (ue_div_group*ue_div_part, no_snapshots), nBS = 3 , nUE = ue_part_group_num = ue_div_group, max_time = ue_div_part * no_snapshots
    """
    def process(self):
        ue_div_group = self.sim_par.ue_div_group
        ue_div_part = self.sim_par.ue_div_part//ue_div_group
        ue_part_num = self.sim_par.ue_part_num # ue_part_num=1
        ue_part_group_num = ue_part_num*ue_div_group
        max_time = self.max_time
        nBS = self.nBS
        modnBS = min(3, nBS)
        # reamin issue if ue_part_num > 1, to be tested
        cut_length = ue_div_part*ue_part_group_num
        print("cut_length",cut_length)
        H_freq = self.H_freq[0:cut_length]
        H_freq = H_freq.reshape(ue_div_group, ue_div_part, ue_part_num, max_time, self.nSub, self.UE_port, self.Ants_all_2pol)
        # H_freq = H_freq.transpose(1,2,0,3,4,5)
        # consider max_time in last or in 2rd last
        H_freq = H_freq.transpose(0,2,3,1,4,5,6)
        H_freq = H_freq.reshape(ue_part_group_num, -1, self.nSub, self.UE_port, self.Ants_all_2pol)
        self.H_freq = H_freq
        
        loc = self.loc[0:cut_length]
        loc = loc.reshape(ue_div_group,ue_div_part, ue_part_num, max_time, 3)
        loc = loc.transpose(0,2,3,1,4)
        loc = loc.reshape(ue_part_group_num, -1, 3)
        self.loc = loc
        
        rsrp = self.rsrp[0:cut_length]
        
        if self.rsrp_data_type == "pg_all":
            # pg_all 数据: (ue_div_group, ue_div_part, ue_part_num, max_time, nBS)
            print(f"处理 pg_all 数据，原始维度参数: {ue_div_group}, {ue_div_part}, {ue_part_num}, {max_time}, {nBS}")
            rsrp = rsrp.reshape(ue_div_group, ue_div_part, ue_part_num, max_time, nBS)
            rsrp = rsrp.transpose(0, 2, 3, 1, 4)
            rsrp = rsrp.reshape(ue_part_group_num, -1, nBS)
            self.rsrp = rsrp[:, :, 0:modnBS]
            print(f"pg_all 处理后 rsrp 形状: {self.rsrp.shape}")
        
        elif self.rsrp_data_type == "pg" or self.rsrp_data_type == "H_pg":
            # pg 数据: (ue_div_group, ue_div_part, ue_part_num, max_time, 1)
            print(f"处理 pg/H_pg 数据，原始维度参数: {ue_div_group}, {ue_div_part}, {ue_part_num}, {max_time}, 1")
            rsrp = rsrp.reshape(ue_div_group, ue_div_part, ue_part_num, max_time, 1)
            rsrp = rsrp.transpose(0, 2, 3, 1, 4)
            rsrp = rsrp.reshape(ue_part_group_num, -1, 1)
            
            # 扩展到 modnBS 维度 (复制第一个 BS 的数据到其他 BS)
            rsrp_expanded = np.tile(rsrp, (1, 1, modnBS))
            self.rsrp = rsrp_expanded
            print(f"pg/H_pg 处理后 rsrp 形状: {self.rsrp.shape}")
        
        else:
            raise ValueError(f"未知的 rsrp_data_type: {self.rsrp_data_type}")
        
        self.bs_loc = self.bs_loc[0:modnBS]
        
        self.nUE = ue_part_group_num
        self.max_time = ue_div_part * max_time
        self.nBS = modnBS
        print(self.nUE, self.max_time, self.nBS)

    # In the time dimension, max_time is extended to max_length
    def expand_time(self):
        times = list(np.arange(self.max_time))
        times = times + times[::-1]
        repeat = self.max_length // (self.max_time * 2) + 1
        times = times * repeat
        
        H_origin = self.H_freq
        H_origin = H_origin.transpose(1,0,2,3,4) # time*nUE*nSub*UE_port*Ants_all
        H_re = H_origin[times]
        H_re = H_re[0:self.max_length]
        H_re = H_re.transpose(1,0,2,3,4)
        self.H_expand = H_re
        print('expand H:', self.H_expand.shape)
        
        loc_origin = self.loc
        loc_origin = loc_origin.transpose(1,0,2) # time*nUE*3
        loc_re = loc_origin[times]
        loc_re = loc_re[0:self.max_length]
        loc_re = loc_re.transpose(1,0,2)
        self.loc_expand = loc_re
        print('expand ue loc:', self.loc_expand.shape)
        
        rsrp_origin = self.rsrp
        rsrp_origin = rsrp_origin.transpose(1,0,2) # time*nUE*nBS
        rsrp_re = rsrp_origin[times]
        rsrp_re = rsrp_re[0:self.max_length]
        rsrp_re = rsrp_re.transpose(1,0,2)
        self.rsrp_expand = rsrp_re
        print('expand rsrp:', self.rsrp_expand.shape)
    
    # according to select_subs and select_ports, get sampled H
    def sequential_simulate(self):
        np.random.seed(self.seed)
        srs_choose_all = []
        sub_choose_all = []
        port_choose_all = []
        for i in range(0,self.nUE):
            srs_choose,sub_choose,port_choose = simulate_srs(self.sim_par,self.max_length,self.gNB_tti,self.select_subs, self.select_ports)
            srs_choose_all.append(srs_choose)
            sub_choose_all.append(sub_choose)
            port_choose_all.append(port_choose)
        self.srs_choose_all = np.array(srs_choose_all)
        self.sub_choose_all = np.array(sub_choose_all)
        self.port_choose_all = np.array(port_choose_all)
    
    # according to sampled H, get H_srs
    def sequential_srs(self):
        UEs = np.arange(self.nUE)
        times = np.arange(self.max_length)
        UEs_new, times_new = np.meshgrid(UEs, times) #用户连续
        UEs = UEs_new.reshape(-1)
        times = times_new.reshape(-1)
        mod_time = times%self.max_length #sim_par.time代表数据总的个数
        srs_idx = times//self.gNB_tti
        
        nUE = UEs.shape[0]
        H = self.H_expand[UEs,mod_time]
        srs_choose = self.srs_choose_all[UEs,srs_idx]
        
        H_origin = self.H_expand
        srs_subs = [[[i for i in range(self.nSub)]]*self.UE_port]*nUE
        srs_subs = np.array(srs_subs).transpose(0,2,1)
        srs_ports = [[[i for i in range(self.UE_port)]]*self.nSub]*nUE
        srs_ports = np.array(srs_ports)
        srs_ues = [[list(UEs)]*self.nSub]*self.UE_port
        srs_ues = np.array(srs_ues).transpose(2,1,0)
        
        H_srs = H_origin[srs_ues,srs_choose,srs_subs,srs_ports]
        H = H.astype(np.complex64)
        H_srs = H_srs.astype(np.complex64)
        H_srs = H_srs.reshape(self.max_length, self.nUE, self.nSub, self.UE_port, -1)
        H_srs = H_srs.transpose(1,0,2,3,4)

        self.H_srs = H_srs # nUE*max_length*nSub*UE_port*Ants_all_2pol
    
    # according to sampled H, get coma
    def sequential_coma(self):
        coma_all = []
        for i in range(0,self.nUE):
            coma_ue,H_ue = simulate_coma(self.H_expand[i],self.sub_choose_all[i],self.port_choose_all[i],self.gNB_tti,self.mu,self.test_real)
            coma_all.append(coma_ue)
        coma_all = np.array(coma_all)
        self.coma_all = coma_all # nUE*(max_length//gNB_tti)*1*Ants_num*Ants_num 
    
    # according to sampled tti, get rsrp_srs
    def sequential_rsrp(self):
        mod_rsrp = np.ones_like(self.rsrp_expand) 
        for i in range(self.max_length):
            mod_idx = (i // self.gNB_tti) * self.gNB_tti
            mod_rsrp[:,i,:] = self.rsrp_expand[:,mod_idx,:]
        mod_rsrp = np.array(mod_rsrp)
        self.rsrp_srs = mod_rsrp # nUE*max_length*nBS
        
    # calculate the true aod,eod,distance
    def cal_aod_eod_dist_real(self):
        ue_pos = self.loc_expand # nUE*max_length*3
        bs_loc = self.bs_loc[0]
        
        relative_pos = np.array(ue_pos) - np.array(bs_loc)
        self.Distance = np.sqrt(np.sum(np.power(relative_pos, 2),axis=-1))
        self.azimuths = np.arctan2(relative_pos[:,:,1], relative_pos[:,:,0]) * 180 / np.pi
        self.elevation = np.arctan2(relative_pos[:,:,2], np.linalg.norm(relative_pos[:,:,:2],axis=-1)) * 180 / np.pi     
    
    # based on svd, calculate aod,eod
    def cal_angle(self, z1, z2):
        absz1 = np.linalg.norm(np.expand_dims(z1, axis=-1), axis=-1)
        absz2 = np.linalg.norm(np.expand_dims(z2, axis=-1), axis=-1)

        cosine_theta = (np.real(z1)*np.real(z2) + np.imag(z1)*np.imag(z2)) / (absz1 * absz2)
        sinine_theta = (np.real(z1)*np.imag(z2) - np.imag(z1)*np.real(z2)) / (absz1 * absz2)
        cosine_theta = np.clip(cosine_theta, -1, 1)
        sinine_theta = np.clip(sinine_theta, -1, 1)
        theta = np.where((sinine_theta >= 0), np.arccos(cosine_theta), -np.arccos(cosine_theta))

        N = 0.5
        cosine_aphla = theta / N / (2*np.pi) 
        cosine_aphla = np.clip(cosine_aphla, -1, 1)  
        aphla = np.arccos(cosine_aphla) * 180 / np.pi
        
        return aphla
    def cal_svd_angle(self):
        H = self.H_srs
        batch = self.nUE*self.max_length
        H_re = H.reshape(self.nUE, self.max_length, self.nSub, self.UE_port, 2, self.V_ants, self.H_ants)
        
        # H_srs aod
        H = H_re
        H = H.reshape(batch, -1, self.H_ants)
        conv = H.transpose(0,2,1)@H
        s,v,d = np.linalg.svd(conv)
        z = s[:,:,0]
        z1 = z[:,0]
        z2 = z[:,1]
        aphla = - self.cal_angle(z1, z2) + 90
        self.H_angle = aphla.reshape(self.nUE, -1) # nUE*max_length 
        
        # H_srs eod
        H = H_re.transpose(0,1,2,3,4,6,5)
        H = H.reshape(batch, -1, self.V_ants)
        conv = H.transpose(0,2,1)@H
        s,v,d = np.linalg.svd(conv)
        z = s[:,:,0]
        z1 = z[:,0]
        z2 = z[:,1]
        aphla = - self.cal_angle(z1, z2) + 90
        self.V_angle = aphla.reshape(self.nUE, -1) # nUE*max_length 

        # coma aod, eod, eig
        coma_all = self.coma_all
        coma_all = coma_all.reshape(-1,self.Ants_num,self.Ants_num)
        s,v,d = np.linalg.svd(coma_all)
        z = s[:,:,0]
        z1 = z[:,0]
        z2 = z[:,1]
        z3 = z[:,self.H_ants]
        aphla = self.cal_angle(z1, z2) - 90
        self.coma_H_angle = aphla.reshape(self.nUE, -1)
        self.coma_H_angle = np.repeat(self.coma_H_angle, self.gNB_tti, axis=-1)
        self.coma_H_angle = self.coma_H_angle[:,0:self.max_length] # nUE*(max_length//gNB_tti) 
        aphla = self.cal_angle(z1, z3) - 90
        self.coma_V_angle = aphla.reshape(self.nUE, -1)
        self.coma_V_angle = np.repeat(self.coma_V_angle, self.gNB_tti, axis=-1)
        self.coma_V_angle = self.coma_V_angle[:,0:self.max_length] # nUE*(max_length//gNB_tti)
        
        eig = s[:,:,0]
        eig = eig.reshape(self.nUE,-1,self.Ants_num)
        self.coma_eig = eig
        self.coma_eig = np.repeat(self.coma_eig, self.gNB_tti, axis=1)
        self.coma_eig = self.coma_eig[:,0:self.max_length] # nUE*(max_length//gNB_tti)
    
    # calculate the ue position based on aod,eod,distance with the first BS                  
    def cal_ue_pos(self, aod, eod, dist):
        bs_loc = self.bs_loc

        H_angle = aod
        V_angle = eod
        distance = dist
        ue_pos = np.zeros((3),dtype=np.float64)
        ue_pos[0] = distance*np.cos(V_angle/180*np.pi)*np.cos(H_angle/180*np.pi) + bs_loc[0,0]
        ue_pos[1] = distance*np.cos(V_angle/180*np.pi)*np.sin(H_angle/180*np.pi) + bs_loc[0,1]
        ue_pos[2] = distance*np.sin(V_angle/180*np.pi) + bs_loc[0,2]    
        return ue_pos                     
    
    # calculate the 3D positioning error due to the aod,eod
    def cal_pos_error_test(self, aod, eod, ueid, timeid):
        dist = self.Distance[ueid,timeid]
        
        ue_pos = self.cal_ue_pos(aod, eod, dist)
        ue_real_pos = self.loc_expand[ueid, timeid]
        relative_pos = np.array(ue_pos) - np.array(ue_real_pos)
        error = np.sqrt(np.sum(np.power(relative_pos,2)))
        return error
    def cal_pos_error_batch(self, aods, eods, ues, times):
        errors = np.zeros(len(ues))
        for i in range(0,len(ues)):
            errors[i] = self.cal_pos_error_test(aods[i],eods[i],ues[i],times[i])
        return errors
    def cal_pos_error_batch_all(self, aods, eods, ues, times):
        errors = np.zeros((len(ues),self.gNB_tti))
        for i in range(0,len(ues)):
            for j in range(0,self.gNB_tti):
                errors[i,j] = self.cal_pos_error_test(aods[i],eods[i],ues[i],times[i]-j)
        errors = np.average(errors,axis=-1)
        return errors
    
    # calculate next state,reward
    def calculate_real(self, ues, times):
        self.aod_real = self.azimuths[ues, times]
        print("aod_real has been set:", self.aod_real)
        
    def get_state_method3(self,batch,actions,ues,times):
        aod = actions[:,0]*self.hangle_bound
        eod = actions[:,1]*self.vangle_bound
        
        times = (times + 1) * self.gNB_tti # next state
        times[times  >= self.max_length] = self.max_length - 1
        H_real = self.H_expand[ues,times]

        # coma aod eod
        srs_aod = self.coma_H_angle[ues,times]/ self.hangle_bound
        srs_eod = self.coma_V_angle[ues,times]/ self.vangle_bound
        srs_aod = srs_aod[:,np.newaxis]
        srs_eod = srs_eod[:,np.newaxis]
        
        # cal beam
        angleH = np.array([aod])
        angleV = np.array([eod])
        beam = generate_beams_multiple(H_angles=angleH, V_angles=angleV, numVant=self.V_ants, numHant=self.H_ants)
        
        # cal FRF
        FRF = cal_analog_matrix(beam,V_ants=self.V_ants,H_ants=self.H_ants,V_split=self.V_split,H_split=self.H_split,Port=self.gNB_port)
        Ports_vector = self.default_pattern
        beam = beam[:,:,np.newaxis] # add an axit to match Ports_vector
        Beam_Port = beam*Ports_vector
        FRF = np.kron(np.eye(2,dtype=np.complex64),Beam_Port)
        FRF = FRF/np.sqrt(self.V_ants*self.H_ants*2)/np.sqrt(self.nBeam)*np.sqrt(self.UE_port)
        FRF = np.expand_dims(FRF,axis=1)
        
        # cal HP power
        HF = H_real@FRF # batch*sub*UE_port*ants@batch*1*ants*Port->batch*sub*UE_port*Port
        HF = np.expand_dims(HF,(0,1,2,3))
        codebook_re = self.codebook_origin.reshape(-1,self.gNB_port,self.RI)
        codebook = np.expand_dims(self.codebook_origin, axis=(4,5))
        HP = np.matmul(HF,codebook)
        Layer_power = np.conj(HP.transpose(0,1,2,3,4,5,7,6))@HP
        HP_power = np.trace(np.abs(Layer_power),axis1=-1,axis2=-2)
    
        # find PMI
        bs_shape = HP_power.shape[0:4]
        HP_power = HP_power.reshape(-1,batch,self.nSub)
        idx = np.argmax(HP_power,axis=0)
        PMI = np.array(np.unravel_index(idx,bs_shape))
        PMI = np.array(PMI)
        PMI = PMI.transpose(1,2,0)
        
        # cal PMI angle
        FBB = codebook_re[idx]
        F = FRF@FBB
        F = F.transpose(2,0,1,3)
        F = F.reshape(F.shape[0],-1)
        angle = self.draw.cal_angle(F[0:self.Ants_num])
        pmi_aod = angle[0].reshape(batch,self.nSub)/ self.hangle_bound
        pmi_eod = angle[1].reshape(batch,self.nSub)/ self.vangle_bound
        
        # return data
        self.aod_real = self.azimuths[ues,times]
        self.eod_real = self.elevation[ues,times]
        self.svd_aod_er = np.average(srs_aod*60,axis=-1) - self.aod_real
        self.pmi_aod_er = np.average(pmi_aod*60,axis=-1) - self.aod_real
        self.svd_eod_er = np.average(srs_eod*60,axis=-1) - self.eod_real
        self.pmi_eod_er = np.average(pmi_eod*60,axis=-1) - self.eod_real
        
        # reward 
        eig = self.coma_eig[ues,times]
        eig = eig[:,:,np.newaxis]
        corr = np.conj(beam.transpose((0, 2, 1)))@eig
        corr = corr.squeeze() / (np.linalg.norm(beam, axis=(1, 2)) * np.linalg.norm(eig, axis=(1, 2)))
        corr = np.abs(corr)
        reward = corr

        score = -self.cal_pos_error_batch(aod, eod, ues, times)
        score_all = -self.cal_pos_error_batch_all(aod, eod, ues, times)
        
        self.reward = reward
        self.score = score
        self.score_all = score_all
                                
        return [srs_aod,pmi_aod,srs_eod,pmi_eod]

if __name__ == '__main__':
    pass