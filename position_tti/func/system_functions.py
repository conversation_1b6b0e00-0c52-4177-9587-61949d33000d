import copy
import numpy as np
from func.BM_functions import cal_coma
from utils import *


def simulate_srs(sim_par, time=4000, gNB_tti=1, select_subs=[0], select_ports=1):
    nSub = sim_par.nSub
    UE_Port = sim_par.UE_Port
    choose_sub = list(np.arange(nSub))
    choose_port = list(np.arange(UE_Port))
    srs_choose = []
    rand_sub = []
    rand_port = []
    srs_inst = np.zeros((nSub, UE_Port), dtype=int)
    
    for i in range(time):
        if i % gNB_tti == 0:
            # 直接使用传入的子载波索引列表
            sub = select_subs  
            port = np.random.choice(choose_port, size=select_ports, replace=False)
            choose_port = [x for x in choose_port if x not in port]
            if len(choose_port) < select_ports:
                choose_port = list(np.arange(UE_Port))
            
            rand_sub.append(sub)
            rand_port.append(port)
            
            redo_time = i
            rows, cols = np.meshgrid(sub, port, indexing='ij')
            srs_inst[rows, cols] = redo_time
            srs_choose.append(copy.deepcopy(srs_inst))
    
    return srs_choose, rand_sub, rand_port

def simulate_coma(H,rand_sub,rand_port,gNB_tti,mu,test_real=False):
    short_coma = None
    long_coma = []
    Ants_all_2pol = H.shape[-1]
    Ants_all = Ants_all_2pol//2

    H_select = []
    
    for i in range(len(rand_sub)):
        time = i*gNB_tti        
        if test_real == True:
            H_re = H[time]
            srs_pol1 = H_re[:,:,0:Ants_all]
            srs_pol2 = H_re[:,:,Ants_all:Ants_all_2pol]
        else:
            sub = rand_sub[i]
            port = rand_port[i]
            H_re = np.take(np.take(H[time],sub,axis=0),port,axis=1)
            srs_pol1 = H_re[:,:,0:Ants_all] # sub*port*ants_num
            srs_pol2 = H_re[:,:,Ants_all:Ants_all_2pol]
            
        short_coma = cal_coma(srs_pol1.reshape(-1,Ants_all),srs_pol2.reshape(-1,Ants_all))
        s,v,d = np.linalg.svd(short_coma)
        short_coma = s
        short_coma = np.average(short_coma,axis=(0))
        short_coma = short_coma[np.newaxis,:,:]

        if long_coma:
            long_coma.append(mu*long_coma[-1]+(1-mu)*short_coma)
        else:
            long_coma.append(short_coma)
            
        H_select.append(H_re)
    return long_coma, H_select
