{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os,sys\n", "sys.path.append(os.getcwd())\n", "from func.BM_functions import *\n", "from func.system_functions import *\n", "from func.process import *\n", "from func.setsumi_env import setsumi_env\n", "from utils import *\n", "import torch\n", "\n", "from train_td3_1 import seed_torch,parse_args,train,infer"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 训练"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["setsumi load sim log OK\n", "Load begin\n", "(1000, 1)\n", "[[0.]\n", " [0.]\n", " [8.]]\n", "使用 pg_all.txt 文件\n", "初始 rsrp 形状: (1000, 1, 1)\n", "初始 rsrp 元素总数: 1000\n", "Load Finish\n", "处理后 rsrp 形状: (1000, 1, 1)\n", "处理后 rsrp 元素总数: 1000\n", "cut_length 1000\n"]}, {"ename": "ValueError", "evalue": "未知的 rsrp_data_type: None", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[2], line 5\u001b[0m\n\u001b[1;32m      3\u001b[0m seed \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m777\u001b[39m\n\u001b[1;32m      4\u001b[0m seed_torch(seed)\n\u001b[0;32m----> 5\u001b[0m env \u001b[38;5;241m=\u001b[39m \u001b[43msetsumi_env\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpath\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mpath\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mseed\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mseed\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmax_length\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m4000\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43mmu\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m0.99\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43mgNB_tti\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43mselect_subs\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m[\u001b[49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43mselect_ports\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43mtest_real\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m)\u001b[49m  \n", "File \u001b[0;32m/home/<USER>/position_tti/func/setsumi_env.py:69\u001b[0m, in \u001b[0;36msetsumi_env.__init__\u001b[0;34m(self, logger, path, seed, max_time, max_length, mu, gNB_tti, select_subs, select_ports, test_real, use_pg_all)\u001b[0m\n\u001b[1;32m     66\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhangle_bound \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m60\u001b[39m\n\u001b[1;32m     67\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mvangle_bound \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m60\u001b[39m\n\u001b[0;32m---> 69\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mload_H\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     70\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcal_aod_eod_dist_real()\n\u001b[1;32m     71\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcal_svd_angle()\n", "File \u001b[0;32m/home/<USER>/position_tti/func/setsumi_env.py:100\u001b[0m, in \u001b[0;36msetsumi_env.load_H\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m     97\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m处理后 rsrp 元素总数:\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mrsrp\u001b[38;5;241m.\u001b[39msize)\n\u001b[1;32m     99\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mnorm_H()\n\u001b[0;32m--> 100\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mprocess\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    101\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mexpand_time()            \n\u001b[1;32m    103\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msequential_simulate()\n", "File \u001b[0;32m/home/<USER>/position_tti/func/setsumi_env.py:182\u001b[0m, in \u001b[0;36msetsumi_env.process\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    179\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpg/H_pg 处理后 rsrp 形状: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mrsrp\u001b[38;5;241m.\u001b[39mshape\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    181\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 182\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON><PERSON><PERSON><PERSON>r\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m未知的 rsrp_data_type: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mrsrp_data_type\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    184\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mbs_loc \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mbs_loc[\u001b[38;5;241m0\u001b[39m:modnBS]\n\u001b[1;32m    186\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mnUE \u001b[38;5;241m=\u001b[39m ue_part_group_num\n", "\u001b[0;31mValueError\u001b[0m: 未知的 rsrp_data_type: None"]}], "source": ["path = os.getcwd()\n", "path = './data_f/'\n", "seed = 777\n", "seed_torch(seed)\n", "env = setsumi_env(path=path, seed=seed, max_length=4000,mu=0.99,gNB_tti=1,select_subs=[1],select_ports=1,test_real=False)  "]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["cpu\n"]}], "source": ["modelpath = './model/td3/'\n", "seed = 777\n", "action_dim = 2\n", "state_dim = 18*2\n", "# 总数据集时间\n", "time_length = env.max_time - 1\n", "# 开始和结束数据集时间，转换为对应的tti_idx\n", "start_time = 1000\n", "end_time = start_time+time_length\n", "start_time = start_time//env.gNB_tti\n", "end_time = end_time//env.gNB_tti\n", "\n", "args = parse_args(args=[])\n", "args.seed = seed\n", "args.device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "# 训练集UE数\n", "args.train_nUE = 10\n", "# 每一轮训练UE数\n", "args.batch_size = 10\n", "# 总训练步长\n", "args.num_frame = 20000\n", "# 网络优化周期\n", "args.optim_frame = 1\n", "args.done_cycle = env.max_length//env.gNB_tti\n", "# 换一批UE训练周期\n", "args.change_ue_cycle = 2000// env.gNB_tti\n", "# 推理周期\n", "args.train_log_cycle = args.num_frame\n", "\n", "# td3\n", "args.sample_size = 200\n", "args.discount = 0.99\n", "args.tau = 0.005\n", "args.noise_clip = 1\n", "args.policy_freq = 2\n", "args.policy_noise = 0.2\n", "args.pi_lr = 1e-3\n", "args.q_lr = 1e-3\n", "# args.noise_std=0.1\n", "# args.start_timesteps=10000         \n", "print(args.device)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[4], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m results \u001b[38;5;241m=\u001b[39m \u001b[43mtrain\u001b[49m\u001b[43m(\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43menv\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mseed\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmodelpath\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maction_dim\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstate_dim\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstart_time\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mend_time\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/home/<USER>/position_tti/train_td3_1.py:442\u001b[0m, in \u001b[0;36mtrain\u001b[0;34m(args, env, seed, modelpath, action_dim, state_dim, start_time, end_time)\u001b[0m\n\u001b[1;32m    440\u001b[0m \u001b[38;5;66;03m# 每optim_frame步优化\u001b[39;00m\n\u001b[1;32m    441\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m frame_idx \u001b[38;5;241m%\u001b[39m args\u001b[38;5;241m.\u001b[39moptim_frame \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m0\u001b[39m \u001b[38;5;129;01mand\u001b[39;00m frame_idx \u001b[38;5;241m!=\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[0;32m--> 442\u001b[0m     actor_loss,critic_loss \u001b[38;5;241m=\u001b[39m \u001b[43magent\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptim\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    443\u001b[0m     results[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mactor_loss\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m.\u001b[39mappend(actor_loss)\n\u001b[1;32m    444\u001b[0m     results[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mcritic_loss\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m.\u001b[39mappend(critic_loss)\n", "File \u001b[0;32m/home/<USER>/position_tti/train_td3_1.py:202\u001b[0m, in \u001b[0;36mTD3.optim\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    199\u001b[0m     target_Q \u001b[38;5;241m=\u001b[39m target_Q\u001b[38;5;241m.\u001b[39mtype(torch\u001b[38;5;241m.\u001b[39mfloat32)\n\u001b[1;32m    201\u001b[0m \u001b[38;5;66;03m# Get current Q estimates\u001b[39;00m\n\u001b[0;32m--> 202\u001b[0m current_Q1, current_Q2 \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcritic\u001b[49m\u001b[43m(\u001b[49m\u001b[43mstate\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maction\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    204\u001b[0m \u001b[38;5;66;03m# Calculate Critic Loss\u001b[39;00m\n\u001b[1;32m    205\u001b[0m critic_loss \u001b[38;5;241m=\u001b[39m F\u001b[38;5;241m.\u001b[39mmse_loss(current_Q1, target_Q) \u001b[38;5;241m+\u001b[39m F\u001b[38;5;241m.\u001b[39mmse_loss(current_Q2, target_Q)\n", "File \u001b[0;32m~/miniconda3/envs/meiziy/lib/python3.10/site-packages/torch/nn/modules/module.py:1736\u001b[0m, in \u001b[0;36mModule._wrapped_call_impl\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1734\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_compiled_call_impl(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)  \u001b[38;5;66;03m# type: ignore[misc]\u001b[39;00m\n\u001b[1;32m   1735\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1736\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_call_impl\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/meiziy/lib/python3.10/site-packages/torch/nn/modules/module.py:1747\u001b[0m, in \u001b[0;36mModule._call_impl\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1742\u001b[0m \u001b[38;5;66;03m# If we don't have any hooks, we want to skip the rest of the logic in\u001b[39;00m\n\u001b[1;32m   1743\u001b[0m \u001b[38;5;66;03m# this function, and just call forward.\u001b[39;00m\n\u001b[1;32m   1744\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_pre_hooks\n\u001b[1;32m   1745\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_backward_hooks\n\u001b[1;32m   1746\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_forward_pre_hooks):\n\u001b[0;32m-> 1747\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mforward_call\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1749\u001b[0m result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m   1750\u001b[0m called_always_called_hooks \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mset\u001b[39m()\n", "File \u001b[0;32m/home/<USER>/position_tti/train_td3_1.py:119\u001b[0m, in \u001b[0;36mCritic.forward\u001b[0;34m(self, state, action)\u001b[0m\n\u001b[1;32m    118\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mforward\u001b[39m(\u001b[38;5;28mself\u001b[39m,state,action):\n\u001b[0;32m--> 119\u001b[0m     q1 \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mQ1\u001b[49m\u001b[43m(\u001b[49m\u001b[43mstate\u001b[49m\u001b[43m,\u001b[49m\u001b[43maction\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    120\u001b[0m     q2 \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mQ2(state,action)\n\u001b[1;32m    121\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m q1,q2\n", "File \u001b[0;32m~/miniconda3/envs/meiziy/lib/python3.10/site-packages/torch/nn/modules/module.py:1736\u001b[0m, in \u001b[0;36mModule._wrapped_call_impl\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1734\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_compiled_call_impl(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)  \u001b[38;5;66;03m# type: ignore[misc]\u001b[39;00m\n\u001b[1;32m   1735\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1736\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_call_impl\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/meiziy/lib/python3.10/site-packages/torch/nn/modules/module.py:1747\u001b[0m, in \u001b[0;36mModule._call_impl\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1742\u001b[0m \u001b[38;5;66;03m# If we don't have any hooks, we want to skip the rest of the logic in\u001b[39;00m\n\u001b[1;32m   1743\u001b[0m \u001b[38;5;66;03m# this function, and just call forward.\u001b[39;00m\n\u001b[1;32m   1744\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_pre_hooks\n\u001b[1;32m   1745\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_backward_hooks\n\u001b[1;32m   1746\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_forward_pre_hooks):\n\u001b[0;32m-> 1747\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mforward_call\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1749\u001b[0m result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m   1750\u001b[0m called_always_called_hooks \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mset\u001b[39m()\n", "File \u001b[0;32m/home/<USER>/position_tti/train_td3_1.py:311\u001b[0m, in \u001b[0;36mCriticNet.forward\u001b[0;34m(self, state, action)\u001b[0m\n\u001b[1;32m    309\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mforward\u001b[39m(\u001b[38;5;28mself\u001b[39m, state, action):\n\u001b[1;32m    310\u001b[0m     output \u001b[38;5;241m=\u001b[39m torch\u001b[38;5;241m.\u001b[39mcat((state\u001b[38;5;241m.\u001b[39msrs_aod, state\u001b[38;5;241m.\u001b[39mpmi_aod,state\u001b[38;5;241m.\u001b[39msrs_eod, state\u001b[38;5;241m.\u001b[39mpmi_eod, action),dim \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m)\n\u001b[0;32m--> 311\u001b[0m     output \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mlayer\u001b[49m\u001b[43m(\u001b[49m\u001b[43moutput\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    312\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m output\n", "File \u001b[0;32m~/miniconda3/envs/meiziy/lib/python3.10/site-packages/torch/nn/modules/module.py:1736\u001b[0m, in \u001b[0;36mModule._wrapped_call_impl\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1734\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_compiled_call_impl(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)  \u001b[38;5;66;03m# type: ignore[misc]\u001b[39;00m\n\u001b[1;32m   1735\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1736\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_call_impl\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/meiziy/lib/python3.10/site-packages/torch/nn/modules/module.py:1747\u001b[0m, in \u001b[0;36mModule._call_impl\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1742\u001b[0m \u001b[38;5;66;03m# If we don't have any hooks, we want to skip the rest of the logic in\u001b[39;00m\n\u001b[1;32m   1743\u001b[0m \u001b[38;5;66;03m# this function, and just call forward.\u001b[39;00m\n\u001b[1;32m   1744\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_pre_hooks\n\u001b[1;32m   1745\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_backward_hooks\n\u001b[1;32m   1746\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_forward_pre_hooks):\n\u001b[0;32m-> 1747\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mforward_call\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1749\u001b[0m result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m   1750\u001b[0m called_always_called_hooks \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mset\u001b[39m()\n", "File \u001b[0;32m~/miniconda3/envs/meiziy/lib/python3.10/site-packages/torch/nn/modules/container.py:250\u001b[0m, in \u001b[0;36mSequential.forward\u001b[0;34m(self, input)\u001b[0m\n\u001b[1;32m    248\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mforward\u001b[39m(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;28minput\u001b[39m):\n\u001b[1;32m    249\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m module \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m:\n\u001b[0;32m--> 250\u001b[0m         \u001b[38;5;28minput\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[43mmodule\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m    251\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28minput\u001b[39m\n", "File \u001b[0;32m~/miniconda3/envs/meiziy/lib/python3.10/site-packages/torch/nn/modules/module.py:1736\u001b[0m, in \u001b[0;36mModule._wrapped_call_impl\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1734\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_compiled_call_impl(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)  \u001b[38;5;66;03m# type: ignore[misc]\u001b[39;00m\n\u001b[1;32m   1735\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1736\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_call_impl\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/meiziy/lib/python3.10/site-packages/torch/nn/modules/module.py:1747\u001b[0m, in \u001b[0;36mModule._call_impl\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1742\u001b[0m \u001b[38;5;66;03m# If we don't have any hooks, we want to skip the rest of the logic in\u001b[39;00m\n\u001b[1;32m   1743\u001b[0m \u001b[38;5;66;03m# this function, and just call forward.\u001b[39;00m\n\u001b[1;32m   1744\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_pre_hooks\n\u001b[1;32m   1745\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_backward_hooks\n\u001b[1;32m   1746\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_forward_pre_hooks):\n\u001b[0;32m-> 1747\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mforward_call\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1749\u001b[0m result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m   1750\u001b[0m called_always_called_hooks \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mset\u001b[39m()\n", "File \u001b[0;32m~/miniconda3/envs/meiziy/lib/python3.10/site-packages/torch/nn/modules/linear.py:125\u001b[0m, in \u001b[0;36mLinear.forward\u001b[0;34m(self, input)\u001b[0m\n\u001b[1;32m    124\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mforward\u001b[39m(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;28minput\u001b[39m: Tensor) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m Tensor:\n\u001b[0;32m--> 125\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mF\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mlinear\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mweight\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mbias\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["results = train(args, env, seed, modelpath, action_dim, state_dim, start_time, end_time)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "10 100 3\n", "expand H: (10, 1000, 17, 4, 64)\n", "expand ue loc: (10, 1000, 3)\n", "expand rsrp: (10, 1000, 3)\n", "srs_choose_all:(10, 1000, 17, 4)\n", "simulate srs:(10, 1000, 17, 4, 64)\n", "simulate coma:(10, 1000, 1, 32, 32)\n", "simulate rsrp:(10, 1000, 3)\n", "end load_H\n"]}], "source": ["path = os.getcwd()\n", "path = './inf/inf_nlos_v3/'\n", "seed = 777\n", "seed_torch(seed)\n", "env = setsumi_env(path=path, seed=seed, max_length=1000,mu=0.99,gNB_tti=1,select_subs=1,select_ports=1,test_real=False)  "]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["cuda\n", "averg aod eod pos pos_all err: 4.781743821504442 1.0434878313510887 -1.8783866 -1.8783866\n"]}], "source": ["modelpath = './mo/td3_inf_v3/2024-10-19-04-59-27_frame20000_score-0.6959907412528992'\n", "\n", "seed = 777\n", "action_dim = 2\n", "state_dim = 18*2\n", "\n", "time_length = env.max_time - 1\n", "start_time = 0\n", "end_time = start_time+time_length\n", "start_time = start_time//env.gNB_tti\n", "end_time = end_time//env.gNB_tti\n", "\n", "args = parse_args(args=[])\n", "args.seed = seed\n", "args.device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "# 推理数据集UE\n", "# args.global_ues = list(np.arange(env.nUE))\n", "\n", "print(args.device)\n", "results = infer(args, env, seed, modelpath, action_dim, state_dim, start_time, end_time)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["actor_loss = np.array(results['actor_loss'])\n", "critic_loss = np.array(results['critic_loss'])\n", "reward = np.array(results['reward'])\n", "aod = np.array(results['aod'])\n", "azimuths = np.array(results['azimuths'])\n", "eod = np.array(results['eod'])\n", "elevation = np.array(results['elevation'])\n", "\n", "reward_infer = np.array(results['reward_infer'])\n", "aod_infer = np.array(results['aod_infer'])\n", "azimuths_infer = np.array(results['azimuths_infer'])\n", "eod_infer = np.array(results['eod_infer'])\n", "elevation_infer = np.array(results['elevation_infer'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fig_r = draw_line(\"aod\",\"训练时间\",\"aod_infer\")\n", "for i in range(env.nUE):\n", "    c = aod_infer[:,i]\n", "    c = c.reshape(-1)\n", "    x = np.arange(len(c))\n", "    fig_r.add_line(x=x,y=c,name=i,mode=\"lines\")\n", "fig_r.fig.show()\n", "\n", "fig_r = draw_line(\"eod\",\"训练时间\",\"eod_infer\")\n", "for i in range(env.nUE):\n", "    c = eod_infer[:,i]\n", "    c = c.reshape(-1)\n", "    x = np.arange(len(c))\n", "    fig_r.add_line(x=x,y=c,name=i,mode=\"lines\")\n", "fig_r.fig.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["view_start = 0\n", "view_length = args.num_frame\n", "view_end = view_start+view_length\n", "\n", "fig_r = draw_line(\"aod\",\"训练时间\",\"aod\")\n", "for i in range(args.batch_size):\n", "    c = aod[view_start:view_end,i]\n", "    c = c.reshape(-1)\n", "    x = np.arange(len(c))\n", "    fig_r.add_line(x=x,y=c,name=i,mode=\"lines\")\n", "fig_r.fig.show()\n", "\n", "fig_r = draw_line(\"aod\",\"训练时间\",\"aod-真实值\")\n", "for i in range(args.batch_size):\n", "    c = aod[view_start:view_end,i]  - azimuths[view_start:view_end,i]\n", "    c = c.reshape(-1)\n", "    x = np.arange(len(c))\n", "    fig_r.add_line(x=x,y=c,name=i,mode=\"lines\")\n", "fig_r.fig.show()\n", "\n", "\n", "fig_r = draw_line(\"eod\",\"训练时间\",\"eod\")\n", "for i in range(args.batch_size):\n", "    c = eod[view_start:view_end,i]\n", "    c = c.reshape(-1)\n", "    x = np.arange(len(c))\n", "    fig_r.add_line(x=x,y=c,name=i,mode=\"lines\")\n", "fig_r.fig.show()\n", "\n", "fig_r = draw_line(\"eod\",\"训练时间\",\"eod-真实值\")\n", "for i in range(args.batch_size):\n", "    c = eod[view_start:view_end,i]  - elevation[view_start:view_end,i]\n", "    c = c.reshape(-1)\n", "    x = np.arange(len(c))\n", "    fig_r.add_line(x=x,y=c,name=i,mode=\"lines\")\n", "fig_r.fig.show()\n", "\n", "fig_r = draw_line(\"reward\",\"训练时间\",\"reward\")\n", "for i in range(args.batch_size):\n", "    c = reward[view_start:view_end,i]\n", "    c = c.reshape(-1)\n", "    x = np.arange(len(c))\n", "    fig_r.add_line(x=x,y=c,name=i,mode=\"lines\")\n", "fig_r.fig.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["view_start = 0\n", "view_length = args.num_frame\n", "view_end = view_start+view_length\n", "\n", "fig_r = draw_line(\"reward\",\"训练时间\",\"reward\")\n", "c = reward[view_start:view_end]\n", "c = np.average(c,axis=-1)\n", "c = c.reshape(-1)\n", "x = np.arange(len(c))\n", "fig_r.add_line(x=x,y=c,name='avg',mode=\"lines\")\n", "fig_r.fig.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fig_r = draw_line(\"actor_loss\",\"优化次数\",\"actor_loss\")\n", "actor_loss = [x for x in actor_loss if x is not None]\n", "x = np.arange(len(actor_loss))\n", "fig_r.add_line(x=x,y=actor_loss,name=\"actor_loss\",mode=\"lines\")\n", "fig_r.fig.show()\n", "\n", "\n", "fig_r = draw_line(\"critic_loss\",\"优化次数\",\"critic_loss\")\n", "critic_loss = [x for x in critic_loss if x is not None]\n", "x = np.arange(len(critic_loss))\n", "fig_r.add_line(x=x,y=critic_loss,name=\"critic_loss\",mode=\"lines\")\n", "fig_r.fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 平滑"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def moving_average(a, window_size):\n", "    cumulative_sum = np.cumsum(np.insert(a, 0, 0)) \n", "    middle = (cumulative_sum[window_size:] - cumulative_sum[:-window_size]) / window_size\n", "    r = np.arange(1, window_size-1, 2)\n", "    begin = np.cumsum(a[:window_size-1])[::2] / r\n", "    end = (np.cumsum(a[:-window_size:-1])[::2] / r)[::-1]\n", "    return np.concatenate((begin, middle, end))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["view_start = 0\n", "view_length = args.num_frame\n", "view_end = view_start+view_length\n", "\n", "window_size = 51\n", "\n", "fig_r = draw_line(\"aod\",\"训练时间\",\"aod\")\n", "for i in range(args.batch_size):\n", "    c = aod[view_start:view_end,i]\n", "    c = c.reshape(-1)\n", "    c = moving_average(c, window_size)\n", "    x = np.arange(len(c))\n", "    fig_r.add_line(x=x,y=c,name=i,mode=\"lines\")\n", "fig_r.fig.show()\n", "\n", "fig_r = draw_line(\"aod\",\"训练时间\",\"aod-真实值\")\n", "for i in range(args.batch_size):\n", "    c = aod[view_start:view_end,i]  - azimuths[view_start:view_end,i]\n", "    c = c.reshape(-1)\n", "    c = moving_average(c, window_size)\n", "    x = np.arange(len(c))\n", "    fig_r.add_line(x=x,y=c,name=i,mode=\"lines\")\n", "fig_r.fig.show()\n", "\n", "fig_r = draw_line(\"reward\",\"训练时间\",\"reward\")\n", "for i in range(args.batch_size):\n", "    c = reward[view_start:view_end,i]\n", "    print(np.average(c[-1000:-1]))\n", "    c = c.reshape(-1)\n", "    c = moving_average(c, window_size)\n", "    x = np.arange(len(c))\n", "    fig_r.add_line(x=x,y=c,name=i,mode=\"lines\")\n", "fig_r.fig.show()\n", "\n", "fig_r = draw_line(\"reward\",\"训练时间\",\"reward\")\n", "c = reward[view_start:view_end]\n", "c = np.average(c,axis=-1)\n", "c = c.reshape(-1)\n", "c = moving_average(c, window_size)\n", "x = np.arange(len(c))\n", "fig_r.add_line(x=x,y=c,name='avg',mode=\"lines\")\n", "fig_r.fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 推理"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "100 1000 3\n", "expand H: (100, 1000, 17, 4, 64)\n", "expand ue loc: (100, 1000, 3)\n", "expand rsrp: (100, 1000, 3)\n", "srs_choose_all:(100, 1000, 17, 4)\n", "simulate srs:(100, 1000, 17, 4, 64)\n", "simulate coma:(100, 1000, 1, 32, 32)\n", "simulate rsrp:(100, 1000, 3)\n", "end load_H\n"]}], "source": ["path = os.getcwd()\n", "path = './Data/dataset/speed0/'\n", "\n", "seed = 777\n", "seed_torch(seed)\n", "env = setsumi_env(path=path, seed=seed, max_length=1000,mu=0.99,gNB_tti=1,select_subs=16,select_ports=1,test_real=False)  "]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["cuda\n"]}], "source": ["modelpath = './mod/td3_v0/2024-10-08-00-51-39_frame10000_score-0.7279875874519348'\n", "\n", "seed = 777\n", "action_dim = 2\n", "state_dim = 18*2\n", "\n", "time_length = env.max_time - 1\n", "start_time = 0\n", "end_time = start_time+time_length\n", "start_time = start_time//env.gNB_tti\n", "end_time = end_time//env.gNB_tti\n", "\n", "args = parse_args(args=[])\n", "args.seed = seed\n", "args.device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "# 推理数据集UE\n", "# args.global_ues = list(np.arange(env.nUE))\n", "\n", "print(args.device)\n", "results = infer(args, env, seed, modelpath, action_dim, state_dim, start_time, end_time)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["averg aod eod pos pos_all err: 1.4393423463418635 1.6854466774660704 -0.9021476 -0.9021476\n"]}], "source": ["results = infer(args, env, seed, modelpath, action_dim, state_dim, start_time, end_time)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["()\n", "()\n", "(100,)\n", "(100,)\n", "(100,)\n", "(100,)\n", "(100,)\n"]}], "source": ["print(np.array(env.hangle_bound).shape)\n", "print(np.array(env.vangle_bound).shape)\n", "print(np.array(env.aod_real).shape)\n", "print(np.array(env.eod_real).shape)\n", "print(np.array(env.reward).shape)\n", "print(np.array(env.score).shape)\n", "print(np.array(env.score_all).shape)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reward_infer = np.array(results['reward_infer'])\n", "aod_infer = np.array(results['aod_infer'])\n", "azimuths_infer = np.array(results['azimuths_infer'])\n", "eod_infer = np.array(results['eod_infer'])\n", "elevation_infer = np.array(results['elevation_infer'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fig_r = draw_line(\"aod\",\"训练时间\",\"aod-真实值\")\n", "for i in range(env.nUE):\n", "    c = aod_infer[:,i] -azimuths_infer[:,i]\n", "    c = c.reshape(-1)\n", "    x = np.arange(len(c))\n", "    fig_r.add_line(x=x,y=c,name=i,mode=\"lines\")\n", "fig_r.fig.show()\n", "\n", "fig_r = draw_line(\"eod\",\"训练时间\",\"eod-真实值\")\n", "for i in range(env.nUE):\n", "    c = eod_infer[:,i] -elevation_infer[:,i]\n", "    c = c.reshape(-1)\n", "    x = np.arange(len(c))\n", "    fig_r.add_line(x=x,y=c,name=i,mode=\"lines\")\n", "fig_r.fig.show()\n", "\n", "fig_r = draw_line(\"reward_infer\",\"训练时间\",\"reward_infer\")\n", "for i in range(env.nUE):\n", "    c = reward_infer[:,i]\n", "    c = c.reshape(-1)\n", "    x = np.arange(len(c))\n", "    fig_r.add_line(x=x,y=c,name=i,mode=\"lines\")\n", "fig_r.fig.show()"]}], "metadata": {"kernelspec": {"display_name": "meiziy", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 2}