{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["#导入函数\n", "import os,sys\n", "sys.path.append(os.getcwd())\n", "from func.BM_functions import *\n", "from func.system_functions import *\n", "from func.process import *\n", "from func.setsumi_env import setsumi_env\n", "from utils import *\n", "import numpy as np\n", "import torch\n", "from train_td3 import seed_torch\n", "import pickle\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["gNB_tti=1\n", "setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "100 1000 3\n", "expand H: (100, 100, 17, 4, 64)\n", "expand ue loc: (100, 100, 3)\n", "expand rsrp: (100, 100, 3)\n", "simulate srs:(100, 100, 17, 4, 64)\n", "simulate coma:(100, 100, 1, 32, 32)\n", "simulate rsrp:(100, 100, 3)\n", "end load_H\n", "gNB_tti=2\n", "setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "100 1000 3\n", "expand H: (100, 100, 17, 4, 64)\n", "expand ue loc: (100, 100, 3)\n", "expand rsrp: (100, 100, 3)\n", "simulate srs:(100, 100, 17, 4, 64)\n", "simulate coma:(100, 50, 1, 32, 32)\n", "simulate rsrp:(100, 100, 3)\n", "end load_H\n", "gNB_tti=3\n", "setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "100 1000 3\n", "expand H: (100, 100, 17, 4, 64)\n", "expand ue loc: (100, 100, 3)\n", "expand rsrp: (100, 100, 3)\n", "simulate srs:(100, 100, 17, 4, 64)\n", "simulate coma:(100, 34, 1, 32, 32)\n", "simulate rsrp:(100, 100, 3)\n", "end load_H\n", "gNB_tti=4\n", "setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "100 1000 3\n", "expand H: (100, 100, 17, 4, 64)\n", "expand ue loc: (100, 100, 3)\n", "expand rsrp: (100, 100, 3)\n", "simulate srs:(100, 100, 17, 4, 64)\n", "simulate coma:(100, 25, 1, 32, 32)\n", "simulate rsrp:(100, 100, 3)\n", "end load_H\n", "gNB_tti=5\n", "setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "100 1000 3\n", "expand H: (100, 100, 17, 4, 64)\n", "expand ue loc: (100, 100, 3)\n", "expand rsrp: (100, 100, 3)\n", "simulate srs:(100, 100, 17, 4, 64)\n", "simulate coma:(100, 20, 1, 32, 32)\n", "simulate rsrp:(100, 100, 3)\n", "end load_H\n", "gNB_tti=6\n", "setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "100 1000 3\n", "expand H: (100, 100, 17, 4, 64)\n", "expand ue loc: (100, 100, 3)\n", "expand rsrp: (100, 100, 3)\n", "simulate srs:(100, 100, 17, 4, 64)\n", "simulate coma:(100, 17, 1, 32, 32)\n", "simulate rsrp:(100, 100, 3)\n", "end load_H\n", "gNB_tti=7\n", "setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "100 1000 3\n", "expand H: (100, 100, 17, 4, 64)\n", "expand ue loc: (100, 100, 3)\n", "expand rsrp: (100, 100, 3)\n", "simulate srs:(100, 100, 17, 4, 64)\n", "simulate coma:(100, 15, 1, 32, 32)\n", "simulate rsrp:(100, 100, 3)\n", "end load_H\n", "gNB_tti=8\n", "setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "100 1000 3\n", "expand H: (100, 100, 17, 4, 64)\n", "expand ue loc: (100, 100, 3)\n", "expand rsrp: (100, 100, 3)\n", "simulate srs:(100, 100, 17, 4, 64)\n", "simulate coma:(100, 13, 1, 32, 32)\n", "simulate rsrp:(100, 100, 3)\n", "end load_H\n", "gNB_tti=9\n", "setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "100 1000 3\n", "expand H: (100, 100, 17, 4, 64)\n", "expand ue loc: (100, 100, 3)\n", "expand rsrp: (100, 100, 3)\n", "simulate srs:(100, 100, 17, 4, 64)\n", "simulate coma:(100, 12, 1, 32, 32)\n", "simulate rsrp:(100, 100, 3)\n", "end load_H\n", "gNB_tti=10\n", "setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "100 1000 3\n", "expand H: (100, 100, 17, 4, 64)\n", "expand ue loc: (100, 100, 3)\n", "expand rsrp: (100, 100, 3)\n", "simulate srs:(100, 100, 17, 4, 64)\n", "simulate coma:(100, 10, 1, 32, 32)\n", "simulate rsrp:(100, 100, 3)\n", "end load_H\n", "gNB_tti=11\n", "setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "100 1000 3\n", "expand H: (100, 100, 17, 4, 64)\n", "expand ue loc: (100, 100, 3)\n", "expand rsrp: (100, 100, 3)\n", "simulate srs:(100, 100, 17, 4, 64)\n", "simulate coma:(100, 10, 1, 32, 32)\n", "simulate rsrp:(100, 100, 3)\n", "end load_H\n", "gNB_tti=12\n", "setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "100 1000 3\n", "expand H: (100, 100, 17, 4, 64)\n", "expand ue loc: (100, 100, 3)\n", "expand rsrp: (100, 100, 3)\n", "simulate srs:(100, 100, 17, 4, 64)\n", "simulate coma:(100, 9, 1, 32, 32)\n", "simulate rsrp:(100, 100, 3)\n", "end load_H\n", "gNB_tti=13\n", "setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "100 1000 3\n", "expand H: (100, 100, 17, 4, 64)\n", "expand ue loc: (100, 100, 3)\n", "expand rsrp: (100, 100, 3)\n", "simulate srs:(100, 100, 17, 4, 64)\n", "simulate coma:(100, 8, 1, 32, 32)\n", "simulate rsrp:(100, 100, 3)\n", "end load_H\n", "gNB_tti=14\n", "setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "100 1000 3\n", "expand H: (100, 100, 17, 4, 64)\n", "expand ue loc: (100, 100, 3)\n", "expand rsrp: (100, 100, 3)\n", "simulate srs:(100, 100, 17, 4, 64)\n", "simulate coma:(100, 8, 1, 32, 32)\n", "simulate rsrp:(100, 100, 3)\n", "end load_H\n", "gNB_tti=15\n", "setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "100 1000 3\n", "expand H: (100, 100, 17, 4, 64)\n", "expand ue loc: (100, 100, 3)\n", "expand rsrp: (100, 100, 3)\n", "simulate srs:(100, 100, 17, 4, 64)\n", "simulate coma:(100, 7, 1, 32, 32)\n", "simulate rsrp:(100, 100, 3)\n", "end load_H\n", "gNB_tti=16\n", "setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "100 1000 3\n", "expand H: (100, 100, 17, 4, 64)\n", "expand ue loc: (100, 100, 3)\n", "expand rsrp: (100, 100, 3)\n", "simulate srs:(100, 100, 17, 4, 64)\n", "simulate coma:(100, 7, 1, 32, 32)\n", "simulate rsrp:(100, 100, 3)\n", "end load_H\n"]}], "source": ["import numpy as np\n", "from scipy.signal import correlate\n", "\n", "# 假设 `setsumi_env` 函数和 `seed_torch` 函数已经定义\n", "path = './Data/dataset/speed3/'\n", "\n", "# 假设你要分析的 TTI 值列表\n", "gNB_tti_list = [i for i in range(1, 17)]  # 包含 1 到 16 的 TTI\n", "\n", "# 创建一个空的列表来存储不同 TTI 的 H_srs 矩阵\n", "H_srs_list = []\n", "\n", "# 遍历不同的 TTI，生成 H_srs 矩阵并保存\n", "for gNB_tti in gNB_tti_list:\n", "    print(f'gNB_tti={gNB_tti}')\n", "    \n", "    seed = 777\n", "    seed_torch(seed)\n", "    \n", "    if gNB_tti == 1:\n", "        env = setsumi_env(path=path, seed=seed, max_length=100, mu=0.99, gNB_tti=1, select_subs=1, select_ports=1, test_real=True)\n", "        H_srs = env.H_freq\n", "    else:\n", "        env = setsumi_env(path=path, seed=seed, max_length=100, mu=0.99, gNB_tti=gNB_tti, select_subs=1, select_ports=1, test_real=False)\n", "        H_srs = env.H_srs\n", "    \n", "    # 保存 H_srs 矩阵到列表中\n", "    H_srs_list.append(H_srs)                                                                                                                                                                                                                                    "]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1950/1878904427.py:19: ComplexWarning: Casting complex values to real discards the imaginary part\n", "  output[i, :, k, l, m] = correlate(first_H_srs[i, :, k, l, m], current_H_srs[i, :, k, l, m], mode='valid')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["(15, 100, 901, 17, 4, 64)\n"]}], "source": ["\n", "\n", "# 假设 H_srs 的维度是 (10, 100, 17, 4, 64)，对不同的 TTI 进行互相关分析\n", "first_H_srs = H_srs_list[0]  # 第一个 TTI 的 H_srs\n", "\n", "results = []\n", "\n", "# 遍历其他 TTI，进行互相关计算\n", "for tti_index in range(1, len(H_srs_list)):\n", "    current_H_srs = H_srs_list[tti_index]\n", "    \n", "    # 创建一个输出矩阵来存储互相关结果\n", "    output = np.zeros((100, 1000 - 99, 17, 4, 64))  # 这里核长度为 10，调整输出大小\n", "    \n", "    # 对所有维度进行循环\n", "    for i in range(100):  # 第一个维度\n", "        for k in range(17):  # 第三个维度\n", "            for l in range(4):  # 第四个维度\n", "                for m in range(64):  # 第五个维度\n", "                    # 对第二维度 100 进行互相关\n", "                    output[i, :, k, l, m] = correlate(first_H_srs[i, :, k, l, m], current_H_srs[i, :, k, l, m], mode='valid')\n", "    \n", "    # 保存互相关结果\n", "    results.append(output)\n", "\n", "# 输出结果的维度\n", "print(np.array(results).shape)  \n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "results_array = np.array(results)\n", "\n", "# 对维度 (1, 3, 4, 5) 取平均\n", "results_mean = results_array.mean(axis=(1, 3, 4, 5))  # 结果维度：(15, 10)\n", "\n", "# 对第1维 (10) 再取平均\n", "results_mean = results_mean.mean(axis=1)  # 结果维度：(15,)\n", "\n", "# 绘制峰值相关系数随TTI的变化\n", "tti_indices = np.arange(2, 17)\n", "plt.bar(tti_indices, results_mean)\n", "plt.xlabel('TTI Index')\n", "plt.ylabel('Mean Cross-correlation')\n", "plt.title('Mean Cross-correlation between TTI 1 and other TTIs')\n", "plt.show()\n", "plt.savefig(\"./tti_3.jpg\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 2}