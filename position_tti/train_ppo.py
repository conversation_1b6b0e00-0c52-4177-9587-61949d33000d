import os,sys
import pickle
sys.path.append(os.getcwd())
from func.BM_functions import *
from func.system_functions import *
from func.process import *
from func.setsumi_env import setsumi_env
from utils import *

import datetime
import logging
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import os
import random
import torch
import numpy as np
import torch.nn as nn
import torch.nn.functional as F
import argparse
from collections import deque

def seed_torch(seed):
    np.random.seed(seed)
    random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
    if torch.cuda.is_available():
        torch.backends.cudnn.enabled = True
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False
    torch.use_deterministic_algorithms(True, warn_only=True)
    os.environ['CUBLAS_WORKSPACE_CONFIG']=':4096:8' 
       
def parse_args(args=None):
    # ++ Basic Configs ++
    parser = argparse.ArgumentParser(description="BM")
    parser.add_argument("--desc", type=str, default="No desc", help="train desc")
    parser.add_argument("--seed", type=int, default=777, help="random seed")
    parser.add_argument("--device", type=str, default="cuda:0", help="train on device")
    
    # ++ Train Configs ++
    parser.add_argument("--memory_size", type=int, default=500000, help="DQN replay memory size")
    parser.add_argument("--batch_size", type=int, default=20, help="batch size")
    parser.add_argument("--train_nUE", type=int, default=20, help="train ue num")
    parser.add_argument("--optim_frame", type=int, default=1, help="update one time every N frame")
    parser.add_argument("--change_ue_cycle", type=int, default=100, help="change_ue_cycle")
    parser.add_argument("--done_cycle", type=int, default=100, help="done cycle")
    parser.add_argument("--num_frame", type=int, default=1000000, help="how many times agent train")
    parser.add_argument("--train_log_cycle", type=int, default=100, help="every N step log")

    # ++ TD3 Configs ++
    # parser.add_argument("--start_timesteps", default=25e2, type=int)
    # parser.add_argument("--eval_freq", default=5e2, type=int)       
    # parser.add_argument("--expl_noise", default=0.1)                
    parser.add_argument("--discount", default=0.99)  
    parser.add_argument("--pi_lr", default=1e-3)   
    parser.add_argument("--q_lr", default=1e-3)                  
    parser.add_argument("--tau", default=0.005)                    
    parser.add_argument("--policy_noise", default=0.2)
    # parser.add_argument("--policy_noise_decay", default=0.2)
    # parser.add_argument("--policy_noise_min", default=0.2)             
    parser.add_argument("--noise_clip", default=1)                
    parser.add_argument("--policy_freq", default=2, type=int)
    parser.add_argument("--sample_size", type=int, default=20, help="sample size")
    
    # ++ PPO  Configs ++
    parser.add_argument("--gamma", default=0.99)                 
    parser.add_argument("--lmbda", default=0.99)                 
    parser.add_argument("--epochs", default=10)                 
    parser.add_argument("--eps", default=0.2)                 
    parser.add_argument("--actor_lr", default=1e-3)  
    parser.add_argument("--critic_lr", default=3e-4)   
    
    if args is None:
        # 从命令行读取参数
        parsed_args = parser.parse_args()
    else:
        # 使用 parse_known_args 传递参数列表
        parsed_args, _ = parser.parse_known_args(args)
    
    return parsed_args

class ReplayMemoryPool():
    def __init__(self, size=10000):
        self.size = size # Memory size
        self.memories = deque(maxlen=self.size)
    def add_memory(self, state, reward, action, next_state, done):
        self.memories.append([state, reward, action, next_state, done])
    def get_batch(self, batch_size):
        choosen_index = np.random.choice(np.arange(0,len(self.memories)), batch_size, replace=False)
        return [self.memories[i] for i in choosen_index]
    def get_all_batch(self):
        choosen_index = np.arange(0,len(self.memories))
        return [self.memories[i] for i in choosen_index]
    def clear(self):
        self.memories.clear()

def compute_advantage(gamma, lmbda, td_delta):
    td_delta = td_delta.detach().numpy()
    advantage_list = []
    advantage = 0.0
    for delta in td_delta[::-1]:
        advantage = gamma * lmbda * advantage + delta
        advantage_list.append(advantage)
    advantage_list.reverse()
    return torch.tensor(np.array(advantage_list), dtype=torch.float)

class Actor(nn.Module):
    def __init__(self,state_dim):
        super(Actor, self).__init__()
        self.net = ActorNetPPO()

    def forward(self, state):
        mu,std = self.net(state)
        return mu,std

class Critic(nn.Module):
    def __init__(self,state_dim):
        super(Critic,self).__init__()
        self.net = CriticNetPPO(state_dim)

    def forward(self,state):
        q = self.net(state)
        return q
                            
class PPOContinuous:
    ''' 处理连续动作的PPO算法 '''
    def __init__(
            self, 
            device,
            batch_size = 2,
            state_dim = 71, 
            action_dim = 3, 
            actor_lr = 1e-3, 
            critic_lr = 1e-3,
            lmbda = 0.99, 
            epochs = 10, 
            eps = 0.2, 
            gamma = 0.99,
            memory_size = 10000
    ):
        self.action_dim = action_dim
        self.batch_size = batch_size
        # Actor Init
        self.actor = Actor(state_dim).to(device)
        self.actor_optimizer = torch.optim.Adam(self.actor.parameters(),lr=actor_lr)
        # Critic Init
        self.critic = Critic(state_dim).to(device)
        self.critic_optimizer = torch.optim.Adam(self.critic.parameters(),lr=critic_lr)
        # Hyper Parameters
        self.gamma = gamma
        self.lmbda = lmbda
        self.epochs = epochs
        self.eps = eps
        self.device = device
        self.memory_pool = ReplayMemoryPool(size=memory_size)
    
    def train(self):
        self.actor.train()

    def eval(self):
        self.actor.eval()
        
    def select_action(self, state):
        mu, sigma = self.actor(state)
        action_dist = torch.distributions.Normal(mu, sigma)
        action = action_dist.sample()
        action = action.detach().cpu().numpy()
        mu = mu.detach().cpu().numpy()
        sigma = sigma.detach().cpu().numpy()
        return action,mu,sigma

    def optim(self):
        # Initialize losses with None
        actor_loss = None
        critic_loss = None
        
        # states = torch.tensor(transition_dict['states'],
        #                       dtype=torch.float).to(self.device)
        # actions = torch.tensor(transition_dict['actions'],
        #                        dtype=torch.float).view(-1, 1).to(self.device)
        # rewards = torch.tensor(transition_dict['rewards'],
        #                        dtype=torch.float).view(-1, 1).to(self.device)
        # next_states = torch.tensor(transition_dict['next_states'],
        #                            dtype=torch.float).to(self.device)
        # dones = torch.tensor(transition_dict['dones'],
        #                      dtype=torch.float).view(-1, 1).to(self.device)
        # rewards = (rewards + 8.0) / 8.0  # 和TRPO一样,对奖励进行修改,方便训练
        
        # Sample all
        transition_dict = self.memory_pool.get_all_batch()
        dict_num = len(transition_dict)
        states = State(self.device, states=[transition_dict[i][0] for i in range(dict_num)])
        rewards = torch.tensor(np.array([transition_dict[i][1] for i in range(dict_num)]), device=self.device).view(-1,1)
        actions = torch.tensor(np.array([transition_dict[i][2] for i in range(dict_num)]), device=self.device).view(-1,self.action_dim)
        next_states = State(self.device, states=[transition_dict[i][3] for i in range(dict_num)])
        dones = torch.tensor(np.array([1-transition_dict[i][4] for i in range(dict_num)]), device=self.device).view(-1,1)
        rewards = (rewards - rewards.mean()) / (rewards.std() + 1e-7)
        td_target = rewards + self.gamma * self.critic(next_states) * (1 - dones)
        td_delta = td_target - self.critic(states)
        
        td_delta = td_delta.view(-1,self.batch_size)
        advantage = compute_advantage(self.gamma, self.lmbda, td_delta.cpu()).to(self.device)
        advantage = advantage.view(-1,1)
        
        mu, std = self.actor(states)
        action_dists = torch.distributions.Normal(mu.detach(), std.detach())
        # 动作是正态分布
        old_log_probs = action_dists.log_prob(actions)
        
        for _ in range(self.epochs):
            mu, std = self.actor(states)
            action_dists = torch.distributions.Normal(mu, std)
            log_probs = action_dists.log_prob(actions)
            ratio = torch.exp(log_probs - old_log_probs)
            surr1 = ratio * advantage
            surr2 = torch.clamp(ratio, 1 - self.eps, 1 + self.eps) * advantage
            actor_loss = torch.mean(-torch.min(surr1, surr2))
            critic_loss = torch.mean(
                F.mse_loss(self.critic(states), td_target.type(torch.float32).detach()))
            self.actor_optimizer.zero_grad()
            self.critic_optimizer.zero_grad()
            actor_loss.backward()
            critic_loss.backward()
            self.actor_optimizer.step()
            self.critic_optimizer.step()
        
        self.memory_pool.clear()
        
        return [actor_loss.item() if actor_loss is not None else None, critic_loss.item() if critic_loss is not None else None]
    
    def save(self, path):
        torch.save(self.actor.state_dict(), path)
   
    def load(self, path):
        self.actor.load_state_dict(torch.load(path))
         
class State():
    def __init__(self, device, srs_aod=None, pmi_aod=None, srs_eod=None, pmi_eod=None, states=None):
        if states is not None:
            self.nUE = states[0].nUE
            self.srs_aod = torch.cat([state.srs_aod for state in states])
            self.pmi_aod = torch.cat([state.pmi_aod for state in states])
            self.srs_eod = torch.cat([state.srs_eod for state in states])
            self.pmi_eod = torch.cat([state.pmi_eod for state in states])
        else:
            self.flatten = torch.nn.Flatten()
            self.nUE = srs_aod.shape[0]
            self.srs_aod = self.flatten(torch.tensor(srs_aod, device=device, dtype=torch.float32))
            self.pmi_aod = self.flatten(torch.tensor(pmi_aod, device=device, dtype=torch.float32))
            self.srs_eod = self.flatten(torch.tensor(srs_eod, device=device, dtype=torch.float32))
            self.pmi_eod = self.flatten(torch.tensor(pmi_eod, device=device, dtype=torch.float32))

class ActorNetPPO(torch.nn.Module):
    def __init__(self):
        super(ActorNetPPO, self).__init__()        

        self.hlayer = nn.Linear(18, 16)
        
        self.vlayer = nn.Linear(18, 16)
        
        self.mix_layer = nn.Linear(16*2,16)

        self.mu_jointlayer = nn.Linear(16, 2)
        self.std_jointlayer = nn.Linear(16, 2)
        
        self.relu = nn.ReLU()
        self.tanh = nn.Tanh()
        self.softmax = nn.Softmax(dim=-1)
    
    def forward(self, state):
        h = self.hlayer(torch.cat((state.srs_aod, state.pmi_aod),dim=-1))
        h = self.relu(h)
        
        v = self.hlayer(torch.cat((state.srs_eod, state.pmi_eod),dim=-1))
        v = self.relu(v)
        
        mix = self.mix_layer(torch.cat((h, v),dim=-1))
        mix = self.relu(mix)
        
        mu = self.mu_jointlayer(mix)
        mu = self.tanh(mu)
        
        std = self.std_jointlayer(mix)
        std = F.softplus(std)
        # std = self.softmax(std)
        
        return mu,std
               
class CriticNetPPO(torch.nn.Module):
    def __init__(self, state_dim):
        super(CriticNetPPO, self).__init__()        

        self.layer = nn.Sequential(
            nn.Linear(state_dim,256),
            nn.ReLU(),
            nn.LayerNorm([256]),
            nn.Linear(256,64),
            nn.ReLU(),
            nn.LayerNorm([64]),
            nn.Linear(64,1)
        ) 
        
    def forward(self, state):
        output = torch.cat((state.srs_aod, state.pmi_aod,state.srs_eod, state.pmi_eod),dim = -1)
        output = self.layer(output)
        return output

def calscore(args, env, agent, batch, start_time, end_time, action_dim, ues):
    agent.eval()
    
    time_length = end_time - start_time
    reward = np.zeros((batch, time_length), dtype=np.float32)
    score = np.zeros((batch, time_length), dtype=np.float32)
    score_all = np.zeros((batch, time_length), dtype=np.float32)
    action = np.zeros((batch, action_dim), dtype=np.float32)
    aod = np.zeros((batch, time_length), dtype=np.float64)
    eod = np.zeros((batch, time_length), dtype=np.float64)
    aod_real = np.zeros((batch, time_length), dtype=np.float64)
    eod_real = np.zeros((batch, time_length), dtype=np.float64)
    for i in range(0, time_length):
        times = np.ones_like(ues)*(i+start_time)
        state = State(args.device, *env.get_state_method3(batch=batch,actions=action,ues=ues,times=times))
        action,mu,sigma = agent.select_action(state)
        aod[:,i] = action[:,0]*env.hangle_bound
        eod[:,i] = action[:,1]*env.vangle_bound
        aod_real[:,i] = env.aod_real
        eod_real[:,i] = env.eod_real
        reward[:,i] = env.reward
        score[:,i] = env.score
        score_all[:,i] = env.score_all
        
    res = np.average(score[:,1:])
        
    agent.train()
    
    return reward,aod,eod,aod_real,eod_real,score,score_all,res

def train(args, env, seed, modelpath, action_dim, state_dim, start_time, end_time):
    # PPO agent
    agent = PPOContinuous(device=args.device, 
            memory_size=args.memory_size,
            gamma = args.gamma,
            eps = args.eps,
            epochs = args.epochs,
            lmbda = args.lmbda,
            actor_lr = args.actor_lr,
            critic_lr = args.critic_lr,
            action_dim=action_dim,
            state_dim=state_dim,
            batch_size=args.batch_size)
    
    # 记录
    args.daytime = datetime.datetime.now().strftime('%Y-%m-%d-%H-%M-%S')
    if not os.path.exists(modelpath):
        os.makedirs(modelpath)
    logging.basicConfig(format='%(asctime)s - %(levelname)s - %(message)s',
                    datefmt='%Y-%m-%d %H:%M:%S',
                    level=logging.INFO,
                    filename=f'{modelpath}/{args.daytime}.log',
                    filemode='w')
    logger = logging.getLogger(__name__)
    logger.info("parameters: %s", args)
    logger.info("path=%s,seed=%d,max_time=%d,max_length=%d,mu=%f,gNB_tti=%d,select_subs=%d,select_ports=%d,test_real=%d", env.path,env.seed,env.max_time,env.max_length,env.mu,env.gNB_tti,env.select_subs,env.select_ports,env.test_real)        
    # 训练参数
    max_score = -3
    if hasattr(args,"ue_pool"):
        ue_pool = args.ue_pool
    else:
        ue_pool = list(np.arange(args.train_nUE))
    batch = args.batch_size
    # 推理参数
    if hasattr(args,"global_batch"):
        global_batch = args.global_batch
    else:
        global_batch = env.nUE
    if hasattr(args,"global_ues"):
        global_ues = args.global_ues
    else:
        global_ues = list(np.arange(env.nUE))
    # 初始数据选择
    ptr = 0
    UE = ue_pool[ptr:min(ptr+batch,len(ue_pool))]
    times = np.ones_like(UE)*start_time
    ues = UE
    # 开始训练
    frame_idx = 0
    # 初始化 state
    action = np.random.random((batch, action_dim))
    state = State(args.device, *env.get_state_method3(batch=batch,actions=action,ues=ues,times=times))
    
    results = dict()
    results['actor_loss'] = []
    results['critic_loss'] = []
    results['reward'] = []
    results['aod'] = []
    results['eod'] = []
    results['azimuths'] = []
    results['elevation'] = []
    results['svd_aod_er'] = []
    results['pmi_aod_er'] = []
    
    results['reward_infer'] = []
    results['aod_infer'] = []
    results['eod_infer'] = []
    results['score_infer'] = []
    results['azimuths_infer'] = []
    results['elevation_infer'] = []
    results['score_all_infer'] = []

    while frame_idx < args.num_frame:
        # 选择当前state的动作action
        # 基于当前时刻state推理下一时刻的角度
        action,mu,sigma = agent.select_action(state)
        # 执行action，获得下一个state和reward
        next_state = State(args.device, *env.get_state_method3(batch=batch,actions=action,ues=ues,times=times))
        reward = env.reward
        # 将（St, At, Rt, St+1）放入回放池
        done = frame_idx % args.done_cycle == 0
        done = np.ones_like(ues)*done
        if frame_idx != 0:
            agent.memory_pool.add_memory(state, reward, action, next_state, done)
        # 更新状态
        state = next_state
        frame_idx += 1
        times = times + 1
        results['reward'].append(reward)
        results['aod'].append(action[:,0]*env.hangle_bound)
        results['eod'].append(action[:,1]*env.vangle_bound)
        results['azimuths'].append(env.aod_real)
        results['elevation'].append(env.eod_real)
        results['svd_aod_er'].append(env.svd_aod_er)
        results['pmi_aod_er'].append(env.pmi_aod_er)
        # 每optim_frame步优化
        if frame_idx % args.optim_frame == 0 and frame_idx != 0:
            actor_loss,critic_loss = agent.optim()
            results['actor_loss'].append(actor_loss)
            results['critic_loss'].append(critic_loss)
            
        
        # 每M步更新选择的UE,重新从start_time开始
        if frame_idx % args.change_ue_cycle == 0 and frame_idx != 0:
            times = np.ones_like(UE)*start_time
            ptr += batch
            if ptr+batch >= len(ue_pool):
                ue_pool.extend(ue_pool)
            UE = ue_pool[ptr:min(ptr+batch,len(ue_pool))]
        
        # 每M步计算模型训练结果
        if frame_idx % args.train_log_cycle == 0 and frame_idx != 0:
            agent.eval()
            reward,aod,eod,aod_real,eod_real,score,score_all,res = calscore(args, env, agent, global_batch, start_time, end_time, action_dim, global_ues)
            results['reward_infer'].append(reward)
            results['aod_infer'].append(aod)
            results['eod_infer'].append(eod)
            results['azimuths_infer'].append(aod_real)
            results['elevation_infer'].append(eod_real)
            results['score_infer'].append(score)
            results['score_all_infer'].append(score_all)

            print('reward:', np.average(reward[:,1:],axis=-1))
            print("aod err:", np.average(np.abs(aod[:,1:]-aod_real[:,1:]),axis=-1))
            print("eod err:", np.average(np.abs(eod[:,1:]-eod_real[:,1:]),axis=-1))
            print('pos err:',np.average(score[:,1:],axis=-1))
            print('pos all err:',np.average(score_all[:,1:],axis=-1))
            print('averg aod eod pos pos_all err:', np.average(np.abs(aod[:,1:]-aod_real[:,1:])), np.average(np.abs(eod[:,1:]-eod_real[:,1:])), np.average(score[:,1:]), np.average(score_all[:,1:]))
            
            if res > max_score:
                agent.save(modelpath+f'{args.daytime}_frame{frame_idx}_score{res}')
                max_score = res
            agent.train()

    name = f'{modelpath}/{args.daytime}_results.pkl'
    with open(name, 'wb') as file:
        pickle.dump(results, file) 
    
    return results

def infer(args, env, seed, modelpath, action_dim, state_dim, start_time, end_time):
    # PPO agent
    agent = PPOContinuous(device=args.device, 
            memory_size=args.memory_size,
            gamma = args.gamma,
            eps = args.eps,
            epochs = args.epochs,
            lmbda = args.lmbda,
            actor_lr = args.actor_lr,
            critic_lr = args.critic_lr,
            action_dim=action_dim,
            state_dim=state_dim,
            batch_size=args.batch_size)
    
    # 加载模型
    agent.load(modelpath)
    # 推理参数
    if hasattr(args,"global_ues"):
        global_ues = args.global_ues
    else:
        global_ues = list(np.arange(env.nUE))
    global_batch = len(global_ues)
    # 推理
    results = dict()
    results['reward_infer'] = []
    results['aod_infer'] = []
    results['eod_infer'] = []
    results['score_infer'] = []
    results['azimuths_infer'] = []
    results['elevation_infer'] = []
    results['score_all_infer'] = []
    
    reward,aod,eod,aod_real,eod_real,score,score_all,res = calscore(args, env, agent, global_batch, start_time, end_time, action_dim, global_ues)
    results['reward_infer'].append(reward)
    results['aod_infer'].append(aod)
    results['eod_infer'].append(eod)
    results['azimuths_infer'].append(aod_real)
    results['elevation_infer'].append(eod_real)
    results['score_infer'].append(score)
    results['score_all_infer'].append(score_all)

    # print('reward:', np.average(reward[:,1:],axis=-1))
    # print("aod err:", np.average(np.abs(aod[:,1:]-aod_real[:,1:]),axis=-1))
    # print("eod err:", np.average(np.abs(eod[:,1:]-eod_real[:,1:]),axis=-1))
    # print('pos err:',np.average(score[:,1:],axis=-1))
    # print('pos all err:',np.average(score_all[:,1:],axis=-1))
    print('averg aod eod pos pos_all err:', np.average(np.abs(aod[:,1:]-aod_real[:,1:])), np.average(np.abs(eod[:,1:]-eod_real[:,1:])), np.average(score[:,1:]), np.average(score_all[:,1:]))
    
    return results