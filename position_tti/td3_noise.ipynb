{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import os,sys\n", "sys.path.append(os.getcwd())\n", "from func.BM_functions import *\n", "from func.system_functions import *\n", "from func.process import *\n", "from func.setsumi_env import setsumi_env\n", "from utils import *\n", "import torch\n", "import pickle\n", "from train_td3 import seed_torch,parse_args,train,infer"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["setsumi load sim log OK\n", "Load begin\n"]}, {"ename": "BadZipFile", "evalue": "File is not a zip file", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mBadZipFile\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[5], line 5\u001b[0m\n\u001b[0;32m      3\u001b[0m seed \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m777\u001b[39m\n\u001b[0;32m      4\u001b[0m seed_torch(seed)\n\u001b[1;32m----> 5\u001b[0m env \u001b[38;5;241m=\u001b[39m \u001b[43msetsumi_env\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpath\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mpath\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mseed\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mseed\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmax_length\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m4000\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43mmu\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m0.99\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43mgNB_tti\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43mselect_subs\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m[\u001b[49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43mselect_ports\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43mtest_real\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m)\u001b[49m  \n", "File \u001b[1;32md:\\biye\\position_tti\\func\\setsumi_env.py:69\u001b[0m, in \u001b[0;36msetsumi_env.__init__\u001b[1;34m(self, logger, path, seed, max_time, max_length, mu, gNB_tti, select_subs, select_ports, test_real)\u001b[0m\n\u001b[0;32m     66\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhangle_bound \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m60\u001b[39m\n\u001b[0;32m     67\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mvangle_bound \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m60\u001b[39m\n\u001b[1;32m---> 69\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mload_H\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     70\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcal_aod_eod_dist_real()\n\u001b[0;32m     71\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcal_svd_angle()\n", "File \u001b[1;32md:\\biye\\position_tti\\func\\setsumi_env.py:76\u001b[0m, in \u001b[0;36msetsumi_env.load_H\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m     74\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mlogger \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m:\n\u001b[0;32m     75\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mlogger(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mLoad begin\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m---> 76\u001b[0m \u001b[43mget_data\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msim_par\u001b[49m\u001b[43m,\u001b[49m\u001b[43mclear_time\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[0;32m     77\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mH_freq \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39marray(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msim_par\u001b[38;5;241m.\u001b[39mdata[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mH_freq\u001b[39m\u001b[38;5;124m'\u001b[39m],dtype\u001b[38;5;241m=\u001b[39mnp\u001b[38;5;241m.\u001b[39mcomplex64)\n\u001b[0;32m     78\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mH_pg \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39marray(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msim_par\u001b[38;5;241m.\u001b[39mdata[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mH_pg\u001b[39m\u001b[38;5;124m'\u001b[39m],dtype\u001b[38;5;241m=\u001b[39mnp\u001b[38;5;241m.\u001b[39mfloat64)\n", "File \u001b[1;32md:\\biye\\position_tti\\func\\process.py:15\u001b[0m, in \u001b[0;36mget_data\u001b[1;34m(sim_par, clear_time)\u001b[0m\n\u001b[0;32m     13\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m os\u001b[38;5;241m.\u001b[39mpath\u001b[38;5;241m.\u001b[39mexists(sim_par\u001b[38;5;241m.\u001b[39mData_dir\u001b[38;5;241m+\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdata_redo.npz\u001b[39m\u001b[38;5;124m\"\u001b[39m):\n\u001b[0;32m     14\u001b[0m     pre_process(sim_par\u001b[38;5;241m.\u001b[39mData_dir,\u001b[38;5;241m~\u001b[39mclear_time)\n\u001b[1;32m---> 15\u001b[0m data_redo \u001b[38;5;241m=\u001b[39m \u001b[43mnp\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mload\u001b[49m\u001b[43m(\u001b[49m\u001b[43msim_par\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mData_dir\u001b[49m\u001b[38;5;241;43m+\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mdata_redo.npz\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43mallow_pickle\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m)\u001b[49m[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124marr_0\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m.\u001b[39mitem()\n\u001b[0;32m     16\u001b[0m sim_par\u001b[38;5;241m.\u001b[39mdata \u001b[38;5;241m=\u001b[39m data_redo\n\u001b[0;32m     17\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m(clear_time):\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\numpy\\lib\\npyio.py:444\u001b[0m, in \u001b[0;36mload\u001b[1;34m(file, mmap_mode, allow_pickle, fix_imports, encoding, max_header_size)\u001b[0m\n\u001b[0;32m    440\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m magic\u001b[38;5;241m.\u001b[39mstartswith(_ZIP_PREFIX) \u001b[38;5;129;01mor\u001b[39;00m magic\u001b[38;5;241m.\u001b[39mstartswith(_ZIP_SUFFIX):\n\u001b[0;32m    441\u001b[0m     \u001b[38;5;66;03m# zip-file (assume .npz)\u001b[39;00m\n\u001b[0;32m    442\u001b[0m     \u001b[38;5;66;03m# Potentially transfer file ownership to NpzFile\u001b[39;00m\n\u001b[0;32m    443\u001b[0m     stack\u001b[38;5;241m.\u001b[39mpop_all()\n\u001b[1;32m--> 444\u001b[0m     ret \u001b[38;5;241m=\u001b[39m \u001b[43mNpzFile\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfid\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mown_fid\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mown_fid\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mallow_pickle\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mallow_pickle\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    445\u001b[0m \u001b[43m                  \u001b[49m\u001b[43mpickle_kwargs\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mpickle_kwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    446\u001b[0m \u001b[43m                  \u001b[49m\u001b[43mmax_header_size\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmax_header_size\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    447\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m ret\n\u001b[0;32m    448\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m magic \u001b[38;5;241m==\u001b[39m \u001b[38;5;28mformat\u001b[39m\u001b[38;5;241m.\u001b[39mMAGIC_PREFIX:\n\u001b[0;32m    449\u001b[0m     \u001b[38;5;66;03m# .npy file\u001b[39;00m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\numpy\\lib\\npyio.py:190\u001b[0m, in \u001b[0;36mNpzFile.__init__\u001b[1;34m(self, fid, own_fid, allow_pickle, pickle_kwargs, max_header_size)\u001b[0m\n\u001b[0;32m    185\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__init__\u001b[39m(\u001b[38;5;28mself\u001b[39m, fid, own_fid\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m, allow_pickle\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[0;32m    186\u001b[0m              pickle_kwargs\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;241m*\u001b[39m,\n\u001b[0;32m    187\u001b[0m              max_header_size\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mformat\u001b[39m\u001b[38;5;241m.\u001b[39m_MAX_HEADER_SIZE):\n\u001b[0;32m    188\u001b[0m     \u001b[38;5;66;03m# Import is postponed to here since zipfile depends on gzip, an\u001b[39;00m\n\u001b[0;32m    189\u001b[0m     \u001b[38;5;66;03m# optional component of the so-called standard library.\u001b[39;00m\n\u001b[1;32m--> 190\u001b[0m     _zip \u001b[38;5;241m=\u001b[39m \u001b[43mzipfile_factory\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfid\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    191\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_files \u001b[38;5;241m=\u001b[39m _zip\u001b[38;5;241m.\u001b[39mnamelist()\n\u001b[0;32m    192\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mfiles \u001b[38;5;241m=\u001b[39m []\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\numpy\\lib\\npyio.py:103\u001b[0m, in \u001b[0;36mzipfile_factory\u001b[1;34m(file, *args, **kwargs)\u001b[0m\n\u001b[0;32m    101\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mzipfile\u001b[39;00m\n\u001b[0;32m    102\u001b[0m kwargs[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mallowZip64\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[1;32m--> 103\u001b[0m \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m zipfile\u001b[38;5;241m.\u001b[39mZipFile(file, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\zipfile.py:1266\u001b[0m, in \u001b[0;36mZipFile.__init__\u001b[1;34m(self, file, mode, compression, allowZip64, compresslevel, strict_timestamps)\u001b[0m\n\u001b[0;32m   1264\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m   1265\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m mode \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mr\u001b[39m\u001b[38;5;124m'\u001b[39m:\n\u001b[1;32m-> 1266\u001b[0m         \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_RealGetContents\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1267\u001b[0m     \u001b[38;5;28;01melif\u001b[39;00m mode \u001b[38;5;129;01min\u001b[39;00m (\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mw\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mx\u001b[39m\u001b[38;5;124m'\u001b[39m):\n\u001b[0;32m   1268\u001b[0m         \u001b[38;5;66;03m# set the modified flag so central directory gets written\u001b[39;00m\n\u001b[0;32m   1269\u001b[0m         \u001b[38;5;66;03m# even if no files are added to the archive\u001b[39;00m\n\u001b[0;32m   1270\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_didModify \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\zipfile.py:1333\u001b[0m, in \u001b[0;36mZipFile._RealGetContents\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m   1331\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m BadZipFile(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFile is not a zip file\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m   1332\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m endrec:\n\u001b[1;32m-> 1333\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m BadZipFile(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFile is not a zip file\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m   1334\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdebug \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[0;32m   1335\u001b[0m     \u001b[38;5;28mprint\u001b[39m(endrec)\n", "\u001b[1;31mBadZipFile\u001b[0m: File is not a zip file"]}], "source": ["path = os.getcwd()\n", "path = './umi_v3/'\n", "seed = 777\n", "seed_torch(seed)\n", "env = setsumi_env(path=path, seed=seed, max_length=4000,mu=0.99,gNB_tti=1,select_subs=[1],select_ports=1,test_real=False)  "]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 2}