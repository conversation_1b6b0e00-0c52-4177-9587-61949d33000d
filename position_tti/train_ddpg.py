import os,sys
import pickle
sys.path.append(os.getcwd())
from func.BM_functions import *
from func.system_functions import *
from func.process import *
from func.setsumi_env import setsumi_env
from utils import *

import datetime
import logging
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import os
import random
import torch
import numpy as np
import torch.nn as nn
import torch.nn.functional as F
import argparse
from collections import deque

def seed_torch(seed):
    np.random.seed(seed)
    random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
    if torch.cuda.is_available():
        torch.backends.cudnn.enabled = True
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False
    torch.use_deterministic_algorithms(True, warn_only=True)
    os.environ['CUBLAS_WORKSPACE_CONFIG']=':4096:8' 
       
def parse_args(args=None):
    # ++ Basic Configs ++
    parser = argparse.ArgumentParser(description="BM")
    parser.add_argument("--desc", type=str, default="No desc", help="train desc")
    parser.add_argument("--seed", type=int, default=777, help="random seed")
    parser.add_argument("--device", type=str, default="cuda:0", help="train on device")
    
    # ++ Train Configs ++
    parser.add_argument("--memory_size", type=int, default=500000, help="DQN replay memory size")
    parser.add_argument("--train_nUE", type=int, default=20, help="train ue num")
    parser.add_argument("--batch_size", type=int, default=20, help="batch size")
    parser.add_argument("--optim_frame", type=int, default=1, help="update one time every N frame")
    parser.add_argument("--change_ue_cycle", type=int, default=100, help="change_ue_cycle")
    parser.add_argument("--done_cycle", type=int, default=100, help="done cycle")
    parser.add_argument("--num_frame", type=int, default=1000000, help="how many times agent train")
    parser.add_argument("--train_log_cycle", type=int, default=100, help="every N step log")

    # ++ DDPG Configs ++
    parser.add_argument("--start_timesteps", default=25e2, type=int)
    parser.add_argument("--discount", default=0.99)  
    parser.add_argument("--pi_lr", default=1e-3)   
    parser.add_argument("--q_lr", default=1e-3)                  
    parser.add_argument("--tau", default=0.005)                    
    parser.add_argument("--noise_std", default=0.1)
    parser.add_argument("--sample_size", type=int, default=20, help="sample size")
    
    # 添加精度监控相关参数
    parser.add_argument("--target_accuracy", type=float, default=0.9, help="Target accuracy to achieve")
    parser.add_argument("--accuracy_window", type=int, default=10, help="Window size for accuracy calculation")
    parser.add_argument("--patience", type=int, default=50, help="Number of evaluations to maintain target accuracy")
    parser.add_argument("--min_episodes_for_accuracy", type=int, default=100, help="Minimum episodes before checking accuracy")
    
    if args is None:
        # 从命令行读取参数
        parsed_args = parser.parse_args()
    else:
        # 使用 parse_known_args 传递参数列表
        parsed_args, _ = parser.parse_known_args(args)
    
    return parsed_args

class ReplayMemoryPool():
    def __init__(self, size=10000):
        self.size = size # Memory size
        self.memories = deque(maxlen=self.size)
    def add_memory(self, state, reward, action, next_state, done):
        self.memories.append([state, reward, action, next_state, done])
    def get_batch(self, batch_size):
        choosen_index = np.random.choice(np.arange(0,len(self.memories)), batch_size, replace=False)
        return [self.memories[i] for i in choosen_index]
    def get_all_batch(self):
        choosen_index = np.arange(0,len(self.memories))
        return [self.memories[i] for i in choosen_index]
    def clear(self):
        self.memories.clear()
    def sample(self, batch_size):
        # 假设这是 ReplayMemoryPool 类的 sample 方法
        indices = np.random.randint(0, len(self.memories), size=batch_size)
        batch = [self.memories[idx] for idx in indices]
        
        state, action, next_state, reward, done = zip(*batch)
        
        # 转换为张量并确保类型为 float32
        state = torch.FloatTensor(np.array(state)).to(self.device)
        action = torch.FloatTensor(np.array(action)).to(self.device)
        next_state = torch.FloatTensor(np.array(next_state)).to(self.device)
        reward = torch.FloatTensor(np.array(reward)).to(self.device)
        done = torch.FloatTensor(np.array(done)).to(self.device)
        
        return state, action, next_state, reward, done

class Actor(nn.Module):
    def __init__(self,state_dim):
        super(Actor, self).__init__()
        self.net = ActorNet()

    def forward(self, state):
        action = self.net(state)
        return action

class Critic(nn.Module):
    def __init__(self,state_dim,action_dim):
        super(Critic,self).__init__()
        self.Q = CriticNet(state_dim, action_dim)

    def forward(self,state,action):
        q = self.Q(state,action)
        return q

class DDPG(object):
    def __init__(
        self,
        device,
        state_dim=71,
        action_dim=3,
        discount = 0.98,
        tau = 0.005,
        memory_size = 10000,
        sample_size = 100,
        pi_lr = 1e-3,
        q_lr = 1e-3,
        noise_std=0.1
    ):
        self.device = device
        self.state_dim = state_dim
        self.action_dim = action_dim
        # 设置默认数据类型为 float32
        torch.set_default_dtype(torch.float32)
        # Actor Init 
        self.actor = Actor(state_dim=state_dim).to(device)
        self.actor_target = copy.deepcopy(self.actor)
        self.actor_optimizer = torch.optim.Adam(self.actor.parameters(),lr = pi_lr)
        # Critic Init
        self.critic = Critic(state_dim=state_dim,action_dim=action_dim).to(device)
        self.critic_target = copy.deepcopy(self.critic)
        self.critic_optimizer = torch.optim.Adam(self.critic.parameters(),lr = q_lr)
        # Hyper Parameters
        self.discount = discount
        self.tau = tau
        self.sample_size = sample_size
        self.device = device
        self.total_it = 0
        # memory
        self.memory_pool = ReplayMemoryPool(size=memory_size)
        

    def train(self):
        self.actor.train()

    def eval(self):
        self.actor.eval()

    def select_action(self,state) :
        action = self.actor(state)
        action = action.detach().cpu().numpy()
    
        return action

    def optim(self):
        # Initialize losses with None
        actor_loss = None
        critic_loss = None
        if len(self.memory_pool.memories) < self.sample_size:
            return [None, None]
        
        self.total_it +=1
        
        # Sample
        batch = self.memory_pool.get_batch(self.sample_size)
        state = State(self.device, states=[batch[i][0] for i in range(self.sample_size)])
        reward = torch.tensor(np.array([batch[i][1] for i in range(self.sample_size)]), device=self.device).view(-1,1)
        action = torch.tensor(np.array([batch[i][2] for i in range(self.sample_size)]), device=self.device).view(-1,self.action_dim)
        next_state = State(self.device, states=[batch[i][3] for i in range(self.sample_size)])
        done = torch.tensor(np.array([1-batch[i][4] for i in range(self.sample_size)]), device=self.device).view(-1,1)

        with torch.no_grad():
            # 计算目标Q值
            next_action = self.actor_target(next_state)
            target_Q = self.critic_target(next_state, next_action)
            target_Q = reward + (1.0 - done) * self.discount * target_Q
            target_Q = target_Q.type(torch.float32)

        # 计算当前Q值
        current_Q = self.critic(state, action)
        
        # 计算Critic损失
        critic_loss = F.mse_loss(current_Q, target_Q)

        # 优化Critic
        self.critic_optimizer.zero_grad()
        critic_loss.backward()
        self.critic_optimizer.step()    

        # 计算Actor损失
        actor_loss = -self.critic(state, self.actor(state)).mean()

        # 优化Actor
        self.actor_optimizer.zero_grad()
        actor_loss.backward()
        self.actor_optimizer.step()

        # 软更新目标网络
        with torch.no_grad():
            for param, target_param in zip(self.critic.parameters(), self.critic_target.parameters()):
                target_param.data.copy_(self.tau * param.data + (1 - self.tau) * target_param.data)

            for param, target_param in zip(self.actor.parameters(), self.actor_target.parameters()):
                target_param.data.copy_(self.tau * param.data + (1 - self.tau) * target_param.data)

        return [actor_loss.item() if actor_loss is not None else None, critic_loss.item() if critic_loss is not None else None]

    def save(self,filename):
        torch.save(self.critic.state_dict(), filename + "_critic")
        torch.save(self.critic_optimizer.state_dict(), filename + "_critic_optimizer")
        
        torch.save(self.actor.state_dict(), filename + "_actor")
        torch.save(self.actor_optimizer.state_dict(), filename + "_actor_optimizer")

    def load(self, filename):
        self.critic.load_state_dict(torch.load(filename + "_critic"))
        self.critic_optimizer.load_state_dict(torch.load(filename + "_critic_optimizer"))
        self.critic_target = copy.deepcopy(self.critic)

        self.actor.load_state_dict(torch.load(filename + "_actor"))
        self.actor_optimizer.load_state_dict(torch.load(filename + "_actor_optimizer"))
        self.actor_target = copy.deepcopy(self.actor)    


class State():
    def __init__(self, device, srs_aod=None, pmi_aod=None, srs_eod=None, pmi_eod=None, states=None):
        if states is not None:
            self.nUE = states[0].nUE
            self.srs_aod = torch.cat([state.srs_aod for state in states])
            self.pmi_aod = torch.cat([state.pmi_aod for state in states])
            self.srs_eod = torch.cat([state.srs_eod for state in states])
            self.pmi_eod = torch.cat([state.pmi_eod for state in states])
        else:
            self.flatten = torch.nn.Flatten()
            self.nUE = srs_aod.shape[0]
            self.srs_aod = self.flatten(torch.tensor(srs_aod, device=device, dtype=torch.float32))
            self.pmi_aod = self.flatten(torch.tensor(pmi_aod, device=device, dtype=torch.float32))
            self.srs_eod = self.flatten(torch.tensor(srs_eod, device=device, dtype=torch.float32))
            self.pmi_eod = self.flatten(torch.tensor(pmi_eod, device=device, dtype=torch.float32))

class ActorNet(torch.nn.Module):
    def __init__(self):
        super(ActorNet, self).__init__()        

        self.hlayer = nn.Linear(18, 16)
        
        self.vlayer = nn.Linear(18, 16)
        
        self.mix_layer = nn.Linear(16*2,16)

        self.mu_jointlayer = nn.Linear(16, 2)

        self.relu = nn.ReLU()
        self.tanh = nn.Tanh()
        self.softmax = nn.Softmax(dim=-1)
    
    def forward(self, state):
        h = self.hlayer(torch.cat((state.srs_aod, state.pmi_aod),dim=-1))
        h = self.relu(h)
        
        v = self.hlayer(torch.cat((state.srs_eod, state.pmi_eod),dim=-1))
        v = self.relu(v)
        
        mix = self.mix_layer(torch.cat((h, v),dim=-1))
        mix = self.relu(mix)
        
        action = self.mu_jointlayer(mix)
        action = self.tanh(action)
        
        return action
               
class CriticNet(torch.nn.Module):
    def __init__(self, state_dim, action_dim):
        super(CriticNet, self).__init__()        

        self.layer = nn.Sequential(
            nn.Linear(state_dim+action_dim,256),
            nn.ReLU(),
            nn.LayerNorm([256]),
            nn.Linear(256,64),
            nn.ReLU(),
            nn.LayerNorm([64]),
            nn.Linear(64,1)
        ) 
        
    def forward(self, state, action):
        output = torch.cat((state.srs_aod, state.pmi_aod,state.srs_eod, state.pmi_eod, action),dim = -1)
        output = self.layer(output)
        return output
    
def calscore(args, env, agent, batch, start_time, end_time, action_dim, ues):
    agent.eval()
    
    time_length = end_time - start_time
    reward = np.zeros((batch, time_length), dtype=np.float32)
    score = np.zeros((batch, time_length), dtype=np.float32)
    score_all = np.zeros((batch, time_length), dtype=np.float32)
    action = np.zeros((batch, action_dim), dtype=np.float32)
    aod = np.zeros((batch, time_length), dtype=np.float64)
    eod = np.zeros((batch, time_length), dtype=np.float64)
    aod_real = np.zeros((batch, time_length), dtype=np.float64)
    eod_real = np.zeros((batch, time_length), dtype=np.float64)
    for i in range(0, time_length):
        times = np.ones_like(ues)*(i+start_time)
        state = State(args.device, *env.get_state_method3(batch=batch,actions=action,ues=ues,times=times))
        action = agent.select_action(state)
        aod[:,i] = action[:,0]*env.hangle_bound
        eod[:,i] = action[:,1]*env.vangle_bound
        aod_real[:,i] = env.aod_real
        eod_real[:,i] = env.eod_real
        reward[:,i] = env.reward
        score[:,i] = env.score
        score_all[:,i] = env.score_all
        
    res = np.average(score[:,1:])
        
    agent.train()
    
    return reward,aod,eod,aod_real,eod_real,score,score_all,res

# 添加精度监控类
class AccuracyMonitor:
    def __init__(self, target_accuracy=0.9, window_size=10, patience=5):
        """
        初始化精度监控器
        
        参数:
            target_accuracy: 目标精度阈值 (0-1之间)
            window_size: 计算平均精度的窗口大小
            patience: 达到目标后需要维持的评估次数
        """
        self.target_accuracy = target_accuracy
        self.window_size = window_size
        self.patience = patience
        self.accuracies = []
        self.target_reached = False
        self.target_reached_frame = None
        self.target_reached_episode = None
        self.consecutive_target_count = 0
        
    def update(self, frame_idx, episode_idx, rewards, aod, eod, aod_real, eod_real, scores):
        """
        更新精度监控器
        
        参数:
            frame_idx: 当前训练帧数
            episode_idx: 当前训练回合数
            rewards, aod, eod, aod_real, eod_real, scores: 评估结果
            
        返回:
            当前精度
        """
        # 计算当前精度 (这里使用平均分数作为精度指标)
        current_accuracy = -np.average(scores)  # 负值转为正值
        
        # 归一化到0-1范围 (假设分数范围在-3到0之间)
        normalized_accuracy = min(max(current_accuracy / 3.0, 0), 1)
        
        # 添加到历史记录
        self.accuracies.append(normalized_accuracy)
        
        # 保持窗口大小
        if len(self.accuracies) > self.window_size:
            self.accuracies.pop(0)
        
        # 计算窗口内平均精度
        avg_accuracy = np.mean(self.accuracies)
        
        # 检查是否达到目标精度
        if avg_accuracy >= self.target_accuracy:
            if not self.target_reached:
                self.target_reached = True
                self.target_reached_frame = frame_idx
                self.target_reached_episode = episode_idx
            self.consecutive_target_count += 1
        else:
            self.consecutive_target_count = 0
            
        return normalized_accuracy
    
    def should_stop_training(self):
        """
        判断是否应该停止训练（达到目标后维持一定时间）
        """
        return (self.target_reached and 
                self.consecutive_target_count >= self.patience)
    
    def get_current_accuracy(self):
        """
        获取当前平均精度
        """
        return np.mean(self.accuracies) if self.accuracies else 0

def train(args, env, seed, modelpath, action_dim, state_dim, start_time, end_time):
    # DDPG agent
    agent = DDPG(device=args.device, 
            memory_size=args.memory_size,
            sample_size=args.sample_size, 
            discount=args.discount,
            tau = args.tau,
            pi_lr = args.pi_lr,
            q_lr = args.q_lr,
            action_dim=action_dim,
            state_dim=state_dim)
    
    # 初始化精度监控器
    accuracy_monitor = AccuracyMonitor(
        target_accuracy=args.target_accuracy,
        window_size=args.accuracy_window,
        patience=args.patience
    )
    
    # 记录
    args.daytime = datetime.datetime.now().strftime('%Y-%m-%d-%H-%M-%S')
    if not os.path.exists(modelpath):
        os.makedirs(modelpath)
    logging.basicConfig(format='%(asctime)s - %(levelname)s - %(message)s',
                    datefmt='%Y-%m-%d %H:%M:%S',
                    level=logging.INFO,
                    filename=f'{modelpath}/{args.daytime}.log',
                    filemode='w')
    logger = logging.getLogger(__name__)
    logger.info("parameters: %s", args)
    logger.info("path=%s,seed=%d,max_time=%d,max_length=%d,mu=%f,gNB_tti=%d,select_subs=%s,select_ports=%d,test_real=%d", env.path,env.seed,env.max_time,env.max_length,env.mu,env.gNB_tti,env.select_subs,env.select_ports,env.test_real)        
    # 训练参数
    max_score = -3
    if hasattr(args,"ue_pool"):
        ue_pool = args.ue_pool
    else:
        ue_pool = list(np.arange(args.train_nUE))
    batch = args.batch_size
    # 推理参数
    if hasattr(args,"global_batch"):
        global_batch = args.global_batch
    else:
        global_batch = env.nUE
    if hasattr(args,"global_ues"):
        global_ues = args.global_ues
    else:
        global_ues = list(np.arange(env.nUE))
    # 初始数据选择
    ptr = 0
    UE = ue_pool[ptr:min(ptr+batch,len(ue_pool))]
    times = np.ones_like(UE)*start_time
    ues = UE
    # 开始训练
    frame_idx = 0
    episode_idx = 0  # 添加回合计数
    # 初始化 state
    action = np.random.random((batch, action_dim))
    state = State(args.device, *env.get_state_method3(batch=batch,actions=action,ues=ues,times=times))
    
    results = dict()
    results['actor_loss'] = []
    results['critic_loss'] = []
    results['reward'] = []
    results['aod'] = []
    results['eod'] = []
    results['azimuths'] = []
    results['elevation'] = []
    results['svd_aod_er'] = []
    results['pmi_aod_er'] = []
    
    results['reward_infer'] = []
    results['aod_infer'] = []
    results['eod_infer'] = []
    results['score_infer'] = []
    results['azimuths_infer'] = []
    results['elevation_infer'] = []
    results['score_all_infer'] = []
    
    # 添加精度监控结果字段
    results['accuracies'] = []
    results['target_reached'] = False
    results['target_reached_frame'] = None
    results['target_reached_episode'] = None

    while frame_idx < args.num_frame:
        if frame_idx >= args.start_timesteps:
            # 在训练的后期，使用策略选择动作并加噪声
            action = agent.select_action(state)
            noise = np.random.normal(0, args.noise_std, size=action_dim).clip(-1, 1)
            action = action + noise
            action = torch.tensor(action, dtype=torch.float32)  # 确保动作是 float32 类型
            action = action.cpu().numpy()  # 转换为 NumPy 数组（如果需要）

        else:
            # 在训练初期，随机选择动作
            action = agent.select_action(state)
            action = torch.tensor(action, dtype=torch.float32)
            action = action.cpu().numpy()  
        next_state = State(args.device, *env.get_state_method3(batch=batch,actions=action,ues=ues,times=times))
        reward = env.reward
        # 将（St, At, Rt, St+1）放入回放池
        done = frame_idx % args.done_cycle == 0
        done = np.ones_like(ues)*done
        if frame_idx != 0:
            agent.memory_pool.add_memory(state, reward, action, next_state, done)
        # 更新状态
        state = next_state
        frame_idx += 1
        times = times + 1
        results['reward'].append(reward)
        results['aod'].append(action[:,0]*env.hangle_bound)
        results['eod'].append(action[:,1]*env.vangle_bound)
        results['azimuths'].append(env.aod_real)
        results['elevation'].append(env.eod_real)
        results['svd_aod_er'].append(env.svd_aod_er)
        results['pmi_aod_er'].append(env.pmi_aod_er)
        
        # 每optim_frame步优化
        if frame_idx % args.optim_frame == 0 and frame_idx != 0:
            actor_loss,critic_loss = agent.optim()
            results['actor_loss'].append(actor_loss)
            results['critic_loss'].append(critic_loss)
        
        # 每M步更新选择的UE,重新从start_time开始
        if frame_idx % args.change_ue_cycle == 0 and frame_idx != 0:
            times = np.ones_like(UE)*start_time
            ptr += batch
            if ptr+batch >= len(ue_pool):
                ue_pool.extend(ue_pool)
            UE = ue_pool[ptr:min(ptr+batch,len(ue_pool))]
            episode_idx += 1  # 更新回合计数
        
        # 每M步计算模型训练结果
        if frame_idx % args.train_log_cycle == 0 and frame_idx != 0:
            agent.eval()
            reward,aod,eod,aod_real,eod_real,score,score_all,res = calscore(args, env, agent, global_batch, start_time, end_time, action_dim, global_ues)
            results['reward_infer'].append(reward)
            results['aod_infer'].append(aod)
            results['eod_infer'].append(eod)
            results['azimuths_infer'].append(aod_real)
            results['elevation_infer'].append(eod_real)
            results['score_infer'].append(score)
            results['score_all_infer'].append(score_all)

            print('reward:', np.average(reward[:,1:],axis=-1))
            print("aod err:", np.average(np.abs(aod[:,1:]-aod_real[:,1:]),axis=-1))
            print("eod err:", np.average(np.abs(eod[:,1:]-eod_real[:,1:]),axis=-1))
            print('pos err:',np.average(score[:,1:],axis=-1))
            print('pos all err:',np.average(score_all[:,1:],axis=-1))
            print('averg aod eod pos pos_all err:', np.average(np.abs(aod[:,1:]-aod_real[:,1:])), np.average(np.abs(eod[:,1:]-eod_real[:,1:])), np.average(score[:,1:]), np.average(score_all[:,1:]))
            
            # 更新精度监控
            if frame_idx >= args.min_episodes_for_accuracy:
                current_accuracy = accuracy_monitor.update(
                    frame_idx, episode_idx,
                    reward[:, 1:],
                    aod[:, 1:], eod[:, 1:],
                    aod_real[:, 1:], eod_real[:, 1:],
                    score[:, 1:]
                )
                
                results['accuracies'].append(current_accuracy)
                print(f"Current Accuracy: {current_accuracy:.4f}, Target: {args.target_accuracy:.4f}")
                print(f"Consecutive target count: {accuracy_monitor.consecutive_target_count}/{args.patience}")
                
            if res > max_score:
                agent.save(modelpath+f'{args.daytime}_frame{frame_idx}_score{res}')
                max_score = res
                
            # 检查是否应该停止训练
            if accuracy_monitor.should_stop_training():
                print(f"\n✅ TRAINING COMPLETED!")
                print(f"   Target accuracy maintained for {accuracy_monitor.consecutive_target_count} evaluations")
                print(f"   Total frames: {frame_idx}")
                print(f"   Target reached at frame: {accuracy_monitor.target_reached_frame}")
                
                # 记录目标达成信息
                results['target_reached_info'] = {
                    'target_reached': True,
                    'frame': accuracy_monitor.target_reached_frame,
                    'episode': accuracy_monitor.target_reached_episode,
                    'total_frames': frame_idx,
                    'final_accuracy': accuracy_monitor.get_current_accuracy()
                }
                break
                
            agent.train()
    
    # 如果训练结束但未达到目标
    if not hasattr(accuracy_monitor, 'target_reached') or not accuracy_monitor.target_reached:
        results['target_reached_info'] = {
            'target_reached': False,
            'total_frames': frame_idx,
            'final_accuracy': accuracy_monitor.get_current_accuracy()
        }
        print(f"\n⚠️ TRAINING ENDED WITHOUT REACHING TARGET ACCURACY")
        print(f"   Final accuracy: {accuracy_monitor.get_current_accuracy():.4f}")
        print(f"   Target accuracy: {args.target_accuracy:.4f}")
    
    name = f'{modelpath}/{args.daytime}_results.pkl'
    with open(name, 'wb') as file:
        pickle.dump(results, file)
    
    return results

def infer(args, env, seed, modelpath, action_dim, state_dim, start_time, end_time):
    # DDPG agent
    agent = DDPG(device=args.device, 
            memory_size=args.memory_size,
            sample_size=args.sample_size, 
            discount=args.discount,
            tau = args.tau,
            pi_lr = args.pi_lr,
            q_lr = args.q_lr,
            action_dim=action_dim,
            state_dim=state_dim)
    
    # 加载模型
    agent.load(modelpath)
    agent.eval()
    
    # 推理参数
    if hasattr(args,"global_batch"):
        batch = args.global_batch
    else:
        batch = env.nUE
    if hasattr(args,"global_ues"):
        ues = args.global_ues
    else:
        ues = list(np.arange(env.nUE))
    
    # 初始化结果存储
    time_length = end_time - start_time
    reward = np.zeros((batch, time_length), dtype=np.float32)
    score = np.zeros((batch, time_length), dtype=np.float32)
    score_all = np.zeros((batch, time_length), dtype=np.float32)
    aod = np.zeros((batch, time_length), dtype=np.float64)
    eod = np.zeros((batch, time_length), dtype=np.float64)
    aod_real = np.zeros((batch, time_length), dtype=np.float64)
    eod_real = np.zeros((batch, time_length), dtype=np.float64)
    
    # 初始动作
    action = np.zeros((batch, action_dim), dtype=np.float32)
    
    # 推理循环
    for i in range(0, time_length):
        times = np.ones_like(ues)*(i+start_time)
        state = State(args.device, *env.get_state_method3(batch=batch, actions=action, ues=ues, times=times))
        action = agent.select_action(state)
        aod[:,i] = action[:,0]*env.hangle_bound
        eod[:,i] = action[:,1]*env.vangle_bound
        aod_real[:,i] = env.aod_real
        eod_real[:,i] = env.eod_real
        reward[:,i] = env.reward
        score[:,i] = env.score
        score_all[:,i] = env.score_all
    
    # 计算平均得分
    avg_score = np.average(score[:,1:])
    print(f"Average Score: {avg_score}")
    
    # 整理结果
    results = {
        'reward_infer': reward,
        'aod_infer': aod,
        'eod_infer': eod,
        'azimuths_infer': aod_real,
        'elevation_infer': eod_real,
        'score_infer': score,
        'score_all_infer': score_all,
        'avg_score': avg_score
    }
    
    return results

