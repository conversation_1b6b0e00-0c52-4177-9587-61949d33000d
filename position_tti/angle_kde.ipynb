{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from scipy.stats import gaussian_kde\n", "import os,sys\n", "sys.path.append(os.getcwd())\n", "from func.BM_functions import *\n", "from func.system_functions import *\n", "from func.process import *\n", "from func.setsumi_env import setsumi_env\n", "from utils import *\n", "import numpy as np\n", "import torch\n", "from train_td3 import seed_torch\n", "import pickle\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["setsumi load sim log OK\n", "Load begin\n", "setsumi load sim log OK\n", "723\n", "1000\n", "Using nSub=17 for time domain data\n", "open file OK\n", "load UE 0\n"]}, {"ename": "ValueError", "evalue": "cannot reshape array of size 12800 into shape (1,17,64,4,2)", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[2], line 7\u001b[0m\n\u001b[1;32m      5\u001b[0m seed \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m777\u001b[39m\n\u001b[1;32m      6\u001b[0m seed_torch(seed)\n\u001b[0;32m----> 7\u001b[0m env1 \u001b[38;5;241m=\u001b[39m \u001b[43msetsumi_env\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpath\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mpath1\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mseed\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mseed\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmax_length\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m100\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43mmu\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m0.9\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43mgNB_tti\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43mselect_subs\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43mselect_ports\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43mtest_real\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m)\u001b[49m  \n", "File \u001b[0;32m/home/<USER>/position_tti/func/setsumi_env.py:84\u001b[0m, in \u001b[0;36msetsumi_env.__init__\u001b[0;34m(self, logger, path, seed, max_time, max_length, mu, gNB_tti, select_subs, select_ports, test_real, use_pg_all)\u001b[0m\n\u001b[1;32m     81\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39melevation_range \u001b[38;5;241m=\u001b[39m (\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m90\u001b[39m, \u001b[38;5;241m90\u001b[39m)    \u001b[38;5;66;03m# 俯仰角范围\u001b[39;00m\n\u001b[1;32m     82\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mangle_resolution \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1.0\u001b[39m         \u001b[38;5;66;03m# 角度分辨率\u001b[39;00m\n\u001b[0;32m---> 84\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mload_H\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     85\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcal_aod_eod_dist_real()\n\u001b[1;32m     86\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcal_svd_angle()\n", "File \u001b[0;32m/home/<USER>/position_tti/func/setsumi_env.py:92\u001b[0m, in \u001b[0;36msetsumi_env.load_H\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m     90\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mlogger \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m:\n\u001b[1;32m     91\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mlogger(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mLoad begin\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m---> 92\u001b[0m \u001b[43mget_data\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msim_par\u001b[49m\u001b[43m,\u001b[49m\u001b[43mclear_time\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[1;32m     93\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mH_freq \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39marray(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msim_par\u001b[38;5;241m.\u001b[39mdata[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mH_freq\u001b[39m\u001b[38;5;124m'\u001b[39m],dtype\u001b[38;5;241m=\u001b[39mnp\u001b[38;5;241m.\u001b[39mcomplex64)\n\u001b[1;32m     94\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mH_pg \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39marray(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msim_par\u001b[38;5;241m.\u001b[39mdata[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mH_pg\u001b[39m\u001b[38;5;124m'\u001b[39m],dtype\u001b[38;5;241m=\u001b[39mnp\u001b[38;5;241m.\u001b[39mfloat64)\n", "File \u001b[0;32m/home/<USER>/position_tti/func/process.py:14\u001b[0m, in \u001b[0;36mget_data\u001b[0;34m(sim_par, clear_time)\u001b[0m\n\u001b[1;32m     12\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mget_data\u001b[39m(sim_par,clear_time\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m):\n\u001b[1;32m     13\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m os\u001b[38;5;241m.\u001b[39mpath\u001b[38;5;241m.\u001b[39mexists(sim_par\u001b[38;5;241m.\u001b[39mData_dir\u001b[38;5;241m+\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdata_redo.npz\u001b[39m\u001b[38;5;124m\"\u001b[39m):\n\u001b[0;32m---> 14\u001b[0m         \u001b[43mpre_process\u001b[49m\u001b[43m(\u001b[49m\u001b[43msim_par\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mData_dir\u001b[49m\u001b[43m,\u001b[49m\u001b[38;5;241;43m~\u001b[39;49m\u001b[43mclear_time\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     15\u001b[0m     data_redo \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39mload(sim_par\u001b[38;5;241m.\u001b[39mData_dir\u001b[38;5;241m+\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdata_redo.npz\u001b[39m\u001b[38;5;124m\"\u001b[39m,allow_pickle\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124marr_0\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m.\u001b[39mitem()\n\u001b[1;32m     16\u001b[0m     sim_par\u001b[38;5;241m.\u001b[39mdata \u001b[38;5;241m=\u001b[39m data_redo\n", "File \u001b[0;32m/home/<USER>/position_tti/utils/load_data.py:243\u001b[0m, in \u001b[0;36mpre_process\u001b[0;34m(Data_dir, save_time)\u001b[0m\n\u001b[1;32m    241\u001b[0m \u001b[38;5;28mprint\u001b[39m(sim_par\u001b[38;5;241m.\u001b[39mseed)\n\u001b[1;32m    242\u001b[0m \u001b[38;5;28mprint\u001b[39m(sim_par\u001b[38;5;241m.\u001b[39mnUE)\n\u001b[0;32m--> 243\u001b[0m data_time \u001b[38;5;241m=\u001b[39m \u001b[43mload_data\u001b[49m\u001b[43m(\u001b[49m\u001b[43mData_dir\u001b[49m\u001b[38;5;241;43m+\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mH_time.txt\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43msim_par\u001b[49m\u001b[43m,\u001b[49m\u001b[43mfreq\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[1;32m    244\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mload time OK\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    245\u001b[0m data_freq \u001b[38;5;241m=\u001b[39m load_data(Data_dir\u001b[38;5;241m+\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mH_freq.txt\u001b[39m\u001b[38;5;124m\"\u001b[39m,sim_par)\n", "File \u001b[0;32m/home/<USER>/position_tti/utils/load_data.py:176\u001b[0m, in \u001b[0;36mload_data\u001b[0;34m(path, sim_par, freq)\u001b[0m\n\u001b[1;32m    174\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mload UE \u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m+\u001b[39m\u001b[38;5;28mstr\u001b[39m(i))\n\u001b[1;32m    175\u001b[0m data \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39marray(target_data,dtype\u001b[38;5;241m=\u001b[39mnp\u001b[38;5;241m.\u001b[39mfloat32)\n\u001b[0;32m--> 176\u001b[0m data \u001b[38;5;241m=\u001b[39m \u001b[43mdata\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mreshape\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtime\u001b[49m\u001b[43m,\u001b[49m\u001b[43mnSub\u001b[49m\u001b[43m,\u001b[49m\u001b[43mAnts_all_2pol\u001b[49m\u001b[43m,\u001b[49m\u001b[43mPort\u001b[49m\u001b[43m,\u001b[49m\u001b[38;5;241;43m2\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m    177\u001b[0m data \u001b[38;5;241m=\u001b[39m data[:,:,:,:,\u001b[38;5;241m0\u001b[39m] \u001b[38;5;241m+\u001b[39m \u001b[38;5;241m1\u001b[39mj\u001b[38;5;241m*\u001b[39mdata[:,:,:,:,\u001b[38;5;241m1\u001b[39m]\n\u001b[1;32m    178\u001b[0m data \u001b[38;5;241m=\u001b[39m data\u001b[38;5;241m.\u001b[39mreshape(time,nSub,H_ants,V_ants,\u001b[38;5;241m2\u001b[39m,Port)\n", "\u001b[0;31mValueError\u001b[0m: cannot reshape array of size 12800 into shape (1,17,64,4,2)"]}], "source": ["path1 = os.getcwd()\n", "\n", "path1 = '/home/<USER>/quadriga/ne_v0/'\n", "\n", "seed = 777\n", "seed_torch(seed)\n", "env1 = setsumi_env(path=path1, seed=seed, max_length=100,mu=0.9,gNB_tti=1,select_subs=1,select_ports=1,test_real=False)  "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "10 100 3\n", "expand H: (10, 100, 17, 4, 64)\n", "expand ue loc: (10, 100, 3)\n", "expand rsrp: (10, 100, 3)\n", "srs_choose_all:(10, 100, 17, 4)\n", "simulate srs:(10, 100, 17, 4, 64)\n", "simulate coma:(10, 100, 1, 32, 32)\n", "simulate rsrp:(10, 100, 3)\n", "end load_H\n", "setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "10 100 3\n", "expand H: (10, 100, 17, 4, 64)\n", "expand ue loc: (10, 100, 3)\n", "expand rsrp: (10, 100, 3)\n", "srs_choose_all:(10, 100, 17, 4)\n", "simulate srs:(10, 100, 17, 4, 64)\n", "simulate coma:(10, 100, 1, 32, 32)\n", "simulate rsrp:(10, 100, 3)\n", "end load_H\n"]}], "source": ["path1 = os.getcwd()\n", "\n", "path1 = '/home/<USER>/quadriga/ne_v0'\n", "\n", "seed = 777\n", "seed_torch(seed)\n", "env1 = setsumi_env(path=path1, seed=seed, max_length=100,mu=0.9,gNB_tti=1,select_subs=1,select_ports=1,test_real=False)  \n", "\n", "path2 = os.getcwd()\n", "\n", "path2 = './H_data/nlos_3/'\n", "\n", "seed = 777\n", "seed_torch(seed)\n", "env2 = setsumi_env(path=path2, seed=seed, max_length=100,mu=0.9,gNB_tti=1,select_subs=1,select_ports=1,test_real=False)  "]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["NLOS_angles = (env1.azimuths)\n", "LOS_angles =(env2.azimuths)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[10.64648604 10.62990269 10.61363934 10.59681068 10.5802285  10.56396595\n", " 10.54713831 10.53055796 10.51429686 10.49747088 10.48089299 10.46463396\n", " 10.4478103  10.4312355  10.41497919 10.39815847 10.3815874  10.36533443\n", " 10.3485173  10.33219628 10.31570157 10.29888865 10.28257248 10.26608249\n", " 10.24927441 10.2329637  10.21647905 10.19967643 10.18337179 10.16689311\n", " 10.15009657 10.13379862 10.1173265  10.10053666 10.08424599 10.06778105\n", " 10.05099852 10.03471575 10.01825859 10.00148397  9.98520969  9.9687609\n", "  9.9519948   9.93572962  9.9192898   9.90253281  9.8862773   9.86984704\n", "  9.85309977  9.83685452  9.82043441  9.80369744  9.78746304  9.77105365\n", "  9.75432757  9.73810459  9.72170651  9.70499189  9.6887809   9.6723947\n", "  9.65569213  9.63949371  9.62279951  9.60642999  9.5902447   9.5735635\n", "  9.55720718  9.54103557  9.52436793  9.50802537  9.49186801  9.47521449\n", "  9.45888623  9.44274366  9.42610482  9.40979142  9.39366419  9.37704057\n", "  9.36074258  9.34463123  9.32802338  9.31174133  9.2956464   9.27905487\n", "  9.26278929  9.2467113   9.23013662  9.21388806  9.19782754  9.18127025\n", "  9.16503921  9.14899669  9.13245731  9.11624432  9.10022031  9.08369936\n", "  9.06774652  9.05149996  9.03499796  9.01906402 48.53363097 48.5250327\n", " 48.51642603 48.50765916 48.49895071 48.49023467 48.48114951 48.47254093\n", " 48.46371647 48.45452291 48.44580621 48.43687427 48.42784525 48.41874937\n", " 48.40971089 48.40057459 48.39137324 48.38222916 48.37298653 48.36368063\n", " 48.35443188 48.34508385 48.33567434 48.32632185 48.31686936 48.30735715\n", " 48.29790184 48.28834582 48.27873185 48.26917464 48.259516   48.24980118\n", " 48.24014299 48.23038267 48.22056791 48.21080964 48.20094855 48.19103476\n", " 48.18117733 48.17121639 48.16120447 48.15124878 48.14118888 48.13107975\n", " 48.12102669 48.11086876 48.10066329 48.09051376 48.08025869 48.06995779\n", " 48.05971269 48.04936136 48.03896592 48.02862612 48.01817945 48.00769035\n", " 47.99725674 47.9867156  47.97613372 47.96560718 47.95497246 47.94429867\n", " 47.93368008 47.92295266 47.91218784 47.90147806 47.89065882 47.87980383\n", " 47.86900373 47.85809354 47.84714924 47.83625969 47.82525941 47.81422668\n", " 47.80324852 47.79215902 47.7810387  47.7699728  47.75879494 47.74758787\n", " 47.73643508 47.72516971 47.71387676 47.70242573 47.69128589 47.67990789\n", " 47.66837156 47.657146   47.64568379 47.634063   47.62275256 47.61120698\n", " 47.59975511 47.58810808 47.57647996 47.56494459 47.55321506 47.54150521\n", " 47.52988718 47.51807596  1.23881362  1.24537671  1.25150263  1.2582194\n", "  1.26473055  1.27080479  1.27746897  1.28392855  1.29022572  1.29656346\n", "  1.30297182  1.30921731  1.31550396  1.32186149  1.32805565  1.3342916\n", "  1.34059865  1.34674186  1.35292746  1.35897525  1.36527702  1.37141264\n", "  1.37741111  1.38366223  1.38974822  1.39569773  1.40189855  1.40793528\n", "  1.41383618  1.41998708  1.42597489  1.43182754  1.43792887  1.44386812\n", "  1.44967287  1.45572499  1.46161603  1.46737322  1.47337648  1.47921967\n", "  1.48492964  1.49088441  1.49668008  1.50234319  1.5082498   1.51399831\n", "  1.51961489  1.5254737   1.53117539  1.53674578  1.54255713  1.54821233\n", "  1.55373687  1.55950111  1.56511017  1.5705892   1.57630667  1.58186992\n", "  1.58730376  1.5929748   1.59849257  1.60388157  1.60950651  1.61497914\n", "  1.62032361  1.6259028   1.63133062  1.63663089  1.64216466  1.64754799\n", "  1.65280439  1.65829306  1.66363223  1.66884508  1.67428899  1.67958432\n", "  1.68475395  1.69015342  1.69540523  1.70053195  1.7058873   1.71109592\n", "  1.71618004  1.72149161  1.72665734  1.73169918  1.73696728  1.74209045\n", "  1.74734507  1.75231527  1.75739618  1.76260845  1.76753651  1.77257548\n", "  1.77774571  1.78263194  1.78743094  1.79275777  1.79760248  1.80236056\n", "  0.85928351  0.87473447  0.89059203  0.90625692  0.92194456  0.93777728\n", "  0.95341911  0.96908261  0.98488972  1.00050772  1.01614633  1.03192708\n", "  1.04752049  1.06313345  1.07888708  1.09445516  1.11004172  1.12576749\n", "  1.14130947  1.15686889  1.17256605  1.1880812   1.20361274  1.21928055\n", "  1.23476812  1.25027105  1.26590878  1.28136804  1.29684162  1.31244854\n", "  1.32787877  1.34332228  1.35889766  1.37429812  1.38971085  1.40525399\n", "  1.42062396  1.43600519  1.45151536  1.46685414  1.48220316  1.49767966\n", "  1.51298654  1.52830264  1.54374478  1.55901904  1.57430154  1.58970861\n", "  1.60494957  1.62019778  1.63556909  1.65077606  1.66598928  1.68132416\n", "  1.69649644  1.711674    1.72732672  1.74210869  1.75724992  1.77286509\n", "  1.78761079  1.80271502  1.81829196  1.83300075  1.84806732  1.86360536\n", "  1.87827657  1.89330483  1.90880332  1.92343631  1.93842561  1.95388389\n", "  1.96847802  1.98342772  1.99884515  2.01339979  2.02830925  2.04368519\n", "  2.05819971  2.0730683   2.0881369   2.1028759   2.11770301  2.13272871\n", "  2.14742651  2.16221152  2.17719371  2.19184968  2.20659199  2.22153005\n", "  2.23614362  2.25084262  2.26573596  2.2803065   2.29496161  2.30980963\n", "  2.32433657  2.33894719  2.3537493   2.36823207  1.55458182  1.56783141\n", "  1.58079795  1.59346135  1.60665281  1.61956001  1.63216509  1.64529826\n", "  1.65814597  1.67069258  1.68376731  1.69655538  1.70904339  1.72205954\n", "  1.73478783  1.74721709  1.76017452  1.77284291  1.78521329  1.79811186\n", "  1.81072022  1.8230316   1.83587118  1.8484194   1.86067166  1.87345212\n", "  1.88594009  1.89813312  1.91085434  1.92328194  1.93541563  1.94807751\n", "  1.96044464  1.97251889  1.98512132  1.99742789  2.0094426   2.02198547\n", "  2.03423138  2.04618647  2.05866968  2.07085486  2.08275024  2.09517371\n", "  2.10729807  2.11913365  2.13149729  2.14356076  2.15533647  2.16764021\n", "  2.17964272  2.19135849  2.20360224  2.21554373  2.2271995   2.2393832\n", "  2.2512636   2.26285931  2.2749829   2.28680217  2.29833776  2.31040117\n", "  2.32215925  2.33396256  2.34563786  2.35733472  2.36907744  2.38069285\n", "  2.39232844  2.40401053  2.41556599  2.4268972   2.43876171  2.45025721\n", "  2.46152725  2.4733309   2.48476639  2.49597526  2.50771801  2.51909346\n", "  2.53024115  2.54192297  2.55323837  2.56432485  2.57594573  2.58720106\n", "  2.59822634  2.60978625  2.6209815   2.63194557  2.64344451  2.65457968\n", "  2.66548253  2.6769205   2.68799558  2.69883723  2.71021422  2.72122921\n", "  2.73200967  2.74332569 -0.81697955 -0.82894028 -0.84112901 -0.85325824\n", " -0.86511565 -0.87720093 -0.88922772 -0.90098264 -0.91296528 -0.92489044\n", " -0.93686682 -0.94842449 -0.96024883 -0.97212327 -0.98358099 -0.99530531\n", " -1.00707862 -1.01843719 -1.03006227 -1.04173525 -1.05299546 -1.06452211\n", " -1.07609555 -1.08725818 -1.09868717 -1.11016187 -1.12122771 -1.13255982\n", " -1.14393656 -1.15490638 -1.16614238 -1.17742195 -1.18829651 -1.19943717\n", " -1.21062035 -1.22140042 -1.2324465  -1.24353404 -1.25422038 -1.26517264\n", " -1.27616532 -1.28675868 -1.29761786 -1.30851644 -1.31901757 -1.32978442\n", " -1.34058964 -1.35099928 -1.36167455 -1.37238717 -1.38270606 -1.39329047\n", " -1.40391123 -1.41414009 -1.42463438 -1.43516402 -1.44530358 -1.45570847\n", " -1.46614772 -1.47619871 -1.48651492 -1.49666288 -1.50682763 -1.51705587\n", " -1.52711531 -1.53719249 -1.54733347 -1.5573051  -1.56729541 -1.57734984\n", " -1.58723438 -1.59713852 -1.60710709 -1.61690524 -1.62672391 -1.63660731\n", " -1.64631977 -1.65605366 -1.66585258 -1.67548004 -1.68512984 -1.69484497\n", " -1.70438811 -1.7139545  -1.72358651 -1.73304602 -1.74252967 -1.75207923\n", " -1.7614558  -1.77085738 -1.78062113 -1.78961946 -1.79893963 -1.80862131\n", " -1.81753899 -1.82677841 -1.83637868 -1.84521637 -1.8543757  -1.86389521\n", " 10.15902354 10.14535209 10.13158109 10.11760652 10.10399316 10.09028075\n", " 10.0763658  10.06281038 10.04915639 10.03530091 10.02180326 10.00820754\n", "  9.99441134  9.98097131  9.96743369  9.95369663  9.94031405  9.92683437\n", "  9.91315625  9.89983096  9.88640905  9.87278972  9.85952156  9.84615725\n", "  9.83259654  9.81938533  9.80607846  9.79257618  9.77942176  9.76617216\n", "  9.75272813  9.73963034  9.72643783  9.71305189  9.70001056  9.68687496\n", "  9.67354693  9.66056187  9.64748302  9.63421272  9.62128377  9.60826149\n", "  9.59504874  9.58217573  9.56920984  9.55605445  9.5432372   9.53032753\n", "  9.51722932  9.50446766  9.49161402  9.47857281  9.46586656  9.45306878\n", "  9.44008439  9.42743336  9.41469126  9.4017635   9.38916753  9.37648092\n", "  9.3636096   9.3510685   9.33843721  9.32582328  9.31313573  9.30055957\n", "  9.28800129  9.27536867  9.26284746  9.25034464  9.23776677  9.22530032\n", "  9.21285276  9.20032945  9.18764017  9.1755251   9.16305617  9.15042192\n", "  9.1383611   9.12594636  9.11336695  9.10136019  9.08899946  9.07647469\n", "  9.0645218   9.05221489  9.03974459  9.02784538  9.0155921   9.00317607\n", "  8.99133034  8.9791305   8.96676855  8.95497612  8.94282954  8.93052147\n", "  8.91878215  8.90668863  8.89443426  8.88274784 31.99869879 31.98651035\n", " 31.97420743 31.96236    31.95014405 31.93781401 31.92593926 31.91369634\n", " 31.90133976 31.88943823 31.87716891 31.86478633 31.85285859 31.84032587\n", " 31.82815537 31.81620196 31.80364414 31.79144853 31.77946998 31.7668876\n", " 31.75466741 31.74266427 31.73005787 31.71781362 31.70578642 31.69315653\n", " 31.68088876 31.66883801 31.65618516 31.64389439 31.63182063 31.61914533\n", " 31.60683207 31.59473581 31.58203859 31.56970336 31.55758511 31.54486647\n", " 31.53250978 31.52037004 31.5076305  31.49525284 31.48309213 31.47033218\n", " 31.45793406 31.44575286 31.43297301 31.42078325 31.40835373 31.39555446\n", " 31.38334461 31.3708962  31.35807799 31.34584855 31.33338172 31.32054507\n", " 31.3082965  31.29581174 31.28295713 31.27068991 31.25818768 31.24531558\n", " 31.23303019 31.22051096 31.20762184 31.19531875 31.18278298 31.1698773\n", " 31.15755697 31.14500513 31.13208335 31.11974624 31.10717878 31.09424136\n", " 31.08188792 31.06930528 31.05635268 31.04398336 31.03138599 31.01841864\n", " 31.0060339  30.99342224 30.98044059 30.96804086 30.95541534 30.94241984\n", " 30.93000555 30.91736661 30.90435768 30.89192928 30.87904558 30.86625542\n", " 30.85381332 30.8409173  30.82811432 30.81565896 30.80275102 30.78993566\n", " 30.77746745 30.76454801 -0.27599728 -0.27632472 -0.27597974 -0.27597079\n", " -0.2762971  -0.27595151 -0.2759417  -0.27626688 -0.27592069 -0.27591002\n", " -0.27623407 -0.27588728 -0.27587575 -0.27619866 -0.27585128 -0.27583888\n", " -0.27616065 -0.27581269 -0.27602182 -0.27612006 -0.27577151 -0.27598\n", " -0.27607689 -0.27572775 -0.2759356  -0.27580875 -0.27568142 -0.27588862\n", " -0.2757602  -0.2756325  -0.27583906 -0.27570907 -0.27558102 -0.27578692\n", " -0.27565538 -0.27552697 -0.27573222 -0.27559911 -0.27547035 -0.27567495\n", " -0.27554029 -0.27541118 -0.27561512 -0.2754789  -0.27534944 -0.27555274\n", " -0.27541497 -0.27528516 -0.2754878  -0.27534848 -0.27521832 -0.27542031\n", " -0.27527944 -0.27514894 -0.27535027 -0.27520786 -0.27507703 -0.2752777\n", " -0.27513375 -0.27500258 -0.27520259 -0.27505711 -0.27492559 -0.27512496\n", " -0.27497793 -0.27484609 -0.27504479 -0.27489624 -0.27476406 -0.27496211\n", " -0.27481203 -0.27467953 -0.27487691 -0.27472531 -0.27459248 -0.2747892\n", " -0.27463608 -0.27450293 -0.27469899 -0.27454436 -0.27441088 -0.27460628\n", " -0.27445013 -0.27431634 -0.27451108 -0.27435342 -0.27421931 -0.27441339\n", " -0.27425423 -0.2741198  -0.27431323 -0.27415256 -0.27401782 -0.27421059\n", " -0.27404843 -0.27414055 -0.27410548 -0.27394182 -0.2740338  -0.27399791\n", " 16.08635336 16.09834256 16.11040026 16.12291363 16.13482097 16.14679736\n", " 16.15922798 16.1710533  16.18294823 16.19529594 16.20703908 16.21885239\n", " 16.23111703 16.24277784 16.25450939 16.2666908  16.27826915 16.28991878\n", " 16.30201684 16.31351257 16.32508016 16.33709472 16.34850772 16.36030383\n", " 16.37192406 16.3832542  16.39496787 16.40650449 16.41775164 16.42938276\n", " 16.44083564 16.4519997  16.46354814 16.47491719 16.48599804 16.49746371\n", " 16.50874881 16.51974636 16.53112914 16.54212519 16.55324435 16.56454415\n", " 16.5754559  16.58649175 16.59770848 16.60853584 16.61948829 16.63062186\n", " 16.64136475 16.65223374 16.66328406 16.67394242 16.68472786 16.69569487\n", " 16.70626863 16.71697046 16.72785408 16.73834318 16.74896134 16.75976151\n", " 16.77016591 16.78070033 16.79141701 16.80173664 16.81218729 16.82282041\n", " 16.83305524 16.84342207 16.8539716  16.86412159 16.87440456 16.88487045\n", " 16.89493558 16.90513465 16.91551687 16.92549711 16.93561227 16.94591078\n", " 16.95580611 16.96583733 16.97605212 16.98586253 16.9958098  17.00594085\n", " 17.01566633 17.02552963 17.03557692 17.04521748 17.0549968  17.06496034\n", " 17.07451597 17.08421132 17.09409111 17.10356181 17.11317321 17.12296924\n", " 17.13235504 17.14188248 17.15159476 17.16089568]\n"]}], "source": ["# 计算 NLOS 相对 LOS 的角度偏移\n", "angle_offset = (NLOS_angles - LOS_angles).flatten()\n", "print(angle_offset)\n", "angle_offset_normalized = (angle_offset - np.min(angle_offset)) / (np.max(angle_offset) - np.min(angle_offset))\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["平均值: 11.910163439261806\n", "方差: 235.2373320000458\n"]}], "source": ["import numpy as np\n", "\n", "# 假设 NLOS_speed0_angles 和 LOS_angles 是已经定义好的 NumPy 数组\n", "# angle_offset = (NLOS_speed0_angles - LOS_angles).flatten()\n", "\n", "# 计算平均值\n", "mean_angle_offset = np.mean(angle_offset)\n", "\n", "# 计算方差\n", "variance_angle_offset = np.var(angle_offset)\n", "\n", "# 打印结果\n", "print(\"平均值:\", mean_angle_offset)\n", "print(\"方差:\", variance_angle_offset)"]}], "metadata": {"kernelspec": {"display_name": "meiziy", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 2}