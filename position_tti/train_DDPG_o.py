import os,sys
import pickle
sys.path.append(os.getcwd())
from func.BM_functions import *
from func.system_functions import *
from func.process import *
from func.setsumi_env import setsumi_env
from utils import *

import datetime
import logging
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import os
import random
import torch
import numpy as np
import torch.nn as nn
import torch.nn.functional as F
import argparse
from collections import deque

def seed_torch(seed):
    np.random.seed(seed)
    random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
    if torch.cuda.is_available():
        torch.backends.cudnn.enabled = True
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False
    torch.use_deterministic_algorithms(True, warn_only=True)
    os.environ['CUBLAS_WORKSPACE_CONFIG']=':4096:8' 
       
def parse_args(args=None):
    # ++ Basic Configs ++
    parser = argparse.ArgumentParser(description="BM")
    parser.add_argument("--desc", type=str, default="No desc", help="train desc")
    parser.add_argument("--seed", type=int, default=777, help="random seed")
    parser.add_argument("--device", type=str, default="cuda:0", help="train on device")
    
    # ++ Train Configs ++
    parser.add_argument("--memory_size", type=int, default=500000, help="DQN replay memory size")
    parser.add_argument("--train_nUE", type=int, default=20, help="train ue num")
    parser.add_argument("--batch_size", type=int, default=20, help="batch size")
    parser.add_argument("--optim_frame", type=int, default=1, help="update one time every N frame")
    parser.add_argument("--change_ue_cycle", type=int, default=100, help="change_ue_cycle")
    parser.add_argument("--done_cycle", type=int, default=100, help="done cycle")
    parser.add_argument("--num_frame", type=int, default=1000000, help="how many times agent train")
    parser.add_argument("--train_log_cycle", type=int, default=100, help="every N step log")

    # ++ DDPG Configs ++
    parser.add_argument("--discount", default=0.99)  
    parser.add_argument("--pi_lr", default=1e-3)   
    parser.add_argument("--q_lr", default=1e-3)                  
    parser.add_argument("--tau", default=0.005)                    
    parser.add_argument("--noise_std", default=0.1)
    parser.add_argument("--sample_size", type=int, default=20, help="sample size")
    
    if args is None:
        # 从命令行读取参数
        parsed_args = parser.parse_args()
    else:
        # 使用 parse_known_args 传递参数列表
        parsed_args, _ = parser.parse_known_args(args)
    
    return parsed_args

class ReplayMemoryPool():
    def __init__(self, size=10000):
        self.size = size # Memory size
        self.memories = deque(maxlen=self.size)
    def add_memory(self, state, reward, action, next_state, done):
        self.memories.append([state, reward, action, next_state, done])
    def get_batch(self, batch_size):
        choosen_index = np.random.choice(np.arange(0,len(self.memories)), batch_size, replace=False)
        return [self.memories[i] for i in choosen_index]
    def get_all_batch(self):
        choosen_index = np.arange(0,len(self.memories))
        return [self.memories[i] for i in choosen_index]
    def clear(self):
        self.memories.clear()

class Actor(nn.Module):
    def __init__(self,state_dim):
        super(Actor, self).__init__()
        self.net = ActorNet()

    def forward(self, state):
        action = self.net(state)
        return action

class Critic(nn.Module):
    def __init__(self,state_dim,action_dim):
        super(Critic,self).__init__()
        self.Q = CriticNet(state_dim, action_dim)

    def forward(self,state,action):
        q = self.Q(state,action)
        return q

class DDPG(object):
    def __init__(
        self,
        device,
        state_dim=71,
        action_dim=3,
        discount = 0.98,
        tau = 0.005,
        memory_size = 10000,
        sample_size = 100,
        pi_lr = 1e-3,
        q_lr = 1e-3
      
):      
        self.device = device
        self.state_dim = state_dim
        self.action_dim = action_dim
        # Actor Init 
        self.actor = Actor(state_dim=state_dim).to(device)
        self.actor_target = copy.deepcopy(self.actor)
        self.actor_optimizer = torch.optim.Adam(self.actor.parameters(),lr = pi_lr)
        # Critic Init
        self.critic = Critic(state_dim=state_dim,action_dim=action_dim).to(device)
        self.critic_target = copy.deepcopy(self.critic)
        self.critic_optimizer = torch.optim.Adam(self.critic.parameters(),lr = q_lr)
        # Hyper Parameters
        self.discount = discount
        self.tau = tau
        self.sample_size = sample_size
        self.device = device
        self.total_it = 0
        # memory
        self.memory_pool = ReplayMemoryPool(size=memory_size)
        

    def train(self):
        self.actor.train()

    def eval(self):
        self.actor.eval()

    def select_action(self,state) :
        action = self.actor(state)
        action = action.detach().cpu().numpy()
    
        return action

    def optim(self):
        # Initialize losses with None
        actor_loss = None
        critic_loss = None
        if len(self.memory_pool.memories) < self.sample_size:
            return [None, None]
        
        self.total_it +=1
        
        # Sample
        batch = self.memory_pool.get_batch(self.sample_size)
        state = State(self.device, states=[batch[i][0] for i in range(self.sample_size)])
        reward = torch.tensor(np.array([batch[i][1] for i in range(self.sample_size)]), device=self.device).view(-1,1)
        action = torch.tensor(np.array([batch[i][2] for i in range(self.sample_size)]), device=self.device).view(-1,self.action_dim)
        next_state = State(self.device, states=[batch[i][3] for i in range(self.sample_size)])
        done = torch.tensor(np.array([1-batch[i][4] for i in range(self.sample_size)]), device=self.device).view(-1,1)

        with torch.no_grad():
            # 计算目标Q值
            next_action = self.actor_target(next_state)
            target_Q = self.critic_target(next_state, next_action)
            target_Q = reward + (1.0 - done) * self.discount * target_Q
            target_Q = target_Q.type(torch.float32)

        # 计算当前Q值
        current_Q = self.critic(state, action)
        
        # 计算Critic损失
        critic_loss = F.mse_loss(current_Q, target_Q)

        # 优化Critic
        self.critic_optimizer.zero_grad()
        critic_loss.backward()
        self.critic_optimizer.step()    

        # 计算Actor损失
        actor_loss = -self.critic(state, self.actor(state)).mean()

        # 优化Actor
        self.actor_optimizer.zero_grad()
        actor_loss.backward()
        self.actor_optimizer.step()

        # 软更新目标网络
        with torch.no_grad():
            for param, target_param in zip(self.critic.parameters(), self.critic_target.parameters()):
                target_param.data.copy_(self.tau * param.data + (1 - self.tau) * target_param.data)

            for param, target_param in zip(self.actor.parameters(), self.actor_target.parameters()):
                target_param.data.copy_(self.tau * param.data + (1 - self.tau) * target_param.data)

        return [actor_loss.item() if actor_loss is not None else None, critic_loss.item() if critic_loss is not None else None]

    def save(self,filename):
        torch.save(self.critic.state_dict(), filename + "_critic")
        torch.save(self.critic_optimizer.state_dict(), filename + "_critic_optimizer")
        
        torch.save(self.actor.state_dict(), filename + "_actor")
        torch.save(self.actor_optimizer.state_dict(), filename + "_actor_optimizer")

    def load(self, filename):
        self.critic.load_state_dict(torch.load(filename + "_critic"))
        self.critic_optimizer.load_state_dict(torch.load(filename + "_critic_optimizer"))
        self.critic_target = copy.deepcopy(self.critic)

        self.actor.load_state_dict(torch.load(filename + "_actor"))
        self.actor_optimizer.load_state_dict(torch.load(filename + "_actor_optimizer"))
        self.actor_target = copy.deepcopy(self.actor)    


class State():
    def __init__(self, device, srs_aod=None, pmi_aod=None, srs_eod=None, pmi_eod=None, states=None):
        if states is not None:
            self.nUE = states[0].nUE
            self.srs_aod = torch.cat([state.srs_aod for state in states])
            self.pmi_aod = torch.cat([state.pmi_aod for state in states])
            self.srs_eod = torch.cat([state.srs_eod for state in states])
            self.pmi_eod = torch.cat([state.pmi_eod for state in states])
        else:
            self.flatten = torch.nn.Flatten()
            self.nUE = srs_aod.shape[0]
            self.srs_aod = self.flatten(torch.tensor(srs_aod, device=device, dtype=torch.float32))
            self.pmi_aod = self.flatten(torch.tensor(pmi_aod, device=device, dtype=torch.float32))
            self.srs_eod = self.flatten(torch.tensor(srs_eod, device=device, dtype=torch.float32))
            self.pmi_eod = self.flatten(torch.tensor(pmi_eod, device=device, dtype=torch.float32))

class ActorNet(torch.nn.Module):
    def __init__(self):
        super(ActorNet, self).__init__()        

        self.hlayer = nn.Linear(18, 16)
        
        self.vlayer = nn.Linear(18, 16)
        
        self.mix_layer = nn.Linear(16*2,16)

        self.mu_jointlayer = nn.Linear(16, 2)

        self.relu = nn.ReLU()
        self.tanh = nn.Tanh()
        self.softmax = nn.Softmax(dim=-1)
    
    def forward(self, state):
        h = self.hlayer(torch.cat((state.srs_aod, state.pmi_aod),dim=-1))
        h = self.relu(h)
        
        v = self.hlayer(torch.cat((state.srs_eod, state.pmi_eod),dim=-1))
        v = self.relu(v)
        
        mix = self.mix_layer(torch.cat((h, v),dim=-1))
        mix = self.relu(mix)
        
        action = self.mu_jointlayer(mix)
        action = self.tanh(action)
        
        return action
               
class CriticNet(torch.nn.Module):
    def __init__(self, state_dim, action_dim):
        super(CriticNet, self).__init__()        

        self.layer = nn.Sequential(
            nn.Linear(state_dim+action_dim,256),
            nn.ReLU(),
            nn.LayerNorm([256]),
            nn.Linear(256,64),
            nn.ReLU(),
            nn.LayerNorm([64]),
            nn.Linear(64,1)
        ) 
        
    def forward(self, state, action):
        output = torch.cat((state.srs_aod, state.pmi_aod,state.srs_eod, state.pmi_eod, action),dim = -1)
        output = self.layer(output)
        return output
    
def calscore(args, env, agent, batch, start_time, end_time, action_dim, ues):
    agent.eval()
    
    time_length = end_time - start_time
    reward = np.zeros((batch, time_length), dtype=np.float32)
    score = np.zeros((batch, time_length), dtype=np.float32)
    score_all = np.zeros((batch, time_length), dtype=np.float32)
    action = np.zeros((batch, action_dim), dtype=np.float32)
    aod = np.zeros((batch, time_length), dtype=np.float64)
    eod = np.zeros((batch, time_length), dtype=np.float64)
    aod_real = np.zeros((batch, time_length), dtype=np.float64)
    eod_real = np.zeros((batch, time_length), dtype=np.float64)
    for i in range(0, time_length):
        times = np.ones_like(ues)*(i+start_time)
        state = State(args.device, *env.get_state_method3(batch=batch,actions=action,ues=ues,times=times))
        action = agent.select_action(state)
        aod[:,i] = action[:,0]*env.hangle_bound
        eod[:,i] = action[:,1]*env.vangle_bound
        aod_real[:,i] = env.aod_real
        eod_real[:,i] = env.eod_real
        reward[:,i] = env.reward
        score[:,i] = env.score
        score_all[:,i] = env.score_all
        
    res = np.average(score[:,1:])
        
    agent.train()
    
    return reward,aod,eod,aod_real,eod_real,score,score_all,res

def train(args, env, seed, modelpath, action_dim, state_dim, start_time, end_time):
    # DDPG agent
    agent = DDPG(device=args.device, 
            memory_size=args.memory_size,
            sample_size=args.sample_size, 
            discount=args.discount,
            tau = args.tau,
            pi_lr = args.pi_lr,
            q_lr = args.q_lr,
            action_dim=action_dim,
            state_dim=state_dim)
    
    # 记录
    args.daytime = datetime.datetime.now().strftime('%Y-%m-%d-%H-%M-%S')
    if not os.path.exists(modelpath):
        os.makedirs(modelpath)
    logging.basicConfig(format='%(asctime)s - %(levelname)s - %(message)s',
                    datefmt='%Y-%m-%d %H:%M:%S',
                    level=logging.INFO,
                    filename=f'{modelpath}/{args.daytime}.log',
                    filemode='w')
    logger = logging.getLogger(__name__)
    logger.info("parameters: %s", args)
    logger.info("path=%s,seed=%d,max_time=%d,max_length=%d,mu=%f,gNB_tti=%d,select_subs=%s,select_ports=%d,test_real=%d", env.path,env.seed,env.max_time,env.max_length,env.mu,env.gNB_tti,env.select_subs,env.select_ports,env.test_real)        
    # 训练参数
    max_score = -3
    if hasattr(args,"ue_pool"):
        ue_pool = args.ue_pool
    else:
        ue_pool = list(np.arange(args.train_nUE))
    batch = args.batch_size
    # 推理参数
    if hasattr(args,"global_batch"):
        global_batch = args.global_batch
    else:
        global_batch = env.nUE
    if hasattr(args,"global_ues"):
        global_ues = args.global_ues
    else:
        global_ues = list(np.arange(env.nUE))
    # 初始数据选择
    ptr = 0
    UE = ue_pool[ptr:min(ptr+batch,len(ue_pool))]
    times = np.ones_like(UE)*start_time
    ues = UE
    # 开始训练
    frame_idx = 0
    # 初始化 state
    action = np.random.random((batch, action_dim))
    state = State(args.device, *env.get_state_method3(batch=batch,actions=action,ues=ues,times=times))
    
    results = dict()
    results['actor_loss'] = []
    results['critic_loss'] = []
    results['reward'] = []
    results['aod'] = []
    results['eod'] = []
    results['azimuths'] = []
    results['elevation'] = []
    results['svd_aod_er'] = []
    results['pmi_aod_er'] = []
    
    results['reward_infer'] = []
    results['aod_infer'] = []
    results['eod_infer'] = []
    results['score_infer'] = []
    results['azimuths_infer'] = []
    results['elevation_infer'] = []
    results['score_all_infer'] = []

    while frame_idx < args.num_frame:
        action = agent.select_action(state)    
        noise = np.random.normal(0, args.noise_std, size=action_dim)
        action = action + noise
        action = np.clip(action, -1, 1)
        next_state = State(args.device, *env.get_state_method3(batch=batch,actions=action,ues=ues,times=times))
        reward = env.reward
        # 将（St, At, Rt, St+1）放入回放池
        done = frame_idx % args.done_cycle == 0
        done = np.ones_like(ues)*done
        if frame_idx != 0:
            agent.memory_pool.add_memory(state, reward, action, next_state, done)
        # 更新状态
        state = next_state
        frame_idx += 1
        times = times + 1
        results['reward'].append(reward)
        results['aod'].append(action[:,0]*env.hangle_bound)
        results['eod'].append(action[:,1]*env.vangle_bound)
       
def infer(args, env, seed, modelpath, action_dim, state_dim, start_time, end_time):
    # DDPG agent
    agent = DDPG(device=args.device, 
            memory_size=args.memory_size,
            sample_size=args.sample_size, 
            discount=args.discount,
            tau = args.tau,
            pi_lr = args.pi_lr,
            q_lr = args.q_lr,
            action_dim=action_dim,
            state_dim=state_dim,
            start_timesteps=args.start_timesteps,
            noise_std=args.noise_std)
    
    # 加载模型
    agent.load(modelpath)
    agent.eval()
    
    # 推理参数
    if hasattr(args,"global_batch"):
        batch = args.global_batch
    else:
        batch = env.nUE
    if hasattr(args,"global_ues"):
        ues = args.global_ues
    else:
        ues = list(np.arange(env.nUE))
    
    # 初始化结果存储
    time_length = end_time - start_time
    reward = np.zeros((batch, time_length), dtype=np.float32)
    score = np.zeros((batch, time_length), dtype=np.float32)
    score_all = np.zeros((batch, time_length), dtype=np.float32)
    aod = np.zeros((batch, time_length), dtype=np.float64)
    eod = np.zeros((batch, time_length), dtype=np.float64)
    aod_real = np.zeros((batch, time_length), dtype=np.float64)
    eod_real = np.zeros((batch, time_length), dtype=np.float64)
    
    # 初始动作
    action = np.zeros((batch, action_dim), dtype=np.float32)
    
    # 推理循环
    for i in range(0, time_length):
        times = np.ones_like(ues)*(i+start_time)
        state = State(args.device, *env.get_state_method3(batch=batch, actions=action, ues=ues, times=times))
        action = agent.select_action(state)
        aod[:,i] = action[:,0]*env.hangle_bound
        eod[:,i] = action[:,1]*env.vangle_bound
        aod_real[:,i] = env.aod_real
        eod_real[:,i] = env.eod_real
        reward[:,i] = env.reward
        score[:,i] = env.score
        score_all[:,i] = env.score_all
    
    # 计算平均得分
    avg_score = np.average(score[:,1:])
    print(f"Average Score: {avg_score}")
    
    # 整理结果
    results = {
        'reward_infer': reward,
        'aod_infer': aod,
        'eod_infer': eod,
        'azimuths_infer': aod_real,
        'elevation_infer': eod_real,
        'score_infer': score,
        'score_all_infer': score_all,
        'avg_score': avg_score
    }
    
    return results
