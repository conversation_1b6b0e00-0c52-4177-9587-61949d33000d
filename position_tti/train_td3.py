import os,sys
import pickle
sys.path.append(os.getcwd())
from func.BM_functions import *
from func.system_functions import *
from func.process import *
from func.setsumi_env import setsumi_env
from utils import *

import datetime
import logging
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import os
import random
import torch
import numpy as np
import torch.nn as nn
import torch.nn.functional as F
import argparse
from collections import deque

def seed_torch(seed):
    np.random.seed(seed)
    random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
    if torch.cuda.is_available():
        torch.backends.cudnn.enabled = True
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False
    torch.use_deterministic_algorithms(True, warn_only=True)
    os.environ['CUBLAS_WORKSPACE_CONFIG']=':4096:8' 
       
def parse_args(args=None):
    # ++ Basic Configs ++
    parser = argparse.ArgumentParser(description="BM")
    parser.add_argument("--desc", type=str, default="No desc", help="train desc")
    parser.add_argument("--seed", type=int, default=777, help="random seed")
    parser.add_argument("--device", type=str, default="cuda:0", help="train on device")
    
    # ++ Train Configs ++
    parser.add_argument("--memory_size", type=int, default=500000, help="DQN replay memory size")
    parser.add_argument("--train_nUE", type=int, default=20, help="train ue num")
    parser.add_argument("--batch_size", type=int, default=20, help="batch size")
    parser.add_argument("--optim_frame", type=int, default=1, help="update one time every N frame")
    parser.add_argument("--change_ue_cycle", type=int, default=100, help="change_ue_cycle")
    parser.add_argument("--done_cycle", type=int, default=100, help="done cycle")
    parser.add_argument("--num_frame", type=int, default=1000000, help="how many times agent train")
    parser.add_argument("--train_log_cycle", type=int, default=100, help="every N step log")

    # ++ TD3 Configs ++
    parser.add_argument("--start_timesteps", default=25e2, type=int)
    # parser.add_argument("--eval_freq", default=5e2, type=int)       
    # parser.add_argument("--expl_noise", default=0.1)                
    parser.add_argument("--discount", default=0.99)  
    parser.add_argument("--pi_lr", default=1e-3)   
    parser.add_argument("--q_lr", default=1e-3)                  
    parser.add_argument("--tau", default=0.005)                    
    parser.add_argument("--policy_noise", default=0.2)
    # parser.add_argument("--policy_noise_decay", default=0.2)
    # parser.add_argument("--policy_noise_min", default=0.2)             
    parser.add_argument("--noise_clip", default=1)                
    parser.add_argument("--policy_freq", default=2, type=int)
    parser.add_argument("--sample_size", type=int, default=20, help="sample size")
    
    parser.add_argument("--target_accuracy", type=float, default=0.9, help="target accuracy threshold (90%)")
    parser.add_argument("--accuracy_window", type=int, default=10, help="sliding window size for accuracy calculation")
    parser.add_argument("--patience", type=int, default=50, help="patience for early stopping after reaching target")
    parser.add_argument("--min_episodes_for_accuracy", type=int, default=100, help="minimum episodes before checking accuracy")
    
    # ... 现有返回代码 ...

    # ++ PPO  Configs ++
    parser.add_argument("--gamma", default=0.99)                 
    parser.add_argument("--lmbda", default=0.99)                 
    parser.add_argument("--epochs", default=10)                 
    parser.add_argument("--eps", default=0.2)                 
    parser.add_argument("--actor_lr", default=1e-3)  
    parser.add_argument("--critic_lr", default=3e-4)   
    
    if args is None:
        # 从命令行读取参数
        parsed_args = parser.parse_args()
    else:
        # 使用 parse_known_args 传递参数列表
        parsed_args, _ = parser.parse_known_args(args)
    
    return parsed_args

class ReplayMemoryPool():
    def __init__(self, size=10000):
        self.size = size # Memory size
        self.memories = deque(maxlen=self.size)
    def add_memory(self, state, reward, action, next_state, done):
        self.memories.append([state, reward, action, next_state, done])
    def get_batch(self, batch_size):
        choosen_index = np.random.choice(np.arange(0,len(self.memories)), batch_size, replace=False)
        return [self.memories[i] for i in choosen_index]
    def get_all_batch(self):
        choosen_index = np.arange(0,len(self.memories))
        return [self.memories[i] for i in choosen_index]
    def clear(self):
        self.memories.clear()

class Actor(nn.Module):
    def __init__(self,state_dim):
        super(Actor, self).__init__()
        self.net = ActorNet()

    def forward(self, state):
        action = self.net(state)
        return action

class Critic(nn.Module):
    def __init__(self,state_dim,action_dim):
        super(Critic,self).__init__()
        self.Q1 = CriticNet(state_dim, action_dim)
        self.Q2 = CriticNet(state_dim, action_dim)

    def forward(self,state,action):
        q1 = self.Q1(state,action)
        q2 = self.Q2(state,action)
        return q1,q2

class TD3(object):
    def __init__(
        self,
        device,
        state_dim=71,
        action_dim=3,
        discount = 0.98,
        tau = 0.005,
        policy_noise = 0.2,
        noise_clip = 0.5,
        policy_freq = 2,
        memory_size = 10000,
        sample_size = 100,
        pi_lr = 1e-3,
        q_lr = 1e-3,
        noise_std=0.1,
        start_timesteps = 25e3,
      
):      
        self.noise_std=noise_std
        self.device = device
        self.state_dim = state_dim
        self.action_dim = action_dim
        # Actor Init 
        self.actor = Actor(state_dim=state_dim).to(device)
        self.actor_target = copy.deepcopy(self.actor)
        self.actor_optimizer = torch.optim.Adam(self.actor.parameters(),lr = pi_lr)
        # Critic Init
        self.critic = Critic(state_dim=state_dim,action_dim=action_dim).to(device)
        self.critic_target = copy.deepcopy(self.critic)
        self.critic_optimizer = torch.optim.Adam(self.critic.parameters(),lr = q_lr)
        # Hyper Parameters
        self.discount = discount
        self.tau = tau
        self.policy_noise = policy_noise
        self.noise_clip = noise_clip
        self.policy_freq = policy_freq
        self.sample_size = sample_size
        self.device = device
        self.total_it = 0
        self.noise_std=noise_std
        self.start_timesteps = start_timesteps
        # memory
        self.memory_pool = ReplayMemoryPool(size=memory_size)
        

    def train(self):
        self.actor.train()

    def eval(self):
        self.actor.eval()

    def select_action(self,state) :
        action = self.actor(state)
        action = action.detach().cpu().numpy()
    
        return action

    def optim(self):
    # Initialize losses with None
        actor_loss = None
        critic_loss = None
        if len(self.memory_pool.memories) < self.sample_size:
            return [None, None]
        
        self.total_it +=1
        
        # Sample
        batch = self.memory_pool.get_batch(self.sample_size)
        state = State(self.device, states=[batch[i][0] for i in range(self.sample_size)])
        reward = torch.tensor(np.array([batch[i][1] for i in range(self.sample_size)]), device=self.device).view(-1,1)
        action = torch.tensor(np.array([batch[i][2] for i in range(self.sample_size)]), device=self.device).view(-1,self.action_dim)
        next_state = State(self.device, states=[batch[i][3] for i in range(self.sample_size)])
        done = torch.tensor(np.array([1-batch[i][4] for i in range(self.sample_size)]), device=self.device).view(-1,1)

        with torch.no_grad():
            noise = (torch.randn_like(action) * self.policy_noise).clamp(-self.noise_clip,self.noise_clip)
          
            next_action = self.actor_target(next_state) + noise
            # print("next_action",next_action.dtype)
            # Compute the target Q value
            target_Q1,target_Q2 = self.critic_target(next_state,next_action)
            target_Q = torch.minimum(target_Q1,target_Q2).to(self.device)
            target_Q = reward + (1.0 - done) * self.discount * target_Q
            target_Q = target_Q.type(torch.float32)

        # Get current Q estimates
        current_Q1, current_Q2 = self.critic(state, action)
        
        # Calculate Critic Loss
        critic_loss = F.mse_loss(current_Q1, target_Q) + F.mse_loss(current_Q2, target_Q)

        # Optimizer the Critic
        self.critic_optimizer.zero_grad()
        critic_loss.backward()
        self.critic_optimizer.step()    

        # Delaye policy updates
        if self.total_it % self.policy_freq == 0:
            next_action = self.actor(state)
            actor_loss = -self.critic.Q1(state, next_action).mean()

            # Optimize the actor
            self.actor_optimizer.zero_grad()
            actor_loss.backward()
            self.actor_optimizer.step()

            # Soft Update the frozen target models
            with torch.no_grad():
                for param, target_param in zip(self.critic.parameters(), self.critic_target.parameters()):
                    target_param.data.copy_(self.tau * param.data + (1 - self.tau) * target_param.data)

                for param, target_param in zip(self.actor.parameters(), self.actor_target.parameters()):
                    target_param.data.copy_(self.tau * param.data + (1 - self.tau) * target_param.data)

        return [actor_loss.item() if actor_loss is not None else None, critic_loss.item() if critic_loss is not None else None]

    def save(self,filename):
        torch.save(self.critic.state_dict(), filename + "_critic")
        torch.save(self.critic_optimizer.state_dict(), filename + "_critic_optimizer")
		
        torch.save(self.actor.state_dict(), filename + "_actor")
        torch.save(self.actor_optimizer.state_dict(), filename + "_actor_optimizer")

    def load(self, filename):
        self.critic.load_state_dict(torch.load(filename + "_critic"))
        self.critic_optimizer.load_state_dict(torch.load(filename + "_critic_optimizer"))
        self.critic_target = copy.deepcopy(self.critic)

        self.actor.load_state_dict(torch.load(filename + "_actor"))
        self.actor_optimizer.load_state_dict(torch.load(filename + "_actor_optimizer"))
        self.actor_target = copy.deepcopy(self.actor)    


class State():
    def __init__(self, device, srs_aod=None, pmi_aod=None, srs_eod=None, pmi_eod=None, states=None):
        if states is not None:
            self.nUE = states[0].nUE
            self.srs_aod = torch.cat([state.srs_aod for state in states])
            self.pmi_aod = torch.cat([state.pmi_aod for state in states])
            self.srs_eod = torch.cat([state.srs_eod for state in states])
            self.pmi_eod = torch.cat([state.pmi_eod for state in states])
        else:
            self.flatten = torch.nn.Flatten()
            self.nUE = srs_aod.shape[0]
            self.srs_aod = self.flatten(torch.tensor(srs_aod, device=device, dtype=torch.float32))
            self.pmi_aod = self.flatten(torch.tensor(pmi_aod, device=device, dtype=torch.float32))
            self.srs_eod = self.flatten(torch.tensor(srs_eod, device=device, dtype=torch.float32))
            self.pmi_eod = self.flatten(torch.tensor(pmi_eod, device=device, dtype=torch.float32))

class ActorNet(torch.nn.Module):
    def __init__(self):
        super(ActorNet, self).__init__()        

        self.hlayer = nn.Linear(18, 16)
        
        self.vlayer = nn.Linear(18, 16)
        
        self.mix_layer = nn.Linear(16*2,16)

        self.mu_jointlayer = nn.Linear(16, 2)

        self.relu = nn.ReLU()
        self.tanh = nn.Tanh()
        self.softmax = nn.Softmax(dim=-1)
    
    def forward(self, state):
        h = self.hlayer(torch.cat((state.srs_aod, state.pmi_aod),dim=-1))
        h = self.relu(h)
        
        v = self.hlayer(torch.cat((state.srs_eod, state.pmi_eod),dim=-1))
        v = self.relu(v)
        
        mix = self.mix_layer(torch.cat((h, v),dim=-1))
        mix = self.relu(mix)
        
        action = self.mu_jointlayer(mix)
        action = self.tanh(action)
        
        return action
               
class CriticNet(torch.nn.Module):
    def __init__(self, state_dim, action_dim):
        super(CriticNet, self).__init__()        

        self.layer = nn.Sequential(
            nn.Linear(state_dim+action_dim,256),
            nn.ReLU(),
            nn.LayerNorm([256]),
            nn.Linear(256,64),
            nn.ReLU(),
            nn.LayerNorm([64]),
            nn.Linear(64,1)
        ) 
        
    def forward(self, state, action):
        output = torch.cat((state.srs_aod, state.pmi_aod,state.srs_eod, state.pmi_eod, action),dim = -1)
        output = self.layer(output)
        return output
class AccuracyMonitor:
    def __init__(self, target_accuracy=0.9, window_size=10, patience=50):
        self.target_accuracy = target_accuracy
        self.window_size = window_size
        self.patience = patience
        
        # 存储历史性能指标
        self.accuracy_history = []
        self.reward_history = []
        self.loss_history = []
        
        # 目标达成相关
        self.target_reached = False
        self.target_reached_frame = None
        self.target_reached_episode = None
        self.consecutive_target_count = 0
        
    def calculate_accuracy(self, predicted_actions, true_actions, tolerance=0.1):
        """
        计算动作预测准确率
        tolerance: 允许的误差范围（相对于动作范围的百分比）
        """
        if len(predicted_actions) == 0:
            return 0.0
            
        # 计算每个动作维度的准确率
        accuracies = []
        for i in range(predicted_actions.shape[1]):  # 对每个动作维度
            pred = predicted_actions[:, i]
            true = true_actions[:, i]
            
            # 计算在容忍范围内的准确预测比例
            errors = np.abs(pred - true)
            accurate_predictions = errors <= tolerance
            accuracy = np.mean(accurate_predictions)
            accuracies.append(accuracy)
        
        # 返回所有维度的平均准确率
        return np.mean(accuracies)
    
    def calculate_position_accuracy(self, scores, threshold=0.1):
        """
        基于位置误差计算准确率
        threshold: 位置误差的阈值
        """
        if len(scores) == 0:
            return 0.0
        
        # 将位置误差转换为准确率（误差越小，准确率越高）
        accurate_positions = scores <= threshold
        return np.mean(accurate_positions)
    
    def update(self, frame_idx, episode_idx, reward, aod_pred, eod_pred, aod_true, eod_true, scores):
        """
        更新监控指标
        """
        # 计算当前批次的准确率
        actions_pred = np.column_stack([aod_pred, eod_pred])
        actions_true = np.column_stack([aod_true, eod_true])
        
        action_accuracy = self.calculate_accuracy(actions_pred, actions_true)
        position_accuracy = self.calculate_position_accuracy(scores)
        
        # 综合准确率（可以根据需要调整权重）
        combined_accuracy = 0.6 * action_accuracy + 0.4 * position_accuracy
        
        # 更新历史记录
        self.accuracy_history.append({
            'frame': frame_idx,
            'episode': episode_idx,
            'action_accuracy': action_accuracy,
            'position_accuracy': position_accuracy,
            'combined_accuracy': combined_accuracy,
            'avg_reward': np.mean(reward)
        })
        
        # 检查是否达到目标精度
        if len(self.accuracy_history) >= self.window_size:
            # 计算滑动窗口内的平均准确率
            recent_accuracies = [h['combined_accuracy'] for h in self.accuracy_history[-self.window_size:]]
            avg_accuracy = np.mean(recent_accuracies)
            
            if avg_accuracy >= self.target_accuracy:
                if not self.target_reached:
                    self.target_reached = True
                    self.target_reached_frame = frame_idx
                    self.target_reached_episode = episode_idx
                    print(f"\n🎯 TARGET ACCURACY REACHED!")
                    print(f"   Frame: {frame_idx}")
                    print(f"   Episode: {episode_idx}")
                    print(f"   Accuracy: {avg_accuracy:.4f}")
                    print(f"   Window size: {self.window_size}")
                
                self.consecutive_target_count += 1
            else:
                self.consecutive_target_count = 0
        
        return combined_accuracy
    
    def should_stop_training(self):
        """
        判断是否应该停止训练（达到目标后维持一定时间）
        """
        return (self.target_reached and 
                self.consecutive_target_count >= self.patience)
    
    def get_current_accuracy(self):
        """
        获取当前准确率
        """
        if len(self.accuracy_history) >= self.window_size:
            recent_accuracies = [h['combined_accuracy'] for h in self.accuracy_history[-self.window_size:]]
            return np.mean(recent_accuracies)
        elif len(self.accuracy_history) > 0:
            return self.accuracy_history[-1]['combined_accuracy']
        else:
            return 0.0   
def calscore(args, env, agent, batch, start_time, end_time, action_dim, ues):
    agent.eval()
    
    time_length = end_time - start_time
    reward = np.zeros((batch, time_length), dtype=np.float32)
    score = np.zeros((batch, time_length), dtype=np.float32)
    score_all = np.zeros((batch, time_length), dtype=np.float32)
    action = np.zeros((batch, action_dim), dtype=np.float32)
    aod = np.zeros((batch, time_length), dtype=np.float64)
    eod = np.zeros((batch, time_length), dtype=np.float64)
    aod_real = np.zeros((batch, time_length), dtype=np.float64)
    eod_real = np.zeros((batch, time_length), dtype=np.float64)
    for i in range(0, time_length):
        times = np.ones_like(ues)*(i+start_time)
        state = State(args.device, *env.get_state_method3(batch=batch,actions=action,ues=ues,times=times))
        action = agent.select_action(state)
        aod[:,i] = action[:,0]*env.hangle_bound
        eod[:,i] = action[:,1]*env.vangle_bound
        aod_real[:,i] = env.aod_real
        eod_real[:,i] = env.eod_real
        reward[:,i] = env.reward
        score[:,i] = env.score
        score_all[:,i] = env.score_all
        
    res = np.average(score[:,1:])
        
    agent.train()
    
    return reward,aod,eod,aod_real,eod_real,score,score_all,res

def train(args, env, seed, modelpath, action_dim, state_dim, start_time, end_time):
    # TD3 agent
    agent = TD3(device=args.device, 
            memory_size=args.memory_size,
            sample_size=args.sample_size, 
            discount=args.discount,
            tau = args.tau,
            noise_clip = args.noise_clip,
            policy_freq = args.policy_freq,
            policy_noise = args.policy_noise,
            pi_lr = args.pi_lr,
            q_lr = args.q_lr,
            action_dim=action_dim,
            state_dim=state_dim,
            start_timesteps=args.start_timesteps,
            noise_std=args.noise_std)
    
    # 记录
    args.daytime = datetime.datetime.now().strftime('%Y-%m-%d-%H-%M-%S')
    if not os.path.exists(modelpath):
        os.makedirs(modelpath)
    logging.basicConfig(format='%(asctime)s - %(levelname)s - %(message)s',
                    datefmt='%Y-%m-%d %H:%M:%S',
                    level=logging.INFO,
                    filename=f'{modelpath}/{args.daytime}.log',
                    filemode='w')
    logger = logging.getLogger(__name__)
    logger.info("parameters: %s", args)
    logger.info("path=%s,seed=%d,max_time=%d,max_length=%d,mu=%f,gNB_tti=%d,select_subs=%s,select_ports=%d,test_real=%d", env.path,env.seed,env.max_time,env.max_length,env.mu,env.gNB_tti,env.select_subs,env.select_ports,env.test_real)        
    # 训练参数
    max_score = -3
    if hasattr(args,"ue_pool"):
        ue_pool = args.ue_pool
    else:
        ue_pool = list(np.arange(args.train_nUE))
    batch = args.batch_size
    # 推理参数
    if hasattr(args,"global_batch"):
        global_batch = args.global_batch
    else:
        global_batch = env.nUE
    if hasattr(args,"global_ues"):
        global_ues = args.global_ues
    else:
        global_ues = list(np.arange(env.nUE))
    # 初始数据选择
    ptr = 0
    UE = ue_pool[ptr:min(ptr+batch,len(ue_pool))]
    times = np.ones_like(UE)*start_time
    ues = UE
    # 开始训练
    frame_idx = 0
    # 初始化 state
    action = np.random.random((batch, action_dim))
    state = State(args.device, *env.get_state_method3(batch=batch,actions=action,ues=ues,times=times))
    
    accuracy_monitor = AccuracyMonitor(
        target_accuracy=args.target_accuracy,
        window_size=args.accuracy_window,
        patience=args.patience
    )

    results = dict()
    results['actor_loss'] = []
    results['critic_loss'] = []
    results['reward'] = []
    results['aod'] = []
    results['eod'] = []
    results['azimuths'] = []
    results['elevation'] = []
    results['svd_aod_er'] = []
    results['pmi_aod_er'] = []
    
    results['reward_infer'] = []
    results['aod_infer'] = []
    results['eod_infer'] = []
    results['score_infer'] = []
    results['azimuths_infer'] = []
    results['elevation_infer'] = []
    results['score_all_infer'] = []
    results['accuracy_history'] = []
    results['target_reached_info'] = {}
    while frame_idx < args.num_frame:
        if frame_idx >= args.start_timesteps:
            # 在训练的后期，使用策略选择动作并加噪声
            action = agent.select_action(state)
            noise = np.random.normal(0, args.noise_std, size=action_dim).clip(-1, 1)
            action = action + noise
            action = torch.tensor(action, dtype=torch.float32)  # 确保动作是 float32 类型
            action = action.cpu().numpy()  # 转换为 NumPy 数组（如果需要）

        else:
            # 在训练初期，随机选择动作
            action = agent.select_action(state)
            action = torch.tensor(action, dtype=torch.float32)
            action = action.cpu().numpy()
        next_state = State(args.device, *env.get_state_method3(batch=batch,actions=action,ues=ues,times=times))
        reward = env.reward
        # 将（St, At, Rt, St+1）放入回放池
        done = frame_idx % args.done_cycle == 0
        done = np.ones_like(ues)*done
        if frame_idx != 0:
            agent.memory_pool.add_memory(state, reward, action, next_state, done)
        # 更新状态
        state = next_state
        frame_idx += 1
        times = times + 1
        results['reward'].append(reward)
        results['aod'].append(action[:,0]*env.hangle_bound)
        results['eod'].append(action[:,1]*env.vangle_bound)
        results['azimuths'].append(env.aod_real)
        results['elevation'].append(env.eod_real)
        results['svd_aod_er'].append(env.svd_aod_er)
        results['pmi_aod_er'].append(env.pmi_aod_er)
        # 每optim_frame步优化
        if frame_idx % args.optim_frame == 0 and frame_idx != 0:
            actor_loss,critic_loss = agent.optim()
            results['actor_loss'].append(actor_loss)
            results['critic_loss'].append(critic_loss)
            
        
        # 每M步更新选择的UE,重新从start_time开始
        if frame_idx % args.change_ue_cycle == 0 and frame_idx != 0:
            times = np.ones_like(UE)*start_time
            ptr += batch
            if ptr+batch >= len(ue_pool):
                ue_pool.extend(ue_pool)
            UE = ue_pool[ptr:min(ptr+batch,len(ue_pool))]
        
        # 每M步计算模型训练结果
        if frame_idx % args.train_log_cycle == 0 and frame_idx != 0:
            agent.eval()
            reward,aod,eod,aod_real,eod_real,score,score_all,res = calscore(args, env, agent, global_batch, start_time, end_time, action_dim, global_ues)
            results['reward_infer'].append(reward)
            results['aod_infer'].append(aod)
            results['eod_infer'].append(eod)
            results['azimuths_infer'].append(aod_real)
            results['elevation_infer'].append(eod_real)
            results['score_infer'].append(score)
            results['score_all_infer'].append(score_all)

            print('reward:', np.average(reward[:,1:],axis=-1))
            print("aod err:", np.average(np.abs(aod[:,1:]-aod_real[:,1:]),axis=-1))
            print("eod err:", np.average(np.abs(eod[:,1:]-eod_real[:,1:]),axis=-1))
            print('pos err:',np.average(score[:,1:],axis=-1))
            print('pos all err:',np.average(score_all[:,1:],axis=-1))
            print('averg aod eod pos pos_all err:', np.average(np.abs(aod[:,1:]-aod_real[:,1:])), np.average(np.abs(eod[:,1:]-eod_real[:,1:])), np.average(score[:,1:]), np.average(score_all[:,1:]))
            if frame_idx >= args.min_episodes_for_accuracy:
                current_accuracy = accuracy_monitor.update(
                    frame_idx, episode_idx,
                    reward[:, 1:],  # 排除第一个时间步
                    aod[:, 1:], eod[:, 1:],
                    aod_real[:, 1:], eod_real[:, 1:],
                    score[:, 1:]
                )
                
                # 记录精度信息
                results['accuracy_history'].append({
                    'frame': frame_idx,
                    'episode': episode_idx,
                    'accuracy': current_accuracy,
                    'target_reached': accuracy_monitor.target_reached
                })
                
                # 打印精度信息
                print(f'Current Accuracy: {current_accuracy:.4f} (Target: {args.target_accuracy:.4f})')
                if accuracy_monitor.target_reached:
                    print(f'🎯 Target reached at frame {accuracy_monitor.target_reached_frame}')
            if res > max_score:
                agent.save(modelpath+f'{args.daytime}_frame{frame_idx}_score{res}')
                max_score = res
            if accuracy_monitor.should_stop_training():
                print(f"\n✅ TRAINING COMPLETED!")
                print(f"   Target accuracy maintained for {accuracy_monitor.consecutive_target_count} evaluations")
                print(f"   Total frames: {frame_idx}")
                print(f"   Target reached at frame: {accuracy_monitor.target_reached_frame}")
                
                # 记录目标达成信息
                results['target_reached_info'] = {
                    'target_reached': True,
                    'frame': accuracy_monitor.target_reached_frame,
                    'episode': accuracy_monitor.target_reached_episode,
                    'total_frames': frame_idx,
                    'final_accuracy': accuracy_monitor.get_current_accuracy()
                }
                break
            
            episode_idx += 1
            agent.train()
    if not accuracy_monitor.target_reached:
        results['target_reached_info'] = {
            'target_reached': False,
            'total_frames': frame_idx,
            'final_accuracy': accuracy_monitor.get_current_accuracy(),
            'max_accuracy': max([h['accuracy'] for h in results['accuracy_history']]) if results['accuracy_history'] else 0.0
        }
        print(f"\n⚠️  Training completed without reaching target accuracy")
        print(f"   Final accuracy: {accuracy_monitor.get_current_accuracy():.4f}")
        print(f"   Target accuracy: {args.target_accuracy:.4f}")

    name = f'{modelpath}/{args.daytime}_results.pkl'
    with open(name, 'wb') as file:
        pickle.dump(results, file)
    

    return results
def analyze_training_results(results, target_accuracy=0.9):
    """
    分析训练结果，提供详细的精度达成报告
    """
    print("\n" + "="*50)
    print("TRAINING ANALYSIS REPORT")
    print("="*50)
    
    if results['target_reached_info']['target_reached']:
        print(f"✅ SUCCESS: Target accuracy of {target_accuracy:.1%} was reached!")
        print(f"   Frames to reach target: {results['target_reached_info']['frame']:,}")
        print(f"   Episodes to reach target: {results['target_reached_info']['episode']:,}")
        print(f"   Final accuracy: {results['target_reached_info']['final_accuracy']:.4f}")
    else:
        print(f"❌ Target accuracy of {target_accuracy:.1%} was NOT reached")
        print(f"   Final accuracy: {results['target_reached_info']['final_accuracy']:.4f}")
        print(f"   Max accuracy achieved: {results['target_reached_info'].get('max_accuracy', 0):.4f}")
        print(f"   Total frames trained: {results['target_reached_info']['total_frames']:,}")
    
    # 绘制精度曲线
    if results['accuracy_history']:
        frames = [h['frame'] for h in results['accuracy_history']]
        accuracies = [h['accuracy'] for h in results['accuracy_history']]
        
        print(f"\nAccuracy progression:")
        print(f"   Initial accuracy: {accuracies[0]:.4f}")
        print(f"   Final accuracy: {accuracies[-1]:.4f}")
        print(f"   Max accuracy: {max(accuracies):.4f}")
        print(f"   Accuracy improvement: {accuracies[-1] - accuracies[0]:.4f}")

def infer(args, env, seed, modelpath, action_dim, state_dim, start_time, end_time):
    # TD3 agent
    agent = TD3(device=args.device, 
            memory_size=args.memory_size,
            sample_size=args.sample_size, 
            discount=args.discount,
            tau = args.tau,
            noise_clip = args.noise_clip,
            policy_freq = args.policy_freq,
            policy_noise = args.policy_noise,
            pi_lr = args.pi_lr,
            q_lr = args.q_lr,
            action_dim=action_dim,
            state_dim=state_dim)
    
    # 加载模型
    agent.load(modelpath)

    
    # 推理参数
    if hasattr(args,"global_ues"):
        global_ues = args.global_ues
    else:
        global_ues = list(np.arange(env.nUE))
    global_batch = len(global_ues)
    # 推理
    results = dict()
    results['reward_infer'] = []
    results['aod_infer'] = []
    results['eod_infer'] = []
    results['score_infer'] = []
    results['azimuths_infer'] = []
    results['elevation_infer'] = []
    results['score_all_infer'] = []
    
    reward,aod,eod,aod_real,eod_real,score,score_all,res = calscore1(args, env, agent, global_batch, start_time, end_time, action_dim, global_ues)
    results['reward_infer'].append(reward)
    results['aod_infer'].append(aod)
    results['eod_infer'].append(eod)
    results['azimuths_infer'].append(aod_real)
    results['elevation_infer'].append(eod_real)
    results['score_infer'].append(score)
    results['score_all_infer'].append(score_all)

    print('reward:', np.average(reward[:,1:],axis=-1))
    print("aod err:", np.average(np.abs(aod[:,1:]-aod_real[:,1:]),axis=-1))
    print("eod err:", np.average(np.abs(eod[:,1:]-eod_real[:,1:]),axis=-1))
    print('pos err:',np.average(score[:,1:],axis=-1))
    print('pos all err:',np.average(score_all[:,1:],axis=-1))
    print('averg aod eod pos pos_all err:', np.average(np.abs(aod[:,1:]-aod_real[:,1:])), np.average(np.abs(eod[:,1:]-eod_real[:,1:])), np.average(score[:,1:]), np.average(score_all[:,1:]))
    
    return results