
import numpy as np
from math import pi

def get_channel(H_angles=[], V_angles=[], numHant=8, numVant=4, HSpacing=0.5, VSpacing=0.5,downtilt=0,isCombineV = False,CombiningWeight = np.array([]),isAEPattern = 0,enableAntennaPattern = 0,H_pattern = [],V_pattern = []):
	HAnt_pos = HSpacing * np.repeat(np.expand_dims(np.arange(0, numHant), 0), numVant, 0)
	VAnt_pos = VSpacing * np.repeat(np.expand_dims(np.arange(0, numVant), 1), numHant, 1)
	HAnt_pos = np.reshape(HAnt_pos,(-1,1))
	HAnt_pos = HAnt_pos.transpose(1,0)
	VAnt_pos = np.reshape(VAnt_pos,(1,-1))
	## add V_shift
	# VAntennaPositions = reshape(VAntennaPositions,numHAntennas,[]);
	# VAntennaPositions(2:2:end) = VAntennaPositions(2:2:end)+VShift;
	# VAntennaPositions = reshape(VAntennaPositions,1,[]);
	#combining V

	if(CombiningWeight.any() and isCombineV):
		combingV = CombiningWeight[0:numVant]
	else:
    ## change combing
		combingV = np.ones((numVant,1))
	CombiningWeight_complex = combingV
	CombiningWeight_complex = np.repeat(np.expand_dims(CombiningWeight_complex,axis = 1),numHant,axis = 1)
	CombiningWeight_complex = CombiningWeight_complex.reshape(-1)

	
	H_channel_angles = np.repeat(np.expand_dims(H_angles, 1), V_angles.size, 1)
	V_channel_angles = np.repeat(np.expand_dims(V_angles, 0), H_angles.size, 0)
	H_channel_angles = 90-H_channel_angles

	[H_channel_angles, V_channel_angles] = AngleFromLtoG(H_channel_angles, V_channel_angles, downtilt)
	H_channel_angles = 90 - H_channel_angles
	H_channel_angles = np.reshape(H_channel_angles,(-1,1))
	V_channel_angles = np.reshape(V_channel_angles,(1,-1))
	V_channel_angles = V_channel_angles.transpose(1,0)
	channel = np.exp(1j*2*pi*(np.sin(H_channel_angles/180*pi)*np.cos(V_channel_angles/180*pi)@HAnt_pos+np.sin(V_channel_angles/180*pi)@VAnt_pos))
	## AEPattern
	if(isAEPattern):
		if(enableAntennaPattern):
			H_idx = np.ceil(np.mod((360+H_channel_angles),360)).astype(int).squeeze(axis=-1)
			V_idx = np.ceil(np.mod((360-V_channel_angles),360)).astype(int).squeeze(axis=-1)
			par = np.power(10,(H_pattern[H_idx]+ V_pattern[V_idx])/20)
			par = np.expand_dims(par,1)
			channel = par*channel
		else:
			H_min = 12*(H_channel_angles/65)**2
			H_min[H_min>30] = 30
			V_min = 12*(V_channel_angles/65)**2
			V_min[V_min>30] = 30
			par = np.power(10,(8*np.ones_like(H_channel_angles) - H_min - V_min)/20)
			channel = par*channel
	channel = channel*(np.expand_dims(CombiningWeight_complex,axis=0))
	return channel

def AngleFromLtoG(H_angle,V_angle,downtilt):
	H_angle = H_angle/180*pi
	V_angle = V_angle/180*pi
	downtilt = downtilt/180*pi
	[lx,ly,lz] = sph2cart(H_angle,V_angle,1)
	R = np.array([[1,0,0],
		[0,np.cos(downtilt),-np.sin(downtilt)],
		[0,np.sin(downtilt),np.cos(downtilt)]]) 
	R = np.linalg.inv(R)
	L = np.array([lx,ly,lz])
	L = np.expand_dims(L,0)
	L = np.transpose(L)
	G = R@L
	G = np.transpose(G)
	G = G[0]
	[H_angle,V_angle,r] = cart2sph(G[0],G[1],G[2]) 
	H_angle = H_angle/pi*180
	V_angle = V_angle/pi*180
	return H_angle,V_angle

def sph2cart(az, el, r):
	rcos_theta = r * np.cos(el)
	x = rcos_theta * np.cos(az)
	y = rcos_theta * np.sin(az)
	z = r * np.sin(el)
	return x, y, z
  
def cart2sph(x, y, z):
	hxy = np.hypot(x, y)
	r = np.hypot(hxy, z)
	el = np.arctan2(z, hxy)
	az = np.arctan2(y, x)
	return az, el, r

if __name__ == '__main__':
	Hangles = np.array(np.arange(-2,3,1),np.float64)
	Vangles = np.array(np.arange(-1,2,1),np.float64)
	H_angle = np.array([30.0,40.0])
	V_angle = np.array([60.0,50.0])
	downtilt = 30
	[H,V] = AngleFromLtoG(H_angle,V_angle,downtilt)
	print(H,V)
	H_angle = np.arange(0,361)
	H_angle= (H_angle>180)*(-360) + H_angle
	H_pattern = -12*(H_angle/65)*(H_angle/65)
	H_pattern = np.clip(H_pattern,-30,0)
	V_pattern = H_pattern
	combine_weight = np.array([[0.5,0.5j],[0.5,0.5j],[0.5,0.5j],[0.5,0.5j]])
	result = get_channel(Hangles, Vangles,downtilt = downtilt,enableAntennaPattern=1,H_pattern=H_pattern,V_pattern=V_pattern )         
	print(result.shape)    
	print(result)
