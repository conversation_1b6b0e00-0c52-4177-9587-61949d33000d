import numpy as np
import math

# Table 5.1.3.1-2: MCS index table 2 for PDSCH
ModulationSchemeForMcsQAM256 = np.array([2, 2, 2, 2, 2,
                                         4, 4, 4, 4, 4, 4,
                                         6, 6, 6, 6, 6, 6, 6, 6, 6,
                                         8, 8, 8, 8, 8, 8, 8, 8,
                                         2, 4, 6 ,8,])
SpectralEfficiencyForMcsQAM256 = [0.2344, 0.3770 , 0.6016 , 0.8770 , 1.1758 , 1.4766 ,1.6953 , 1.9141 ,
                                 2.1602 , 2.4063, 3.5703 , 2.7305 , 3.0293 , 3.3223,3.6094 , 3.9023 ,
                                 4.2129 , 4.5234 , 4.8164 , 5.1152 , 5.3320 , 5.5547,5.8906 , 6.2266 ,
                                 6.5703 , 6.9141 , 7.1602 , 7.4063 , 0, 0, 0, 0]

TargetCodeRateQAM256 = np.array([120,193,308,449,602,378,434,490,553,
                               616,658,466,517,567,616,666,719,772,
                               822,873,682.5,711,754,797,841,885,916.5,948])

# Table 5.1.3.1-1: MCS index table 1 for PDSCH
ModulationSchemeForMcsQAM64 = np.array([ 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,
                                         4, 4, 4, 4, 4, 4, 4,
                                         6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6])


SpectralEfficiencyForMcsQAM64 = [0.2344, 0.3770 , 0.6016 , 0.8770 , 1.1758 , 1.4766 ,1.6953 , 1.9141 ,
                                 2.1602 , 2.4063, 3.5703 , 2.7305 , 3.0293 , 3.3223,3.6094 , 3.9023 ,
                                 4.2129 , 4.5234 , 4.8164 , 5.1152 , 5.3320 , 5.5547,5.8906 , 6.2266 ,
                                 6.5703 , 6.9141 , 7.1602 , 7.4063 , 0, 0, 0, 0]

TargetCodeRateQAM64 = np.array([120,157,193,251,308,379,449,526,602,679,
                                340,378,434,490,553,616,658,
                                438,466,517,567,616,666,719,772,822,873,910,948])


TbsForN_infoLessThan3824 = [0,24,32,40,48,56,64,72,80,88,96,104,
                            112,120,128,136,144,152,160,168,176,
                            184,192,208,224,240,256,272,288,304,
                            320,336,352,368,384,408,432,456,480,
                            504,528,552,576,608,640,672,704,736,
                            768,808,848,888,928,984,1032,1064,
                            1128,1160,1192,1224,1256,1288,1320,
                            1352,1416,1480,1544,1608,1672,1736,
                            1800,1864,1928,2024,2088,2152,2216,
                            2280,2408,2472,2536,2600,2664,2728,
                            2792,2856,2976,3104,3240,3368,3496,
                            3624,3752,3824]

def TbCalc(mcsIndex,layerNumPerUe,nPrb,nReAvg):
    Qm = ModulationSchemeForMcsQAM64[mcsIndex]
    Rate = TargetCodeRateQAM64[mcsIndex]/1024
    nRE = nReAvg*nPrb
    nRE_info = nRE*Rate*Qm*layerNumPerUe
    if nRE_info <= 3824:
        n = max(3,math.floor(np.log2(nRE_info))-6)
        N_info = max(24,2**n*math.floor(nRE_info/(2**n)))
        TBS = 0
        for tb in TbsForN_infoLessThan3824:
            if tb>N_info:
                TBS = tb
                break
    else:
        n = math.floor(np.log2(nRE_info-24))-5
        N_info = max(3840,2**n*round((nRE_info-24)/pow(2,n)))
        if Rate <= 0.25:
            C = math.ceil((N_info+24)/3816)
            TBS = 8*C*math.ceil((N_info+24)/(8*C))-24
        elif N_info > 8424:
            C = math.ceil((N_info+24)/8424)
            TBS = 8*C*math.ceil((N_info+24)/(8*C))-24
        else:
            TBS = 8*math.ceil((N_info+24)/8)-24
    return TBS


# def get_su_tp(selected_ue, prb_pre_sb: list):
#     assert len(prb_pre_sb) == n_subband
#     cqi = selected_ue.subband_cqi
#     mcs = selected_ue.subband_mcs
#     layer_num = selected_ue.layer_num
#     TP = 0
#     nPRB_PDSCH = 156
#     nREAvg = min(156, nPRB_PDSCH)
#     for band in range(n_subband):
#         TP += TbCalc(mcs,layer_num,prb_pre_sb[band],nREAvg)

if __name__=='__main__':
    nPRB = 272
    n_subband = 17
    nRBG = 17
    nBS_RF_CHAIN = 16
    PRBpreSB = [nBS_RF_CHAIN for i in range(nRBG-1)] + [nPRB-nBS_RF_CHAIN*(nRBG-1)]  # PRB of each subband
    nPRB_PDSCH = 156
    LayerNumPerUe = 1
    # N_Prb = NRB
    nREAvg = min(156, nPRB_PDSCH)
    test_tb = TbCalc(28,LayerNumPerUe,nPRB,nREAvg)
    
    # print(len(PRBpreSB))