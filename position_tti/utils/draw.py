import numpy as np
from .channel import get_channel
from .generate_beam import generate_dft_beams
from .PMI import PMI
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import copy

class draw:
    def __init__(self,H_range = 60,V_range = 60,numHant = 8,numVant = 4,HSpacing = 0.5,VSpacing = 0.5,downtilt = 0,numCombiningV = 1,CombiningWeight = np.array([]),isCombineV = False,isAEPattern = True,enableAntennaPattern = True,H_pattern = np.array([]),V_pattern = np.array([])):
        self.H_angles_pol = np.array(np.arange(-180,180+1,1),np.float64)
        self.V_angles_pol = np.array(np.arange(-180,180+1,1),np.float64)
        
        self.numCombiningV = numCombiningV
        self.CombiningWeight = CombiningWeight
        self.isCombineV = isCombineV
        self.isAEPattern = isAEPattern
        self.enableAntennaPattern = enableAntennaPattern
        self.H_pattern = H_pattern
        self.V_pattern = V_pattern

        if(not (self.H_pattern.size or self.V_pattern.size)):
            self.pattern_3GPP()
        self.update_ant_config(numHant,numVant,HSpacing,VSpacing,downtilt)
        self.update_range(H_range,V_range)
        self.weight = self.dft_beams # kron(V,H) --> V,H
        self.weight_pol1 = copy.deepcopy(self.weight)*0
        self.weight_pol2 = copy.deepcopy(self.weight)*0
        self.load_channel()
        self.display_pol = 1
        self.Beams = None
        self.multile_line = True
        self.PMI_class = PMI()
        self.init_draw()
                
    def update_ant_config(self,numHant,numVant,HSpacing,VSpacing,downtilt):
        self.numHant = numHant
        self.numVant = numVant
        self.HSpacing = HSpacing
        self.VSpacing = VSpacing
        self.downtilt = downtilt
        self.Ants_num = self.numHant * int(self.numVant/self.numCombiningV)
        self.Beam_num = self.Ants_num
        self.dft_beams = generate_dft_beams(numVant=int(self.numVant/self.numCombiningV),numHant=self.numHant)

    def update_range(self,H_range,V_range):
        self.H_angles_power = self.H_angles_pol[180-H_range:180+H_range+1]
        self.V_angles_power = self.V_angles_pol[180-V_range:180+V_range+1]
        self.H_range = H_range
        self.V_range = V_range
        self.H_size = self.H_angles_power.size
        self.V_size = self.V_angles_power.size
    
    def load_channel(self):
        self.channel = get_channel(self.H_angles_pol,self.V_angles_pol,self.numHant,self.numVant,self.HSpacing, self.VSpacing,self.downtilt,self.isCombineV,self.CombiningWeight,self.isAEPattern,self.enableAntennaPattern,self.H_pattern,self.V_pattern) 
        self.channel_re = self.channel.reshape(self.H_angles_pol.size,self.V_angles_pol.size,-1)
        self.channel_re = self.channel_re.transpose(1,0,2) 
        self.short_channel = self.channel_re[180-self.V_range:180+self.V_range,180-self.H_range:180+self.H_range]  
        self.short_channel = self.short_channel.reshape(self.V_range*self.H_range*4,-1)
        

    def pattern_3GPP(self):
        H_angle = np.arange(0,360)
        H_angle= (H_angle>180)*(-360) + H_angle
        H_pattern = -12*(H_angle/65)*(H_angle/65)
        H_pattern = np.clip(H_pattern,-30,0)
        V_pattern = H_pattern
        self.H_pattern = H_pattern
        self.V_pattern = V_pattern
    
    def choose_dft_beam(self,index):
        idx = np.array(index)
        assert(idx.any() in range(0,self.Beam_num))
        self.weight = self.dft_beams[:,idx]

    def load_beam(self,data):
        beam_data = data
        length = beam_data.shape[0]
        beam_data = beam_data.reshape(length,-1,2)
        beam_data = beam_data[:,:,0]+1j*beam_data[:,:,1]
        self.Beams = beam_data
    
    def get_beam_from_srs(self,srs_p1,srs_p2):
        srs_p1 = srs_p1.transpose(1,0)
        srs_p2 = srs_p2.transpose(1,0)
        srs_p1 = np.expand_dims(srs_p1,-2)
        srs_p2 = np.expand_dims(srs_p2,-2)
        Coma_p1 = np.conj(srs_p1.transpose(0,2,1))@srs_p1     #srs.H@srs
        Coma_p2 = np.conj(srs_p2.transpose(0,2,1))@srs_p2     #srs.H@srs

        Coma_p1 = np.average(Coma_p1,axis=(0))
        Coma_p2 = np.average(Coma_p2,axis=(0))

        s1,v1,d1 = np.linalg.svd(Coma_p1)
        s2,v2,d2 = np.linalg.svd(Coma_p2)
        s1 = s1.T
        s2 = s2.T
        beam_p1 = np.transpose(s1[0:2],(1,0))
        beam_p2 = np.transpose(s1[0:2],(1,0))
        beam_p1 = np.conj(beam_p1)
        beam_p2 = np.conj(beam_p2)
        return beam_p1,beam_p2
    
    def cal_angle(self,weight):
        gain = self.short_channel@weight
        gain = abs(gain)

        gain_idx = np.argmax(gain,0) 

        idx = np.unravel_index(gain_idx,(self.V_range*2,self.H_range*2))  
        angle_H = self.H_angles_pol[idx[1]+180-self.H_range]
        angle_V = self.V_angles_pol[idx[0]+180-self.V_range]
        
        return angle_H,angle_V

    def cal_gain(self,weight,same_max=False,Find_angle_only=False):
        weight = weight.reshape(int(self.numVant/self.numCombiningV),self.numHant,-1)
        weight = weight.repeat(self.numCombiningV,axis=0)
        weight = weight.reshape(self.numVant*self.numHant,-1)
        if(same_max):
            maxAmp = np.max(abs(weight))
            max_div = 1./(maxAmp + 1e-7)
            if maxAmp == 0:
                max_div = 0
            weight = weight*max_div
        else:
            maxAmp = np.max(abs(weight),0)
            nonzero_num = np.sum(np.abs(weight[:,0])!=0)
            if nonzero_num == 0:
                nonzero_num = 1
            max_div = 1./(maxAmp + 1e-7)
            max_div[maxAmp == 0] = 0 
            c = np.diag(max_div)/np.sqrt(nonzero_num)
            weight = weight@c

        data = self.channel@weight
        self.origin_result = data
        gain = 20*np.log10(abs(data)+1e-7)
        gain[gain<-30] = -30
        gain_max = np.max(gain,1) 
        gain_idx = np.argmax(gain,1) 
        gain_max = gain_max.reshape(self.H_angles_pol.size,self.V_angles_pol.size).T
        gain_idx = gain_idx.reshape(self.H_angles_pol.size,self.V_angles_pol.size).T   
        self.gain_max = gain_max  
        
        if Find_angle_only:
            gain_max_ = gain_max[90:270,90:270]
            max_idx = np.unravel_index(np.argmax(gain_max_),gain_max_.shape)
            angle_H = self.H_angles_pol[max_idx[1]+90]
            angle_V = self.V_angles_pol[max_idx[0]+90]
            return angle_H,angle_V

        gain_ = gain.reshape(self.H_angles_pol.size,self.V_angles_pol.size,-1)
        gain_ = gain_[90:270,90:270,:]
        direction = [np.unravel_index(np.argmax(gain_[:,:,i]),gain_[:,:,i].shape) for i in range(0,gain_.shape[2])]
        direction = np.array(direction)

        self.direcition_H = direction[:,0] - 90
        self.direcition_V = direction[:,1] - 90
        
        if(self.multile_line == True):
            gain = gain.reshape(self.H_angles_pol.size,self.V_angles_pol.size,-1)
            gain = np.transpose(gain,(1,0,2))
            gain_ = gain[90:270,90:270,:]
            max_idx = np.unravel_index(np.argmax(gain_),gain_.shape)
            max_idx = max_idx

            H_power = np.zeros((361,gain.shape[2]),dtype=np.float64)
            V_power = np.zeros((361,gain.shape[2]),dtype=np.float64)
            for i in range(0,gain.shape[2]):                
                H_power[:,i] = gain[max_idx[0]+90,:,i]
                V_power[:,i] = gain[:,max_idx[1]+90,i]
        else:
            gain_max_ = gain_max[90:270,90:270]
            max_idx = np.unravel_index(np.argmax(gain_max_),gain_max_.shape)
            H_power = gain_max[max_idx[0]+90,:]
            V_power = gain_max[:,max_idx[1]+90]
            H_power = np.expand_dims(H_power,axis=1)
            V_power = np.expand_dims(V_power,axis=1)
            
            
        
        gain_max = gain_max[180-self.V_range:180+self.V_range+1, 180-self.H_range:180+self.H_range+1]
        return gain_max,H_power,V_power
    
    def init_draw(self):
        self.weight = self.dft_beams
        gain_max,H_power,V_power = self.cal_gain(self.weight)
        self.draw_P = go.Contour(
            z = list(np.zeros(self.H_size*self.V_size)),
            x = list(self.H_angles_power),
            y = list(self.V_angles_power),
            contours=dict(
                showlabels = True, # show labels on contours
                labelfont = dict( # label font properties
                size = 8,
                color = 'white'
            )),
            colorbar=dict(
                lenmode="pixels", len=200,
                y = 0.25
            ),
            hovertemplate = "H:%{x},V:%{y},dB:%{z}",
            colorscale = [[0, 'royalblue'], [0.5, 'mediumturquoise'], [1, 'yellow']]
        )

        self.draw_scatter = go.Scatter(x=self.direcition_H,y=self.direcition_V,mode="markers",marker_size=10)
        self.draw_ploar = go.Scatterpolar(
            r = list(self.H_angles_pol*0),
            theta=list(self.H_angles_pol),
            mode = 'lines',
            name = "H",
            customdata=list(self.H_angles_pol*0),
            hovertemplate = "theta is %{theta}<br>power is %{r:.2f}<br>%{customdata}"
        )
        
    def get_polar_add_msg(self,data):
        max_H = np.max(data)
        max_H_angle = self.H_angles_pol[np.argmax(data)]
        msg = "max power is %f dB at theta %d" %(max_H,max_H_angle)
        customdata = list()
        customdata.append(msg)
        return customdata*361
        
    def draw_weight(self,weight,same_max = False):
        """draw figs from weight

        Parameters
        ----------
        weight : 2-D array of shape(Ants* nBeam) or 1-D array of shape(Ants)
            Weight need to draw, you can input Ants num equal Ants_all to draw one pol or equal Ants_all_2pol to draw two pols 
        same_max : bool, optional
            Set True to norm all beams with same parameters, by default False

        Returns
        -------
        Fig1,Fig2
            Figs by plotly, fig1 for 2D Contour View, fig2 for Polar coordinates View
        """
        assert(weight.shape[0] == self.Ants_num or weight.shape[0] == self.Ants_num*2), print("input shape error")
        if (len(weight.shape)) == 1:
            weight = np.expand_dims(weight,axis=-1)
        if weight.shape[0] == self.Ants_num:
            self.display_pol = 1
            fig1 = make_subplots(rows=1,cols=1)
            fig2 = make_subplots(rows=1,cols=2,specs=[[{"type":"polar"},{"type":"polar"}]],subplot_titles=("H pol1","V pol1"))
            fig1.update_layout(width=800,height=600,xaxis={'title':"水平角",'titlefont':{'size':20}},
            yaxis={'title':"垂直角",'titlefont':{'size':20}})
            fig2.update_layout(title='Polar coordinates View',width=800,height=600)
        else: 
            self.display_pol = 2
            fig1 = make_subplots(rows=2,cols=1,subplot_titles=("pol1","pol2"))
            fig2 = make_subplots(rows=2,cols=2,specs=[[{"type":"polar"},{"type":"polar"}],[{"type":"polar"},{"type":"polar"}]],subplot_titles=("H pol1","V pol1","H pol2","V pol2"))
            fig1.update_layout(title='2D Contour View',width=800,height=1000,xaxis={'title':"水平角",'titlefont':{'size':20}},
            yaxis={'title':"垂直角",'titlefont':{'size':20}})
            fig2.update_layout(title='Polar coordinates View',width=800,height=1000)
        weight_pol1 = weight[0:self.Ants_num,:]
        gain_max,H_power,V_power = self.cal_gain(weight_pol1,same_max)
        fig1.add_trace(self.draw_P.update(z=gain_max),row=1,col=1)
        fig1.add_trace(self.draw_scatter.update(x=self.direcition_H,y=self.direcition_V),row=1,col=1)
        length = H_power.shape[1]
        for i in range(0,length):
            fig2.add_trace(self.draw_ploar.update(r=H_power[:,i],name="H Beam "+str(i),customdata=self.get_polar_add_msg(H_power[:,i])),row=1,col=1)
            fig2.add_trace(self.draw_ploar.update(r=V_power[:,i],name="V Beam "+str(i),customdata=self.get_polar_add_msg(V_power[:,i])),row=1,col=2)
        if self.display_pol == 2:
            weight_pol2 = weight[self.Ants_num:self.Ants_num*2,:]
            gain_max,H_power,V_power = self.cal_gain(weight_pol2,same_max)
            fig1.add_trace(self.draw_P.update(z=gain_max),row=2,col=1)
            fig1.add_trace(self.draw_scatter.update(x=self.direcition_H,y=self.direcition_V),row=2,col=1)
            length = H_power.shape[1]
            for i in range(0,length):
                fig2.add_trace(self.draw_ploar.update(r=H_power[:,i],name="H Beam "+str(i),customdata=self.get_polar_add_msg(H_power[:,i])),row=2,col=1)
                fig2.add_trace(self.draw_ploar.update(r=V_power[:,i],name="V Beam "+str(i),customdata=self.get_polar_add_msg(V_power[:,i])),row=2,col=2)     
        return fig1,fig2 
        
# class draw_line():
#     def __init__(self,title,x_name,y_name):
#         self.title = title
#         self.x_name = x_name
#         self.y_name = y_name
#         self.new_fig()
    
#     def new_fig(self):
#         self.fig = make_subplots(rows=1,cols=1,specs=[[{"type":"xy"}]])
#         self.fig.update_layout(
#             title=self.title,
#             width=1000,
#             height=700,
#             xaxis={'title':self.x_name,'titlefont':{'size':30}},
#             yaxis={'title':self.y_name,'titlefont':{'size':30}}
#             )
    
#     def add_line(self,x,y,name,msg=None):
#         x = list(x)
#         y = list(y)
#         customdata = list()
#         if msg: 
#             customdata.append("")
#         else:
#             customdata.append(msg)
#         customdata = customdata*len(x)
#         line = go.Scatter(x = x,y = y,mode = 'lines',name = name,
#                     customdata=customdata,
#                     hovertemplate = self.x_name+":%{x}<br>"+self.y_name+":%{y}"+"<br>%{customdata}",
#                 )
#         self.fig.add_trace(line)

#     def get_fig(self):
#         return self.fig
    
class draw_line():
    def __init__(self, title, x_name, y_name):
        self.title = title
        self.x_name = x_name
        self.y_name = y_name
        self.marker_size = 10
        self.colors = [
            'rgb(237, 188, 255)', 'rgb(163, 205, 234)', 'rgb(255, 124, 128)', 'rgb(95, 151, 198)',
            'rgb(255, 192, 0)', 'rgb(141, 209, 198)', 'rgb(189, 186, 219)', 
            'rgb(31, 119, 180)', 'rgb(255, 127, 14)', 'rgb(44, 160, 44)', 'rgb(214, 39, 40)',
            'rgb(148, 103, 189)', 'rgb(140, 86, 75)', 'rgb(227, 119, 194)', 'rgb(127, 127, 127)',
            'rgb(188, 189, 34)', 'rgb(23, 190, 207)'
        ]
        self.line_index = 0
        self.new_fig()

    def new_fig(self):
        self.fig = make_subplots(rows=1, cols=1, specs=[[{"type": "xy"}]])
        self.fig.update_layout(
            # title={'text': self.title, 'font': {'size': 24, 'color': 'black'}},
            width=1200,
            height=800,
            xaxis={'title': self.x_name, 'titlefont': {'size': 32, 'color': 'black'}},
            yaxis={'title': self.y_name, 'titlefont': {'size': 32, 'color': 'black'}},
            legend={'font': {'size': 30}, 'x': 0.05, 'y': 0.95, 'bgcolor': 'rgba(255, 255, 255, 0.8)', 'bordercolor': 'white', 'borderwidth': 1, 'itemwidth':50.0},
            plot_bgcolor='white'
        )
        self.fig.update_xaxes(
            showline=True,
            showgrid=False,
            showticklabels=True,
            linewidth=2,
            linecolor='black',
            mirror=True, 
            title_standoff=5,
            ticks='inside',
            ticklen=5,        # 设置刻度标记的长度（像素）
            tickwidth=2,      # 设置刻度标记的宽度（像素）
            tickfont=dict(size=32)
        )
        self.fig.update_yaxes(
            showline=True,
            showgrid=False,
            showticklabels=True,
            linewidth=2,
            linecolor='black',
            mirror=True, 
            title_standoff=5,
            ticks='inside',
            ticklen=5,        # 设置刻度标记的长度（像素）
            tickwidth=2,      # 设置刻度标记的宽度（像素）
            tickfont=dict(size=32)
        )

    def add_line(self, x, y, name,mode='lines+markers',line_style_change=None,marker_symbols_change=None):
        x = list(x)
        y = list(y)

        if line_style_change is None:
            line_style = ['solid', 'dot', 'dash', 'longdash', 'dashdot', 'longdashdot']
        else:
            line_style = line_style_change
        if marker_symbols_change is None:
            marker_symbols = ['circle', 'square', 'diamond', 'cross', 'x', 'star']
        else:
            marker_symbols = marker_symbols_change

        line_color = self.colors[self.line_index % len(self.colors)]
        line_width = 4
        line = go.Scatter(
            x=x,
            y=y,
            mode=mode,
            name=name,
            hovertemplate=self.x_name + ":%{x}<br>" + self.y_name + ":%{y}",
            line={'dash': line_style[self.line_index % len(line_style)], 'color': line_color, 'width': line_width},
            marker={'symbol': marker_symbols[self.line_index % len(marker_symbols)], 'size': self.marker_size, 'color': line_color}
        )
        self.fig.add_trace(line)
        self.line_index += 1
        
    def add_bar(self, x, y, name):
        x = list(x)
        y = list(y)

        line_color = self.colors[self.line_index % len(self.colors)]
        line = go.Bar(
            x=x,
            y=y,
            name=name,
            hovertemplate=self.x_name + ":%{x}<br>" + self.y_name + ":%{y}",
            marker={'color': line_color}
        )
        self.fig.add_trace(line)
        self.line_index += 1  
              
    def get_fig(self):
        return self.fig
   
   
def set_fig(fig):
    fig.update_layout(width=1200,height=800,legend={'x': 0.76, 'y': 0.98})   
    # fig.fig.update_layout(yaxis_range=[0.5, 1.2])     
    fig.update_layout(yaxis_tickmode='auto')
    fig.update_xaxes(
        # range=[0,1.1],
        showline=True,
        showgrid=False,
        showticklabels=True,
        ticks='inside',
        ticklen=5,        # 设置刻度标记的长度（像素）
        tickwidth=2,      # 设置刻度标记的宽度（像素）
        tickfont=dict(size=32)
    )
    fig.update_yaxes(
        range=[0,1],
        showline=True,
        showgrid=False,
        showticklabels=True,
        ticks='inside',
        ticklen=5,        # 设置刻度标记的长度（像素）
        tickwidth=2,      # 设置刻度标记的宽度（像素）
        tickfont=dict(size=32)
    )
     
### 绘制ue分布
# to redo
import matplotlib.pyplot as plt
def draw_ue_loc(ueid = 0):
    plt.figure(figsize = (16,16))
    s3 = np.sqrt(3)
    gx = 250*2/s3+250/s3
    gNB_loc = np.array([[0,0],[gx,250],[0,500],[-gx,250],[-gx,-250],[0,-500],[gx,-250]])
    gNB_locx = gNB_loc[:,0]
    gNB_locy = gNB_loc[:,1]
    gNB_cellx = np.array([gNB_locx+gx/3,gNB_locx-gx/3,gNB_locx])
    gNB_cellx = gNB_cellx.transpose(1,0)
    gNB_cellx = gNB_cellx.reshape(-1)
    gNB_celly = np.array([gNB_locy+250/3,gNB_locy+250/3,gNB_locy-500/3])
    gNB_celly = gNB_celly.transpose(1,0)
    gNB_celly = gNB_celly.reshape(-1)
    print(gNB_cellx,gNB_celly)
    for i in range(0,21):
        line_x = np.array([gx/3,gx/3,0,-gx/3,-gx/3,0,gx/3]) + gNB_cellx[i]
        line_y = np.array([250/3,-250/3,-500/3,-250/3,250/3,500/3,250/3]) + gNB_celly[i]
        plt.plot(line_x,line_y)

    plt.scatter(gNB_loc[:,0],gNB_loc[:,1],s=30,c='r')
    plt.show()
    
class draw_ue():
    def __init__(self,isd=500,bs_loc=None):
        s3 = np.sqrt(3)
        gx = isd/s3+isd/2/s3

        if bs_loc is None:
            gNB_loc = np.array([[0,0],[gx,isd/2],[0,isd],[-gx,isd/2],[-gx,-isd/2],[0,-isd],[gx,-isd/2]])
        else:
            gNB_loc = bs_loc
        gNB_locx = gNB_loc[:,0]
        gNB_locy = gNB_loc[:,1]
        gNB_locz = gNB_loc[:,2]
        gNB_cellx = np.array([gNB_locx+gx/3,gNB_locx-gx/3,gNB_locx])
        gNB_cellx = gNB_cellx.transpose(1,0)
        gNB_cellx = gNB_cellx.reshape(-1)
        gNB_celly = np.array([gNB_locy+250/3,gNB_locy+250/3,gNB_locy-500/3])
        gNB_celly = gNB_celly.transpose(1,0)
        gNB_celly = gNB_celly.reshape(-1)
        gNB_cellz = np.array([gNB_locz,gNB_locz,gNB_locz])
        gNB_cellz = gNB_cellz.transpose(1,0)
        gNB_cellz = gNB_cellz.reshape(-1)
        
        self.gx = gx
        self.gNB_locx = gNB_locx
        self.gNB_locy = gNB_locy
        self.gNB_locz = gNB_locz
        self.gNB_cellx = gNB_cellx
        self.gNB_celly = gNB_celly
        self.gNB_cellz = gNB_cellz
        self.reset()
                
    def reset(self):
        fig = go.Figure()

        for i in range(0,21):
            line_x = np.array([self.gx/3,self.gx/3,0,-self.gx/3,-self.gx/3,0,self.gx/3]) + self.gNB_cellx[i]
            line_y = np.array([250/3,-250/3,-500/3,-250/3,250/3,500/3,250/3]) + self.gNB_celly[i]
            line_z = np.array([0,0,0,0,0,0,0])
            fig.add_trace(go.Scatter3d(x=line_x, y=line_y, z=line_z, mode='lines',marker=dict(size=10, color='blue'),showlegend=False))

            fig.add_trace(go.Scatter3d(x=self.gNB_locx, y=self.gNB_locy[:], z=self.gNB_locz[:], mode='markers', marker=dict(size=3, color='red'),showlegend=False))

            for i in range(0,7):
                fig.add_trace(go.Scatter3d(x=[self.gNB_locx[i], self.gNB_locx[i]], y=[self.gNB_locy[i], self.gNB_locy[i]], z=[self.gNB_locz[i], 0], mode='lines',marker=dict(size=30, color='rgba(0,191,255,0.1)'),showlegend=False))

            fig.update_layout(scene=dict(xaxis=dict(showgrid=False), yaxis=dict(showgrid=False), zaxis=dict(showgrid=False,range=[0, 50])),
                            width=1000,
                            height=1000)
        
        self.fig = fig
        
    def add_ue(self,ue_loc,name):
        self.fig.add_trace(go.Scatter3d(x=ue_loc[:,0], y=ue_loc[:,1], z=ue_loc[:,2], mode='lines',marker=dict(size=20, color='black'),name=name))