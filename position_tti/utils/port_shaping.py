from cmath import cos, sin
import numpy as np
from math import pi
from copy import deepcopy

# 3GPP TS 38.214 Table 5.2.2.2.1-2
port_conf = {'4':{ '2':{'N1N2':(2,1),'O1O2':(4,1)}},\
             '8':{ '2':{'N1N2':(2,2),'O1O2':(4,4)},\
                   '4':{'N1N2':(4,1),'O1O2':(4,1)}},\
             '12':{'3':{'N1N2':(3,2),'O1O2':(4,4)},\
                   '6':{'N1N2':(6,1),'O1O2':(4,1)}},\
             '16':{'4':{'N1N2':(4,2),'O1O2':(4,4)},\
                   '8':{'N1N2':(8,1),'O1O2':(4,1)}},\
             '24':{'4':{'N1N2':(4,3),'O1O2':(4,4)},\
                   '6':{'N1N2':(6,2),'O1O2':(4,4)},\
                   '12':{'N1N2':(12,1),'O1O2':(4,1)}},\
             '32':{'4':{'N1N2':(4,4),'O1O2':(4,4)},\
                   '8':{'N1N2':(8,2),'O1O2':(4,4)},\
                  '16':{'N1N2':(16,1),'O1O2':(4,1)}}}

# all_ports = [4,8,12,16,24,32]



def check_one_pattern(pattern, numVant=4, numHant=8):
      (N_v, N_h) = pattern
      metric = ((numVant % N_v == 0) and (numHant % N_h == 0))
      if metric == True:
            return True
      else:
            return False
      
    
    
def generate_available_pattern(conf=port_conf, numVant=4, numHant=8, port=None):
      '''
            To generate available logical antenna pattern based on physical pattern
            WARNING: Please Handle Antenna Combinning in advance!!
                 i.e. numVant = numVant/nunVcombine
      '''
      available_conf = deepcopy(conf)
      target_list = [(1,1)]
      if port is not None:
            if str(port) not in available_conf.keys():
                  raise ValueError(f"User specified port number {port} is not available")  
            for one_key in available_conf.keys():
                  if not (one_key == port):
                        available_conf.pop(one_key)

      for port_key in available_conf.keys():
            certain_port_conf = available_conf[port_key]
            for key_dim in certain_port_conf.keys():
                  (N1,N2) =  certain_port_conf[key_dim]['N1N2']
                  if check_one_pattern((N1,N2), numVant, numHant):
                        target_list.append((N1,N2))
                  if (not (N1==N2)) and check_one_pattern((N2,N1), numVant, numHant):
                        target_list.append((N2,N1))
      return target_list
                  
                  
def port_shaping_with_one_beam(selected_beam, conf=port_conf, numVant=4, numHant=8, pattern=(1,2)):
      '''
            Port shaping based on 3GPP Type-I CSI-RS Codebook
            Using the first selected beam
            Basically support 4、8、16、32 Ports
            WARNING: Please Handle Antenna Combinning in advance!!
                 i.e. numVant = numVant/nunVcombine
        
      '''
      if check_one_pattern(pattern,numVant, numHant) ==  False:
            raise ValueError(f"User specified pattern {pattern} is not available for phsical ({numVant},{numHant})") 
      (N_v, N_h) = pattern
      port_num = 2*N_v*N_h
      best_beam = selected_beam[:,0]
      if port_num == 2:
            pol_beam = np.kron(np.eye(2,dtype=np.complex64), best_beam)
      else:
            paneled_beam = generate_beam_pattern(beam_weight=best_beam,numVant=numVant,numHant=numHant,H_split=N_h,V_split=N_v)
            print(f"pattern{N_v},{N_h}")
            # paneled_beam = generate_beam_pattern(best_beam,4,8,1,2)
            pol_beam = np.kron(np.eye(2,dtype=np.complex64), paneled_beam)
      return pol_beam
      
      
def port_shaping(selected_beam, conf=port_conf, numVant=4, numHant=8, patterns=[(1,2)]):
      '''
            Port shaping based on 3GPP Type-I CSI-RS Codebook
            Using the first selected beam
            Basically support 4、8、16、32 Ports
            WARNING: Please Handle Antenna Combinning in advance!!
                 i.e. numVant = numVant/nunVcombine
        
      '''
      patterns=(1,2)
      if check_one_pattern(pattern,numVant, numHant) ==  False:
            raise ValueError(f"User specified pattern {pattern} is not available for phsical ({numVant},{numHant})") 
      (N_v, N_h) = pattern
      port_num = 2*N_v*N_h
      best_beam = selected_beam[:,0]
      if port_num == 2:
            pol_beam = np.kron(np.eye(2,dtype=np.complex64), best_beam)
      else:
            paneled_beam = generate_beam_pattern(beam_weight=best_beam,numVant=numVant,numHant=numHant,H_split=N_h,V_split=N_v)
            print(f"pattern{N_v},{N_h}")
            # paneled_beam = generate_beam_pattern(best_beam,4,8,1,2)
            pol_beam = np.kron(np.eye(2,dtype=np.complex64), paneled_beam)
      return pol_beam    
          
          
def check_orthogonality():
    pass
    
if __name__ == '__main__':
    from generate_beam_pattern import generate_beam_pattern
    from generate_beam import generate_dft_beams

    li = generate_available_pattern()
    print(li)
    from generate_beam import generate_dft_beams
    beam_32 = generate_dft_beams()
    for pa in li:
      res = port_shaping_with_one_beam(selected_beam=beam_32,pattern=pa)
    print(res)