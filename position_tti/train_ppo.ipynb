{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os,sys\n", "sys.path.append(os.getcwd())\n", "from func.BM_functions import *\n", "from func.system_functions import *\n", "from func.process import *\n", "from func.setsumi_env import setsumi_env\n", "from utils import *\n", "import torch\n", "\n", "from train_ppo import seed_torch,parse_args,train,infer"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 训练"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "10 100 3\n", "expand H: (10, 4000, 17, 4, 64)\n", "expand ue loc: (10, 4000, 3)\n", "expand rsrp: (10, 4000, 3)\n", "srs_choose_all:(10, 4000, 17, 4)\n", "simulate srs:(10, 4000, 17, 4, 64)\n", "simulate coma:(10, 4000, 1, 32, 32)\n", "simulate rsrp:(10, 4000, 3)\n", "end load_H\n"]}], "source": ["path = os.getcwd()\n", "path ='./uma/uma_nlos_3/'\n", "\n", "seed = 777\n", "seed_torch(seed)\n", "env = setsumi_env(path=path, seed=seed, max_length=4000,mu=0.99,gNB_tti=1,select_subs=1,select_ports=1,test_real=False)  "]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["cuda\n", "cuda\n"]}], "source": ["modelpath = './mod/ppo_uma_v3/'\n", "seed = 777\n", "action_dim = 2\n", "state_dim = 18*2\n", "time_length = env.max_time - 1\n", "start_time = 1000\n", "end_time = start_time+time_length\n", "start_time = start_time//env.gNB_tti\n", "end_time = end_time//env.gNB_tti\n", "\n", "args = parse_args(args=[])\n", "args.seed = seed\n", "args.device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "args.train_nUE = 10\n", "args.batch_size = 10\n", "args.num_frame = 20000\n", "args.optim_frame = 20\n", "args.done_cycle = env.max_length//env.gNB_tti\n", "args.change_ue_cycle = 2000// env.gNB_tti\n", "args.train_log_cycle = args.change_ue_cycle\n", "\n", "# ppo\n", "args.gamma = 0.99\n", "args.eps = 0.2\n", "args.epochs = 10\n", "args.lmbda = 0.99\n", "args.actor_lr = 1e-3\n", "args.critic_lr = 3e-4\n", "            \n", "print(args.device)\n", "            "]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["reward: [0.48802847 0.597793   0.46726307 0.53754795 0.52368796 0.6486586\n", " 0.5473777  0.54461795 0.5430322  0.42981252]\n", "aod err: [ 8.20883705  8.99932925 21.07815517  3.94614707  3.13682437  2.02999621\n", "  7.37239595  2.28490282  7.57489359 19.0624272 ]\n", "eod err: [4.63731807 2.66007838 1.74808774 1.53899673 1.73313905 4.22259807\n", " 2.88256211 2.02802188 2.06680242 4.07953443]\n", "pos err: [-4.267068  -3.5440543 -6.177533  -1.3040426 -1.3385987 -2.1580985\n", " -2.2679143 -1.2240319 -2.834445  -8.513378 ]\n", "pos all err: [-4.267068  -3.5440543 -6.177533  -1.3040426 -1.3385987 -2.1580985\n", " -2.2679143 -1.2240319 -2.834445  -8.513378 ]\n", "averg aod eod pos pos_all err: 8.369390868009146 2.759713888352183 -3.3629165 -3.3629165\n", "reward: [0.4815821  0.60718596 0.39518294 0.49668613 0.5349051  0.65802336\n", " 0.5591496  0.54742277 0.5264738  0.43091032]\n", "aod err: [ 3.57101196 11.19835475 14.99981515  5.88096101  5.29542264  4.22768746\n", "  2.38379373  2.63155412  9.26002698 19.93973259]\n", "eod err: [ 2.5835099   2.58882901  9.06844964  4.67315361  3.46155804  2.4848244\n", " 10.05951447  4.00945649  2.26389773  2.9285031 ]\n", "pos err: [-2.2056475 -4.3407154 -5.4033837 -2.292618  -2.2808673 -2.21421\n", " -3.0756936 -1.8618743 -3.4499846 -8.819233 ]\n", "pos all err: [-2.2056475 -4.3407154 -5.4033837 -2.292618  -2.2808673 -2.21421\n", " -3.0756936 -1.8618743 -3.4499846 -8.819233 ]\n", "averg aod eod pos pos_all err: 7.9388360387802965 4.412169638961811 -3.5944226 -3.5944226\n", "reward: [0.45076582 0.5477843  0.31504172 0.478316   0.5023626  0.66092443\n", " 0.42463318 0.5022384  0.5089979  0.5245892 ]\n", "aod err: [ 5.74149387  7.8825694  16.55659057  3.02336988  2.39666704  2.11693973\n", "  3.81592316  2.0108063   6.20402551 16.70367092]\n", "eod err: [ 6.90210401  5.7408696  15.20384811 10.05372607  7.50068117  5.54444783\n", " 15.60940733  8.19618266  5.98942896  3.37874973]\n", "pos err: [-4.288361  -3.7818317 -6.9463425 -3.2386353 -2.8300605 -2.6755474\n", " -4.7494645 -3.2113655 -3.243733  -7.5833135]\n", "pos all err: [-4.288361  -3.7818317 -6.9463425 -3.2386353 -2.8300605 -2.6755474\n", " -4.7494645 -3.2113655 -3.243733  -7.5833135]\n", "averg aod eod pos pos_all err: 6.645205639339352 8.411944547187233 -4.2548656 -4.2548656\n", "reward: [0.46660358 0.5459051  0.33919758 0.5085801  0.50970745 0.66336566\n", " 0.45160937 0.5145011  0.5278859  0.53370976]\n", "aod err: [ 5.87216846  6.67647499 16.25010653  2.16374167  2.20132037  1.97666837\n", "  3.58545629  2.50047637  5.11532874 15.3130892 ]\n", "eod err: [ 5.30026559  4.34339329 13.50021187  8.20017923  6.21591685  3.86147513\n", " 14.41640623  6.75597699  4.92133759  2.62396895]\n", "pos err: [-3.9160519 -3.1130507 -6.601367  -2.6129427 -2.4029095 -2.0403252\n", " -4.4156303 -2.7706347 -2.6410644 -6.894812 ]\n", "pos all err: [-3.9160519 -3.1130507 -6.601367  -2.6129427 -2.4029095 -2.0403252\n", " -4.4156303 -2.7706347 -2.6410644 -6.894812 ]\n", "averg aod eod pos pos_all err: 6.165483099765549 7.013913171401017 -3.7408788 -3.7408788\n", "reward: [0.28614134 0.47096485 0.16431566 0.37024444 0.40565673 0.5394567\n", " 0.23346615 0.39013007 0.40787777 0.48958883]\n", "aod err: [ 2.88744396  9.54054782 12.24721548  4.13562952  4.83172494  3.85348721\n", "  2.01412833  2.73188369  7.70912298 17.29392326]\n", "eod err: [15.30306509 11.52563659 25.27598872 15.36725801 14.52555899 12.33892636\n", " 24.5746703  15.09720444 11.33441562  7.47073188]\n", "pos err: [-7.173853  -5.8387494 -8.69899   -4.8821983 -5.4582405 -5.718005\n", " -7.1741266 -5.7264595 -5.1240096 -8.488156 ]\n", "pos all err: [-7.173853  -5.8387494 -8.69899   -4.8821983 -5.4582405 -5.718005\n", " -7.1741266 -5.7264595 -5.1240096 -8.488156 ]\n", "averg aod eod pos pos_all err: 6.724510718088799 15.281345599672857 -6.4282784 -6.4282784\n", "reward: [0.26406902 0.45095465 0.1411696  0.32177567 0.36070746 0.45749488\n", " 0.19149816 0.35253388 0.35145947 0.43526292]\n", "aod err: [ 2.14606485 13.66136967  8.470973    7.6007656   7.96576935  6.77319406\n", "  3.57162424  5.04019919 11.52472905 20.01743251]\n", "eod err: [16.49600577 14.44317011 27.37786529 16.68589876 16.46682772 14.12881179\n", " 26.97011065 17.49184214 13.46121732  7.23449571]\n", "pos err: [-7.588057  -7.677346  -8.87543   -5.5803795 -6.48831   -6.8799644\n", " -7.9120464 -6.8072677 -6.5605636 -9.517167 ]\n", "pos all err: [-7.588057  -7.677346  -8.87543   -5.5803795 -6.48831   -6.8799644\n", " -7.9120464 -6.8072677 -6.5605636 -9.517167 ]\n", "averg aod eod pos pos_all err: 8.677212151369762 17.075624526898174 -7.388653 -7.388653\n", "reward: [0.29472098 0.5004474  0.15447907 0.36465776 0.35245916 0.4452391\n", " 0.1965177  0.3603301  0.37572598 0.4685581 ]\n", "aod err: [ 2.58501791 12.44139868  7.13943775  6.69296783  8.91880965  7.59834631\n", "  5.68828291  6.09931498 11.35613788 18.90677247]\n", "eod err: [15.4252108  11.60497429 25.71818597 14.76682033 16.5605793  13.63955091\n", " 26.4767427  16.99094364 12.2344895   5.32683749]\n", "pos err: [-7.147202  -6.605426  -8.261803  -4.9439864 -6.6927657 -6.8757124\n", " -7.8762555 -6.7458763 -6.200666  -8.805146 ]\n", "pos all err: [-7.147202  -6.605426  -8.261803  -4.9439864 -6.6927657 -6.8757124\n", " -7.8762555 -6.7458763 -6.200666  -8.805146 ]\n", "averg aod eod pos pos_all err: 8.742648637061036 15.874433492656792 -7.015484 -7.015484\n", "reward: [0.34805742 0.53849834 0.17074114 0.41498604 0.44616002 0.54774004\n", " 0.25878793 0.43613648 0.44779104 0.48504385]\n", "aod err: [ 2.38497427 10.42787475  7.83746643  5.15482357  5.45566757  5.31525304\n", "  3.33827136  3.59301544  8.7019832  18.3137295 ]\n", "eod err: [12.4102913   8.51464491 24.40131455 12.58566685 11.87237475 10.471807\n", " 23.44167837 13.20628289  8.59575984  4.47908468]\n", "pos err: [-5.8320193 -5.260205  -7.954471  -4.1591763 -4.6677704 -5.203724\n", " -6.901812  -5.123244  -4.585238  -8.475934 ]\n", "pos all err: [-5.8320193 -5.260205  -7.954471  -4.1591763 -4.6677704 -5.203724\n", " -6.901812  -5.123244  -4.585238  -8.475934 ]\n", "averg aod eod pos pos_all err: 7.052305913570233 12.997890513824697 -5.8163595 -5.8163595\n", "reward: [0.40298995 0.5511055  0.22274344 0.46906444 0.4892719  0.58364075\n", " 0.2673904  0.48001248 0.49387375 0.5227341 ]\n", "aod err: [ 2.4300789   8.55107165  6.21278477  3.88443811  4.18723643  5.08942882\n", "  5.92387351  3.46639713  7.13839415 16.60215034]\n", "eod err: [10.01350609  6.18957873 21.8018923   9.98906687  8.9093333   7.73582147\n", " 21.97685033 10.4715936   6.5331224   3.25707946]\n", "pos err: [-4.7640243 -4.108066  -7.045529  -3.30258   -3.5543964 -4.1292367\n", " -6.6356726 -4.1456466 -3.6101053 -7.54102  ]\n", "pos all err: [-4.7640243 -4.108066  -7.045529  -3.30258   -3.5543964 -4.1292367\n", " -6.6356726 -4.1456466 -3.6101053 -7.54102  ]\n", "averg aod eod pos pos_all err: 6.348585381309622 10.68778445470008 -4.8836274 -4.8836274\n", "reward: [0.3274106  0.5495012  0.19070306 0.45335367 0.45487002 0.46122122\n", " 0.20540465 0.34060195 0.494067   0.5083846 ]\n", "aod err: [ 6.79897466  9.50085206  4.27913883  4.50574094  6.4439102   8.8447113\n", "  8.84929482  9.50952133  7.01838393 17.48773784]\n", "eod err: [15.62634919  6.89952774 24.1909301  10.61392568 10.80420581 10.77917527\n", " 24.64049745 15.59079805  6.63702444  3.77153065]\n", "pos err: [-7.7548885 -4.5811877 -7.6154623 -3.545265  -4.5287094 -6.1519756\n", " -7.57988   -6.824367  -3.6165202 -7.9711723]\n", "pos all err: [-7.7548885 -4.5811877 -7.6154623 -3.545265  -4.5287094 -6.1519756\n", " -7.57988   -6.824367  -3.6165202 -7.9711723]\n", "averg aod eod pos pos_all err: 8.323826591088633 12.955396438514821 -6.016943 -6.016943\n"]}], "source": ["results = train(args, env, seed, modelpath, action_dim, state_dim, start_time, end_time)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["actor_loss = np.array(results['actor_loss'])\n", "critic_loss = np.array(results['critic_loss'])\n", "reward = np.array(results['reward'])\n", "aod = np.array(results['aod'])\n", "azimuths = np.array(results['azimuths'])\n", "eod = np.array(results['eod'])\n", "elevation = np.array(results['elevation'])\n", "\n", "svd_aod_er = np.array(results['svd_aod_er'])\n", "pmi_aod_er = np.array(results['pmi_aod_er'])\n", "\n", "\n", "reward_infer = np.array(results['reward_infer'])\n", "aod_infer = np.array(results['aod_infer'])\n", "azimuths_infer = np.array(results['azimuths_infer'])\n", "eod_infer = np.array(results['eod_infer'])\n", "elevation_infer = np.array(results['elevation_infer'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fig_r = draw_line(\"aod\",\"训练时间\",\"aod_infer\")\n", "for i in range(env.nUE):\n", "    c = aod_infer[:,i]\n", "    c = c.reshape(-1)\n", "    x = np.arange(len(c))\n", "    fig_r.add_line(x=x,y=c,name=i,mode=\"lines\")\n", "fig_r.fig.show()\n", "\n", "fig_r = draw_line(\"eod\",\"训练时间\",\"eod_infer\")\n", "for i in range(env.nUE):\n", "    c = eod_infer[:,i]\n", "    c = c.reshape(-1)\n", "    x = np.arange(len(c))\n", "    fig_r.add_line(x=x,y=c,name=i,mode=\"lines\")\n", "fig_r.fig.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["view_start = 0\n", "view_length = args.num_frame\n", "view_end = view_start+view_length\n", "\n", "fig_r = draw_line(\"aod\",\"训练时间\",\"aod\")\n", "for i in range(args.batch_size):\n", "    c = aod[view_start:view_end,i]\n", "    c = c.reshape(-1)\n", "    x = np.arange(len(c))\n", "    fig_r.add_line(x=x,y=c,name=i,mode=\"lines\")\n", "fig_r.fig.show()\n", "\n", "fig_r = draw_line(\"aod\",\"训练时间\",\"aod-真实值\")\n", "for i in range(args.batch_size):\n", "    c = aod[view_start:view_end,i] - azimuths[view_start:view_end,i]\n", "    c = c.reshape(-1)\n", "    x = np.arange(len(c))\n", "    fig_r.add_line(x=x,y=c,name=i,mode=\"lines\")\n", "fig_r.fig.show()\n", "\n", "\n", "fig_r = draw_line(\"eod\",\"训练时间\",\"eod\")\n", "for i in range(args.batch_size):\n", "    c = eod[view_start:view_end,i]*env.vangle_bound\n", "    c = c.reshape(-1)\n", "    x = np.arange(len(c))\n", "    fig_r.add_line(x=x,y=c,name=i,mode=\"lines\")\n", "fig_r.fig.show()\n", "\n", "fig_r = draw_line(\"eod\",\"训练时间\",\"eod-真实值\")\n", "for i in range(args.batch_size):\n", "    c = eod[view_start:view_end,i] - elevation[view_start:view_end,i]\n", "    c = c.reshape(-1)\n", "    x = np.arange(len(c))\n", "    fig_r.add_line(x=x,y=c,name=i,mode=\"lines\")\n", "fig_r.fig.show()\n", "\n", "fig_r = draw_line(\"reward\",\"训练时间\",\"reward\")\n", "for i in range(args.batch_size):\n", "    c = reward[view_start:view_end,i]\n", "    c = c.reshape(-1)\n", "    x = np.arange(len(c))\n", "    fig_r.add_line(x=x,y=c,name=i,mode=\"lines\")\n", "fig_r.fig.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["view_start = 0\n", "view_length = args.num_frame\n", "view_end = view_start+view_length\n", "\n", "fig_r = draw_line(\"reward\",\"训练时间\",\"reward\")\n", "c = reward[view_start:view_end]\n", "c = np.average(c,axis=-1)\n", "c = c.reshape(-1)\n", "x = np.arange(len(c))\n", "fig_r.add_line(x=x,y=c,name='avg',mode=\"lines\")\n", "fig_r.fig.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fig_r = draw_line(\"actor_loss\",\"优化次数\",\"actor_loss\")\n", "actor_loss = [x for x in actor_loss if x is not None]\n", "x = np.arange(len(actor_loss))\n", "fig_r.add_line(x=x,y=actor_loss,name=\"actor_loss\",mode=\"lines\")\n", "fig_r.fig.show()\n", "\n", "\n", "fig_r = draw_line(\"critic_loss\",\"优化次数\",\"critic_loss\")\n", "critic_loss = [x for x in critic_loss if x is not None]\n", "x = np.arange(len(critic_loss))\n", "fig_r.add_line(x=x,y=critic_loss,name=\"critic_loss\",mode=\"lines\")\n", "fig_r.fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 平滑"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def moving_average(a, window_size):\n", "    cumulative_sum = np.cumsum(np.insert(a, 0, 0)) \n", "    middle = (cumulative_sum[window_size:] - cumulative_sum[:-window_size]) / window_size\n", "    r = np.arange(1, window_size-1, 2)\n", "    begin = np.cumsum(a[:window_size-1])[::2] / r\n", "    end = (np.cumsum(a[:-window_size:-1])[::2] / r)[::-1]\n", "    return np.concatenate((begin, middle, end))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["view_start = 0\n", "view_length = args.num_frame\n", "view_end = view_start+view_length\n", "\n", "window_size = 51\n", "\n", "fig_r = draw_line(\"aod\",\"训练时间\",\"aod\")\n", "for i in range(args.batch_size):\n", "    c = aod[view_start:view_end,i]\n", "    c = c.reshape(-1)\n", "    c = moving_average(c, window_size)\n", "    x = np.arange(len(c))\n", "    fig_r.add_line(x=x,y=c,name=i,mode=\"lines\")\n", "fig_r.fig.show()\n", "\n", "fig_r = draw_line(\"aod\",\"训练时间\",\"aod-真实值\")\n", "for i in range(args.batch_size):\n", "    c = aod[view_start:view_end,i] - azimuths[view_start:view_end,i]\n", "    c = c.reshape(-1)\n", "    c = moving_average(c, window_size)\n", "    x = np.arange(len(c))\n", "    fig_r.add_line(x=x,y=c,name=i,mode=\"lines\")\n", "fig_r.fig.show()\n", "\n", "fig_r = draw_line(\"reward\",\"训练时间\",\"reward\")\n", "for i in range(args.batch_size):\n", "    c = reward[view_start:view_end,i]\n", "    print(np.average(c[-1000:-1]))\n", "    c = c.reshape(-1)\n", "    c = moving_average(c, window_size)\n", "    x = np.arange(len(c))\n", "    fig_r.add_line(x=x,y=c,name=i,mode=\"lines\")\n", "fig_r.fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 推理"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["path = os.getcwd()\n", "path = path + '/Data/dataset/speed3/'\n", "\n", "seed = 777\n", "seed_torch(seed)\n", "env = setsumi_env(path=path, seed=seed, max_length=1000,mu=0.99,gNB_tti=1,select_subs=17,select_ports=4,test_real=False)  "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["modelpath = './model/ppo/2024-03-30-01-33-07_frame3400_score-1.8507689237594604'\n", "\n", "seed = 777\n", "action_dim = 2\n", "state_dim = 18*2\n", "\n", "time_length = env.max_time - 1\n", "start_time = 0\n", "end_time = start_time+time_length\n", "start_time = start_time//env.gNB_tti\n", "end_time = end_time//env.gNB_tti\n", "\n", "args = parse_args(args=[])\n", "args.seed = seed\n", "args.device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "# 推理数据集UE\n", "# args.global_ues = list(np.arange(env.nUE))\n", "\n", "print(args.device)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results = infer(args, env, seed, modelpath, action_dim, state_dim, start_time, end_time)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reward_infer = np.array(results['reward_infer'])\n", "aod_infer = np.array(results['aod_infer'])\n", "azimuths_infer = np.array(results['azimuths_infer'])\n", "eod_infer = np.array(results['eod_infer'])\n", "elevation_infer = np.array(results['elevation_infer'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fig_r = draw_line(\"aod\",\"训练时间\",\"aod-真实值\")\n", "for i in range(env.nUE):\n", "    c = aod_infer[:,i] -azimuths_infer[:,i]\n", "    c = c.reshape(-1)\n", "    x = np.arange(len(c))\n", "    fig_r.add_line(x=x,y=c,name=i,mode=\"lines\")\n", "fig_r.fig.show()\n", "\n", "fig_r = draw_line(\"eod\",\"训练时间\",\"eod-真实值\")\n", "for i in range(env.nUE):\n", "    c = eod_infer[:,i] -elevation_infer[:,i]\n", "    c = c.reshape(-1)\n", "    x = np.arange(len(c))\n", "    fig_r.add_line(x=x,y=c,name=i,mode=\"lines\")\n", "fig_r.fig.show()\n", "\n", "fig_r = draw_line(\"reward_infer\",\"训练时间\",\"reward_infer\")\n", "for i in range(env.nUE):\n", "    c = reward_infer[:,i]\n", "    c = c.reshape(-1)\n", "    x = np.arange(len(c))\n", "    fig_r.add_line(x=x,y=c,name=i,mode=\"lines\")\n", "fig_r.fig.show()"]}], "metadata": {"kernelspec": {"display_name": "setsumi", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 2}