{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 导入函数库"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/root/miniconda3/envs/setsumi/lib/python3.10/site-packages/scipy/__init__.py:155: UserWarning: A NumPy version >=1.18.5 and <1.25.0 is required for this version of SciPy (detected version 1.26.0\n", "  warnings.warn(f\"A NumPy version >={np_minversion} and <{np_maxversion}\"\n"]}], "source": ["import os,sys\n", "sys.path.append(os.getcwd())\n", "from func.BM_functions import *\n", "from func.system_functions import *\n", "from func.process import *\n", "from func.setsumi_env import setsumi_env\n", "from utils import *\n", "import torch\n", "\n", "from train_td3 import seed_torch,parse_args,train,infer"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def cal_long_rsrp(mu=0.9, nUE=100, max_length=20000, rsrp_srs=None):\n", "    long_rsrp = []\n", "    for ueid in range(nUE):\n", "        for timeid in range(max_length):\n", "            short_rsrp = rsrp_srs[ueid,timeid]\n", "            if long_rsrp:\n", "                long_rsrp.append(mu*long_rsrp[-1]+(1-mu)*short_rsrp)\n", "            else:\n", "                long_rsrp.append(short_rsrp)\n", "    long_rsrp = np.array(long_rsrp)\n", "    long_rsrp = long_rsrp.reshape(nUE, max_length, -1)\n", "    \n", "    return long_rsrp"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def cal_dist_uma_avg(fc, long_rsrp):\n", "    # 计算 LOS 条件下的距离\n", "    dis_los = np.power(10, (-28 -2 * np.log10(fc) - long_rsrp-7.5) / 22)\n", "    \n", "    # 计算 NLOS 条件下的距离\n", "    dis_nlos = np.power(10, (-13.54  -2* np.log10(fc) - long_rsrp-9.5) / 39.08)\n", "    \n", "    # 取 LOS 和 NLOS 距离中的最大值作为平均距离\n", "    dis_avg = np.maximum(dis_los, dis_nlos)\n", "    print(dis_avg.shape)\n", "    return dis_avg\n", "def cal_dist_umi_avg(fc, long_rsrp):\n", "    # 计算 LOS 条件下的距离\n", "    dis_los = np.power(10, (-32.4 - 2 * np.log10(fc) - long_rsrp) / 21)\n", "    \n", "    # 计算 NLOS 条件下的距离\n", "    dis_nlos = np.power(10, (-22.4 - 2.13 * np.log10(fc) - long_rsrp-9.82) / 35.3)\n", "    \n", "    # 取 LOS 和 NLOS 距离中的最大值作为平均距离\n", "    dis_avg = np.maximum(dis_los, dis_nlos)\n", "    print(dis_avg.shape)\n", "    return dis_avg\n", "def cal_dist_inf_avg(fc, long_rsrp):\n", "    # 计算 LOS 条件下的距离\n", "    dis_avg = np.power(10, (-32.4 - 2 * np.log10(fc) - long_rsrp-4.5) / 23)\n", "    \n", "    \n", "    return dis_avg\n", "def cal_dist(fc, long_rsrp):\n", "    # 计算 LOS 条件下的距离\n", "    dis_avg = np.power(10, (-32.4-6- 20 * np.log10(fc) - long_rsrp) / 21)\n", "    \n", "    \n", "    return dis_avg"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 加权RSRP距离估计"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "10 10 1\n", "expand H: (10, 100, 17, 4, 64)\n", "expand ue loc: (10, 100, 3)\n", "expand rsrp: (10, 100, 1)\n", "srs_choose_all:(10, 100, 17, 4)\n", "simulate srs:(10, 100, 17, 4, 64)\n", "simulate coma:(10, 100, 1, 32, 32)\n", "simulate rsrp:(10, 100, 1)\n", "end load_H\n"]}], "source": ["path = os.getcwd()\n", "# path = './end_1/uma_v0/'\n", "# path='./distance/uma_test/los_3/'\n", "path = './te_umi/umi_3/'\n", "seed = 777\n", "seed_torch(seed)\n", "env = setsumi_env(path=path, seed=seed, max_length=100,mu=0.99,gNB_tti=1,select_subs=[1],select_ports=1,test_real=False)  "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["distance_error: 2.803780851086184\n", "前五十个RSRP加权: -88.15430423796256\n", "前五十个RSRP加权: -86.7750595672303\n"]}], "source": ["distance=env.Distance\n", "# 数据\n", "bsid = 0\n", "long_rsrp = cal_long_rsrp(mu=0.99, nUE=env.nUE, max_length=env.max_length, rsrp_srs=env.rsrp_srs)\n", "# dist_avg = np.power(10, (-241.34 - long_rsrp[:,:,bsid]) / 21)\n", "# dist_avg = np.power(10, (-61.34*****-long_rsrp[:,:,bsid]) / 21)\n", "# dist_avg=cal_dist_umi_avg(fc=28*10**9,long_rsrp=long_rsrp[:,:,bsid])\n", "# dist_avg = np.power(10, (-28-20*np.log10(28)- 20*long_rsrp[:,:,bsid]) / 22)#end/uma_v3_2/数据集下误差2.046\n", "dist_avg = np.power(10, (-32.4******np.log10(28)- long_rsrp[:,:,bsid]) / 21)#end/umi_v3_2/数据集下误差2.899\n", "# dist_avg = np.power(10, (-29.14-20*np.log10(28)- long_rsrp[:,:,bsid]) / 22)#uma_v0/数据集\n", "# dist_avg = np.power(10, (-58.46- long_rsrp[:,:,bsid]) / 22)#uma_v3\n", "\n", "distance=distance.flatten()\n", "\n", "dist_avg=dist_avg.flatten()\n", "\n", "dis_error=dist_avg-distance\n", "print(\"distance_error:\",np.mean(np.abs(dis_error)))\n", "short_rsrp=env.rsrp_srs\n", "rsrp=long_rsrp[0,:50,0]\n", "avg_rsrp=np.average(rsrp)\n", "print(\"前五十个RSRP加权:\",avg_rsrp)\n", "rsrp_1=long_rsrp[0,51:100,0]\n", "avg_rsrp_1=np.average(rsrp_1)\n", "print(\"前五十个RSRP加权:\",avg_rsrp_1)\n"]}, {"cell_type": "code", "execution_count": 404, "metadata": {}, "outputs": [], "source": ["def moving_average(a, window_size):\n", "    cumulative_sum = np.cumsum(np.insert(a, 0, 0)) \n", "    middle = (cumulative_sum[window_size:] - cumulative_sum[:-window_size]) / window_size\n", "    r = np.arange(1, window_size-1, 2)\n", "    begin = np.cumsum(a[:window_size-1])[::2] / r\n", "    end = (np.cumsum(a[:-window_size:-1])[::2] / r)[::-1]\n", "    return np.concatenate((begin, middle, end))"]}, {"cell_type": "code", "execution_count": 410, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.9346044005991465\n", "distance_error: 2.5252525252525255 m\n"]}, {"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"hovertemplate": "UMi Positioning error(v=3):%{x}<br>Cumulative probability:%{y}", "line": {"color": "rgb(31, 119, 180)", "dash": "solid", "width": 4}, "marker": {"color": "rgb(31, 119, 180)", "size": 10, "symbol": "circle"}, "mode": "lines+markers", "name": "distance_error", "type": "scatter", "x": [0, 0.5050505050505051, 1.0101010101010102, 1.5151515151515151, 2.0202020202020203, 2.5252525252525255, 3.0303030303030303, 3.5353535353535355, 4.040404040404041, 4.545454545454546, 5.050505050505051, 5.555555555555556, 6.0606060606060606, 6.565656565656566, 7.070707070707071, 7.575757575757576, 8.080808080808081, 8.585858585858587, 9.090909090909092, 9.595959595959597, 10.101010101010102, 10.606060606060607, 11.111111111111112, 11.616161616161618, 12.121212121212121, 12.626262626262626, 13.131313131313131, 13.636363636363637, 14.141414141414142, 14.646464646464647, 15.151515151515152, 15.656565656565657, 16.161616161616163, 16.666666666666668, 17.171717171717173, 17.67676767676768, 18.181818181818183, 18.68686868686869, 19.191919191919194, 19.6969696969697, 20.202020202020204, 20.70707070707071, 21.212121212121215, 21.71717171717172, 22.222222222222225, 22.72727272727273, 23.232323232323235, 23.73737373737374, 24.242424242424242, 24.747474747474747, 25.252525252525253, 25.757575757575758, 26.262626262626263, 26.767676767676768, 27.272727272727273, 27.77777777777778, 28.282828282828284, 28.78787878787879, 29.292929292929294, 29.7979797979798, 30.303030303030305, 30.80808080808081, 31.313131313131315, 31.81818181818182, 32.323232323232325, 32.82828282828283, 33.333333333333336, 33.83838383838384, 34.343434343434346, 34.84848484848485, 35.35353535353536, 35.85858585858586, 36.36363636363637, 36.86868686868687, 37.37373737373738, 37.87878787878788, 38.38383838383839, 38.88888888888889, 39.3939393939394, 39.8989898989899, 40.40404040404041, 40.909090909090914, 41.41414141414142, 41.919191919191924, 42.42424242424243, 42.929292929292934, 43.43434343434344, 43.939393939393945, 44.44444444444445, 44.949494949494955, 45.45454545454546, 45.959595959595966, 46.46464646464647, 46.969696969696976, 47.47474747474748, 47.979797979797986, 48.484848484848484, 48.98989898989899, 49.494949494949495, 50], "y": [0, 0.39111000000000007, 0.64567, 0.81901, 0.88769, 0.91525, 0.97428, 0.99534, 0.99693, 0.99743, 0.99768, 0.9980100000000001, 0.99833, 0.99871, 0.9989800000000001, 0.9992800000000001, 0.99949, 0.9996, 0.99965, 0.99972, 0.99976, 0.9998600000000001, 0.9999800000000001, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}], "layout": {"height": 850, "legend": {"bgcolor": "rgba(255, 255, 255, 0.8)", "bordercolor": "white", "borderwidth": 1, "font": {"size": 30}, "itemwidth": 50, "x": 0.52, "y": 0.02}, "plot_bgcolor": "white", "shapes": [{"line": {"color": "rgb(0, 0, 0)", "dash": "long<PERSON>h", "width": 4}, "type": "line", "x0": 0.1, "x1": 100, "y0": 0.9, "y1": 0.9}, {"line": {"color": "rgb(0, 0, 0)", "dash": "long<PERSON>h", "width": 4}, "type": "line", "x0": 0.1, "x1": 100, "y0": 0.5, "y1": 0.5}, {"line": {"color": "rgb(0, 0, 0)", "dash": "long<PERSON>h", "width": 4}, "type": "line", "x0": 0.1, "x1": 100, "y0": 0.1, "y1": 0.1}], "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "width": 1200, "xaxis": {"anchor": "y", "domain": [0, 1], "dtick": 0.2, "linecolor": "black", "linewidth": 2, "mirror": true, "range": [0, 3], "showgrid": false, "showline": true, "showticklabels": true, "tickfont": {"size": 24}, "ticklen": 5, "ticks": "inside", "tickwidth": 2, "title": {"font": {"color": "black", "size": 32}, "standoff": 5, "text": "UMi Positioning error(v=3)"}}, "yaxis": {"anchor": "x", "domain": [0, 1], "dtick": 0.1, "linecolor": "black", "linewidth": 2, "mirror": true, "range": [0, 1.01], "showgrid": false, "showline": true, "showticklabels": true, "tickfont": {"size": 24}, "ticklen": 5, "ticks": "inside", "tickwidth": 2, "title": {"font": {"color": "black", "size": 32}, "standoff": 5, "text": "Cumulative probability"}}}}, "text/html": ["<div>                            <div id=\"d2704432-5672-455c-ae02-e79d6e0e98d2\" class=\"plotly-graph-div\" style=\"height:850px; width:1200px;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"d2704432-5672-455c-ae02-e79d6e0e98d2\")) {                    Plotly.newPlot(                        \"d2704432-5672-455c-ae02-e79d6e0e98d2\",                        [{\"hovertemplate\":\"UMi Positioning error(v=3):%{x}\\u003cbr\\u003eCumulative probability:%{y}\",\"line\":{\"color\":\"rgb(31, 119, 180)\",\"dash\":\"solid\",\"width\":4},\"marker\":{\"color\":\"rgb(31, 119, 180)\",\"size\":10,\"symbol\":\"circle\"},\"mode\":\"lines+markers\",\"name\":\"distance_error\",\"x\":[0.0,0.5050505050505051,1.0101010101010102,1.5151515151515151,2.0202020202020203,2.5252525252525255,3.0303030303030303,3.5353535353535355,4.040404040404041,4.545454545454546,5.050505050505051,5.555555555555556,6.0606060606060606,6.565656565656566,7.070707070707071,7.575757575757576,8.080808080808081,8.585858585858587,9.090909090909092,9.595959595959597,10.101010101010102,10.606060606060607,11.111111111111112,11.616161616161618,12.121212121212121,12.626262626262626,13.131313131313131,13.636363636363637,14.141414141414142,14.646464646464647,15.151515151515152,15.656565656565657,16.161616161616163,16.666666666666668,17.171717171717173,17.67676767676768,18.181818181818183,18.68686868686869,19.191919191919194,19.6969696969697,20.202020202020204,20.70707070707071,21.212121212121215,21.71717171717172,22.222222222222225,22.72727272727273,23.232323232323235,23.73737373737374,24.242424242424242,24.747474747474747,25.252525252525253,25.757575757575758,26.262626262626263,26.767676767676768,27.272727272727273,27.77777777777778,28.282828282828284,28.78787878787879,29.292929292929294,29.7979797979798,30.303030303030305,30.80808080808081,31.313131313131315,31.81818181818182,32.323232323232325,32.82828282828283,33.333333333333336,33.83838383838384,34.343434343434346,34.84848484848485,35.35353535353536,35.85858585858586,36.36363636363637,36.86868686868687,37.37373737373738,37.87878787878788,38.38383838383839,38.88888888888889,39.3939393939394,39.8989898989899,40.40404040404041,40.909090909090914,41.41414141414142,41.919191919191924,42.42424242424243,42.929292929292934,43.43434343434344,43.939393939393945,44.44444444444445,44.949494949494955,45.45454545454546,45.959595959595966,46.46464646464647,46.969696969696976,47.47474747474748,47.979797979797986,48.484848484848484,48.98989898989899,49.494949494949495,50.0],\"y\":[0.0,0.39111000000000007,0.64567,0.81901,0.88769,0.91525,0.97428,0.99534,0.99693,0.99743,0.99768,0.9980100000000001,0.99833,0.99871,0.9989800000000001,0.9992800000000001,0.99949,0.9996,0.99965,0.99972,0.99976,0.9998600000000001,0.9999800000000001,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0],\"type\":\"scatter\"}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"xaxis\":{\"anchor\":\"y\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"UMi Positioning error(v=3)\",\"font\":{\"size\":32,\"color\":\"black\"},\"standoff\":5},\"tickfont\":{\"size\":24},\"showline\":true,\"showgrid\":false,\"showticklabels\":true,\"linewidth\":2,\"linecolor\":\"black\",\"mirror\":true,\"ticks\":\"inside\",\"ticklen\":5,\"tickwidth\":2,\"range\":[0,3],\"dtick\":0.2},\"yaxis\":{\"anchor\":\"x\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"Cumulative probability\",\"font\":{\"size\":32,\"color\":\"black\"},\"standoff\":5},\"tickfont\":{\"size\":24},\"showline\":true,\"showgrid\":false,\"showticklabels\":true,\"linewidth\":2,\"linecolor\":\"black\",\"mirror\":true,\"ticks\":\"inside\",\"ticklen\":5,\"tickwidth\":2,\"range\":[0,1.01],\"dtick\":0.1},\"legend\":{\"font\":{\"size\":30},\"x\":0.52,\"y\":0.02,\"bgcolor\":\"rgba(255, 255, 255, 0.8)\",\"bordercolor\":\"white\",\"borderwidth\":1,\"itemwidth\":50.0},\"width\":1200,\"height\":850,\"plot_bgcolor\":\"white\",\"shapes\":[{\"line\":{\"color\":\"rgb(0, 0, 0)\",\"dash\":\"longdash\",\"width\":4},\"type\":\"line\",\"x0\":0.1,\"x1\":100,\"y0\":0.9,\"y1\":0.9},{\"line\":{\"color\":\"rgb(0, 0, 0)\",\"dash\":\"longdash\",\"width\":4},\"type\":\"line\",\"x0\":0.1,\"x1\":100,\"y0\":0.5,\"y1\":0.5},{\"line\":{\"color\":\"rgb(0, 0, 0)\",\"dash\":\"longdash\",\"width\":4},\"type\":\"line\",\"x0\":0.1,\"x1\":100,\"y0\":0.1,\"y1\":0.1}]},                        {\"responsive\": true}                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('d2704432-5672-455c-ae02-e79d6e0e98d2');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })                };                });            </script>        </div>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "import statsmodels.api as sm \n", "# 定位误差 CDF\n", "figr = draw_line(\"角度误差\",\"UMi Positioning error(v=3)\",\"Cumulative probability\")\n", "\n", "x =  np.linspace(0,50, 100)\n", "y =np.abs(dist_avg-distance)\n", "print(np.average(y))\n", "y = y.reshape(-1)\n", "# y = moving_average(y, 51)\n", "ecdf = sm.distributions.ECDF(y)\n", "y = ecdf(x)\n", "figr.add_line(x=x, y=y, name=f'distance_error')\n", "index = np.searchsorted(y, 0.9, side='left')\n", "error_threshold = x[index] if index < len(x) else None\n", "print(f\"distance_error: {error_threshold} m\")\n", "figr.fig.add_shape(type=\"line\", x0=0.1, y0=0.9, x1=100, y1=0.9, line={'dash':'longdash', 'color': 'rgb(0, 0, 0)', 'width': 4})\n", "figr.fig.add_shape(type=\"line\", x0=0.1, y0=0.5, x1=100, y1=0.5, line={'dash':'longdash', 'color': 'rgb(0, 0, 0)', 'width': 4})\n", "figr.fig.add_shape(type=\"line\", x0=0.1, y0=0.1, x1=100, y1=0.1, line={'dash':'longdash', 'color': 'rgb(0, 0, 0)', 'width': 4})\n", "figr.fig.update_layout(width=1200,height=850,legend={'x': 0.52, 'y': 0.02}) \n", "figr.fig.update_xaxes(tickfont=dict(size=24))\n", "figr.fig.update_yaxes(tickfont=dict(size=24))\n", "figr.fig.update_xaxes(range=[0,3],dtick=0.2)\n", "figr.fig.update_yaxes(range=[0,1.01],dtick=0.1)\n", "figr.fig.show()"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ -90.927768   -98.9083929  -99.3330712  -98.8630775  -98.3967443\n", "  -98.1890389  -98.1887843  -99.315874   -97.8916018  -98.3524875\n", "  -97.8883563  -98.6379982  -97.6983114  -98.1021085  -98.4782668\n", "  -98.4020751  -98.9121894  -98.0880065  -98.1906556  -98.645289\n", "  -98.6078861  -97.2545344 -100.1666468  -97.6685267  -97.9302342\n", "  -98.8776745  -99.0626405  -99.1309873  -99.3661358  -98.3029757\n", "  -97.9209611  -98.1874267  -98.5171319  -98.1844317  -97.9258136\n", "  -97.6735361  -98.645266   -97.7902282  -99.0950847  -98.5659521\n", "  -99.8302616  -98.3647754  -99.0749722  -98.917513   -97.9911389\n", "  -98.6054191  -99.4555065  -97.6953114  -98.3314243  -97.8659027\n", "  -99.4209256  -98.8954866  -98.6163672  -98.4284096  -99.8804295\n", "  -99.2502069  -98.5894436  -97.9942199  -98.6472115  -99.0911134\n", "  -98.7099667  -98.9246425  -97.8792481  -99.3566673  -98.755795\n", "  -97.6676977  -99.2740106  -98.289516   -99.4810728  -98.2778322\n", "  -99.3646262  -99.1911007  -98.3252876  -98.5715837  -98.8739638\n", "  -98.4790669  -97.7136403  -99.1260137  -98.937466   -97.4788719\n", "  -97.7652971  -99.0223696  -98.6732745  -98.5772215  -98.7230148\n", "  -99.4182978  -98.394911   -98.2708554  -98.6364684  -97.2587466\n", "  -98.8534153  -98.2186933  -97.8481591  -98.4784349  -99.3544209\n", "  -98.3721123  -98.5318415  -98.0015648  -98.365952   -98.1836665]\n", "[ -90.927768    -98.89812301  -99.28033348  -98.85733915  -98.43763927\n", "  -98.25070441  -98.25047527  -99.264856    -97.98301102  -98.39780815\n", "  -97.98009007  -98.65476778  -97.80904966  -98.17246705  -98.51100952\n", "  -98.44243699  -98.90153986  -98.15977525  -98.25215944  -98.6613295\n", "  -98.62766689  -97.40965036 -100.03055152  -97.78224343  -98.01778018\n", "  -98.87047645  -99.03694585  -99.09845797  -99.31009162  -98.35324753\n", "  -98.00943439  -98.24925343  -98.54598811  -98.24655793  -98.01380164\n", "  -97.78675189  -98.6613088   -97.89177478  -99.06614563  -98.58992629\n", "  -99.72780484  -98.40886726  -99.04804438  -98.9063311   -98.07259441\n", "  -98.62544659  -99.39052525  -97.80634966  -98.37885127  -97.95988183\n", "  -99.35940244  -98.88650734  -98.63529988  -98.46613804  -99.77295595\n", "  -99.20575561  -98.61106864  -98.07536731  -98.66305975  -99.06257146\n", "  -98.71953943  -98.91274765  -97.97189269  -99.30156997  -98.7607849\n", "  -97.78149733  -99.22717894  -98.3411338   -99.41353492  -98.33061838\n", "  -99.30873298  -99.15256003  -98.37332824  -98.59499473  -98.86713682\n", "  -98.51172961  -97.82284567  -99.09398173  -98.9242888   -97.61155411\n", "  -97.86933679  -99.00070204  -98.68651645  -98.60006875  -98.73128272\n", "  -99.35703742  -98.4359893   -98.32433926  -98.65339096  -97.41344134\n", "  -98.84864317  -98.27739337  -97.94391259  -98.51116081  -99.29954821\n", "  -98.41547047  -98.55922675  -98.08197772  -98.4099262   -98.24586925]\n", "[-90.927768   -98.88785312 -99.22759576 -98.8516008  -98.47853424\n", " -98.31236992 -98.31216624 -99.213838   -98.07442024 -98.4431288\n", " -98.07182384 -98.67153736 -97.91978792 -98.2428256  -98.54375224\n", " -98.48279888 -98.89089032 -98.231544   -98.31366328 -98.67737\n", " -98.64744768 -97.56476632 -99.89445624 -97.89596016 -98.10532616\n", " -98.8632784  -99.0112512  -99.06592864 -99.25404744 -98.40351936\n", " -98.09790768 -98.31108016 -98.57484432 -98.30868416 -98.10178968\n", " -97.89996768 -98.6773516  -97.99332136 -99.03720656 -98.61390048\n", " -99.62534808 -98.45295912 -99.02111656 -98.8951492  -98.15404992\n", " -98.64547408 -99.325544   -97.91738792 -98.42627824 -98.05386096\n", " -99.29787928 -98.87752808 -98.65423256 -98.50386648 -99.6654824\n", " -99.16130432 -98.63269368 -98.15651472 -98.678908   -99.03402952\n", " -98.72911216 -98.9008528  -98.06453728 -99.24647264 -98.7657748\n", " -97.89529696 -99.18034728 -98.3927516  -99.34599704 -98.38340456\n", " -99.25283976 -99.11401936 -98.42136888 -98.61840576 -98.86030984\n", " -98.54439232 -97.93205104 -99.06194976 -98.9111116  -97.74423632\n", " -97.97337648 -98.97903448 -98.6997584  -98.622916   -98.73955064\n", " -99.29577704 -98.4770676  -98.37782312 -98.67031352 -97.56813608\n", " -98.84387104 -98.33609344 -98.03966608 -98.54388672 -99.24467552\n", " -98.45882864 -98.586612   -98.16239064 -98.4539004  -98.308072  ]\n", "[-90.927768   -98.87758323 -99.17485804 -98.84586245 -98.51942921\n", " -98.37403543 -98.37385721 -99.16282    -98.16582946 -98.48844945\n", " -98.16355761 -98.68830694 -98.03052618 -98.31318415 -98.57649496\n", " -98.52316077 -98.88024078 -98.30331275 -98.37516712 -98.6934105\n", " -98.66722847 -97.71988228 -99.75836096 -98.00967689 -98.19287214\n", " -98.85608035 -98.98555655 -99.03339931 -99.19800326 -98.45379119\n", " -98.18638097 -98.37290689 -98.60370053 -98.37081039 -98.18977772\n", " -98.01318347 -98.6933944  -98.09486794 -99.00826749 -98.63787467\n", " -99.52289132 -98.49705098 -98.99418874 -98.8839673  -98.23550543\n", " -98.66550157 -99.26056275 -98.02842618 -98.47370521 -98.14784009\n", " -99.23635612 -98.86854882 -98.67316524 -98.54159492 -99.55800885\n", " -99.11685303 -98.65431872 -98.23766213 -98.69475625 -99.00548758\n", " -98.73868489 -98.88895795 -98.15718187 -99.19137531 -98.7707647\n", " -98.00909659 -99.13351562 -98.4443694  -99.27845916 -98.43619074\n", " -99.19694654 -99.07547869 -98.46940952 -98.64181679 -98.85348286\n", " -98.57705503 -98.04125641 -99.02991779 -98.8979344  -97.87691853\n", " -98.07741617 -98.95736692 -98.71300035 -98.64576325 -98.74781856\n", " -99.23451666 -98.5181459  -98.43130698 -98.68723608 -97.72283082\n", " -98.83909891 -98.39479351 -98.13541957 -98.57661263 -99.18980283\n", " -98.50218681 -98.61399725 -98.24280356 -98.4978746  -98.37027475]\n", "[-90.927768   -98.86731334 -99.12212032 -98.8401241  -98.56032418\n", " -98.43570094 -98.43554818 -99.111802   -98.25723868 -98.5337701\n", " -98.25529138 -98.70507652 -98.14126444 -98.3835427  -98.60923768\n", " -98.56352266 -98.86959124 -98.3750815  -98.43667096 -98.709451\n", " -98.68700926 -97.87499824 -99.62226568 -98.12339362 -98.28041812\n", " -98.8488823  -98.9598619  -99.00086998 -99.14195908 -98.50406302\n", " -98.27485426 -98.43473362 -98.63255674 -98.43293662 -98.27776576\n", " -98.12639926 -98.7094372  -98.19641452 -98.97932842 -98.66184886\n", " -99.42043456 -98.54114284 -98.96726092 -98.8727854  -98.31696094\n", " -98.68552906 -99.1955815  -98.13946444 -98.52113218 -98.24181922\n", " -99.17483296 -98.85956956 -98.69209792 -98.57932336 -99.4505353\n", " -99.07240174 -98.67594376 -98.31880954 -98.7106045  -98.97694564\n", " -98.74825762 -98.8770631  -98.24982646 -99.13627798 -98.7757546\n", " -98.12289622 -99.08668396 -98.4959872  -99.21092128 -98.48897692\n", " -99.14105332 -99.03693802 -98.51745016 -98.66522782 -98.84665588\n", " -98.60971774 -98.15046178 -98.99788582 -98.8847572  -98.00960074\n", " -98.18145586 -98.93569936 -98.7262423  -98.6686105  -98.75608648\n", " -99.17325628 -98.5592242  -98.48479084 -98.70415864 -97.87752556\n", " -98.83432678 -98.45349358 -98.23117306 -98.60933854 -99.13493014\n", " -98.54554498 -98.6413825  -98.32321648 -98.5418488  -98.4324775 ]\n", "[-90.927768   -98.85704345 -99.0693826  -98.83438575 -98.60121915\n", " -98.49736645 -98.49723915 -99.060784   -98.3486479  -98.57909075\n", " -98.34702515 -98.7218461  -98.2520027  -98.45390125 -98.6419804\n", " -98.60388455 -98.8589417  -98.44685025 -98.4981748  -98.7254915\n", " -98.70679005 -98.0301142  -99.4861704  -98.23711035 -98.3679641\n", " -98.84168425 -98.93416725 -98.96834065 -99.0859149  -98.55433485\n", " -98.36332755 -98.49656035 -98.66141295 -98.49506285 -98.3657538\n", " -98.23961505 -98.72548    -98.2979611  -98.95038935 -98.68582305\n", " -99.3179778  -98.5852347  -98.9403331  -98.8616035  -98.39841645\n", " -98.70555655 -99.13060025 -98.2505027  -98.56855915 -98.33579835\n", " -99.1133098  -98.8505903  -98.7110306  -98.6170518  -99.34306175\n", " -99.02795045 -98.6975688  -98.39995695 -98.72645275 -98.9484037\n", " -98.75783035 -98.86516825 -98.34247105 -99.08118065 -98.7807445\n", " -98.23669585 -99.0398523  -98.547605   -99.1433834  -98.5417631\n", " -99.0851601  -98.99839735 -98.5654908  -98.68863885 -98.8398289\n", " -98.64238045 -98.25966715 -98.96585385 -98.87158    -98.14228295\n", " -98.28549555 -98.9140318  -98.73948425 -98.69145775 -98.7643544\n", " -99.1119959  -98.6003025  -98.5382747  -98.7210812  -98.0322203\n", " -98.82955465 -98.51219365 -98.32692655 -98.64206445 -99.08005745\n", " -98.58890315 -98.66876775 -98.4036294  -98.585823   -98.49468025]\n", "[-90.927768   -98.84677356 -99.01664488 -98.8286474  -98.64211412\n", " -98.55903196 -98.55893012 -99.009766   -98.44005712 -98.6244114\n", " -98.43875892 -98.73861568 -98.36274096 -98.5242598  -98.67472312\n", " -98.64424644 -98.84829216 -98.518619   -98.55967864 -98.741532\n", " -98.72657084 -98.18523016 -99.35007512 -98.35082708 -98.45551008\n", " -98.8344862  -98.9084726  -98.93581132 -99.02987072 -98.60460668\n", " -98.45180084 -98.55838708 -98.69026916 -98.55718908 -98.45374184\n", " -98.35283084 -98.7415228  -98.39950768 -98.92145028 -98.70979724\n", " -99.21552104 -98.62932656 -98.91340528 -98.8504216  -98.47987196\n", " -98.72558404 -99.065619   -98.36154096 -98.61598612 -98.42977748\n", " -99.05178664 -98.84161104 -98.72996328 -98.65478024 -99.2355882\n", " -98.98349916 -98.71919384 -98.48110436 -98.742301   -98.91986176\n", " -98.76740308 -98.8532734  -98.43511564 -99.02608332 -98.7857344\n", " -98.35049548 -98.99302064 -98.5992228  -99.07584552 -98.59454928\n", " -99.02926688 -98.95985668 -98.61353144 -98.71204988 -98.83300192\n", " -98.67504316 -98.36887252 -98.93382188 -98.8584028  -98.27496516\n", " -98.38953524 -98.89236424 -98.7527262  -98.714305   -98.77262232\n", " -99.05073552 -98.6413808  -98.59175856 -98.73800376 -98.18691504\n", " -98.82478252 -98.57089372 -98.42268004 -98.67479036 -99.02518476\n", " -98.63226132 -98.696153   -98.48404232 -98.6297972  -98.556883  ]\n", "[-90.927768   -98.83650367 -98.96390716 -98.82290905 -98.68300909\n", " -98.62069747 -98.62062109 -98.958748   -98.53146634 -98.66973205\n", " -98.53049269 -98.75538526 -98.47347922 -98.59461835 -98.70746584\n", " -98.68460833 -98.83764262 -98.59038775 -98.62118248 -98.7575725\n", " -98.74635163 -98.34034612 -99.21397984 -98.46454381 -98.54305606\n", " -98.82728815 -98.88277795 -98.90328199 -98.97382654 -98.65487851\n", " -98.54027413 -98.62021381 -98.71912537 -98.61931531 -98.54172988\n", " -98.46604663 -98.7575656  -98.50105426 -98.89251121 -98.73377143\n", " -99.11306428 -98.67341842 -98.88647746 -98.8392397  -98.56132747\n", " -98.74561153 -99.00063775 -98.47257922 -98.66341309 -98.52375661\n", " -98.99026348 -98.83263178 -98.74889596 -98.69250868 -99.12811465\n", " -98.93904787 -98.74081888 -98.56225177 -98.75814925 -98.89131982\n", " -98.77697581 -98.84137855 -98.52776023 -98.97098599 -98.7907243\n", " -98.46429511 -98.94618898 -98.6508406  -99.00830764 -98.64733546\n", " -98.97337366 -98.92131601 -98.66157208 -98.73546091 -98.82617494\n", " -98.70770587 -98.47807789 -98.90178991 -98.8452256  -98.40764737\n", " -98.49357493 -98.87069668 -98.76596815 -98.73715225 -98.78089024\n", " -98.98947514 -98.6824591  -98.64524242 -98.75492632 -98.34160978\n", " -98.82001039 -98.62959379 -98.51843353 -98.70751627 -98.97031207\n", " -98.67561949 -98.72353825 -98.56445524 -98.6737714  -98.61908575]\n", "[-90.927768   -98.82623378 -98.91116944 -98.8171707  -98.72390406\n", " -98.68236298 -98.68231206 -98.90773    -98.62287556 -98.7150527\n", " -98.62222646 -98.77215484 -98.58421748 -98.6649769  -98.74020856\n", " -98.72497022 -98.82699308 -98.6621565  -98.68268632 -98.773613\n", " -98.76613242 -98.49546208 -99.07788456 -98.57826054 -98.63060204\n", " -98.8200901  -98.8570833  -98.87075266 -98.91778236 -98.70515034\n", " -98.62874742 -98.68204054 -98.74798158 -98.68144154 -98.62971792\n", " -98.57926242 -98.7736084  -98.60260084 -98.86357214 -98.75774562\n", " -99.01060752 -98.71751028 -98.85954964 -98.8280578  -98.64278298\n", " -98.76563902 -98.9356565  -98.58361748 -98.71084006 -98.61773574\n", " -98.92874032 -98.82365252 -98.76782864 -98.73023712 -99.0206411\n", " -98.89459658 -98.76244392 -98.64339918 -98.7739975  -98.86277788\n", " -98.78654854 -98.8294837  -98.62040482 -98.91588866 -98.7957142\n", " -98.57809474 -98.89935732 -98.7024584  -98.94076976 -98.70012164\n", " -98.91748044 -98.88277534 -98.70961272 -98.75887194 -98.81934796\n", " -98.74036858 -98.58728326 -98.86975794 -98.8320484  -98.54032958\n", " -98.59761462 -98.84902912 -98.7792101  -98.7599995  -98.78915816\n", " -98.92821476 -98.7235374  -98.69872628 -98.77184888 -98.49630452\n", " -98.81523826 -98.68829386 -98.61418702 -98.74024218 -98.91543938\n", " -98.71897766 -98.7509235  -98.64486816 -98.7177456  -98.6812885 ]\n", "[-90.927768   -98.81596389 -98.85843172 -98.81143235 -98.76479903\n", " -98.74402849 -98.74400303 -98.856712   -98.71428478 -98.76037335\n", " -98.71396023 -98.78892442 -98.69495574 -98.73533545 -98.77295128\n", " -98.76533211 -98.81634354 -98.73392525 -98.74419016 -98.7896535\n", " -98.78591321 -98.65057804 -98.94178928 -98.69197727 -98.71814802\n", " -98.81289205 -98.83138865 -98.83822333 -98.86173818 -98.75542217\n", " -98.71722071 -98.74386727 -98.77683779 -98.74356777 -98.71770596\n", " -98.69247821 -98.7896512  -98.70414742 -98.83463307 -98.78171981\n", " -98.90815076 -98.76160214 -98.83262182 -98.8168759  -98.72423849\n", " -98.78566651 -98.87067525 -98.69465574 -98.75826703 -98.71171487\n", " -98.86721716 -98.81467326 -98.78676132 -98.76796556 -98.91316755\n", " -98.85014529 -98.78406896 -98.72454659 -98.78984575 -98.83423594\n", " -98.79612127 -98.81758885 -98.71304941 -98.86079133 -98.8007041\n", " -98.69189437 -98.85252566 -98.7540762  -98.87323188 -98.75290782\n", " -98.86158722 -98.84423467 -98.75765336 -98.78228297 -98.81252098\n", " -98.77303129 -98.69648863 -98.83772597 -98.8188712  -98.67301179\n", " -98.70165431 -98.82736156 -98.79245205 -98.78284675 -98.79742608\n", " -98.86695438 -98.7646157  -98.75221014 -98.78877144 -98.65099926\n", " -98.81046613 -98.74699393 -98.70994051 -98.77296809 -98.86056669\n", " -98.76233583 -98.77830875 -98.72528108 -98.7617198  -98.74349125]\n"]}, {"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"hovertemplate": "t:%{x}<br>RSRP:%{y}", "line": {"color": "rgb(31, 119, 180)", "dash": "solid", "width": 4}, "marker": {"color": "rgb(31, 119, 180)", "size": 10, "symbol": "circle"}, "mode": "lines+markers", "name": "longRSRP_mu=0.90", "type": "scatter", "x": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99], "y": [-90.927768, -98.9083929, -99.3330712, -98.8630775, -98.3967443, -98.1890389, -98.18878430000001, -99.31587400000001, -97.8916018, -98.3524875, -97.8883563, -98.6379982, -97.69831140000001, -98.1021085, -98.4782668, -98.4020751, -98.9121894, -98.0880065, -98.1906556, -98.645289, -98.6078861, -97.2545344, -100.1666468, -97.6685267, -97.9302342, -98.8776745, -99.0626405, -99.1309873, -99.3661358, -98.3029757, -97.9209611, -98.1874267, -98.51713190000001, -98.1844317, -97.9258136, -97.6735361, -98.645266, -97.7902282, -99.0950847, -98.5659521, -99.8302616, -98.3647754, -99.0749722, -98.917513, -97.99113890000001, -98.6054191, -99.4555065, -97.69531140000001, -98.33142430000001, -97.8659027, -99.4209256, -98.8954866, -98.6163672, -98.42840960000001, -99.8804295, -99.2502069, -98.58944360000001, -97.9942199, -98.6472115, -99.0911134, -98.7099667, -98.9246425, -97.8792481, -99.3566673, -98.755795, -97.6676977, -99.2740106, -98.289516, -99.4810728, -98.2778322, -99.3646262, -99.1911007, -98.3252876, -98.5715837, -98.8739638, -98.4790669, -97.71364030000001, -99.1260137, -98.937466, -97.4788719, -97.7652971, -99.0223696, -98.6732745, -98.57722150000001, -98.7230148, -99.4182978, -98.39491100000001, -98.2708554, -98.6364684, -97.2587466, -98.8534153, -98.2186933, -97.8481591, -98.4784349, -99.35442090000001, -98.3721123, -98.5318415, -98.0015648, -98.36595200000001, -98.1836665]}, {"hovertemplate": "t:%{x}<br>RSRP:%{y}", "line": {"color": "rgb(255, 127, 14)", "dash": "dot", "width": 4}, "marker": {"color": "rgb(255, 127, 14)", "size": 10, "symbol": "square"}, "mode": "lines+markers", "name": "longRSRP_mu=0.91", "type": "scatter", "x": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99], "y": [-90.927768, -98.89812301, -99.28033348000001, -98.85733915, -98.43763927, -98.25070441000001, -98.25047527000001, -99.26485600000001, -97.98301102, -98.39780815, -97.98009007, -98.65476778000001, -97.80904966000001, -98.17246705000001, -98.51100952000002, -98.44243699, -98.90153986000001, -98.15977525000001, -98.25215944000001, -98.66132950000001, -98.62766689, -97.40965036000001, -100.03055152, -97.78224343000001, -98.01778018, -98.87047645000001, -99.03694585000001, -99.09845797000001, -99.31009162000001, -98.35324753, -98.00943439000001, -98.24925343000001, -98.54598811000001, -98.24655793000001, -98.01380164000001, -97.78675189, -98.6613088, -97.89177478, -99.06614563000001, -98.58992629000001, -99.72780484, -98.40886726000001, -99.04804438000001, -98.9063311, -98.07259441000001, -98.62544659000001, -99.39052525000001, -97.80634966000001, -98.37885127000001, -97.95988183, -99.35940244000001, -98.88650734000001, -98.63529988, -98.46613804, -99.77295595000001, -99.20575561000001, -98.61106864000001, -98.07536731, -98.66305975, -99.06257146000002, -98.71953943000001, -98.91274765, -97.97189269, -99.30156997, -98.7607849, -97.78149733000001, -99.22717894, -98.34113380000001, -99.41353492, -98.33061838, -99.30873298, -99.15256003, -98.37332824, -98.59499473000001, -98.86713682000001, -98.51172961, -97.82284567, -99.09398173000001, -98.9242888, -97.61155411000001, -97.86933679, -99.00070204000001, -98.68651645000001, -98.60006875, -98.73128272000001, -99.35703742000001, -98.4359893, -98.32433926, -98.65339096000001, -97.41344134, -98.84864317, -98.27739337000001, -97.94391259000001, -98.51116081, -99.29954821000001, -98.41547047, -98.55922675000001, -98.08197772000001, -98.4099262, -98.24586925000001]}, {"hovertemplate": "t:%{x}<br>RSRP:%{y}", "line": {"color": "rgb(44, 160, 44)", "dash": "dash", "width": 4}, "marker": {"color": "rgb(44, 160, 44)", "size": 10, "symbol": "diamond"}, "mode": "lines+markers", "name": "longRSRP_mu=0.92", "type": "scatter", "x": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99], "y": [-90.927768, -98.88785312, -99.22759576, -98.8516008, -98.47853424, -98.31236992, -98.31216624, -99.213838, -98.07442024, -98.4431288, -98.07182384, -98.67153736, -97.91978792, -98.2428256, -98.54375224, -98.48279888, -98.89089032, -98.231544, -98.31366328, -98.67737, -98.64744768, -97.56476632, -99.89445624, -97.89596016, -98.10532616, -98.8632784, -99.0112512, -99.06592864, -99.25404744, -98.40351936, -98.09790768, -98.31108016, -98.57484432, -98.30868416, -98.10178968, -97.89996768, -98.6773516, -97.99332136, -99.03720656, -98.61390048, -99.62534808, -98.45295912, -99.02111656, -98.8951492, -98.15404992, -98.64547408, -99.325544, -97.91738792, -98.42627824, -98.05386096, -99.29787928, -98.87752808, -98.65423256, -98.50386648, -99.6654824, -99.16130432, -98.63269368, -98.15651472, -98.67890799999999, -99.03402952, -98.72911216, -98.9008528, -98.06453728, -99.24647264, -98.7657748, -97.89529696, -99.18034728, -98.3927516, -99.34599704, -98.38340456, -99.25283976, -99.11401936, -98.42136888, -98.61840576, -98.86030984, -98.54439232, -97.93205104, -99.06194976, -98.9111116, -97.74423632, -97.97337648, -98.97903448, -98.6997584, -98.622916, -98.73955064, -99.29577704, -98.4770676, -98.37782312, -98.67031352, -97.56813608, -98.84387104, -98.33609344, -98.03966608, -98.54388672, -99.24467552, -98.45882864, -98.586612, -98.16239064, -98.4539004, -98.308072]}, {"hovertemplate": "t:%{x}<br>RSRP:%{y}", "line": {"color": "rgb(214, 39, 40)", "dash": "long<PERSON>h", "width": 4}, "marker": {"color": "rgb(214, 39, 40)", "size": 10, "symbol": "cross"}, "mode": "lines+markers", "name": "longRSRP_mu=0.93", "type": "scatter", "x": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99], "y": [-90.927768, -98.87758323, -99.17485804, -98.84586245, -98.51942921, -98.37403543, -98.37385721000001, -99.16282000000001, -98.16582946000001, -98.48844945, -98.16355761000001, -98.68830694, -98.03052618000001, -98.31318415000001, -98.57649496, -98.52316077, -98.88024078000001, -98.30331275, -98.37516712, -98.6934105, -98.66722847000001, -97.71988228000001, -99.75836096, -98.00967689000001, -98.19287214, -98.85608035, -98.98555655000001, -99.03339931000001, -99.19800326000001, -98.45379119, -98.18638097, -98.37290689000001, -98.60370053000001, -98.37081039, -98.18977772000001, -98.01318347, -98.6933944, -98.09486794, -99.00826749000001, -98.63787467, -99.52289132, -98.49705098000001, -98.99418874, -98.88396730000001, -98.23550543, -98.66550157, -99.26056275, -98.02842618000001, -98.47370521, -98.14784009, -99.23635612000001, -98.86854882, -98.67316524, -98.54159492000001, -99.55800885000001, -99.11685303, -98.65431872, -98.23766213, -98.69475625000001, -99.00548758000001, -98.73868489, -98.88895795, -98.15718187, -99.19137531000001, -98.7707647, -98.00909659000001, -99.13351562000001, -98.4443694, -99.27845916000001, -98.43619074, -99.19694654, -99.07547869000001, -98.46940952, -98.64181679000001, -98.85348286, -98.57705503000001, -98.04125641, -99.02991779000001, -98.89793440000001, -97.87691853000001, -98.07741617, -98.95736692, -98.71300035, -98.64576325, -98.74781856000001, -99.23451666000001, -98.51814590000001, -98.43130698, -98.68723608, -97.72283082000001, -98.83909891, -98.39479351, -98.13541957000001, -98.57661263, -99.18980283, -98.50218681000001, -98.61399725000001, -98.24280356000001, -98.4978746, -98.37027475000001]}, {"hovertemplate": "t:%{x}<br>RSRP:%{y}", "line": {"color": "rgb(148, 103, 189)", "dash": "dashdot", "width": 4}, "marker": {"color": "rgb(148, 103, 189)", "size": 10, "symbol": "x"}, "mode": "lines+markers", "name": "longRSRP_mu=0.94", "type": "scatter", "x": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99], "y": [-90.927768, -98.86731334, -99.12212032, -98.8401241, -98.56032418, -98.43570094, -98.43554818, -99.111802, -98.25723868, -98.5337701, -98.25529138, -98.70507651999999, -98.14126444, -98.38354269999999, -98.60923767999999, -98.56352266, -98.86959123999999, -98.3750815, -98.43667096, -98.709451, -98.68700926, -97.87499824, -99.62226568, -98.12339362, -98.28041812, -98.8488823, -98.95986189999999, -99.00086998, -99.14195907999999, -98.50406302, -98.27485426, -98.43473362, -98.63255674, -98.43293661999999, -98.27776576, -98.12639926, -98.7094372, -98.19641452, -98.97932842, -98.66184885999999, -99.42043456, -98.54114283999999, -98.96726092, -98.8727854, -98.31696094, -98.68552906, -99.1955815, -98.13946444, -98.52113218, -98.24181922, -99.17483296, -98.85956956, -98.69209792, -98.57932336, -99.4505353, -99.07240174, -98.67594376, -98.31880954, -98.7106045, -98.97694564, -98.74825762, -98.8770631, -98.24982646, -99.13627798, -98.7757546, -98.12289622, -99.08668396, -98.4959872, -99.21092128, -98.48897692, -99.14105332, -99.03693802, -98.51745016, -98.66522782, -98.84665588, -98.60971774, -98.15046178, -98.99788582, -98.8847572, -98.00960074, -98.18145586, -98.93569936, -98.7262423, -98.6686105, -98.75608648, -99.17325628, -98.5592242, -98.48479084, -98.70415864, -97.87752556, -98.83432678, -98.45349358, -98.23117306, -98.60933854, -99.13493014, -98.54554498, -98.64138249999999, -98.32321648, -98.5418488, -98.4324775]}, {"hovertemplate": "t:%{x}<br>RSRP:%{y}", "line": {"color": "rgb(140, 86, 75)", "dash": "<PERSON>das<PERSON><PERSON><PERSON>", "width": 4}, "marker": {"color": "rgb(140, 86, 75)", "size": 10, "symbol": "star"}, "mode": "lines+markers", "name": "longRSRP_mu=0.95", "type": "scatter", "x": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99], "y": [-90.927768, -98.85704345, -99.0693826, -98.83438575000001, -98.60121915, -98.49736645, -98.49723915, -99.060784, -98.3486479, -98.57909075, -98.34702515000001, -98.72184610000001, -98.2520027, -98.45390125, -98.64198040000001, -98.60388455, -98.8589417, -98.44685025000001, -98.4981748, -98.7254915, -98.70679005000001, -98.0301142, -99.4861704, -98.23711035000001, -98.36796410000001, -98.84168425, -98.93416725, -98.96834065, -99.0859149, -98.55433485, -98.36332755000001, -98.49656035000001, -98.66141295, -98.49506285000001, -98.36575380000001, -98.23961505, -98.72548, -98.29796110000001, -98.95038935000001, -98.68582305000001, -99.31797780000001, -98.5852347, -98.9403331, -98.8616035, -98.39841645, -98.70555655, -99.13060025, -98.2505027, -98.56855915, -98.33579835, -99.1133098, -98.85059030000001, -98.7110306, -98.6170518, -99.34306175, -99.02795045, -98.6975688, -98.39995695, -98.72645275, -98.9484037, -98.75783035, -98.86516825000001, -98.34247105, -99.08118065000001, -98.7807445, -98.23669585, -99.0398523, -98.547605, -99.1433834, -98.5417631, -99.0851601, -98.99839735, -98.5654908, -98.68863885, -98.8398289, -98.64238045, -98.25966715, -98.96585385, -98.87158000000001, -98.14228295000001, -98.28549555000001, -98.9140318, -98.73948425, -98.69145775, -98.7643544, -99.1119959, -98.6003025, -98.5382747, -98.7210812, -98.0322203, -98.82955465, -98.51219365, -98.32692655000001, -98.64206445, -99.08005745, -98.58890315000001, -98.66876775, -98.4036294, -98.585823, -98.49468025]}, {"hovertemplate": "t:%{x}<br>RSRP:%{y}", "line": {"color": "rgb(227, 119, 194)", "dash": "solid", "width": 4}, "marker": {"color": "rgb(227, 119, 194)", "size": 10, "symbol": "circle"}, "mode": "lines+markers", "name": "longRSRP_mu=0.96", "type": "scatter", "x": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99], "y": [-90.927768, -98.84677356, -99.01664488, -98.8286474, -98.64211412, -98.55903196, -98.55893012, -99.009766, -98.44005711999999, -98.6244114, -98.43875892, -98.73861568, -98.36274096, -98.5242598, -98.67472312, -98.64424643999999, -98.84829216, -98.518619, -98.55967864, -98.74153199999999, -98.72657084, -98.18523016, -99.35007512, -98.35082708, -98.45551008, -98.8344862, -98.9084726, -98.93581132, -99.02987071999999, -98.60460667999999, -98.45180083999999, -98.55838707999999, -98.69026916, -98.55718908, -98.45374183999999, -98.35283084, -98.7415228, -98.39950768, -98.92145028, -98.70979724, -99.21552104, -98.62932656, -98.91340527999999, -98.85042159999999, -98.47987196, -98.72558404, -99.065619, -98.36154096, -98.61598612, -98.42977748, -99.05178663999999, -98.84161103999999, -98.72996327999999, -98.65478024, -99.2355882, -98.98349916, -98.71919384, -98.48110435999999, -98.742301, -98.91986175999999, -98.76740308, -98.85327339999999, -98.43511563999999, -99.02608332, -98.7857344, -98.35049547999999, -98.99302064, -98.59922279999999, -99.07584552, -98.59454928, -99.02926688, -98.95985668, -98.61353144, -98.71204988, -98.83300192, -98.67504316, -98.36887252, -98.93382188, -98.8584028, -98.27496516, -98.38953524, -98.89236423999999, -98.7527262, -98.714305, -98.77262232, -99.05073551999999, -98.6413808, -98.59175856, -98.73800376, -98.18691504, -98.82478252, -98.57089372, -98.42268004, -98.67479036, -99.02518476, -98.63226132, -98.696153, -98.48404232, -98.6297972, -98.556883]}, {"hovertemplate": "t:%{x}<br>RSRP:%{y}", "line": {"color": "rgb(127, 127, 127)", "dash": "dot", "width": 4}, "marker": {"color": "rgb(127, 127, 127)", "size": 10, "symbol": "square"}, "mode": "lines+markers", "name": "longRSRP_mu=0.97", "type": "scatter", "x": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99], "y": [-90.927768, -98.83650367, -98.96390716, -98.82290905, -98.68300909, -98.62069747, -98.62062109, -98.958748, -98.53146634000001, -98.66973205000001, -98.53049269, -98.75538526, -98.47347922, -98.59461835, -98.70746584, -98.68460833, -98.83764262, -98.59038775, -98.62118248, -98.75757250000001, -98.74635163, -98.34034612, -99.21397984000001, -98.46454381000001, -98.54305606, -98.82728815, -98.88277795, -98.90328199, -98.97382654, -98.65487851, -98.54027413, -98.62021381, -98.71912537, -98.61931531, -98.54172988, -98.46604663000001, -98.7575656, -98.50105426, -98.89251121, -98.73377143, -99.11306428, -98.67341842, -98.88647746, -98.83923970000001, -98.56132747000001, -98.74561153, -99.00063775, -98.47257922, -98.66341309, -98.52375661, -98.99026348, -98.83263178, -98.74889596, -98.69250868, -99.12811465, -98.93904787, -98.74081888, -98.56225177, -98.75814925, -98.89131982, -98.77697581, -98.84137855, -98.52776023, -98.97098599, -98.79072430000001, -98.46429511000001, -98.94618898, -98.6508406, -99.00830764, -98.64733546000001, -98.97337366000001, -98.92131601, -98.66157208, -98.73546091, -98.82617494, -98.70770587, -98.47807789000001, -98.90178991, -98.8452256, -98.40764737, -98.49357493000001, -98.87069668, -98.76596815, -98.73715225000001, -98.78089024, -98.98947514, -98.6824591, -98.64524242, -98.75492632, -98.34160978, -98.82001039000001, -98.62959379, -98.51843353, -98.70751627, -98.97031207, -98.67561949, -98.72353825, -98.56445524, -98.6737714, -98.61908575]}, {"hovertemplate": "t:%{x}<br>RSRP:%{y}", "line": {"color": "rgb(188, 189, 34)", "dash": "dash", "width": 4}, "marker": {"color": "rgb(188, 189, 34)", "size": 10, "symbol": "diamond"}, "mode": "lines+markers", "name": "longRSRP_mu=0.98", "type": "scatter", "x": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99], "y": [-90.927768, -98.82623378000001, -98.91116944000001, -98.8171707, -98.72390406000001, -98.68236298000001, -98.68231206000002, -98.90773, -98.62287556000001, -98.7150527, -98.62222646000001, -98.77215484000001, -98.58421748, -98.66497690000001, -98.74020856000001, -98.72497022, -98.82699308000001, -98.66215650000001, -98.68268632, -98.77361300000001, -98.76613242, -98.49546208000001, -99.07788456, -98.57826054, -98.63060204000001, -98.8200901, -98.85708330000001, -98.87075266000001, -98.91778236, -98.70515034, -98.62874742000001, -98.68204054, -98.74798158, -98.68144154000001, -98.62971792, -98.57926242, -98.77360840000001, -98.60260084000001, -98.86357214, -98.75774562000001, -99.01060752000001, -98.71751028000001, -98.85954964000001, -98.82805780000001, -98.64278298, -98.76563902000001, -98.93565650000001, -98.58361748000002, -98.71084006000001, -98.61773574000001, -98.92874032, -98.82365252000001, -98.76782864, -98.73023712000001, -99.0206411, -98.89459658000001, -98.76244392000001, -98.64339918, -98.77399750000001, -98.86277788000001, -98.78654854000001, -98.82948370000001, -98.62040482, -98.91588866000001, -98.7957142, -98.57809474000001, -98.89935732000001, -98.70245840000001, -98.94076976000001, -98.70012164, -98.91748044, -98.88277534000001, -98.70961272000001, -98.75887194, -98.81934796, -98.74036858000001, -98.58728326, -98.86975794000001, -98.8320484, -98.54032958, -98.59761462, -98.84902912000001, -98.77921010000001, -98.7599995, -98.78915816000001, -98.92821476, -98.72353740000001, -98.69872628, -98.77184888000001, -98.49630452000001, -98.81523826, -98.68829386, -98.61418702, -98.74024218000001, -98.91543938000001, -98.71897766000001, -98.75092350000001, -98.64486816000002, -98.71774560000001, -98.68128850000001]}, {"hovertemplate": "t:%{x}<br>RSRP:%{y}", "line": {"color": "rgb(23, 190, 207)", "dash": "long<PERSON>h", "width": 4}, "marker": {"color": "rgb(23, 190, 207)", "size": 10, "symbol": "cross"}, "mode": "lines+markers", "name": "longRSRP_mu=0.99", "type": "scatter", "x": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99], "y": [-90.927768, -98.81596389, -98.85843172, -98.81143235, -98.76479903, -98.74402849, -98.74400303, -98.856712, -98.71428478, -98.76037335, -98.71396023, -98.78892442, -98.69495574, -98.73533545, -98.77295128, -98.76533211, -98.81634354, -98.73392525, -98.74419016, -98.7896535, -98.78591321, -98.65057804, -98.94178928, -98.69197727, -98.71814802, -98.81289205, -98.83138865, -98.83822333, -98.86173818, -98.75542217, -98.71722071, -98.74386727, -98.77683779, -98.74356777, -98.71770596, -98.69247821, -98.7896512, -98.70414742, -98.83463307, -98.78171981, -98.90815076, -98.76160214, -98.83262182, -98.8168759, -98.72423849, -98.78566651, -98.87067525, -98.69465574, -98.75826703, -98.71171487, -98.86721716, -98.81467326, -98.78676132, -98.76796556, -98.91316755, -98.85014529, -98.78406896, -98.72454659, -98.78984575, -98.83423594, -98.79612127, -98.81758884999999, -98.71304941, -98.86079133, -98.8007041, -98.69189437, -98.85252566, -98.7540762, -98.87323188, -98.75290782, -98.86158722, -98.84423467, -98.75765336, -98.78228297, -98.81252098, -98.77303129, -98.69648863, -98.83772597, -98.8188712, -98.67301179, -98.70165431, -98.82736156, -98.79245205, -98.78284675, -98.79742608, -98.86695438, -98.7646157, -98.75221014, -98.78877144, -98.65099926, -98.81046613, -98.74699393, -98.70994051, -98.77296809, -98.86056669, -98.76233583, -98.77830875, -98.72528108, -98.7617198, -98.74349125]}], "layout": {"height": 1200, "legend": {"bgcolor": "rgba(255, 255, 255, 0.8)", "bordercolor": "white", "borderwidth": 1, "font": {"size": 30}, "itemwidth": 50, "x": 0.52, "y": 0.92}, "plot_bgcolor": "white", "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "width": 1200, "xaxis": {"anchor": "y", "domain": [0, 1], "dtick": 5, "linecolor": "black", "linewidth": 2, "mirror": true, "range": [0, 100], "showgrid": false, "showline": true, "showticklabels": true, "tickfont": {"size": 24}, "ticklen": 5, "ticks": "inside", "tickwidth": 2, "title": {"font": {"color": "black", "size": 32}, "standoff": 5, "text": "t"}}, "yaxis": {"anchor": "x", "domain": [0, 1], "dtick": 10, "linecolor": "black", "linewidth": 2, "mirror": true, "range": [-105, -90], "showgrid": false, "showline": true, "showticklabels": true, "tickfont": {"size": 24}, "ticklen": 5, "ticks": "inside", "tickwidth": 2, "title": {"font": {"color": "black", "size": 32}, "standoff": 5, "text": "RSRP"}}}}, "text/html": ["<div>                            <div id=\"c078173c-83a9-4fd2-bffb-afe368ee6200\" class=\"plotly-graph-div\" style=\"height:1200px; width:1200px;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"c078173c-83a9-4fd2-bffb-afe368ee6200\")) {                    Plotly.newPlot(                        \"c078173c-83a9-4fd2-bffb-afe368ee6200\",                        [{\"hovertemplate\":\"t:%{x}\\u003cbr\\u003eRSRP:%{y}\",\"line\":{\"color\":\"rgb(31, 119, 180)\",\"dash\":\"solid\",\"width\":4},\"marker\":{\"color\":\"rgb(31, 119, 180)\",\"size\":10,\"symbol\":\"circle\"},\"mode\":\"lines+markers\",\"name\":\"longRSRP_mu=0.90\",\"x\":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99],\"y\":[-90.927768,-98.9083929,-99.3330712,-98.8630775,-98.3967443,-98.1890389,-98.18878430000001,-99.31587400000001,-97.8916018,-98.3524875,-97.8883563,-98.6379982,-97.69831140000001,-98.1021085,-98.4782668,-98.4020751,-98.9121894,-98.0880065,-98.1906556,-98.645289,-98.6078861,-97.2545344,-100.1666468,-97.6685267,-97.9302342,-98.8776745,-99.0626405,-99.1309873,-99.3661358,-98.3029757,-97.9209611,-98.1874267,-98.51713190000001,-98.1844317,-97.9258136,-97.6735361,-98.645266,-97.7902282,-99.0950847,-98.5659521,-99.8302616,-98.3647754,-99.0749722,-98.917513,-97.99113890000001,-98.6054191,-99.4555065,-97.69531140000001,-98.33142430000001,-97.8659027,-99.4209256,-98.8954866,-98.6163672,-98.42840960000001,-99.8804295,-99.2502069,-98.58944360000001,-97.9942199,-98.6472115,-99.0911134,-98.7099667,-98.9246425,-97.8792481,-99.3566673,-98.755795,-97.6676977,-99.2740106,-98.289516,-99.4810728,-98.2778322,-99.3646262,-99.1911007,-98.3252876,-98.5715837,-98.8739638,-98.4790669,-97.71364030000001,-99.1260137,-98.937466,-97.4788719,-97.7652971,-99.0223696,-98.6732745,-98.57722150000001,-98.7230148,-99.4182978,-98.39491100000001,-98.2708554,-98.6364684,-97.2587466,-98.8534153,-98.2186933,-97.8481591,-98.4784349,-99.35442090000001,-98.3721123,-98.5318415,-98.0015648,-98.36595200000001,-98.1836665],\"type\":\"scatter\"},{\"hovertemplate\":\"t:%{x}\\u003cbr\\u003eRSRP:%{y}\",\"line\":{\"color\":\"rgb(255, 127, 14)\",\"dash\":\"dot\",\"width\":4},\"marker\":{\"color\":\"rgb(255, 127, 14)\",\"size\":10,\"symbol\":\"square\"},\"mode\":\"lines+markers\",\"name\":\"longRSRP_mu=0.91\",\"x\":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99],\"y\":[-90.927768,-98.89812301,-99.28033348000001,-98.85733915,-98.43763927,-98.25070441000001,-98.25047527000001,-99.26485600000001,-97.98301102,-98.39780815,-97.98009007,-98.65476778000001,-97.80904966000001,-98.17246705000001,-98.51100952000002,-98.44243699,-98.90153986000001,-98.15977525000001,-98.25215944000001,-98.66132950000001,-98.62766689,-97.40965036000001,-100.03055152,-97.78224343000001,-98.01778018,-98.87047645000001,-99.03694585000001,-99.09845797000001,-99.31009162000001,-98.35324753,-98.00943439000001,-98.24925343000001,-98.54598811000001,-98.24655793000001,-98.01380164000001,-97.78675189,-98.6613088,-97.89177478,-99.06614563000001,-98.58992629000001,-99.72780484,-98.40886726000001,-99.04804438000001,-98.9063311,-98.07259441000001,-98.62544659000001,-99.39052525000001,-97.80634966000001,-98.37885127000001,-97.95988183,-99.35940244000001,-98.88650734000001,-98.63529988,-98.46613804,-99.77295595000001,-99.20575561000001,-98.61106864000001,-98.07536731,-98.66305975,-99.06257146000002,-98.71953943000001,-98.91274765,-97.97189269,-99.30156997,-98.7607849,-97.78149733000001,-99.22717894,-98.34113380000001,-99.41353492,-98.33061838,-99.30873298,-99.15256003,-98.37332824,-98.59499473000001,-98.86713682000001,-98.51172961,-97.82284567,-99.09398173000001,-98.9242888,-97.61155411000001,-97.86933679,-99.00070204000001,-98.68651645000001,-98.60006875,-98.73128272000001,-99.35703742000001,-98.4359893,-98.32433926,-98.65339096000001,-97.41344134,-98.84864317,-98.27739337000001,-97.94391259000001,-98.51116081,-99.29954821000001,-98.41547047,-98.55922675000001,-98.08197772000001,-98.4099262,-98.24586925000001],\"type\":\"scatter\"},{\"hovertemplate\":\"t:%{x}\\u003cbr\\u003eRSRP:%{y}\",\"line\":{\"color\":\"rgb(44, 160, 44)\",\"dash\":\"dash\",\"width\":4},\"marker\":{\"color\":\"rgb(44, 160, 44)\",\"size\":10,\"symbol\":\"diamond\"},\"mode\":\"lines+markers\",\"name\":\"longRSRP_mu=0.92\",\"x\":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99],\"y\":[-90.927768,-98.88785312,-99.22759576,-98.8516008,-98.47853424,-98.31236992,-98.31216624,-99.213838,-98.07442024,-98.4431288,-98.07182384,-98.67153736,-97.91978792,-98.2428256,-98.54375224,-98.48279888,-98.89089032,-98.231544,-98.31366328,-98.67737,-98.64744768,-97.56476632,-99.89445624,-97.89596016,-98.10532616,-98.8632784,-99.0112512,-99.06592864,-99.25404744,-98.40351936,-98.09790768,-98.31108016,-98.57484432,-98.30868416,-98.10178968,-97.89996768,-98.6773516,-97.99332136,-99.03720656,-98.61390048,-99.62534808,-98.45295912,-99.02111656,-98.8951492,-98.15404992,-98.64547408,-99.325544,-97.91738792,-98.42627824,-98.05386096,-99.29787928,-98.87752808,-98.65423256,-98.50386648,-99.6654824,-99.16130432,-98.63269368,-98.15651472,-98.67890799999999,-99.03402952,-98.72911216,-98.9008528,-98.06453728,-99.24647264,-98.7657748,-97.89529696,-99.18034728,-98.3927516,-99.34599704,-98.38340456,-99.25283976,-99.11401936,-98.42136888,-98.61840576,-98.86030984,-98.54439232,-97.93205104,-99.06194976,-98.9111116,-97.74423632,-97.97337648,-98.97903448,-98.6997584,-98.622916,-98.73955064,-99.29577704,-98.4770676,-98.37782312,-98.67031352,-97.56813608,-98.84387104,-98.33609344,-98.03966608,-98.54388672,-99.24467552,-98.45882864,-98.586612,-98.16239064,-98.4539004,-98.308072],\"type\":\"scatter\"},{\"hovertemplate\":\"t:%{x}\\u003cbr\\u003eRSRP:%{y}\",\"line\":{\"color\":\"rgb(214, 39, 40)\",\"dash\":\"longdash\",\"width\":4},\"marker\":{\"color\":\"rgb(214, 39, 40)\",\"size\":10,\"symbol\":\"cross\"},\"mode\":\"lines+markers\",\"name\":\"longRSRP_mu=0.93\",\"x\":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99],\"y\":[-90.927768,-98.87758323,-99.17485804,-98.84586245,-98.51942921,-98.37403543,-98.37385721000001,-99.16282000000001,-98.16582946000001,-98.48844945,-98.16355761000001,-98.68830694,-98.03052618000001,-98.31318415000001,-98.57649496,-98.52316077,-98.88024078000001,-98.30331275,-98.37516712,-98.6934105,-98.66722847000001,-97.71988228000001,-99.75836096,-98.00967689000001,-98.19287214,-98.85608035,-98.98555655000001,-99.03339931000001,-99.19800326000001,-98.45379119,-98.18638097,-98.37290689000001,-98.60370053000001,-98.37081039,-98.18977772000001,-98.01318347,-98.6933944,-98.09486794,-99.00826749000001,-98.63787467,-99.52289132,-98.49705098000001,-98.99418874,-98.88396730000001,-98.23550543,-98.66550157,-99.26056275,-98.02842618000001,-98.47370521,-98.14784009,-99.23635612000001,-98.86854882,-98.67316524,-98.54159492000001,-99.55800885000001,-99.11685303,-98.65431872,-98.23766213,-98.69475625000001,-99.00548758000001,-98.73868489,-98.88895795,-98.15718187,-99.19137531000001,-98.7707647,-98.00909659000001,-99.13351562000001,-98.4443694,-99.27845916000001,-98.43619074,-99.19694654,-99.07547869000001,-98.46940952,-98.64181679000001,-98.85348286,-98.57705503000001,-98.04125641,-99.02991779000001,-98.89793440000001,-97.87691853000001,-98.07741617,-98.95736692,-98.71300035,-98.64576325,-98.74781856000001,-99.23451666000001,-98.51814590000001,-98.43130698,-98.68723608,-97.72283082000001,-98.83909891,-98.39479351,-98.13541957000001,-98.57661263,-99.18980283,-98.50218681000001,-98.61399725000001,-98.24280356000001,-98.4978746,-98.37027475000001],\"type\":\"scatter\"},{\"hovertemplate\":\"t:%{x}\\u003cbr\\u003eRSRP:%{y}\",\"line\":{\"color\":\"rgb(148, 103, 189)\",\"dash\":\"dashdot\",\"width\":4},\"marker\":{\"color\":\"rgb(148, 103, 189)\",\"size\":10,\"symbol\":\"x\"},\"mode\":\"lines+markers\",\"name\":\"longRSRP_mu=0.94\",\"x\":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99],\"y\":[-90.927768,-98.86731334,-99.12212032,-98.8401241,-98.56032418,-98.43570094,-98.43554818,-99.111802,-98.25723868,-98.5337701,-98.25529138,-98.70507651999999,-98.14126444,-98.38354269999999,-98.60923767999999,-98.56352266,-98.86959123999999,-98.3750815,-98.43667096,-98.709451,-98.68700926,-97.87499824,-99.62226568,-98.12339362,-98.28041812,-98.8488823,-98.95986189999999,-99.00086998,-99.14195907999999,-98.50406302,-98.27485426,-98.43473362,-98.63255674,-98.43293661999999,-98.27776576,-98.12639926,-98.7094372,-98.19641452,-98.97932842,-98.66184885999999,-99.42043456,-98.54114283999999,-98.96726092,-98.8727854,-98.31696094,-98.68552906,-99.1955815,-98.13946444,-98.52113218,-98.24181922,-99.17483296,-98.85956956,-98.69209792,-98.57932336,-99.4505353,-99.07240174,-98.67594376,-98.31880954,-98.7106045,-98.97694564,-98.74825762,-98.8770631,-98.24982646,-99.13627798,-98.7757546,-98.12289622,-99.08668396,-98.4959872,-99.21092128,-98.48897692,-99.14105332,-99.03693802,-98.51745016,-98.66522782,-98.84665588,-98.60971774,-98.15046178,-98.99788582,-98.8847572,-98.00960074,-98.18145586,-98.93569936,-98.7262423,-98.6686105,-98.75608648,-99.17325628,-98.5592242,-98.48479084,-98.70415864,-97.87752556,-98.83432678,-98.45349358,-98.23117306,-98.60933854,-99.13493014,-98.54554498,-98.64138249999999,-98.32321648,-98.5418488,-98.4324775],\"type\":\"scatter\"},{\"hovertemplate\":\"t:%{x}\\u003cbr\\u003eRSRP:%{y}\",\"line\":{\"color\":\"rgb(140, 86, 75)\",\"dash\":\"longdashdot\",\"width\":4},\"marker\":{\"color\":\"rgb(140, 86, 75)\",\"size\":10,\"symbol\":\"star\"},\"mode\":\"lines+markers\",\"name\":\"longRSRP_mu=0.95\",\"x\":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99],\"y\":[-90.927768,-98.85704345,-99.0693826,-98.83438575000001,-98.60121915,-98.49736645,-98.49723915,-99.060784,-98.3486479,-98.57909075,-98.34702515000001,-98.72184610000001,-98.2520027,-98.45390125,-98.64198040000001,-98.60388455,-98.8589417,-98.44685025000001,-98.4981748,-98.7254915,-98.70679005000001,-98.0301142,-99.4861704,-98.23711035000001,-98.36796410000001,-98.84168425,-98.93416725,-98.96834065,-99.0859149,-98.55433485,-98.36332755000001,-98.49656035000001,-98.66141295,-98.49506285000001,-98.36575380000001,-98.23961505,-98.72548,-98.29796110000001,-98.95038935000001,-98.68582305000001,-99.31797780000001,-98.5852347,-98.9403331,-98.8616035,-98.39841645,-98.70555655,-99.13060025,-98.2505027,-98.56855915,-98.33579835,-99.1133098,-98.85059030000001,-98.7110306,-98.6170518,-99.34306175,-99.02795045,-98.6975688,-98.39995695,-98.72645275,-98.9484037,-98.75783035,-98.86516825000001,-98.34247105,-99.08118065000001,-98.7807445,-98.23669585,-99.0398523,-98.547605,-99.1433834,-98.5417631,-99.0851601,-98.99839735,-98.5654908,-98.68863885,-98.8398289,-98.64238045,-98.25966715,-98.96585385,-98.87158000000001,-98.14228295000001,-98.28549555000001,-98.9140318,-98.73948425,-98.69145775,-98.7643544,-99.1119959,-98.6003025,-98.5382747,-98.7210812,-98.0322203,-98.82955465,-98.51219365,-98.32692655000001,-98.64206445,-99.08005745,-98.58890315000001,-98.66876775,-98.4036294,-98.585823,-98.49468025],\"type\":\"scatter\"},{\"hovertemplate\":\"t:%{x}\\u003cbr\\u003eRSRP:%{y}\",\"line\":{\"color\":\"rgb(227, 119, 194)\",\"dash\":\"solid\",\"width\":4},\"marker\":{\"color\":\"rgb(227, 119, 194)\",\"size\":10,\"symbol\":\"circle\"},\"mode\":\"lines+markers\",\"name\":\"longRSRP_mu=0.96\",\"x\":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99],\"y\":[-90.927768,-98.84677356,-99.01664488,-98.8286474,-98.64211412,-98.55903196,-98.55893012,-99.009766,-98.44005711999999,-98.6244114,-98.43875892,-98.73861568,-98.36274096,-98.5242598,-98.67472312,-98.64424643999999,-98.84829216,-98.518619,-98.55967864,-98.74153199999999,-98.72657084,-98.18523016,-99.35007512,-98.35082708,-98.45551008,-98.8344862,-98.9084726,-98.93581132,-99.02987071999999,-98.60460667999999,-98.45180083999999,-98.55838707999999,-98.69026916,-98.55718908,-98.45374183999999,-98.35283084,-98.7415228,-98.39950768,-98.92145028,-98.70979724,-99.21552104,-98.62932656,-98.91340527999999,-98.85042159999999,-98.47987196,-98.72558404,-99.065619,-98.36154096,-98.61598612,-98.42977748,-99.05178663999999,-98.84161103999999,-98.72996327999999,-98.65478024,-99.2355882,-98.98349916,-98.71919384,-98.48110435999999,-98.742301,-98.91986175999999,-98.76740308,-98.85327339999999,-98.43511563999999,-99.02608332,-98.7857344,-98.35049547999999,-98.99302064,-98.59922279999999,-99.07584552,-98.59454928,-99.02926688,-98.95985668,-98.61353144,-98.71204988,-98.83300192,-98.67504316,-98.36887252,-98.93382188,-98.8584028,-98.27496516,-98.38953524,-98.89236423999999,-98.7527262,-98.714305,-98.77262232,-99.05073551999999,-98.6413808,-98.59175856,-98.73800376,-98.18691504,-98.82478252,-98.57089372,-98.42268004,-98.67479036,-99.02518476,-98.63226132,-98.696153,-98.48404232,-98.6297972,-98.556883],\"type\":\"scatter\"},{\"hovertemplate\":\"t:%{x}\\u003cbr\\u003eRSRP:%{y}\",\"line\":{\"color\":\"rgb(127, 127, 127)\",\"dash\":\"dot\",\"width\":4},\"marker\":{\"color\":\"rgb(127, 127, 127)\",\"size\":10,\"symbol\":\"square\"},\"mode\":\"lines+markers\",\"name\":\"longRSRP_mu=0.97\",\"x\":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99],\"y\":[-90.927768,-98.83650367,-98.96390716,-98.82290905,-98.68300909,-98.62069747,-98.62062109,-98.958748,-98.53146634000001,-98.66973205000001,-98.53049269,-98.75538526,-98.47347922,-98.59461835,-98.70746584,-98.68460833,-98.83764262,-98.59038775,-98.62118248,-98.75757250000001,-98.74635163,-98.34034612,-99.21397984000001,-98.46454381000001,-98.54305606,-98.82728815,-98.88277795,-98.90328199,-98.97382654,-98.65487851,-98.54027413,-98.62021381,-98.71912537,-98.61931531,-98.54172988,-98.46604663000001,-98.7575656,-98.50105426,-98.89251121,-98.73377143,-99.11306428,-98.67341842,-98.88647746,-98.83923970000001,-98.56132747000001,-98.74561153,-99.00063775,-98.47257922,-98.66341309,-98.52375661,-98.99026348,-98.83263178,-98.74889596,-98.69250868,-99.12811465,-98.93904787,-98.74081888,-98.56225177,-98.75814925,-98.89131982,-98.77697581,-98.84137855,-98.52776023,-98.97098599,-98.79072430000001,-98.46429511000001,-98.94618898,-98.6508406,-99.00830764,-98.64733546000001,-98.97337366000001,-98.92131601,-98.66157208,-98.73546091,-98.82617494,-98.70770587,-98.47807789000001,-98.90178991,-98.8452256,-98.40764737,-98.49357493000001,-98.87069668,-98.76596815,-98.73715225000001,-98.78089024,-98.98947514,-98.6824591,-98.64524242,-98.75492632,-98.34160978,-98.82001039000001,-98.62959379,-98.51843353,-98.70751627,-98.97031207,-98.67561949,-98.72353825,-98.56445524,-98.6737714,-98.61908575],\"type\":\"scatter\"},{\"hovertemplate\":\"t:%{x}\\u003cbr\\u003eRSRP:%{y}\",\"line\":{\"color\":\"rgb(188, 189, 34)\",\"dash\":\"dash\",\"width\":4},\"marker\":{\"color\":\"rgb(188, 189, 34)\",\"size\":10,\"symbol\":\"diamond\"},\"mode\":\"lines+markers\",\"name\":\"longRSRP_mu=0.98\",\"x\":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99],\"y\":[-90.927768,-98.82623378000001,-98.91116944000001,-98.8171707,-98.72390406000001,-98.68236298000001,-98.68231206000002,-98.90773,-98.62287556000001,-98.7150527,-98.62222646000001,-98.77215484000001,-98.58421748,-98.66497690000001,-98.74020856000001,-98.72497022,-98.82699308000001,-98.66215650000001,-98.68268632,-98.77361300000001,-98.76613242,-98.49546208000001,-99.07788456,-98.57826054,-98.63060204000001,-98.8200901,-98.85708330000001,-98.87075266000001,-98.91778236,-98.70515034,-98.62874742000001,-98.68204054,-98.74798158,-98.68144154000001,-98.62971792,-98.57926242,-98.77360840000001,-98.60260084000001,-98.86357214,-98.75774562000001,-99.01060752000001,-98.71751028000001,-98.85954964000001,-98.82805780000001,-98.64278298,-98.76563902000001,-98.93565650000001,-98.58361748000002,-98.71084006000001,-98.61773574000001,-98.92874032,-98.82365252000001,-98.76782864,-98.73023712000001,-99.0206411,-98.89459658000001,-98.76244392000001,-98.64339918,-98.77399750000001,-98.86277788000001,-98.78654854000001,-98.82948370000001,-98.62040482,-98.91588866000001,-98.7957142,-98.57809474000001,-98.89935732000001,-98.70245840000001,-98.94076976000001,-98.70012164,-98.91748044,-98.88277534000001,-98.70961272000001,-98.75887194,-98.81934796,-98.74036858000001,-98.58728326,-98.86975794000001,-98.8320484,-98.54032958,-98.59761462,-98.84902912000001,-98.77921010000001,-98.7599995,-98.78915816000001,-98.92821476,-98.72353740000001,-98.69872628,-98.77184888000001,-98.49630452000001,-98.81523826,-98.68829386,-98.61418702,-98.74024218000001,-98.91543938000001,-98.71897766000001,-98.75092350000001,-98.64486816000002,-98.71774560000001,-98.68128850000001],\"type\":\"scatter\"},{\"hovertemplate\":\"t:%{x}\\u003cbr\\u003eRSRP:%{y}\",\"line\":{\"color\":\"rgb(23, 190, 207)\",\"dash\":\"longdash\",\"width\":4},\"marker\":{\"color\":\"rgb(23, 190, 207)\",\"size\":10,\"symbol\":\"cross\"},\"mode\":\"lines+markers\",\"name\":\"longRSRP_mu=0.99\",\"x\":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99],\"y\":[-90.927768,-98.81596389,-98.85843172,-98.81143235,-98.76479903,-98.74402849,-98.74400303,-98.856712,-98.71428478,-98.76037335,-98.71396023,-98.78892442,-98.69495574,-98.73533545,-98.77295128,-98.76533211,-98.81634354,-98.73392525,-98.74419016,-98.7896535,-98.78591321,-98.65057804,-98.94178928,-98.69197727,-98.71814802,-98.81289205,-98.83138865,-98.83822333,-98.86173818,-98.75542217,-98.71722071,-98.74386727,-98.77683779,-98.74356777,-98.71770596,-98.69247821,-98.7896512,-98.70414742,-98.83463307,-98.78171981,-98.90815076,-98.76160214,-98.83262182,-98.8168759,-98.72423849,-98.78566651,-98.87067525,-98.69465574,-98.75826703,-98.71171487,-98.86721716,-98.81467326,-98.78676132,-98.76796556,-98.91316755,-98.85014529,-98.78406896,-98.72454659,-98.78984575,-98.83423594,-98.79612127,-98.81758884999999,-98.71304941,-98.86079133,-98.8007041,-98.69189437,-98.85252566,-98.7540762,-98.87323188,-98.75290782,-98.86158722,-98.84423467,-98.75765336,-98.78228297,-98.81252098,-98.77303129,-98.69648863,-98.83772597,-98.8188712,-98.67301179,-98.70165431,-98.82736156,-98.79245205,-98.78284675,-98.79742608,-98.86695438,-98.7646157,-98.75221014,-98.78877144,-98.65099926,-98.81046613,-98.74699393,-98.70994051,-98.77296809,-98.86056669,-98.76233583,-98.77830875,-98.72528108,-98.7617198,-98.74349125],\"type\":\"scatter\"}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"xaxis\":{\"anchor\":\"y\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"t\",\"font\":{\"size\":32,\"color\":\"black\"},\"standoff\":5},\"tickfont\":{\"size\":24},\"showline\":true,\"showgrid\":false,\"showticklabels\":true,\"linewidth\":2,\"linecolor\":\"black\",\"mirror\":true,\"ticks\":\"inside\",\"ticklen\":5,\"tickwidth\":2,\"range\":[0,100],\"dtick\":5},\"yaxis\":{\"anchor\":\"x\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"RSRP\",\"font\":{\"size\":32,\"color\":\"black\"},\"standoff\":5},\"tickfont\":{\"size\":24},\"showline\":true,\"showgrid\":false,\"showticklabels\":true,\"linewidth\":2,\"linecolor\":\"black\",\"mirror\":true,\"ticks\":\"inside\",\"ticklen\":5,\"tickwidth\":2,\"range\":[-105,-90],\"dtick\":10},\"legend\":{\"font\":{\"size\":30},\"x\":0.52,\"y\":0.92,\"bgcolor\":\"rgba(255, 255, 255, 0.8)\",\"bordercolor\":\"white\",\"borderwidth\":1,\"itemwidth\":50.0},\"width\":1200,\"height\":1200,\"plot_bgcolor\":\"white\"},                        {\"responsive\": true}                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('c078173c-83a9-4fd2-bffb-afe368ee6200');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })                };                });            </script>        </div>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["figr = draw_line(\"角度误差\",\"t\",\"RSRP\")\n", "mu_values = np.arange(0.9, 1.0, 0.01)\n", "for mu in mu_values:\n", "    long_rsrp = cal_long_rsrp(mu=mu, nUE=env.nUE, max_length=env.max_length, rsrp_srs=env.rsrp_srs)\n", "    x =  np.arange(1,100,1)\n", "    y = long_rsrp[0,:,0]\n", "    print(y)\n", "    figr.add_line(x=x, y=y, name=f'longRSRP_mu={mu:.2f}')\n", "\n", "figr.fig.update_layout(width=1200,height=1200,legend={'x': 0.52, 'y': 0.92}) \n", "figr.fig.update_xaxes(tickfont=dict(size=24))\n", "figr.fig.update_yaxes(tickfont=dict(size=24))\n", "figr.fig.update_xaxes(range=[0,100],dtick=5)\n", "figr.fig.update_yaxes(range=[-105,-90],dtick=10)\n", "figr.fig.show()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[[-0.90927768 -1.15320349 -0.98805694]\n", "  [-1.9765032  -2.14493923 -1.91086979]\n", "  [-2.01897103 -2.2310449  -1.84764774]\n", "  ...\n", "  [-1.88582039 -2.12125145 -2.03874446]\n", "  [-1.92225911 -2.12450324 -1.86175911]\n", "  [-1.90403056 -2.1667982  -1.87800064]]\n", "\n", " [[-2.82424286 -2.89265077 -2.85029133]\n", "  [-2.83969075 -3.08632313 -2.9231977 ]\n", "  [-2.81511994 -3.07255137 -2.96013518]\n", "  ...\n", "  [-2.79692854 -3.00583413 -2.95503979]\n", "  [-2.8513434  -3.10153974 -2.88186033]\n", "  [-2.85294515 -3.04723484 -2.90829567]]\n", "\n", " [[-2.75296592 -2.85309812 -2.84151973]\n", "  [-2.69678908 -2.8039062  -2.84128515]\n", "  [-2.65137932 -2.99658487 -2.85515972]\n", "  ...\n", "  [-2.72088942 -2.92272903 -2.80845542]\n", "  [-2.74369993 -2.90258602 -2.93312383]\n", "  [-2.78156264 -2.98287653 -2.83980275]]\n", "\n", " ...\n", "\n", " [[-3.02931394 -3.26030294 -3.16961145]\n", "  [-3.00392279 -3.26940935 -3.08710061]\n", "  [-3.01316069 -3.17563014 -3.09330175]\n", "  ...\n", "  [-2.97756387 -3.29087642 -3.04633362]\n", "  [-2.98360493 -3.12441814 -3.02381922]\n", "  [-3.09453604 -3.16601322 -3.09863931]]\n", "\n", " [[-2.8019014  -3.09900626 -2.94743005]\n", "  [-2.83180918 -3.18946418 -2.99810823]\n", "  [-2.76607825 -3.0856404  -2.88776847]\n", "  ...\n", "  [-2.88345391 -3.13029145 -2.95187915]\n", "  [-2.92832429 -3.06996319 -2.99169255]\n", "  [-2.91742172 -3.12156096 -3.02015215]]\n", "\n", " [[-2.69612321 -3.06831755 -2.84694386]\n", "  [-2.83277098 -3.12540705 -2.80335963]\n", "  [-2.91538843 -2.97675542 -3.01255968]\n", "  ...\n", "  [-2.81216275 -3.11323782 -2.92153495]\n", "  [-2.84161653 -3.08928699 -3.05293384]\n", "  [-2.71605509 -3.0089369  -2.98383651]]]\n"]}], "source": ["distance=env.Distance.flatten()\n", "# 数据\n", "bsid = 0\n", "# long_rsrp1 = cal_long_rsrp1(bs=env.nBS,mu=0.1, nUE=env.nUE, max_length=env.max_length, rsrp_srs=env.rsrp_srs)\n", "\n", "long_rsrp = cal_long_rsrp(mu=0.99, nUE=env.nUE, max_length=env.max_length, rsrp_srs=env.rsrp_srs)\n", "print(long_rsrp)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'long_rsrp' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m rsrp\u001b[38;5;241m=\u001b[39m\u001b[43mlong_rsrp\u001b[49m[\u001b[38;5;241m1\u001b[39m,:\u001b[38;5;241m50\u001b[39m,\u001b[38;5;241m0\u001b[39m]\n\u001b[1;32m      2\u001b[0m avg_rsrp\u001b[38;5;241m=\u001b[39mnp\u001b[38;5;241m.\u001b[39maverage(rsrp)\n\u001b[1;32m      3\u001b[0m \u001b[38;5;28mprint\u001b[39m(avg_rsrp)\n", "\u001b[0;31mNameError\u001b[0m: name 'long_rsrp' is not defined"]}], "source": ["rsrp=long_rsrp[1,:50,0]\n", "avg_rsrp=np.average(rsrp)\n", "print(avg_rsrp)\n", "rsrp_1=long_rsrp[1,51:100,0]\n", "avg_rsrp_1=np.average(rsrp_1)\n", "print(avg_rsrp_1)\n", "rsrp_2=env.rsrp_srs[1,:50,0]\n", "avg_rsrp=np.average(rsrp_2)\n", "print(avg_rsrp)\n", "rsrp_3=env.rsrp_srs[1,51:100,0]\n", "avg_rsrp_3=np.average(rsrp_3)\n", "print(avg_rsrp_3)"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ -9.0927768  -18.87578076 -19.30045906 -18.83046536 -18.36413216\n", " -18.15642676 -18.15617216 -19.28326186 -17.85898966 -18.31987536\n", " -17.85574416 -18.60538606 -17.66569926 -18.06949636 -18.44565466\n", " -18.36946296 -18.87957726 -18.05539436 -18.15804346 -18.61267686\n", " -18.57527396 -17.22192226 -20.13403466 -17.63591456 -17.89762206\n", " -18.84506236 -19.03002836 -19.09837516 -19.33352366 -18.27036356\n", " -17.88834896 -18.15481456 -18.48451976 -18.15181956 -17.89320146\n", " -17.64092396 -18.61265386 -17.75761606 -19.06247256 -18.53333996\n", " -19.79764946 -18.33216326 -19.04236006 -18.88490086 -17.95852676\n", " -18.57280696 -19.42289436 -17.66269926 -18.29881216 -17.83329056\n", " -19.38831346 -18.86287446 -18.58375506 -18.39579746 -19.84781736\n", " -19.21759476 -18.55683146 -17.96160776 -18.61459936 -19.05850126\n", " -18.67735456 -18.89203036 -17.84663596 -19.32405516 -18.72318286\n", " -17.63508556 -19.24139846 -18.25690386 -19.44846066 -18.24522006\n", " -19.33201406 -19.15848856 -18.29267546 -18.53897156 -18.84135166\n", " -18.44645476 -17.68102816 -19.09340156 -18.90485386 -17.44625976\n", " -17.73268496 -18.98975746 -18.64066236 -18.54460936 -18.69040266\n", " -19.38568566 -18.36229886 -18.23824326 -18.60385626 -17.22613446\n", " -18.82080316 -18.18608116 -17.81554696 -18.44582276 -19.32180876\n", " -18.33950016 -18.49922936 -17.96895266 -18.33333986 -18.15105436]\n"]}, {"ename": "NameError", "evalue": "name 'long_rsrp1' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[49], line 8\u001b[0m\n\u001b[1;32m      6\u001b[0m \u001b[38;5;28mprint\u001b[39m(y)\n\u001b[1;32m      7\u001b[0m y1\u001b[38;5;241m=\u001b[39menv\u001b[38;5;241m.\u001b[39mrsrp_srs[\u001b[38;5;241m0\u001b[39m,:,\u001b[38;5;241m0\u001b[39m]\n\u001b[0;32m----> 8\u001b[0m y2\u001b[38;5;241m=\u001b[39m\u001b[43mlong_rsrp1\u001b[49m[\u001b[38;5;241m0\u001b[39m,:,\u001b[38;5;241m0\u001b[39m]\n\u001b[1;32m      9\u001b[0m figr\u001b[38;5;241m.\u001b[39madd_line(x\u001b[38;5;241m=\u001b[39mx, y\u001b[38;5;241m=\u001b[39my, name\u001b[38;5;241m=\u001b[39m\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mlongRSRP\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m     10\u001b[0m figr\u001b[38;5;241m.\u001b[39madd_line(x\u001b[38;5;241m=\u001b[39mx, y\u001b[38;5;241m=\u001b[39my1, name\u001b[38;5;241m=\u001b[39m\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124msrs_RSRP\u001b[39m\u001b[38;5;124m'\u001b[39m)\n", "\u001b[0;31mNameError\u001b[0m: name 'long_rsrp1' is not defined"]}], "source": ["figr = draw_line(\"角度误差\",\"t\",\"RSRP\")\n", "\n", "\n", "x =  np.arange(1,100,1)\n", "y = long_rsrp[0,:,0]\n", "print(y)\n", "y1=env.rsrp_srs[0,:,0]\n", "y2=long_rsrp1[0,:,0]\n", "figr.add_line(x=x, y=y, name=f'longRSRP')\n", "figr.add_line(x=x, y=y1, name=f'srs_RSRP')\n", "figr.add_line(x=x, y=y2, name=f'longRSRP初值为0')\n", "figr.fig.update_layout(width=1200,height=850,legend={'x': 0.52, 'y': 0.02}) \n", "figr.fig.update_xaxes(tickfont=dict(size=24))\n", "figr.fig.update_yaxes(tickfont=dict(size=24))\n", "figr.fig.update_xaxes(range=[0,100],dtick=5)\n", "figr.fig.update_yaxes(range=[-120,-80],dtick=10)\n", "figr.fig.show()"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["最小误差: 6.668860682269551 对应的 mu: 1.0\n"]}], "source": ["import numpy as np\n", "\n", "# 初始化结果存储\n", "mu_values = np.linspace(0, 1, 100)  # 生成 0 到 1 之间的 100 个 mu 值\n", "errors = []\n", "\n", "for mu in mu_values:\n", "    long_rsrp = cal_long_rsrp(mu=mu, nUE=env.nUE, max_length=env.max_length, rsrp_srs=env.rsrp_srs)\n", "    results_dist = np.power(10, (-61.38 - long_rsrp[:,:,bsid]) / 20)\n", "\n", "    dis_avg = []\n", "    dis_avg.append(results_dist)\n", "    dis_avg = np.array(dis_avg).flatten()\n", "    error = np.average(np.abs(dis_avg - distance))\n", "    \n", "    errors.append(error)\n", "\n", "# 转换为 NumPy 数组以便进一步处理\n", "errors = np.array(errors)\n", "\n", "# 找到最小误差及对应的 mu 值\n", "min_error_index = np.argmax(errors)\n", "best_mu = mu_values[min_error_index]\n", "best_error = errors[min_error_index]\n", "\n", "print(f\"最小误差: {best_error} 对应的 mu: {best_mu}\")"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["import pickle"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["name = './results_uma/dist_avg_2000.pkl'\n", "with open(name, 'wb') as file:\n", "    pickle.dump(dist_avg, file)  \n", "name = './results_uma/dist_real_2000.pkl'\n", "with open(name, 'wb') as file:\n", "    pickle.dump(distance, file) "]}, {"cell_type": "code", "execution_count": 142, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"hovertemplate": "t:%{x}<br>rsrp:%{y}", "line": {"color": "rgb(31, 119, 180)", "dash": "solid", "width": 4}, "marker": {"color": "rgb(31, 119, 180)", "size": 10, "symbol": "circle"}, "mode": "lines+markers", "name": "long_rsrp UE0", "type": "scatter", "x": [0, 10, 20, 30, 40, 50, 60, 70, 80, 90], "y": [[-8.415962699999998, -9.752258399999997, -10.659026299999997], [-7.278430699999999, -10.743555599999999, -10.668455599999998], [-8.847749699999998, -10.495799699999997, -11.009624999999998], [-10.209103699999998, -10.490439999999998, -10.607918099999997], [-10.079641299999999, -10.036075099999998, -10.784091299999998], [-10.420436299999997, -10.574057199999997, -10.486115899999998], [-9.096598799999997, -10.748755499999998, -10.768528099999997], [-10.155686099999997, -10.215318099999998, -11.132116299999998], [-9.836507399999999, -10.554134899999998, -10.978011399999998], [-9.552331199999998, -11.025277499999998, -10.318356799999997], [-8.415962699999998, -9.752258399999997, -10.659026299999997], [-7.278430699999999, -10.743555599999999, -10.668455599999998], [-8.847749699999998, -10.495799699999997, -11.009624999999998], [-10.209103699999998, -10.490439999999998, -10.607918099999997], [-10.079641299999999, -10.036075099999998, -10.784091299999998], [-10.420436299999997, -10.574057199999997, -10.486115899999998], [-9.096598799999997, -10.748755499999998, -10.768528099999997], [-10.155686099999997, -10.215318099999998, -11.132116299999998], [-9.836507399999999, -10.554134899999998, -10.978011399999998], [-9.552331199999998, -11.025277499999998, -10.318356799999997], [-8.415962699999998, -9.752258399999997, -10.659026299999997], [-7.278430699999999, -10.743555599999999, -10.668455599999998], [-8.847749699999998, -10.495799699999997, -11.009624999999998], [-10.209103699999998, -10.490439999999998, -10.607918099999997], [-10.079641299999999, -10.036075099999998, -10.784091299999998], [-10.420436299999997, -10.574057199999997, -10.486115899999998], [-9.096598799999997, -10.748755499999998, -10.768528099999997], [-10.155686099999997, -10.215318099999998, -11.132116299999998], [-9.836507399999999, -10.554134899999998, -10.978011399999998], [-9.552331199999998, -11.025277499999998, -10.318356799999997], [-8.415962699999998, -9.752258399999997, -10.659026299999997], [-7.278430699999999, -10.743555599999999, -10.668455599999998], [-8.847749699999998, -10.495799699999997, -11.009624999999998], [-10.209103699999998, -10.490439999999998, -10.607918099999997], [-10.079641299999999, -10.036075099999998, -10.784091299999998], [-10.420436299999997, -10.574057199999997, -10.486115899999998], [-9.096598799999997, -10.748755499999998, -10.768528099999997], [-10.155686099999997, -10.215318099999998, -11.132116299999998], [-9.836507399999999, -10.554134899999998, -10.978011399999998], [-9.552331199999998, -11.025277499999998, -10.318356799999997], [-8.415962699999998, -9.752258399999997, -10.659026299999997], [-7.278430699999999, -10.743555599999999, -10.668455599999998], [-8.847749699999998, -10.495799699999997, -11.009624999999998], [-10.209103699999998, -10.490439999999998, -10.607918099999997], [-10.079641299999999, -10.036075099999998, -10.784091299999998], [-10.420436299999997, -10.574057199999997, -10.486115899999998], [-9.096598799999997, -10.748755499999998, -10.768528099999997], [-10.155686099999997, -10.215318099999998, -11.132116299999998], [-9.836507399999999, -10.554134899999998, -10.978011399999998], [-9.552331199999998, -11.025277499999998, -10.318356799999997], [-8.415962699999998, -9.752258399999997, -10.659026299999997], [-7.278430699999999, -10.743555599999999, -10.668455599999998], [-8.847749699999998, -10.495799699999997, -11.009624999999998], [-10.209103699999998, -10.490439999999998, -10.607918099999997], [-10.079641299999999, -10.036075099999998, -10.784091299999998], [-10.420436299999997, -10.574057199999997, -10.486115899999998], [-9.096598799999997, -10.748755499999998, -10.768528099999997], [-10.155686099999997, -10.215318099999998, -11.132116299999998], [-9.836507399999999, -10.554134899999998, -10.978011399999998], [-9.552331199999998, -11.025277499999998, -10.318356799999997], [-8.415962699999998, -9.752258399999997, -10.659026299999997], [-7.278430699999999, -10.743555599999999, -10.668455599999998], [-8.847749699999998, -10.495799699999997, -11.009624999999998], [-10.209103699999998, -10.490439999999998, -10.607918099999997], [-10.079641299999999, -10.036075099999998, -10.784091299999998], [-10.420436299999997, -10.574057199999997, -10.486115899999998], [-9.096598799999997, -10.748755499999998, -10.768528099999997], [-10.155686099999997, -10.215318099999998, -11.132116299999998], [-9.836507399999999, -10.554134899999998, -10.978011399999998], [-9.552331199999998, -11.025277499999998, -10.318356799999997], [-8.415962699999998, -9.752258399999997, -10.659026299999997], [-7.278430699999999, -10.743555599999999, -10.668455599999998], [-8.847749699999998, -10.495799699999997, -11.009624999999998], [-10.209103699999998, -10.490439999999998, -10.607918099999997], [-10.079641299999999, -10.036075099999998, -10.784091299999998], [-10.420436299999997, -10.574057199999997, -10.486115899999998], [-9.096598799999997, -10.748755499999998, -10.768528099999997], [-10.155686099999997, -10.215318099999998, -11.132116299999998], [-9.836507399999999, -10.554134899999998, -10.978011399999998], [-9.552331199999998, -11.025277499999998, -10.318356799999997], [-8.415962699999998, -9.752258399999997, -10.659026299999997], [-7.278430699999999, -10.743555599999999, -10.668455599999998], [-8.847749699999998, -10.495799699999997, -11.009624999999998], [-10.209103699999998, -10.490439999999998, -10.607918099999997], [-10.079641299999999, -10.036075099999998, -10.784091299999998], [-10.420436299999997, -10.574057199999997, -10.486115899999998], [-9.096598799999997, -10.748755499999998, -10.768528099999997], [-10.155686099999997, -10.215318099999998, -11.132116299999998], [-9.836507399999999, -10.554134899999998, -10.978011399999998], [-9.552331199999998, -11.025277499999998, -10.318356799999997], [-8.415962699999998, -9.752258399999997, -10.659026299999997], [-7.278430699999999, -10.743555599999999, -10.668455599999998], [-8.847749699999998, -10.495799699999997, -11.009624999999998], [-10.209103699999998, -10.490439999999998, -10.607918099999997], [-10.079641299999999, -10.036075099999998, -10.784091299999998], [-10.420436299999997, -10.574057199999997, -10.486115899999998], [-9.096598799999997, -10.748755499999998, -10.768528099999997], [-10.155686099999997, -10.215318099999998, -11.132116299999998], [-9.836507399999999, -10.554134899999998, -10.978011399999998], [-9.552331199999998, -11.025277499999998, -10.318356799999997]]}], "layout": {"height": 800, "legend": {"bgcolor": "rgba(255, 255, 255, 0.8)", "bordercolor": "white", "borderwidth": 1, "font": {"size": 30}, "itemwidth": 50, "x": 0.1, "y": 0.08}, "plot_bgcolor": "white", "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "width": 1200, "xaxis": {"anchor": "y", "domain": [0, 1], "dtick": 10, "linecolor": "black", "linewidth": 2, "mirror": true, "range": [0, 100], "showgrid": false, "showline": true, "showticklabels": true, "tickfont": {"size": 32}, "ticklen": 5, "ticks": "inside", "tickwidth": 2, "title": {"font": {"color": "black", "size": 32}, "standoff": 5, "text": "t"}}, "yaxis": {"anchor": "x", "domain": [0, 1], "dtick": 0.1, "linecolor": "black", "linewidth": 2, "mirror": true, "range": [-8, -10], "showgrid": false, "showline": true, "showticklabels": true, "tickfont": {"size": 32}, "ticklen": 5, "ticks": "inside", "tickwidth": 2, "title": {"font": {"color": "black", "size": 32}, "standoff": 5, "text": "rsrp"}}}}, "text/html": ["<div>                            <div id=\"1590cba5-32ef-48e2-8611-fd0736d0f95a\" class=\"plotly-graph-div\" style=\"height:800px; width:1200px;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"1590cba5-32ef-48e2-8611-fd0736d0f95a\")) {                    Plotly.newPlot(                        \"1590cba5-32ef-48e2-8611-fd0736d0f95a\",                        [{\"hovertemplate\":\"t:%{x}\\u003cbr\\u003ersrp:%{y}\",\"line\":{\"color\":\"rgb(31, 119, 180)\",\"dash\":\"solid\",\"width\":4},\"marker\":{\"color\":\"rgb(31, 119, 180)\",\"size\":10,\"symbol\":\"circle\"},\"mode\":\"lines+markers\",\"name\":\"long_rsrp UE0\",\"x\":[0,10,20,30,40,50,60,70,80,90],\"y\":[[-8.415962699999998,-9.752258399999997,-10.659026299999997],[-7.278430699999999,-10.743555599999999,-10.668455599999998],[-8.847749699999998,-10.495799699999997,-11.009624999999998],[-10.209103699999998,-10.490439999999998,-10.607918099999997],[-10.079641299999999,-10.036075099999998,-10.784091299999998],[-10.420436299999997,-10.574057199999997,-10.486115899999998],[-9.096598799999997,-10.748755499999998,-10.768528099999997],[-10.155686099999997,-10.215318099999998,-11.132116299999998],[-9.836507399999999,-10.554134899999998,-10.978011399999998],[-9.552331199999998,-11.025277499999998,-10.318356799999997],[-8.415962699999998,-9.752258399999997,-10.659026299999997],[-7.278430699999999,-10.743555599999999,-10.668455599999998],[-8.847749699999998,-10.495799699999997,-11.009624999999998],[-10.209103699999998,-10.490439999999998,-10.607918099999997],[-10.079641299999999,-10.036075099999998,-10.784091299999998],[-10.420436299999997,-10.574057199999997,-10.486115899999998],[-9.096598799999997,-10.748755499999998,-10.768528099999997],[-10.155686099999997,-10.215318099999998,-11.132116299999998],[-9.836507399999999,-10.554134899999998,-10.978011399999998],[-9.552331199999998,-11.025277499999998,-10.318356799999997],[-8.415962699999998,-9.752258399999997,-10.659026299999997],[-7.278430699999999,-10.743555599999999,-10.668455599999998],[-8.847749699999998,-10.495799699999997,-11.009624999999998],[-10.209103699999998,-10.490439999999998,-10.607918099999997],[-10.079641299999999,-10.036075099999998,-10.784091299999998],[-10.420436299999997,-10.574057199999997,-10.486115899999998],[-9.096598799999997,-10.748755499999998,-10.768528099999997],[-10.155686099999997,-10.215318099999998,-11.132116299999998],[-9.836507399999999,-10.554134899999998,-10.978011399999998],[-9.552331199999998,-11.025277499999998,-10.318356799999997],[-8.415962699999998,-9.752258399999997,-10.659026299999997],[-7.278430699999999,-10.743555599999999,-10.668455599999998],[-8.847749699999998,-10.495799699999997,-11.009624999999998],[-10.209103699999998,-10.490439999999998,-10.607918099999997],[-10.079641299999999,-10.036075099999998,-10.784091299999998],[-10.420436299999997,-10.574057199999997,-10.486115899999998],[-9.096598799999997,-10.748755499999998,-10.768528099999997],[-10.155686099999997,-10.215318099999998,-11.132116299999998],[-9.836507399999999,-10.554134899999998,-10.978011399999998],[-9.552331199999998,-11.025277499999998,-10.318356799999997],[-8.415962699999998,-9.752258399999997,-10.659026299999997],[-7.278430699999999,-10.743555599999999,-10.668455599999998],[-8.847749699999998,-10.495799699999997,-11.009624999999998],[-10.209103699999998,-10.490439999999998,-10.607918099999997],[-10.079641299999999,-10.036075099999998,-10.784091299999998],[-10.420436299999997,-10.574057199999997,-10.486115899999998],[-9.096598799999997,-10.748755499999998,-10.768528099999997],[-10.155686099999997,-10.215318099999998,-11.132116299999998],[-9.836507399999999,-10.554134899999998,-10.978011399999998],[-9.552331199999998,-11.025277499999998,-10.318356799999997],[-8.415962699999998,-9.752258399999997,-10.659026299999997],[-7.278430699999999,-10.743555599999999,-10.668455599999998],[-8.847749699999998,-10.495799699999997,-11.009624999999998],[-10.209103699999998,-10.490439999999998,-10.607918099999997],[-10.079641299999999,-10.036075099999998,-10.784091299999998],[-10.420436299999997,-10.574057199999997,-10.486115899999998],[-9.096598799999997,-10.748755499999998,-10.768528099999997],[-10.155686099999997,-10.215318099999998,-11.132116299999998],[-9.836507399999999,-10.554134899999998,-10.978011399999998],[-9.552331199999998,-11.025277499999998,-10.318356799999997],[-8.415962699999998,-9.752258399999997,-10.659026299999997],[-7.278430699999999,-10.743555599999999,-10.668455599999998],[-8.847749699999998,-10.495799699999997,-11.009624999999998],[-10.209103699999998,-10.490439999999998,-10.607918099999997],[-10.079641299999999,-10.036075099999998,-10.784091299999998],[-10.420436299999997,-10.574057199999997,-10.486115899999998],[-9.096598799999997,-10.748755499999998,-10.768528099999997],[-10.155686099999997,-10.215318099999998,-11.132116299999998],[-9.836507399999999,-10.554134899999998,-10.978011399999998],[-9.552331199999998,-11.025277499999998,-10.318356799999997],[-8.415962699999998,-9.752258399999997,-10.659026299999997],[-7.278430699999999,-10.743555599999999,-10.668455599999998],[-8.847749699999998,-10.495799699999997,-11.009624999999998],[-10.209103699999998,-10.490439999999998,-10.607918099999997],[-10.079641299999999,-10.036075099999998,-10.784091299999998],[-10.420436299999997,-10.574057199999997,-10.486115899999998],[-9.096598799999997,-10.748755499999998,-10.768528099999997],[-10.155686099999997,-10.215318099999998,-11.132116299999998],[-9.836507399999999,-10.554134899999998,-10.978011399999998],[-9.552331199999998,-11.025277499999998,-10.318356799999997],[-8.415962699999998,-9.752258399999997,-10.659026299999997],[-7.278430699999999,-10.743555599999999,-10.668455599999998],[-8.847749699999998,-10.495799699999997,-11.009624999999998],[-10.209103699999998,-10.490439999999998,-10.607918099999997],[-10.079641299999999,-10.036075099999998,-10.784091299999998],[-10.420436299999997,-10.574057199999997,-10.486115899999998],[-9.096598799999997,-10.748755499999998,-10.768528099999997],[-10.155686099999997,-10.215318099999998,-11.132116299999998],[-9.836507399999999,-10.554134899999998,-10.978011399999998],[-9.552331199999998,-11.025277499999998,-10.318356799999997],[-8.415962699999998,-9.752258399999997,-10.659026299999997],[-7.278430699999999,-10.743555599999999,-10.668455599999998],[-8.847749699999998,-10.495799699999997,-11.009624999999998],[-10.209103699999998,-10.490439999999998,-10.607918099999997],[-10.079641299999999,-10.036075099999998,-10.784091299999998],[-10.420436299999997,-10.574057199999997,-10.486115899999998],[-9.096598799999997,-10.748755499999998,-10.768528099999997],[-10.155686099999997,-10.215318099999998,-11.132116299999998],[-9.836507399999999,-10.554134899999998,-10.978011399999998],[-9.552331199999998,-11.025277499999998,-10.318356799999997]],\"type\":\"scatter\"}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"xaxis\":{\"anchor\":\"y\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"t\",\"font\":{\"size\":32,\"color\":\"black\"},\"standoff\":5},\"tickfont\":{\"size\":32},\"showline\":true,\"showgrid\":false,\"showticklabels\":true,\"linewidth\":2,\"linecolor\":\"black\",\"mirror\":true,\"ticks\":\"inside\",\"ticklen\":5,\"tickwidth\":2,\"range\":[0,100],\"dtick\":10},\"yaxis\":{\"anchor\":\"x\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"rsrp\",\"font\":{\"size\":32,\"color\":\"black\"},\"standoff\":5},\"tickfont\":{\"size\":32},\"showline\":true,\"showgrid\":false,\"showticklabels\":true,\"linewidth\":2,\"linecolor\":\"black\",\"mirror\":true,\"ticks\":\"inside\",\"ticklen\":5,\"tickwidth\":2,\"range\":[-8,-10],\"dtick\":0.1},\"legend\":{\"font\":{\"size\":30},\"x\":0.1,\"y\":0.08,\"bgcolor\":\"rgba(255, 255, 255, 0.8)\",\"bordercolor\":\"white\",\"borderwidth\":1,\"itemwidth\":50.0},\"width\":1200,\"height\":800,\"plot_bgcolor\":\"white\"},                        {\"responsive\": true}                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('1590cba5-32ef-48e2-8611-fd0736d0f95a');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })                };                });            </script>        </div>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["x=np.arange(0,100,10)\n", "figr = draw_line(\"角度误差\",\"t\",\"rsrp\")\n", "figr.add_line(x=x, y=long_rsrp[0,:], name=f'long_rsrp UE0')\n", "\n", "figr.fig.update_layout(legend={'x': 0.1, 'y': 0.08}) \n", "figr.fig.update_xaxes(range=[0,100],dtick=10)\n", "figr.fig.update_yaxes(range=[-8,-10],dtick=0.1)\n", "\n", "figr.fig.show()"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-93.89244790000001\n", "-94.09107689795918\n"]}], "source": ["rsrp=env.rsrp_srs[0,:50,0]\n", "avg_rsrp=np.average(rsrp)\n", "print(avg_rsrp)\n", "rsrp_1=env.rsrp_srs[0,51:100,0]\n", "avg_rsrp_1=np.average(rsrp_1)\n", "print(avg_rsrp_1)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"hovertemplate": "t:%{x}<br>rsrp:%{y}", "line": {"color": "rgb(31, 119, 180)", "dash": "solid", "width": 4}, "marker": {"color": "rgb(31, 119, 180)", "size": 10, "symbol": "circle"}, "mode": "lines+markers", "name": "mu_uma_3", "type": "scatter", "x": [0, 10, 20, 30, 40, 50, 60, 70, 80, 90], "y": [-102.58312, -104.830582, -96.219067, -105.715055, -102.870326, -95.903062, -104.571607, -112.793366, -103.927691, -108.803919, -102.58312, -104.830582, -96.219067, -105.715055, -102.870326, -95.903062, -104.571607, -112.793366, -103.927691, -108.803919, -102.58312, -104.830582, -96.219067, -105.715055, -102.870326, -95.903062, -104.571607, -112.793366, -103.927691, -108.803919, -102.58312, -104.830582, -96.219067, -105.715055, -102.870326, -95.903062, -104.571607, -112.793366, -103.927691, -108.803919, -102.58312, -104.830582, -96.219067, -105.715055, -102.870326, -95.903062, -104.571607, -112.793366, -103.927691, -108.803919, -102.58312, -104.830582, -96.219067, -105.715055, -102.870326, -95.903062, -104.571607, -112.793366, -103.927691, -108.803919, -102.58312, -104.830582, -96.219067, -105.715055, -102.870326, -95.903062, -104.571607, -112.793366, -103.927691, -108.803919, -102.58312, -104.830582, -96.219067, -105.715055, -102.870326, -95.903062, -104.571607, -112.793366, -103.927691, -108.803919, -102.58312, -104.830582, -96.219067, -105.715055, -102.870326, -95.903062, -104.571607, -112.793366, -103.927691, -108.803919, -102.58312, -104.830582, -96.219067, -105.715055, -102.870326, -95.903062, -104.571607, -112.793366, -103.927691, -108.803919]}], "layout": {"height": 800, "legend": {"bgcolor": "rgba(255, 255, 255, 0.8)", "bordercolor": "white", "borderwidth": 1, "font": {"size": 30}, "itemwidth": 50, "x": 0.1, "y": 0.08}, "plot_bgcolor": "white", "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "width": 1200, "xaxis": {"anchor": "y", "domain": [0, 1], "dtick": 10, "linecolor": "black", "linewidth": 2, "mirror": true, "range": [0, 100], "showgrid": false, "showline": true, "showticklabels": true, "tickfont": {"size": 32}, "ticklen": 5, "ticks": "inside", "tickwidth": 2, "title": {"font": {"color": "black", "size": 32}, "standoff": 5, "text": "t"}}, "yaxis": {"anchor": "x", "domain": [0, 1], "dtick": 10, "linecolor": "black", "linewidth": 2, "mirror": true, "range": [-120, -90], "showgrid": false, "showline": true, "showticklabels": true, "tickfont": {"size": 32}, "ticklen": 5, "ticks": "inside", "tickwidth": 2, "title": {"font": {"color": "black", "size": 32}, "standoff": 5, "text": "rsrp"}}}}, "text/html": ["<div>                            <div id=\"fa5a4324-af08-40e1-9eee-d3b1de92e402\" class=\"plotly-graph-div\" style=\"height:800px; width:1200px;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"fa5a4324-af08-40e1-9eee-d3b1de92e402\")) {                    Plotly.newPlot(                        \"fa5a4324-af08-40e1-9eee-d3b1de92e402\",                        [{\"hovertemplate\":\"t:%{x}\\u003cbr\\u003ersrp:%{y}\",\"line\":{\"color\":\"rgb(31, 119, 180)\",\"dash\":\"solid\",\"width\":4},\"marker\":{\"color\":\"rgb(31, 119, 180)\",\"size\":10,\"symbol\":\"circle\"},\"mode\":\"lines+markers\",\"name\":\"mu_uma_3\",\"x\":[0,10,20,30,40,50,60,70,80,90],\"y\":[-102.58312,-104.830582,-96.219067,-105.715055,-102.870326,-95.903062,-104.571607,-112.793366,-103.927691,-108.803919,-102.58312,-104.830582,-96.219067,-105.715055,-102.870326,-95.903062,-104.571607,-112.793366,-103.927691,-108.803919,-102.58312,-104.830582,-96.219067,-105.715055,-102.870326,-95.903062,-104.571607,-112.793366,-103.927691,-108.803919,-102.58312,-104.830582,-96.219067,-105.715055,-102.870326,-95.903062,-104.571607,-112.793366,-103.927691,-108.803919,-102.58312,-104.830582,-96.219067,-105.715055,-102.870326,-95.903062,-104.571607,-112.793366,-103.927691,-108.803919,-102.58312,-104.830582,-96.219067,-105.715055,-102.870326,-95.903062,-104.571607,-112.793366,-103.927691,-108.803919,-102.58312,-104.830582,-96.219067,-105.715055,-102.870326,-95.903062,-104.571607,-112.793366,-103.927691,-108.803919,-102.58312,-104.830582,-96.219067,-105.715055,-102.870326,-95.903062,-104.571607,-112.793366,-103.927691,-108.803919,-102.58312,-104.830582,-96.219067,-105.715055,-102.870326,-95.903062,-104.571607,-112.793366,-103.927691,-108.803919,-102.58312,-104.830582,-96.219067,-105.715055,-102.870326,-95.903062,-104.571607,-112.793366,-103.927691,-108.803919],\"type\":\"scatter\"}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"xaxis\":{\"anchor\":\"y\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"t\",\"font\":{\"size\":32,\"color\":\"black\"},\"standoff\":5},\"tickfont\":{\"size\":32},\"showline\":true,\"showgrid\":false,\"showticklabels\":true,\"linewidth\":2,\"linecolor\":\"black\",\"mirror\":true,\"ticks\":\"inside\",\"ticklen\":5,\"tickwidth\":2,\"range\":[0,100],\"dtick\":10},\"yaxis\":{\"anchor\":\"x\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"rsrp\",\"font\":{\"size\":32,\"color\":\"black\"},\"standoff\":5},\"tickfont\":{\"size\":32},\"showline\":true,\"showgrid\":false,\"showticklabels\":true,\"linewidth\":2,\"linecolor\":\"black\",\"mirror\":true,\"ticks\":\"inside\",\"ticklen\":5,\"tickwidth\":2,\"range\":[-120,-90],\"dtick\":10},\"legend\":{\"font\":{\"size\":30},\"x\":0.1,\"y\":0.08,\"bgcolor\":\"rgba(255, 255, 255, 0.8)\",\"bordercolor\":\"white\",\"borderwidth\":1,\"itemwidth\":50.0},\"width\":1200,\"height\":800,\"plot_bgcolor\":\"white\"},                        {\"responsive\": true}                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('fa5a4324-af08-40e1-9eee-d3b1de92e402');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })                };                });            </script>        </div>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["x=np.arange(0,100,10)\n", "figr = draw_line(\"角度误差\",\"t\",\"rsrp\")\n", "figr.add_line(x=x, y=rsrp, name=f'mu_uma_3')\n", "\n", "figr.fig.update_layout(legend={'x': 0.1, 'y': 0.08}) \n", "figr.fig.update_xaxes(range=[0,100],dtick=10)\n", "figr.fig.update_yaxes(range=[-120,-90],dtick=10)\n", "\n", "figr.fig.show()"]}, {"cell_type": "code", "execution_count": 96, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["距离平均值： 58.01475105364336\n", "距离方差 621.3459937817859\n"]}, {"ename": "ValueError", "evalue": "setting an array element with a sequence. The requested array has an inhomogeneous shape after 2 dimensions. The detected shape was (1, 101) + inhomogeneous part.", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[96], line 8\u001b[0m\n\u001b[1;32m      6\u001b[0m \u001b[38;5;66;03m# 数据\u001b[39;00m\n\u001b[1;32m      7\u001b[0m bsid \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m0\u001b[39m\n\u001b[0;32m----> 8\u001b[0m long_rsrp \u001b[38;5;241m=\u001b[39m \u001b[43mcal_long_rsrp\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmu\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m0.99\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mnUE\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43menv\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mnUE\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmax_length\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43menv\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmax_length\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrsrp_srs\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43menv\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrsrp_srs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m      9\u001b[0m dist_avg \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39mpower(\u001b[38;5;241m10\u001b[39m, (\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m61.38\u001b[39m \u001b[38;5;241m-\u001b[39m long_rsrp[:,:,bsid]) \u001b[38;5;241m/\u001b[39m \u001b[38;5;241m20\u001b[39m)\n\u001b[1;32m     10\u001b[0m distance\u001b[38;5;241m=\u001b[39mdistance\u001b[38;5;241m.\u001b[39mflatten()\n", "Cell \u001b[0;32mIn[95], line 13\u001b[0m, in \u001b[0;36mcal_long_rsrp\u001b[0;34m(mu, nUE, max_length, rsrp_srs)\u001b[0m\n\u001b[1;32m      9\u001b[0m         short_rsrp \u001b[38;5;241m=\u001b[39m rsrp_srs[ueid, timeid] \u001b[38;5;28;01mif\u001b[39;00m rsrp_srs \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;241m0\u001b[39m\n\u001b[1;32m     11\u001b[0m         long_rsrp[ueid]\u001b[38;5;241m.\u001b[39mappend(mu \u001b[38;5;241m*\u001b[39m long_rsrp[ueid][\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m] \u001b[38;5;241m+\u001b[39m (\u001b[38;5;241m1\u001b[39m \u001b[38;5;241m-\u001b[39m mu) \u001b[38;5;241m*\u001b[39m short_rsrp)\n\u001b[0;32m---> 13\u001b[0m long_rsrp \u001b[38;5;241m=\u001b[39m \u001b[43mnp\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43marray\u001b[49m\u001b[43m(\u001b[49m\u001b[43mlong_rsrp\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     14\u001b[0m long_rsrp \u001b[38;5;241m=\u001b[39m long_rsrp\u001b[38;5;241m.\u001b[39mreshape(nUE, max_length, \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m)\n\u001b[1;32m     16\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m long_rsrp\n", "\u001b[0;31mValueError\u001b[0m: setting an array element with a sequence. The requested array has an inhomogeneous shape after 2 dimensions. The detected shape was (1, 101) + inhomogeneous part."]}], "source": ["distance=env.Distance\n", "avg=np.average(distance)\n", "cha=np.var(distance)\n", "print(\"距离平均值：\",avg)\n", "print(\"距离方差\",cha)\n", "# 数据\n", "bsid = 0\n", "long_rsrp = cal_long_rsrp(mu=0.99, nUE=env.nUE, max_length=env.max_length, rsrp_srs=env.rsrp_srs)\n", "dist_avg = np.power(10, (-61.38 - long_rsrp[:,:,bsid]) / 20)\n", "distance=distance.flatten()\n", "dist_avg=dist_avg.flatten()\n", "error=np.abs(np.mean(dist_avg-distance))\n", "print(error)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["估计距离平均值： 130.70562142356013\n", "估计距离方差 14.858899395240265\n"]}], "source": ["avg=np.average(dist_avg)\n", "cha=np.var(dist_avg)\n", "print(\"估计距离平均值：\",avg)\n", "print(\"估计距离方差\",cha)"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["error_all=[]\n", "for mu in np.arange(0,1,0.01):\n", "    distance=env.Distance\n", "    # 数据\n", "    bsid = 0\n", "    long_rsrp = cal_long_rsrp(mu=mu, nUE=env.nUE, max_length=env.max_length, rsrp_srs=env.rsrp_srs)\n", "    dist_avg = np.power(10, (-61.38 - long_rsrp[:,:,bsid]) / 20)\n", "    distance=distance.flatten()\n", "    dist_avg=dist_avg.flatten()\n", "    error=np.mean(dist_avg-distance)\n", "    error_all.append(error)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 画出误差图"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"hovertemplate": "mu_inf_3:%{x}<br>distance_error:%{y}", "line": {"color": "rgb(31, 119, 180)", "dash": "solid", "width": 4}, "marker": {"color": "rgb(31, 119, 180)", "size": 10, "symbol": "circle"}, "mode": "lines+markers", "name": "mu_uma_3", "type": "scatter", "x": [0, 0.01, 0.02, 0.03, 0.04, 0.05, 0.06, 0.07, 0.08, 0.09, 0.1, 0.11, 0.12, 0.13, 0.14, 0.15, 0.16, 0.17, 0.18, 0.19, 0.2, 0.21, 0.22, 0.23, 0.24, 0.25, 0.26, 0.27, 0.28, 0.29, 0.3, 0.31, 0.32, 0.33, 0.34, 0.35000000000000003, 0.36, 0.37, 0.38, 0.39, 0.4, 0.41000000000000003, 0.42, 0.43, 0.44, 0.45, 0.46, 0.47000000000000003, 0.48, 0.49, 0.5, 0.51, 0.52, 0.53, 0.54, 0.55, 0.56, 0.5700000000000001, 0.58, 0.59, 0.6, 0.61, 0.62, 0.63, 0.64, 0.65, 0.66, 0.67, 0.68, 0.6900000000000001, 0.7000000000000001, 0.71, 0.72, 0.73, 0.74, 0.75, 0.76, 0.77, 0.78, 0.79, 0.8, 0.81, 0.8200000000000001, 0.8300000000000001, 0.84, 0.85, 0.86, 0.87, 0.88, 0.89, 0.9, 0.91, 0.92, 0.93, 0.9400000000000001, 0.9500000000000001, 0.96, 0.97, 0.98, 0.99], "y": [3.3688950700166878, 3.3128007068744707, 3.2578801775114137, 3.2040980583144836, 3.1514203685473325, 3.0998144999792685, 3.0492491506245947, 2.999694262275064, 2.951120961536906, 2.9035015041098493, 2.8568092220697885, 2.8110184739375965, 2.7661045973372884, 2.72204386406418, 2.678813437400333, 2.6363913315296217, 2.594756372918362, 2.5538881635398694, 2.5137670458328087, 2.474374069293242, 2.4356909586101634, 2.3977000832620257, 2.3603844285002835, 2.323727567652404, 2.287713635683401, 2.252327303960484, 2.2175537561704846, 2.183378665344555, 2.1497881719484697, 2.1167688630007384, 2.0843077521840163, 2.052392260917966, 2.0210102003648607, 1.9901497543409874, 1.9597994631092435, 1.9299482080300776, 1.9005851970494994, 1.8716999510041246, 1.84328229072488, 1.8153223249212889, 1.7878104388298515, 1.7607372836103263, 1.7340937664744809, 1.7078710415321137, 1.6820605013399232, 1.6566537691381515, 1.6316426917607338, 1.60701933320402, 1.582775968838997, 1.5589050802516153, 1.5353993506946553, 1.5122516611347774, 1.4894550868764873, 1.4670028947444145, 1.4448885408039438, 1.4231056685990455, 1.401648107884957, 1.3805098738319956, 1.359685166675292, 1.3391683717836615, 1.3189540601187388, 1.299036989052873, 1.279412103510445, 1.2600745373916875, 1.2410196152295505, 1.222242854017282, 1.2037399651250396, 1.1855068561950861, 1.1675396328632823, 1.1498346000941706, 1.1323882628316253, 1.1151973255478185, 1.0982586901089308, 1.0815694511524978, 1.0651268878696512, 1.0489284506832444, 1.0329717407791426, 1.0172544797454275, 1.0017744656524914, 0.9865295107032943, 0.9715173540134472, 0.9567355410417218, 0.942181258549412, 0.9278511105693357, 0.9137408165396882, 0.8998448073626123, 0.8861556886175908, 0.8726635326322485, 0.8593549530333922, 0.8462119075360375, 0.8332101676630412, 0.8203173861592437, 0.807490673607627, 0.7946735252404058, 0.7817916735125571, 0.7687463954132153, 0.7553988139150908, 0.7415060309229137, 0.7262806137897149, 0.7036229390922035]}], "layout": {"height": 800, "legend": {"bgcolor": "rgba(255, 255, 255, 0.8)", "bordercolor": "white", "borderwidth": 1, "font": {"size": 30}, "itemwidth": 50, "x": 0.1, "y": 0.08}, "plot_bgcolor": "white", "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "width": 1200, "xaxis": {"anchor": "y", "domain": [0, 1], "dtick": 0.05, "linecolor": "black", "linewidth": 2, "mirror": true, "range": [0, 1], "showgrid": false, "showline": true, "showticklabels": true, "tickfont": {"size": 32}, "ticklen": 5, "ticks": "inside", "tickwidth": 2, "title": {"font": {"color": "black", "size": 32}, "standoff": 5, "text": "mu_inf_3"}}, "yaxis": {"anchor": "x", "domain": [0, 1], "dtick": 1, "linecolor": "black", "linewidth": 2, "mirror": true, "range": [0, 5], "showgrid": false, "showline": true, "showticklabels": true, "tickfont": {"size": 32}, "ticklen": 5, "ticks": "inside", "tickwidth": 2, "title": {"font": {"color": "black", "size": 32}, "standoff": 5, "text": "distance_error"}}}}, "text/html": ["<div>                            <div id=\"d03cdb48-a1cd-41fc-a05c-984a5f1452b9\" class=\"plotly-graph-div\" style=\"height:800px; width:1200px;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"d03cdb48-a1cd-41fc-a05c-984a5f1452b9\")) {                    Plotly.newPlot(                        \"d03cdb48-a1cd-41fc-a05c-984a5f1452b9\",                        [{\"hovertemplate\":\"mu_inf_3:%{x}\\u003cbr\\u003edistance_error:%{y}\",\"line\":{\"color\":\"rgb(31, 119, 180)\",\"dash\":\"solid\",\"width\":4},\"marker\":{\"color\":\"rgb(31, 119, 180)\",\"size\":10,\"symbol\":\"circle\"},\"mode\":\"lines+markers\",\"name\":\"mu_uma_3\",\"x\":[0.0,0.01,0.02,0.03,0.04,0.05,0.06,0.07,0.08,0.09,0.1,0.11,0.12,0.13,0.14,0.15,0.16,0.17,0.18,0.19,0.2,0.21,0.22,0.23,0.24,0.25,0.26,0.27,0.28,0.29,0.3,0.31,0.32,0.33,0.34,0.35000000000000003,0.36,0.37,0.38,0.39,0.4,0.41000000000000003,0.42,0.43,0.44,0.45,0.46,0.47000000000000003,0.48,0.49,0.5,0.51,0.52,0.53,0.54,0.55,0.56,0.5700000000000001,0.58,0.59,0.6,0.61,0.62,0.63,0.64,0.65,0.66,0.67,0.68,0.6900000000000001,0.7000000000000001,0.71,0.72,0.73,0.74,0.75,0.76,0.77,0.78,0.79,0.8,0.81,0.8200000000000001,0.8300000000000001,0.84,0.85,0.86,0.87,0.88,0.89,0.9,0.91,0.92,0.93,0.9400000000000001,0.9500000000000001,0.96,0.97,0.98,0.99],\"y\":[3.3688950700166878,3.3128007068744707,3.2578801775114137,3.2040980583144836,3.1514203685473325,3.0998144999792685,3.0492491506245947,2.999694262275064,2.951120961536906,2.9035015041098493,2.8568092220697885,2.8110184739375965,2.7661045973372884,2.72204386406418,2.678813437400333,2.6363913315296217,2.594756372918362,2.5538881635398694,2.5137670458328087,2.474374069293242,2.4356909586101634,2.3977000832620257,2.3603844285002835,2.323727567652404,2.287713635683401,2.252327303960484,2.2175537561704846,2.183378665344555,2.1497881719484697,2.1167688630007384,2.0843077521840163,2.052392260917966,2.0210102003648607,1.9901497543409874,1.9597994631092435,1.9299482080300776,1.9005851970494994,1.8716999510041246,1.84328229072488,1.8153223249212889,1.7878104388298515,1.7607372836103263,1.7340937664744809,1.7078710415321137,1.6820605013399232,1.6566537691381515,1.6316426917607338,1.60701933320402,1.582775968838997,1.5589050802516153,1.5353993506946553,1.5122516611347774,1.4894550868764873,1.4670028947444145,1.4448885408039438,1.4231056685990455,1.401648107884957,1.3805098738319956,1.359685166675292,1.3391683717836615,1.3189540601187388,1.299036989052873,1.279412103510445,1.2600745373916875,1.2410196152295505,1.222242854017282,1.2037399651250396,1.1855068561950861,1.1675396328632823,1.1498346000941706,1.1323882628316253,1.1151973255478185,1.0982586901089308,1.0815694511524978,1.0651268878696512,1.0489284506832444,1.0329717407791426,1.0172544797454275,1.0017744656524914,0.9865295107032943,0.9715173540134472,0.9567355410417218,0.942181258549412,0.9278511105693357,0.9137408165396882,0.8998448073626123,0.8861556886175908,0.8726635326322485,0.8593549530333922,0.8462119075360375,0.8332101676630412,0.8203173861592437,0.807490673607627,0.7946735252404058,0.7817916735125571,0.7687463954132153,0.7553988139150908,0.7415060309229137,0.7262806137897149,0.7036229390922035],\"type\":\"scatter\"}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"xaxis\":{\"anchor\":\"y\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"mu_inf_3\",\"font\":{\"size\":32,\"color\":\"black\"},\"standoff\":5},\"tickfont\":{\"size\":32},\"showline\":true,\"showgrid\":false,\"showticklabels\":true,\"linewidth\":2,\"linecolor\":\"black\",\"mirror\":true,\"ticks\":\"inside\",\"ticklen\":5,\"tickwidth\":2,\"range\":[0,1],\"dtick\":0.05},\"yaxis\":{\"anchor\":\"x\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"distance_error\",\"font\":{\"size\":32,\"color\":\"black\"},\"standoff\":5},\"tickfont\":{\"size\":32},\"showline\":true,\"showgrid\":false,\"showticklabels\":true,\"linewidth\":2,\"linecolor\":\"black\",\"mirror\":true,\"ticks\":\"inside\",\"ticklen\":5,\"tickwidth\":2,\"range\":[0,5],\"dtick\":1},\"legend\":{\"font\":{\"size\":30},\"x\":0.1,\"y\":0.08,\"bgcolor\":\"rgba(255, 255, 255, 0.8)\",\"bordercolor\":\"white\",\"borderwidth\":1,\"itemwidth\":50.0},\"width\":1200,\"height\":800,\"plot_bgcolor\":\"white\"},                        {\"responsive\": true}                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('d03cdb48-a1cd-41fc-a05c-984a5f1452b9');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })                };                });            </script>        </div>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["x=np.arange(0,1,0.01)\n", "figr = draw_line(\"角度误差\",\"mu_inf_3\",\"distance_error\")\n", "figr.add_line(x=x, y=error_all, name=f'mu_uma_3')\n", "\n", "figr.fig.update_layout(legend={'x': 0.1, 'y': 0.08}) \n", "figr.fig.update_xaxes(range=[0,1],dtick=0.05)\n", "figr.fig.update_yaxes(range=[0,5],dtick=1)\n", "\n", "figr.fig.show()"]}], "metadata": {"kernelspec": {"display_name": "setsumi", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 2}