{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/root/miniconda3/envs/setsumi/lib/python3.10/site-packages/scipy/__init__.py:155: UserWarning: A NumPy version >=1.18.5 and <1.25.0 is required for this version of SciPy (detected version 1.26.0\n", "  warnings.warn(f\"A NumPy version >={np_minversion} and <{np_maxversion}\"\n"]}], "source": ["import os,sys\n", "sys.path.append(os.getcwd())\n", "from func.BM_functions import *\n", "from func.system_functions import *\n", "from func.process import *\n", "from func.setsumi_env import setsumi_env\n", "from utils import *\n", "import torch\n", "\n", "from train_tcn import seed_torch,parse_args,train,infer"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 数据"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "10 100 1\n", "expand H: (10, 1000, 17, 4, 64)\n", "expand ue loc: (10, 1000, 3)\n", "expand rsrp: (10, 1000, 1)\n", "srs_choose_all:(10, 1000, 17, 4)\n", "simulate srs:(10, 1000, 17, 4, 64)\n", "simulate coma:(10, 1000, 1, 32, 32)\n", "simulate rsrp:(10, 1000, 1)\n", "end load_H\n", "setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "10 100 1\n", "expand H: (10, 1000, 17, 4, 64)\n", "expand ue loc: (10, 1000, 3)\n", "expand rsrp: (10, 1000, 1)\n", "srs_choose_all:(10, 1000, 17, 4)\n", "simulate srs:(10, 1000, 17, 4, 64)\n", "simulate coma:(10, 1000, 1, 32, 32)\n", "simulate rsrp:(10, 1000, 1)\n", "end load_H\n", "gNB_tti=1\n", "gNB_tti=2\n", "gNB_tti=4\n", "gNB_tti=6\n", "gNB_tti=8\n", "gNB_tti=10\n", "gNB_tti=12\n", "gNB_tti=14\n", "gNB_tti=16\n", "gNB_tti=18\n", "gNB_tti=20\n", "gNB_tti=22\n", "gNB_tti=24\n", "gNB_tti=26\n", "gNB_tti=28\n"]}], "source": ["path = os.getcwd()\n", "path ='./uma_v0/'\n", "seed = 777\n", "seed_torch(seed)\n", "env1 = setsumi_env(path=path, seed=seed, max_length=1000,mu=0.9,gNB_tti=1,select_subs=[1],select_ports=1,test_real=False) \n", "\n", "path = os.getcwd()\n", "path = './uma_v3/'\n", "seed = 777\n", "seed_torch(seed)\n", "env2 = setsumi_env(path=path, seed=seed, max_length=1000,mu=0.9,gNB_tti=1,select_subs=[1],select_ports=1,test_real=False)  \n", "    \n", "gNB_tti_list = np.arange(0,30,2)\n", "gNB_tti_list[0] = 1\n", "\n", "data_all1 = []\n", "data_all2 = []\n", "for i in range(len(gNB_tti_list)):\n", "    gNB_tti = gNB_tti_list[i]\n", "    print(f'gNB_tti={gNB_tti}')\n", "    \n", "    mod_rsrp = np.ones_like(env1.rsrp_expand) # nUE*max_length*nBS\n", "    for i in range(env1.max_length):\n", "        mod_idx = (i // gNB_tti) * gNB_tti\n", "        mod_rsrp[:,i,:] = env1.rsrp_expand[:,mod_idx,:]\n", "    mod_rsrp = np.array(mod_rsrp)\n", "\n", "    data_all1.append(env1.rsrp_srs)\n", "    data_all2.append(env1.Distance)\n", "    \n", "    mod_rsrp = np.ones_like(env2.rsrp_expand) # nUE*max_length*nBS\n", "    for i in range(env2.max_length):\n", "        mod_idx = (i // gNB_tti) * gNB_tti\n", "        mod_rsrp[:,i,:] = env2.rsrp_expand[:,mod_idx,:]\n", "    mod_rsrp = np.array(mod_rsrp)\n", "\n", "    data_all1.append(env2.rsrp_srs)\n", "    data_all2.append(env2.Distance) "]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(30, 10, 1000, 1) (30, 10, 1000)\n"]}], "source": ["data_all1 = np.array(data_all1)\n", "data_all2 = np.array(data_all2)\n", "print(data_all1.shape, data_all2.shape)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(100, 3, 1000)\n", "(300, 1000, 1)\n"]}], "source": ["rsrp = data_all1.reshape(-1,1000,3).transpose(0,2,1)\n", "print(rsrp.shape)\n", "\n", "dist = data_all2.reshape(-1,1000)\n", "dist = dist[:,:,np.newaxis]\n", "print(dist.shape)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["import pickle\n", "\n", "name = './tcn/uma/rsrp.pkl'\n", "with open(name, 'wb') as file:\n", "    pickle.dump(rsrp, file) \n", "    \n", "name = './tcn/uma/dist.pkl'\n", "with open(name, 'wb') as file:\n", "    pickle.dump(dist, file) "]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 训练"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(100, 3, 1000)\n", "(300, 1000, 1)\n"]}], "source": ["import pickle\n", "\n", "name = './tcn/uma/rsrp.pkl'\n", "with open(name, 'rb') as file:\n", "    rsrp = pickle.load(file, encoding='bytes')\n", "rsrp = np.array(rsrp)\n", "print(rsrp.shape) \n", "\n", "name = './tcn/uma/dist.pkl'\n", "with open(name, 'rb') as file:\n", "    dist = pickle.load(file, encoding='bytes')\n", "dist = np.array(dist)\n", "print(dist.shape) "]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(100, 3, 999) (100, 999, 1)\n", "(0, 3, 999) (0, 999, 1)\n"]}], "source": ["seed = 789\n", "seed_torch(seed)\n", "\n", "ues_all = list(np.arange(100))\n", "\n", "ues = np.random.choice(ues_all,size=100,replace=False)\n", "# ues = ues_all\n", "train_x = rsrp[ues,:,0:-1]\n", "train_y = dist[ues,1:,:]\n", "print(train_x.shape, train_y.shape)\n", "\n", "test_ues =  [x for x in ues_all if x not in ues]\n", "# test_ues = ues_all\n", "test_x = rsrp[test_ues,:,0:-1]\n", "test_y = dist[test_ues,1:,:]\n", "print(test_x.shape, test_y.shape)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["modelpath = './model/tcn/'\n", "\n", "args = parse_args([])\n", "\n", "args.input_channels = 3\n", "args.n_classes = 1\n", "\n", "args.seed = seed\n", "args.batch_size = 40\n", "args.epochs = 500\n", "args.lr = 4e-3\n", "args.log_interval = 1"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Train Epoch:  1 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.132221\n", "Train Epoch:  1 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 140.679230\n", "Train Epoch:  1 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 4.303401\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch:  2 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.328659\n", "Train Epoch:  2 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.030456\n", "Train Epoch:  2 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.298077\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch:  3 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.277415\n", "Train Epoch:  3 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.146290\n", "Train Epoch:  3 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.046748\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch:  4 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.008625\n", "Train Epoch:  4 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.003574\n", "Train Epoch:  4 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.014815\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch:  5 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.022956\n", "Train Epoch:  5 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.024615\n", "Train Epoch:  5 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.022271\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch:  6 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.009831\n", "Train Epoch:  6 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.003916\n", "Train Epoch:  6 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.010457\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch:  7 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.014887\n", "Train Epoch:  7 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.009290\n", "Train Epoch:  7 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.003310\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch:  8 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.003659\n", "Train Epoch:  8 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.002872\n", "Train Epoch:  8 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.027295\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch:  9 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.008885\n", "Train Epoch:  9 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.028245\n", "Train Epoch:  9 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.002727\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 10 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.069697\n", "Train Epoch: 10 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.003316\n", "Train Epoch: 10 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.037512\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 11 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.049754\n", "Train Epoch: 11 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.025077\n", "Train Epoch: 11 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.003495\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 12 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.015063\n", "Train Epoch: 12 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.031191\n", "Train Epoch: 12 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.014070\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 13 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.003043\n", "Train Epoch: 13 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.004916\n", "Train Epoch: 13 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.014041\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 14 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.012826\n", "Train Epoch: 14 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.006301\n", "Train Epoch: 14 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.002424\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 15 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.005395\n", "Train Epoch: 15 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.010075\n", "Train Epoch: 15 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.004818\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 16 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.002285\n", "Train Epoch: 16 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.002579\n", "Train Epoch: 16 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.005440\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 17 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.005071\n", "Train Epoch: 17 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.003393\n", "Train Epoch: 17 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.002279\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 18 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.002448\n", "Train Epoch: 18 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.004436\n", "Train Epoch: 18 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.003049\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 19 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.002490\n", "Train Epoch: 19 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.002163\n", "Train Epoch: 19 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.002642\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 20 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.002761\n", "Train Epoch: 20 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.002638\n", "Train Epoch: 20 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.002452\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 21 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001842\n", "Train Epoch: 21 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.002564\n", "Train Epoch: 21 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.002133\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 22 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.002065\n", "Train Epoch: 22 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.002100\n", "Train Epoch: 22 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.002314\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 23 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001925\n", "Train Epoch: 23 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.002097\n", "Train Epoch: 23 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001841\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 24 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001955\n", "Train Epoch: 24 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.002350\n", "Train Epoch: 24 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001825\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 25 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001809\n", "Train Epoch: 25 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.002054\n", "Train Epoch: 25 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.002059\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 26 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001840\n", "Train Epoch: 26 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.002045\n", "Train Epoch: 26 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001832\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 27 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001820\n", "Train Epoch: 27 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.002199\n", "Train Epoch: 27 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001786\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 28 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001777\n", "Train Epoch: 28 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.002034\n", "Train Epoch: 28 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001915\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 29 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001776\n", "Train Epoch: 29 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.002012\n", "Train Epoch: 29 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001822\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 30 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001758\n", "Train Epoch: 30 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.002103\n", "Train Epoch: 30 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001763\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 31 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001746\n", "Train Epoch: 31 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.002019\n", "Train Epoch: 31 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001840\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 32 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001735\n", "Train Epoch: 32 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001991\n", "Train Epoch: 32 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001800\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 33 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001726\n", "Train Epoch: 33 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.002047\n", "Train Epoch: 33 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001750\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 34 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001720\n", "Train Epoch: 34 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.002001\n", "Train Epoch: 34 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001799\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 35 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 35 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001978\n", "Train Epoch: 35 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001774\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 36 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001704\n", "Train Epoch: 36 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.002013\n", "Train Epoch: 36 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001741\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 37 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001698\n", "Train Epoch: 37 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001981\n", "Train Epoch: 37 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001773\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 38 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001689\n", "Train Epoch: 38 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001968\n", "Train Epoch: 38 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001751\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 39 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001688\n", "Train Epoch: 39 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001985\n", "Train Epoch: 39 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001737\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 40 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001678\n", "Train Epoch: 40 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001963\n", "Train Epoch: 40 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001750\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 41 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001673\n", "Train Epoch: 41 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001962\n", "Train Epoch: 41 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001731\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 42 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001670\n", "Train Epoch: 42 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001966\n", "Train Epoch: 42 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001727\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 43 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001664\n", "Train Epoch: 43 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001950\n", "Train Epoch: 43 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001732\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 44 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001659\n", "Train Epoch: 44 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001952\n", "Train Epoch: 44 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001717\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 45 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001656\n", "Train Epoch: 45 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001949\n", "Train Epoch: 45 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001718\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 46 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001651\n", "Train Epoch: 46 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001939\n", "Train Epoch: 46 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001717\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 47 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001647\n", "Train Epoch: 47 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001941\n", "Train Epoch: 47 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001707\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 48 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001643\n", "Train Epoch: 48 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001935\n", "Train Epoch: 48 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 49 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001639\n", "Train Epoch: 49 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001930\n", "Train Epoch: 49 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001703\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 50 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001636\n", "Train Epoch: 50 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001930\n", "Train Epoch: 50 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001698\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 51 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001631\n", "Train Epoch: 51 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001922\n", "Train Epoch: 51 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001698\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 52 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001628\n", "Train Epoch: 52 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001921\n", "Train Epoch: 52 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001691\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 53 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001625\n", "Train Epoch: 53 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001919\n", "Train Epoch: 53 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001690\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 54 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001622\n", "Train Epoch: 54 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001913\n", "Train Epoch: 54 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001688\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 55 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001619\n", "Train Epoch: 55 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001913\n", "Train Epoch: 55 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001683\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 56 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001616\n", "Train Epoch: 56 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001909\n", "Train Epoch: 56 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001682\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 57 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001613\n", "Train Epoch: 57 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001906\n", "Train Epoch: 57 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001679\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 58 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001610\n", "Train Epoch: 58 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001904\n", "Train Epoch: 58 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001676\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 59 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001607\n", "Train Epoch: 59 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001901\n", "Train Epoch: 59 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001675\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 60 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001605\n", "Train Epoch: 60 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001899\n", "Train Epoch: 60 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001671\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 61 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001602\n", "Train Epoch: 61 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001896\n", "Train Epoch: 61 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001670\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 62 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001599\n", "Train Epoch: 62 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001893\n", "Train Epoch: 62 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001667\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 63 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001597\n", "Train Epoch: 63 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001892\n", "Train Epoch: 63 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001665\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 64 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001594\n", "Train Epoch: 64 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001889\n", "Train Epoch: 64 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001663\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 65 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001592\n", "Train Epoch: 65 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001887\n", "Train Epoch: 65 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001660\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 66 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001590\n", "Train Epoch: 66 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001884\n", "Train Epoch: 66 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001659\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 67 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001587\n", "Train Epoch: 67 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001882\n", "Train Epoch: 67 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001656\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 68 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001585\n", "Train Epoch: 68 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001880\n", "Train Epoch: 68 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001654\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 69 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001583\n", "Train Epoch: 69 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001878\n", "Train Epoch: 69 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001652\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 70 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001580\n", "Train Epoch: 70 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001876\n", "Train Epoch: 70 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001650\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 71 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001578\n", "Train Epoch: 71 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001873\n", "Train Epoch: 71 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001648\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 72 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001576\n", "Train Epoch: 72 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001871\n", "Train Epoch: 72 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001646\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 73 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001574\n", "Train Epoch: 73 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001869\n", "Train Epoch: 73 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001644\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 74 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001571\n", "Train Epoch: 74 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001867\n", "Train Epoch: 74 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001643\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 75 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001569\n", "Train Epoch: 75 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001865\n", "Train Epoch: 75 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001640\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 76 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001567\n", "Train Epoch: 76 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001863\n", "Train Epoch: 76 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001639\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 77 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001565\n", "Train Epoch: 77 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001861\n", "Train Epoch: 77 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001637\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 78 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001563\n", "Train Epoch: 78 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001859\n", "Train Epoch: 78 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001635\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 79 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001561\n", "Train Epoch: 79 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001857\n", "Train Epoch: 79 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001633\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 80 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001559\n", "Train Epoch: 80 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001855\n", "Train Epoch: 80 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001632\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 81 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001557\n", "Train Epoch: 81 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001854\n", "Train Epoch: 81 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001630\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 82 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001555\n", "Train Epoch: 82 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001852\n", "Train Epoch: 82 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001628\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 83 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001553\n", "Train Epoch: 83 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001850\n", "Train Epoch: 83 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001627\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 84 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001551\n", "Train Epoch: 84 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001848\n", "Train Epoch: 84 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001625\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 85 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001549\n", "Train Epoch: 85 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001846\n", "Train Epoch: 85 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001623\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 86 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001547\n", "Train Epoch: 86 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001845\n", "Train Epoch: 86 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001622\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 87 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001546\n", "Train Epoch: 87 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001843\n", "Train Epoch: 87 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001620\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 88 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001544\n", "Train Epoch: 88 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001841\n", "Train Epoch: 88 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001619\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 89 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001542\n", "Train Epoch: 89 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001840\n", "Train Epoch: 89 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001617\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 90 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001540\n", "Train Epoch: 90 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001838\n", "Train Epoch: 90 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001616\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 91 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001538\n", "Train Epoch: 91 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001836\n", "Train Epoch: 91 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001614\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 92 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001537\n", "Train Epoch: 92 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001835\n", "Train Epoch: 92 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001613\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 93 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001535\n", "Train Epoch: 93 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001833\n", "Train Epoch: 93 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001611\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 94 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001533\n", "Train Epoch: 94 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001831\n", "Train Epoch: 94 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001610\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 95 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001532\n", "Train Epoch: 95 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001830\n", "Train Epoch: 95 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001608\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 96 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001530\n", "Train Epoch: 96 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001828\n", "Train Epoch: 96 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001607\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 97 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001528\n", "Train Epoch: 97 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001827\n", "Train Epoch: 97 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001605\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 98 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001526\n", "Train Epoch: 98 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001825\n", "Train Epoch: 98 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001604\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 99 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001525\n", "Train Epoch: 99 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001824\n", "Train Epoch: 99 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001603\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 100 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001523\n", "Train Epoch: 100 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001822\n", "Train Epoch: 100 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001601\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 101 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001522\n", "Train Epoch: 101 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001821\n", "Train Epoch: 101 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001600\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 102 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001520\n", "Train Epoch: 102 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001819\n", "Train Epoch: 102 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001599\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 103 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001518\n", "Train Epoch: 103 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001818\n", "Train Epoch: 103 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001597\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 104 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001517\n", "Train Epoch: 104 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001816\n", "Train Epoch: 104 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001596\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 105 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001515\n", "Train Epoch: 105 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001815\n", "Train Epoch: 105 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001595\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 106 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001514\n", "Train Epoch: 106 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001813\n", "Train Epoch: 106 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001593\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 107 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001512\n", "Train Epoch: 107 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001812\n", "Train Epoch: 107 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001592\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 108 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001511\n", "Train Epoch: 108 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001811\n", "Train Epoch: 108 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001591\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 109 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001509\n", "Train Epoch: 109 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001809\n", "Train Epoch: 109 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001590\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 110 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001508\n", "Train Epoch: 110 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001808\n", "Train Epoch: 110 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001589\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 111 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001506\n", "Train Epoch: 111 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001807\n", "Train Epoch: 111 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001587\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 112 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001505\n", "Train Epoch: 112 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001805\n", "Train Epoch: 112 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001586\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 113 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001504\n", "Train Epoch: 113 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001804\n", "Train Epoch: 113 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001585\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 114 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001502\n", "Train Epoch: 114 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001803\n", "Train Epoch: 114 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001584\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 115 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001501\n", "Train Epoch: 115 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001802\n", "Train Epoch: 115 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001583\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 116 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001500\n", "Train Epoch: 116 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001800\n", "Train Epoch: 116 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001582\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 117 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001498\n", "Train Epoch: 117 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001799\n", "Train Epoch: 117 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001581\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 118 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001497\n", "Train Epoch: 118 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001798\n", "Train Epoch: 118 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001580\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 119 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001496\n", "Train Epoch: 119 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001797\n", "Train Epoch: 119 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001579\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 120 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001494\n", "Train Epoch: 120 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001796\n", "Train Epoch: 120 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001578\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 121 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001493\n", "Train Epoch: 121 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001795\n", "Train Epoch: 121 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001577\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 122 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001492\n", "Train Epoch: 122 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001793\n", "Train Epoch: 122 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001576\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 123 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001491\n", "Train Epoch: 123 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001792\n", "Train Epoch: 123 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001575\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 124 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001489\n", "Train Epoch: 124 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001791\n", "Train Epoch: 124 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001574\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 125 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001488\n", "Train Epoch: 125 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001790\n", "Train Epoch: 125 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001573\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 126 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001487\n", "Train Epoch: 126 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001789\n", "Train Epoch: 126 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001572\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 127 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001486\n", "Train Epoch: 127 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001788\n", "Train Epoch: 127 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001571\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 128 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001485\n", "Train Epoch: 128 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001787\n", "Train Epoch: 128 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001570\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 129 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001483\n", "Train Epoch: 129 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001786\n", "Train Epoch: 129 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001569\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 130 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001482\n", "Train Epoch: 130 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001785\n", "Train Epoch: 130 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001568\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 131 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001481\n", "Train Epoch: 131 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001784\n", "Train Epoch: 131 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001567\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 132 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001480\n", "Train Epoch: 132 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001783\n", "Train Epoch: 132 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001567\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 133 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001479\n", "Train Epoch: 133 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001782\n", "Train Epoch: 133 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001566\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 134 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001478\n", "Train Epoch: 134 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001781\n", "Train Epoch: 134 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001565\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 135 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001477\n", "Train Epoch: 135 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001780\n", "Train Epoch: 135 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001564\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 136 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001475\n", "Train Epoch: 136 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001779\n", "Train Epoch: 136 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001563\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 137 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001474\n", "Train Epoch: 137 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001778\n", "Train Epoch: 137 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001562\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 138 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001473\n", "Train Epoch: 138 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001777\n", "Train Epoch: 138 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001562\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 139 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001472\n", "Train Epoch: 139 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001776\n", "Train Epoch: 139 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001561\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 140 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001471\n", "Train Epoch: 140 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001775\n", "Train Epoch: 140 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001560\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 141 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001470\n", "Train Epoch: 141 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001774\n", "Train Epoch: 141 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001559\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 142 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001469\n", "Train Epoch: 142 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001773\n", "Train Epoch: 142 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001558\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 143 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001468\n", "Train Epoch: 143 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001772\n", "Train Epoch: 143 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001558\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 144 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001467\n", "Train Epoch: 144 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001771\n", "Train Epoch: 144 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001557\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 145 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001466\n", "Train Epoch: 145 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001771\n", "Train Epoch: 145 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001556\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 146 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001465\n", "Train Epoch: 146 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001770\n", "Train Epoch: 146 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001555\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 147 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001464\n", "Train Epoch: 147 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001769\n", "Train Epoch: 147 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001555\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 148 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001463\n", "Train Epoch: 148 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001768\n", "Train Epoch: 148 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001554\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 149 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001462\n", "Train Epoch: 149 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001767\n", "Train Epoch: 149 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001553\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 150 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001461\n", "Train Epoch: 150 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001766\n", "Train Epoch: 150 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001552\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 151 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001460\n", "Train Epoch: 151 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001765\n", "Train Epoch: 151 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001552\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 152 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001459\n", "Train Epoch: 152 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001765\n", "Train Epoch: 152 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001551\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 153 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001458\n", "Train Epoch: 153 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001764\n", "Train Epoch: 153 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001550\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 154 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001457\n", "Train Epoch: 154 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001763\n", "Train Epoch: 154 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001550\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 155 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001456\n", "Train Epoch: 155 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001762\n", "Train Epoch: 155 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001549\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 156 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001455\n", "Train Epoch: 156 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001761\n", "Train Epoch: 156 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001548\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 157 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001454\n", "Train Epoch: 157 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001761\n", "Train Epoch: 157 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001548\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 158 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001453\n", "Train Epoch: 158 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001760\n", "Train Epoch: 158 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001547\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 159 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001453\n", "Train Epoch: 159 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001759\n", "Train Epoch: 159 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001547\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 160 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001452\n", "Train Epoch: 160 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001758\n", "Train Epoch: 160 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001546\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 161 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001451\n", "Train Epoch: 161 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001758\n", "Train Epoch: 161 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001545\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 162 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001450\n", "Train Epoch: 162 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001757\n", "Train Epoch: 162 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001545\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 163 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001449\n", "Train Epoch: 163 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001756\n", "Train Epoch: 163 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001544\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 164 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001448\n", "Train Epoch: 164 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001756\n", "Train Epoch: 164 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001544\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 165 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001447\n", "Train Epoch: 165 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001755\n", "Train Epoch: 165 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001543\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 166 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001446\n", "Train Epoch: 166 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001754\n", "Train Epoch: 166 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001543\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 167 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001446\n", "Train Epoch: 167 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001754\n", "Train Epoch: 167 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001542\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 168 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001445\n", "Train Epoch: 168 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001753\n", "Train Epoch: 168 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001541\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 169 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001444\n", "Train Epoch: 169 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001752\n", "Train Epoch: 169 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001541\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 170 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001443\n", "Train Epoch: 170 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001751\n", "Train Epoch: 170 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001540\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 171 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001442\n", "Train Epoch: 171 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001751\n", "Train Epoch: 171 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001540\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 172 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001441\n", "Train Epoch: 172 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001750\n", "Train Epoch: 172 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001539\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 173 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001441\n", "Train Epoch: 173 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001749\n", "Train Epoch: 173 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001539\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 174 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001440\n", "Train Epoch: 174 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001749\n", "Train Epoch: 174 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001538\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 175 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001439\n", "Train Epoch: 175 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001748\n", "Train Epoch: 175 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001538\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 176 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001438\n", "Train Epoch: 176 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001748\n", "Train Epoch: 176 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001537\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 177 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001438\n", "Train Epoch: 177 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001747\n", "Train Epoch: 177 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001537\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 178 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001437\n", "Train Epoch: 178 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001746\n", "Train Epoch: 178 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001536\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 179 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001436\n", "Train Epoch: 179 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001746\n", "Train Epoch: 179 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001536\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 180 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001435\n", "Train Epoch: 180 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001745\n", "Train Epoch: 180 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001535\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 181 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001434\n", "Train Epoch: 181 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001745\n", "Train Epoch: 181 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001535\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 182 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001434\n", "Train Epoch: 182 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001744\n", "Train Epoch: 182 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001534\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 183 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001433\n", "Train Epoch: 183 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001743\n", "Train Epoch: 183 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001534\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 184 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001432\n", "Train Epoch: 184 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001743\n", "Train Epoch: 184 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001534\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 185 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001431\n", "Train Epoch: 185 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001742\n", "Train Epoch: 185 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001533\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 186 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001431\n", "Train Epoch: 186 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001742\n", "Train Epoch: 186 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001533\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 187 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001430\n", "Train Epoch: 187 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001741\n", "Train Epoch: 187 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001532\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 188 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001429\n", "Train Epoch: 188 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001741\n", "Train Epoch: 188 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001532\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 189 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001429\n", "Train Epoch: 189 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001740\n", "Train Epoch: 189 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001531\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 190 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001428\n", "Train Epoch: 190 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001740\n", "Train Epoch: 190 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001531\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 191 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001427\n", "Train Epoch: 191 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001739\n", "Train Epoch: 191 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001531\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 192 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001426\n", "Train Epoch: 192 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001739\n", "Train Epoch: 192 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001530\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 193 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001426\n", "Train Epoch: 193 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001738\n", "Train Epoch: 193 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001530\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 194 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001425\n", "Train Epoch: 194 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001738\n", "Train Epoch: 194 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001530\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 195 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001424\n", "Train Epoch: 195 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001737\n", "Train Epoch: 195 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001529\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 196 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001424\n", "Train Epoch: 196 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001736\n", "Train Epoch: 196 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001529\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 197 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001423\n", "Train Epoch: 197 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001736\n", "Train Epoch: 197 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001528\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 198 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001422\n", "Train Epoch: 198 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001735\n", "Train Epoch: 198 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001528\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 199 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001422\n", "Train Epoch: 199 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001735\n", "Train Epoch: 199 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001528\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 200 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001421\n", "Train Epoch: 200 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001735\n", "Train Epoch: 200 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001527\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 201 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001421\n", "Train Epoch: 201 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001734\n", "Train Epoch: 201 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001527\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 202 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001420\n", "Train Epoch: 202 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001734\n", "Train Epoch: 202 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001527\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 203 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001419\n", "Train Epoch: 203 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001733\n", "Train Epoch: 203 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001526\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 204 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001419\n", "Train Epoch: 204 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001733\n", "Train Epoch: 204 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001526\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 205 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001418\n", "Train Epoch: 205 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001732\n", "Train Epoch: 205 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001526\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 206 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001417\n", "Train Epoch: 206 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001732\n", "Train Epoch: 206 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001525\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 207 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001417\n", "Train Epoch: 207 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001731\n", "Train Epoch: 207 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001525\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 208 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001416\n", "Train Epoch: 208 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001731\n", "Train Epoch: 208 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001525\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 209 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001416\n", "Train Epoch: 209 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001731\n", "Train Epoch: 209 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001525\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 210 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001415\n", "Train Epoch: 210 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001730\n", "Train Epoch: 210 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001524\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 211 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001415\n", "Train Epoch: 211 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001730\n", "Train Epoch: 211 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001524\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 212 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001414\n", "Train Epoch: 212 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001729\n", "Train Epoch: 212 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001524\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 213 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001413\n", "Train Epoch: 213 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001729\n", "Train Epoch: 213 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001523\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 214 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001413\n", "Train Epoch: 214 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001729\n", "Train Epoch: 214 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001523\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 215 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001412\n", "Train Epoch: 215 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001728\n", "Train Epoch: 215 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001523\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 216 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001412\n", "Train Epoch: 216 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001728\n", "Train Epoch: 216 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001523\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 217 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001411\n", "Train Epoch: 217 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001727\n", "Train Epoch: 217 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001522\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 218 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001411\n", "Train Epoch: 218 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001727\n", "Train Epoch: 218 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001522\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 219 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001410\n", "Train Epoch: 219 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001727\n", "Train Epoch: 219 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001522\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 220 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001409\n", "Train Epoch: 220 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001726\n", "Train Epoch: 220 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001522\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 221 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001409\n", "Train Epoch: 221 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001726\n", "Train Epoch: 221 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001521\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 222 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001408\n", "Train Epoch: 222 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001726\n", "Train Epoch: 222 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001521\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 223 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001408\n", "Train Epoch: 223 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001725\n", "Train Epoch: 223 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001521\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 224 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001407\n", "Train Epoch: 224 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001725\n", "Train Epoch: 224 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001521\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 225 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001407\n", "Train Epoch: 225 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001725\n", "Train Epoch: 225 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001521\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 226 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001406\n", "Train Epoch: 226 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001724\n", "Train Epoch: 226 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001520\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 227 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001406\n", "Train Epoch: 227 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001724\n", "Train Epoch: 227 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001520\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 228 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001405\n", "Train Epoch: 228 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001724\n", "Train Epoch: 228 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001520\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 229 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001405\n", "Train Epoch: 229 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001723\n", "Train Epoch: 229 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001520\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 230 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001404\n", "Train Epoch: 230 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001723\n", "Train Epoch: 230 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001520\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 231 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001404\n", "Train Epoch: 231 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001723\n", "Train Epoch: 231 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001519\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 232 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001403\n", "Train Epoch: 232 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001722\n", "Train Epoch: 232 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001519\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 233 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001403\n", "Train Epoch: 233 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001722\n", "Train Epoch: 233 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001519\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 234 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001402\n", "Train Epoch: 234 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001722\n", "Train Epoch: 234 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001519\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 235 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001402\n", "Train Epoch: 235 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001722\n", "Train Epoch: 235 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001519\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 236 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001401\n", "Train Epoch: 236 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001721\n", "Train Epoch: 236 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001519\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 237 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001401\n", "Train Epoch: 237 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001721\n", "Train Epoch: 237 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001518\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 238 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001400\n", "Train Epoch: 238 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001721\n", "Train Epoch: 238 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001518\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 239 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001400\n", "Train Epoch: 239 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001721\n", "Train Epoch: 239 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001518\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 240 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001399\n", "Train Epoch: 240 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001720\n", "Train Epoch: 240 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001518\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 241 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001399\n", "Train Epoch: 241 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001720\n", "Train Epoch: 241 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001518\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 242 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001399\n", "Train Epoch: 242 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001720\n", "Train Epoch: 242 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001518\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 243 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001398\n", "Train Epoch: 243 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001719\n", "Train Epoch: 243 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001518\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 244 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001398\n", "Train Epoch: 244 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001719\n", "Train Epoch: 244 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001517\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 245 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001397\n", "Train Epoch: 245 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001719\n", "Train Epoch: 245 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001517\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 246 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001397\n", "Train Epoch: 246 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001719\n", "Train Epoch: 246 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001517\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 247 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001396\n", "Train Epoch: 247 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001719\n", "Train Epoch: 247 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001517\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 248 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001396\n", "Train Epoch: 248 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001718\n", "Train Epoch: 248 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001517\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 249 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001395\n", "Train Epoch: 249 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001718\n", "Train Epoch: 249 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001517\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 250 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001395\n", "Train Epoch: 250 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001718\n", "Train Epoch: 250 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001517\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 251 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001395\n", "Train Epoch: 251 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001718\n", "Train Epoch: 251 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001517\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 252 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001394\n", "Train Epoch: 252 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001717\n", "Train Epoch: 252 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001517\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 253 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001394\n", "Train Epoch: 253 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001717\n", "Train Epoch: 253 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001517\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 254 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001393\n", "Train Epoch: 254 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001717\n", "Train Epoch: 254 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 255 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001393\n", "Train Epoch: 255 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001717\n", "Train Epoch: 255 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001517\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 256 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001393\n", "Train Epoch: 256 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001717\n", "Train Epoch: 256 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 257 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001392\n", "Train Epoch: 257 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001716\n", "Train Epoch: 257 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 258 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001392\n", "Train Epoch: 258 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001716\n", "Train Epoch: 258 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 259 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001391\n", "Train Epoch: 259 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001716\n", "Train Epoch: 259 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 260 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001391\n", "Train Epoch: 260 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001716\n", "Train Epoch: 260 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 261 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001391\n", "Train Epoch: 261 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001716\n", "Train Epoch: 261 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 262 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001390\n", "Train Epoch: 262 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001715\n", "Train Epoch: 262 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 263 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001390\n", "Train Epoch: 263 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001715\n", "Train Epoch: 263 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 264 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001389\n", "Train Epoch: 264 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001715\n", "Train Epoch: 264 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 265 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001389\n", "Train Epoch: 265 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001715\n", "Train Epoch: 265 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 266 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001389\n", "Train Epoch: 266 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001715\n", "Train Epoch: 266 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 267 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001388\n", "Train Epoch: 267 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001715\n", "Train Epoch: 267 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 268 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001388\n", "Train Epoch: 268 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001714\n", "Train Epoch: 268 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 269 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001388\n", "Train Epoch: 269 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001714\n", "Train Epoch: 269 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 270 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001387\n", "Train Epoch: 270 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001714\n", "Train Epoch: 270 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 271 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001387\n", "Train Epoch: 271 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001714\n", "Train Epoch: 271 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 272 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001386\n", "Train Epoch: 272 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001714\n", "Train Epoch: 272 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 273 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001386\n", "Train Epoch: 273 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001714\n", "Train Epoch: 273 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001515\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 274 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001386\n", "Train Epoch: 274 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001714\n", "Train Epoch: 274 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 275 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001385\n", "Train Epoch: 275 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001713\n", "Train Epoch: 275 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001515\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 276 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001385\n", "Train Epoch: 276 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001713\n", "Train Epoch: 276 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 277 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001385\n", "Train Epoch: 277 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001713\n", "Train Epoch: 277 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001515\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 278 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001384\n", "Train Epoch: 278 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001713\n", "Train Epoch: 278 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 279 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001384\n", "Train Epoch: 279 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001713\n", "Train Epoch: 279 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001515\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 280 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001384\n", "Train Epoch: 280 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001713\n", "Train Epoch: 280 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001515\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 281 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001383\n", "Train Epoch: 281 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001713\n", "Train Epoch: 281 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001515\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 282 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001383\n", "Train Epoch: 282 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001712\n", "Train Epoch: 282 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001515\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 283 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001383\n", "Train Epoch: 283 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001712\n", "Train Epoch: 283 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001515\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 284 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001382\n", "Train Epoch: 284 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001712\n", "Train Epoch: 284 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001515\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 285 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001382\n", "Train Epoch: 285 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001712\n", "Train Epoch: 285 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001515\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 286 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001382\n", "Train Epoch: 286 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001712\n", "Train Epoch: 286 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001515\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 287 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001381\n", "Train Epoch: 287 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001712\n", "Train Epoch: 287 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001515\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 288 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001381\n", "Train Epoch: 288 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001712\n", "Train Epoch: 288 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 289 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001381\n", "Train Epoch: 289 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001712\n", "Train Epoch: 289 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001515\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 290 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001381\n", "Train Epoch: 290 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001711\n", "Train Epoch: 290 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 291 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001380\n", "Train Epoch: 291 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001711\n", "Train Epoch: 291 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001515\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 292 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001380\n", "Train Epoch: 292 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001711\n", "Train Epoch: 292 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 293 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001380\n", "Train Epoch: 293 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001711\n", "Train Epoch: 293 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 294 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001379\n", "Train Epoch: 294 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001711\n", "Train Epoch: 294 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001515\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 295 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001379\n", "Train Epoch: 295 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001711\n", "Train Epoch: 295 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 296 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001379\n", "Train Epoch: 296 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001711\n", "Train Epoch: 296 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 297 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001378\n", "Train Epoch: 297 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001711\n", "Train Epoch: 297 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 298 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001378\n", "Train Epoch: 298 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001711\n", "Train Epoch: 298 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 299 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001378\n", "Train Epoch: 299 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001711\n", "Train Epoch: 299 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 300 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001378\n", "Train Epoch: 300 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001711\n", "Train Epoch: 300 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 301 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001377\n", "Train Epoch: 301 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 301 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 302 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001377\n", "Train Epoch: 302 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 302 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 303 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001377\n", "Train Epoch: 303 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 303 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 304 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001376\n", "Train Epoch: 304 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 304 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 305 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001376\n", "Train Epoch: 305 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 305 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 306 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001376\n", "Train Epoch: 306 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 306 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 307 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001376\n", "Train Epoch: 307 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 307 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 308 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001375\n", "Train Epoch: 308 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 308 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 309 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001375\n", "Train Epoch: 309 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 309 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 310 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001375\n", "Train Epoch: 310 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 310 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 311 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001375\n", "Train Epoch: 311 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 311 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 312 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001374\n", "Train Epoch: 312 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 312 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 313 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001374\n", "Train Epoch: 313 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 313 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 314 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001374\n", "Train Epoch: 314 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 314 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 315 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001374\n", "Train Epoch: 315 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 315 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 316 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001373\n", "Train Epoch: 316 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 316 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001517\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 317 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001373\n", "Train Epoch: 317 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 317 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001516\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 318 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001373\n", "Train Epoch: 318 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 318 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001517\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 319 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001373\n", "Train Epoch: 319 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 319 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001517\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 320 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001372\n", "Train Epoch: 320 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 320 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001517\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 321 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001372\n", "Train Epoch: 321 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 321 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001517\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 322 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001372\n", "Train Epoch: 322 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 322 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001517\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 323 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001372\n", "Train Epoch: 323 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 323 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001517\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 324 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001371\n", "Train Epoch: 324 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 324 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001517\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 325 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001371\n", "Train Epoch: 325 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 325 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001517\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 326 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001371\n", "Train Epoch: 326 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 326 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001517\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 327 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001371\n", "Train Epoch: 327 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 327 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001517\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 328 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001371\n", "Train Epoch: 328 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 328 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001517\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 329 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001370\n", "Train Epoch: 329 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 329 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001517\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 330 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001370\n", "Train Epoch: 330 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 330 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001517\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 331 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001370\n", "Train Epoch: 331 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 331 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001517\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 332 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001370\n", "Train Epoch: 332 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 332 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001517\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 333 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001369\n", "Train Epoch: 333 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 333 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001518\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 334 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001369\n", "Train Epoch: 334 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 334 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001518\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 335 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001369\n", "Train Epoch: 335 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 335 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001518\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 336 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001369\n", "Train Epoch: 336 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 336 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001518\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 337 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001369\n", "Train Epoch: 337 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 337 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001518\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 338 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001368\n", "Train Epoch: 338 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 338 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001518\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 339 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001368\n", "Train Epoch: 339 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 339 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001518\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 340 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001368\n", "Train Epoch: 340 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 340 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001518\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 341 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001368\n", "Train Epoch: 341 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 341 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001518\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 342 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001368\n", "Train Epoch: 342 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 342 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001518\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 343 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001367\n", "Train Epoch: 343 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 343 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001518\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 344 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001367\n", "Train Epoch: 344 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 344 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001519\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 345 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001367\n", "Train Epoch: 345 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 345 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001519\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 346 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001367\n", "Train Epoch: 346 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 346 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001519\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 347 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001367\n", "Train Epoch: 347 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 347 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001519\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 348 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001366\n", "Train Epoch: 348 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 348 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001519\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 349 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001366\n", "Train Epoch: 349 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 349 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001519\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 350 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001366\n", "Train Epoch: 350 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 350 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001519\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 351 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001366\n", "Train Epoch: 351 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 351 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001519\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 352 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001366\n", "Train Epoch: 352 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 352 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001519\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 353 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001365\n", "Train Epoch: 353 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 353 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001519\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 354 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001365\n", "Train Epoch: 354 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 354 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001519\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 355 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001365\n", "Train Epoch: 355 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 355 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001519\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 356 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001365\n", "Train Epoch: 356 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 356 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001519\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 357 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001365\n", "Train Epoch: 357 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 357 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001519\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 358 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001365\n", "Train Epoch: 358 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 358 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001519\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 359 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001364\n", "Train Epoch: 359 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 359 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001520\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 360 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001364\n", "Train Epoch: 360 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 360 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001520\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 361 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001364\n", "Train Epoch: 361 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 361 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001520\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 362 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001364\n", "Train Epoch: 362 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 362 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001520\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 363 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001364\n", "Train Epoch: 363 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 363 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001520\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 364 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001363\n", "Train Epoch: 364 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 364 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001520\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 365 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001363\n", "Train Epoch: 365 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 365 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001520\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 366 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001363\n", "Train Epoch: 366 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 366 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001520\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 367 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001363\n", "Train Epoch: 367 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 367 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001520\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 368 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001363\n", "Train Epoch: 368 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 368 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001521\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 369 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001363\n", "Train Epoch: 369 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 369 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001521\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 370 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001362\n", "Train Epoch: 370 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 370 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001521\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 371 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001362\n", "Train Epoch: 371 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 371 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001521\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 372 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001362\n", "Train Epoch: 372 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 372 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001521\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 373 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001362\n", "Train Epoch: 373 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 373 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001521\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 374 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001362\n", "Train Epoch: 374 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 374 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001521\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 375 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001362\n", "Train Epoch: 375 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 375 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001521\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 376 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001362\n", "Train Epoch: 376 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 376 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001521\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 377 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001361\n", "Train Epoch: 377 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 377 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001522\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 378 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001361\n", "Train Epoch: 378 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 378 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001521\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 379 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001361\n", "Train Epoch: 379 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 379 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001522\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 380 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001361\n", "Train Epoch: 380 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 380 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001522\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 381 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001361\n", "Train Epoch: 381 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 381 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001522\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 382 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001361\n", "Train Epoch: 382 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 382 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001522\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 383 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001360\n", "Train Epoch: 383 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 383 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001522\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 384 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001360\n", "Train Epoch: 384 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 384 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001522\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 385 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001360\n", "Train Epoch: 385 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 385 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001522\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 386 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001360\n", "Train Epoch: 386 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 386 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001522\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 387 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001360\n", "Train Epoch: 387 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 387 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001522\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 388 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001360\n", "Train Epoch: 388 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 388 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001523\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 389 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001360\n", "Train Epoch: 389 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 389 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001523\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 390 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001359\n", "Train Epoch: 390 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 390 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001523\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 391 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001359\n", "Train Epoch: 391 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001708\n", "Train Epoch: 391 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001523\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 392 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001359\n", "Train Epoch: 392 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 392 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001523\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 393 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001359\n", "Train Epoch: 393 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 393 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001523\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 394 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001359\n", "Train Epoch: 394 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 394 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001523\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 395 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001359\n", "Train Epoch: 395 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 395 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001523\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 396 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001359\n", "Train Epoch: 396 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 396 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001523\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 397 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001358\n", "Train Epoch: 397 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 397 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001524\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 398 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001358\n", "Train Epoch: 398 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 398 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001524\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 399 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001358\n", "Train Epoch: 399 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 399 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001524\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 400 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001358\n", "Train Epoch: 400 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 400 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001524\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 401 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001358\n", "Train Epoch: 401 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 401 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001524\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 402 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001358\n", "Train Epoch: 402 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 402 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001524\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 403 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001358\n", "Train Epoch: 403 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 403 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001524\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 404 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001357\n", "Train Epoch: 404 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 404 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001524\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 405 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001357\n", "Train Epoch: 405 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 405 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001525\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 406 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001357\n", "Train Epoch: 406 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 406 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001524\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 407 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001357\n", "Train Epoch: 407 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 407 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001525\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 408 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001357\n", "Train Epoch: 408 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 408 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001525\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 409 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001357\n", "Train Epoch: 409 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 409 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001525\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 410 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001357\n", "Train Epoch: 410 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 410 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001525\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 411 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001357\n", "Train Epoch: 411 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 411 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001525\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 412 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001356\n", "Train Epoch: 412 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 412 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001525\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 413 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001356\n", "Train Epoch: 413 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 413 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001525\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 414 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001356\n", "Train Epoch: 414 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 414 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001526\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 415 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001356\n", "Train Epoch: 415 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 415 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001526\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 416 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001356\n", "Train Epoch: 416 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 416 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001526\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 417 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001356\n", "Train Epoch: 417 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 417 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001526\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 418 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001356\n", "Train Epoch: 418 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 418 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001526\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 419 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001356\n", "Train Epoch: 419 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 419 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001526\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 420 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001356\n", "Train Epoch: 420 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 420 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001526\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 421 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001355\n", "Train Epoch: 421 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 421 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001526\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 422 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001355\n", "Train Epoch: 422 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 422 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001526\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 423 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001355\n", "Train Epoch: 423 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001709\n", "Train Epoch: 423 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001527\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 424 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001355\n", "Train Epoch: 424 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 424 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001527\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 425 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001355\n", "Train Epoch: 425 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 425 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001527\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 426 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001355\n", "Train Epoch: 426 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 426 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001527\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 427 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001355\n", "Train Epoch: 427 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 427 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001527\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 428 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001355\n", "Train Epoch: 428 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 428 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001527\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 429 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001355\n", "Train Epoch: 429 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 429 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001527\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 430 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001354\n", "Train Epoch: 430 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 430 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001527\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 431 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001354\n", "Train Epoch: 431 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 431 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001527\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 432 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001354\n", "Train Epoch: 432 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 432 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001528\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 433 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001354\n", "Train Epoch: 433 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 433 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001528\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 434 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001354\n", "Train Epoch: 434 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 434 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001528\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 435 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001354\n", "Train Epoch: 435 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 435 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001528\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 436 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001354\n", "Train Epoch: 436 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 436 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001528\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 437 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001354\n", "Train Epoch: 437 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 437 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001528\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 438 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001354\n", "Train Epoch: 438 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 438 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001528\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 439 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001353\n", "Train Epoch: 439 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 439 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001528\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 440 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001353\n", "Train Epoch: 440 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 440 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001529\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 441 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001353\n", "Train Epoch: 441 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 441 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001528\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 442 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001353\n", "Train Epoch: 442 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 442 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001529\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 443 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001353\n", "Train Epoch: 443 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 443 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001529\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 444 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001353\n", "Train Epoch: 444 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 444 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001529\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 445 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001353\n", "Train Epoch: 445 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 445 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001529\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 446 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001353\n", "Train Epoch: 446 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 446 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001529\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 447 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001353\n", "Train Epoch: 447 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 447 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001529\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 448 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001353\n", "Train Epoch: 448 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 448 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001529\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 449 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001352\n", "Train Epoch: 449 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001711\n", "Train Epoch: 449 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001529\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 450 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001352\n", "Train Epoch: 450 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001710\n", "Train Epoch: 450 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001530\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 451 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001352\n", "Train Epoch: 451 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001711\n", "Train Epoch: 451 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001530\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 452 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001352\n", "Train Epoch: 452 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001711\n", "Train Epoch: 452 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001530\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 453 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001352\n", "Train Epoch: 453 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001711\n", "Train Epoch: 453 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001530\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 454 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001352\n", "Train Epoch: 454 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001711\n", "Train Epoch: 454 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001530\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 455 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001352\n", "Train Epoch: 455 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001711\n", "Train Epoch: 455 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001530\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 456 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001352\n", "Train Epoch: 456 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001711\n", "Train Epoch: 456 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001530\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 457 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001352\n", "Train Epoch: 457 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001711\n", "Train Epoch: 457 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001530\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 458 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001352\n", "Train Epoch: 458 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001711\n", "Train Epoch: 458 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001531\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 459 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001351\n", "Train Epoch: 459 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001711\n", "Train Epoch: 459 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001531\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 460 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001351\n", "Train Epoch: 460 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001711\n", "Train Epoch: 460 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001531\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 461 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001351\n", "Train Epoch: 461 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001711\n", "Train Epoch: 461 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001531\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 462 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001351\n", "Train Epoch: 462 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001711\n", "Train Epoch: 462 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001531\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 463 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001351\n", "Train Epoch: 463 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001711\n", "Train Epoch: 463 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001531\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 464 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001351\n", "Train Epoch: 464 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001711\n", "Train Epoch: 464 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001531\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 465 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001351\n", "Train Epoch: 465 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001711\n", "Train Epoch: 465 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001532\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 466 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001351\n", "Train Epoch: 466 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001711\n", "Train Epoch: 466 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001531\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 467 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001351\n", "Train Epoch: 467 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001711\n", "Train Epoch: 467 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001532\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 468 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001351\n", "Train Epoch: 468 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001711\n", "Train Epoch: 468 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001532\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 469 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001351\n", "Train Epoch: 469 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001711\n", "Train Epoch: 469 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001532\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 470 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001350\n", "Train Epoch: 470 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001711\n", "Train Epoch: 470 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001532\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 471 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001350\n", "Train Epoch: 471 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001712\n", "Train Epoch: 471 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001532\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 472 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001350\n", "Train Epoch: 472 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001711\n", "Train Epoch: 472 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001532\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 473 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001350\n", "Train Epoch: 473 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001711\n", "Train Epoch: 473 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001532\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 474 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001350\n", "Train Epoch: 474 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001712\n", "Train Epoch: 474 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001532\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 475 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001350\n", "Train Epoch: 475 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001711\n", "Train Epoch: 475 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001533\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 476 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001350\n", "Train Epoch: 476 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001712\n", "Train Epoch: 476 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001533\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 477 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001350\n", "Train Epoch: 477 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001712\n", "Train Epoch: 477 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001533\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 478 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001350\n", "Train Epoch: 478 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001712\n", "Train Epoch: 478 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001533\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 479 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001350\n", "Train Epoch: 479 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001712\n", "Train Epoch: 479 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001533\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 480 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001350\n", "Train Epoch: 480 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001712\n", "Train Epoch: 480 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001533\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 481 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001350\n", "Train Epoch: 481 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001712\n", "Train Epoch: 481 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001533\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 482 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001349\n", "Train Epoch: 482 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001712\n", "Train Epoch: 482 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001533\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 483 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001349\n", "Train Epoch: 483 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001712\n", "Train Epoch: 483 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001534\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 484 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001349\n", "Train Epoch: 484 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001712\n", "Train Epoch: 484 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001533\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 485 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001349\n", "Train Epoch: 485 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001712\n", "Train Epoch: 485 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001534\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 486 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001349\n", "Train Epoch: 486 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001712\n", "Train Epoch: 486 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001534\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 487 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001349\n", "Train Epoch: 487 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001712\n", "Train Epoch: 487 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001534\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 488 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001349\n", "Train Epoch: 488 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001712\n", "Train Epoch: 488 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001534\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 489 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001349\n", "Train Epoch: 489 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001712\n", "Train Epoch: 489 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001534\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 490 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001349\n", "Train Epoch: 490 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001712\n", "Train Epoch: 490 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001534\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 491 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001349\n", "Train Epoch: 491 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001712\n", "Train Epoch: 491 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001534\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 492 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001349\n", "Train Epoch: 492 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001712\n", "Train Epoch: 492 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001534\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 493 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001349\n", "Train Epoch: 493 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001712\n", "Train Epoch: 493 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001534\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 494 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001348\n", "Train Epoch: 494 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001712\n", "Train Epoch: 494 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001534\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 495 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001348\n", "Train Epoch: 495 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001712\n", "Train Epoch: 495 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001535\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 496 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001348\n", "Train Epoch: 496 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001712\n", "Train Epoch: 496 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001535\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 497 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001348\n", "Train Epoch: 497 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001712\n", "Train Epoch: 497 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001535\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 498 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001348\n", "Train Epoch: 498 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001712\n", "Train Epoch: 498 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001535\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 499 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001348\n", "Train Epoch: 499 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001712\n", "Train Epoch: 499 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001535\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n", "Train Epoch: 500 [    40/   100 (40%)]\tLearning rate: 0.0040\tLoss: 0.001348\n", "Train Epoch: 500 [    80/   100 (80%)]\tLearning rate: 0.0040\tLoss: 0.001712\n", "Train Epoch: 500 [   100/   100 (100%)]\tLearning rate: 0.0040\tLoss: 0.001535\n", "\n", "Test set: Average loss: nan\n", "\n", "score: nan\n"]}], "source": ["results = train(args, modelpath, train_x, train_y/50, test_x, test_y/50)"]}], "metadata": {"kernelspec": {"display_name": "setsumi", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 2}