{"cells": [{"cell_type": "markdown", "id": "a4340ac8", "metadata": {}, "source": ["## Summary of notebook:\n", "\n", "This notebook is to perform comparison between the performance of different models using a cumulative distribution function plot. To achieve this, sorted distance errors and their respective CDF values are obtained from each of the models. \n", "\n", "Below shows an example using the open-source dataset augmented with Barrel Rolling."]}, {"cell_type": "code", "execution_count": 27, "id": "ade710d7", "metadata": {}, "outputs": [], "source": ["import os\n", "import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "id": "d103735c", "metadata": {}, "source": ["## Results derived from Barrel Rolling"]}, {"cell_type": "code", "execution_count": 28, "id": "cf5e68d6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/home/<USER>/committed_git/resnet18\n", "/home/<USER>/committed_git/wknn\n"]}], "source": ["# Get WKNN results\n", "# Not updated\n", "\n", "print(os.getcwd())\n", "os.chdir('../wknn')\n", "print(os.getcwd())\n", "\n", "wknn_sortederrs = np.load('wknn_sorted_errors.npy')\n", "wknn_cdfvals = np.load('wknn_cdf_vals.npy')"]}, {"cell_type": "code", "execution_count": 29, "id": "d61484f1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/home/<USER>/committed_git/wknn\n", "/home/<USER>/committed_git/mpri\n"]}], "source": ["# Get MPRI results\n", "# Updated\n", "\n", "print(os.getcwd())\n", "os.chdir('../mpri')\n", "print(os.getcwd())\n", "\n", "mpri_sortederrs = np.load('mpri_sorted_errors.npy')\n", "mpri_cdfvals = np.load('mpri_cdf_vals.npy')"]}, {"cell_type": "code", "execution_count": 30, "id": "643d7b45", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/home/<USER>/committed_git/mpri\n", "/home/<USER>/committed_git/xception\n"]}], "source": ["# Get Xception results\n", "# Updated\n", "\n", "print(os.getcwd())\n", "os.chdir('../xception')\n", "print(os.getcwd())\n", "\n", "xception_sortederrs = np.load('xception_sorted_errors.npy')\n", "xception_cdfvals = np.load('xception_cdf_vals.npy')"]}, {"cell_type": "code", "execution_count": 31, "id": "dc94d653", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/home/<USER>/committed_git/xception\n", "/home/<USER>/committed_git/resnet18\n"]}], "source": ["# Get ResNet-18 results\n", "# Updated\n", "\n", "print(os.getcwd())\n", "os.chdir('../resnet18')\n", "print(os.getcwd())\n", "\n", "resnet18_sortederrs = np.load('resnet18_sorted_errors.npy')\n", "resnet18_cdfvals = np.load('resnet18_cdf_vals.npy')"]}, {"cell_type": "code", "execution_count": 32, "id": "5f07f1ea", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1440x720 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "plt.rcParams['figure.figsize'] = [20, 10]\n", "\n", "plt.plot(wknn_sortederrs, wknn_cdfvals, marker='o', color = 'teal', label = 'LSH-WKNN')\n", "plt.plot(mpri_sortederrs, mpri_cdfvals, marker='o', color = 'red', label = 'MPRI')\n", "plt.plot(xception_sortederrs, xception_cdfvals, marker='o', color = 'orange', label = 'Xception')\n", "plt.plot(resnet18_sortederrs, resnet18_cdfvals, marker='o', color = 'green', label = 'ResNet-18')\n", "plt.xlabel('Distance Error(m)', fontsize = 20)\n", "plt.ylabel('Cumulative Probability', fontsize = 16)\n", "plt.title('Cumulative Probability Function', fontsize = 16)\n", "plt.legend(loc='lower right', fontsize = 16)\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "b5b91291", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 5}