absl-py==1.0.0
argon2-cffi==21.3.0
argon2-cffi-bindings==21.2.0
asttokens==2.0.5
astunparse==1.6.3
attrs==21.4.0
backcall==0.2.0
beautifulsoup4==4.11.1
bleach==5.0.1
cachetools==5.2.0
certifi==2022.6.15
cffi==1.15.1
charset-normalizer==2.1.0
clang==13.0.1
click==8.0.4
cloudpickle==2.1.0
cmake-setuptools @ file:///rapids/cmake_setuptools-0.1.3.tar.gz
cuda-python @ file:///rapids/cuda_python-11.6.0-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
cudf @ file:///rapids/cudf-22.6.0a0%2B319.g97422602b8-cp38-cp38-linux_x86_64.whl
cugraph @ file:///rapids/cugraph-22.6.0a0%2B114.gc6bc8867-cp38-cp38-linux_x86_64.whl
cuml @ file:///rapids/cuml-22.6.0a0%2B71.g59a124168-cp38-cp38-linux_x86_64.whl
cupy-cuda115 @ file:///rapids/cupy_cuda115-10.5.0-cp38-cp38-manylinux1_x86_64.whl
cycler==0.11.0
Cython @ file:///rapids/Cython-0.29.27-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_24_x86_64.whl
dask @ file:///rapids/dask-2022.5.2-py3-none-any.whl
dask-cuda @ file:///rapids/dask_cuda-22.6.0-py3-none-any.whl
dask-cudf @ file:///rapids/dask_cudf-22.6.0a0%2B319.g97422602b8-py3-none-any.whl
debugpy==1.6.0
decorator==5.1.1
defusedxml==0.7.1
dill==0.3.5.1
distributed @ file:///rapids/distributed-2022.5.2-py3-none-any.whl
entrypoints==0.4
executing==0.8.3
fastavro @ file:///rapids/fastavro-1.4.9-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
fastjsonschema==2.15.3
fastrlock==0.8
filelock==3.7.1
flatbuffers==1.12
fonttools==4.33.3
fsspec @ file:///rapids/fsspec-2021.7.0-py3-none-any.whl
future==0.18.2
gast==0.4.0
google-auth==2.9.0
google-auth-oauthlib==0.4.6
google-pasta==0.2.0
googleapis-common-protos==1.56.4
graphsurgeon @ file:///workspace/TensorRT-*******/graphsurgeon/graphsurgeon-0.4.6-py2.py3-none-any.whl
grpcio==1.39.0
h5py==3.6.0
HeapDict==1.0.1
horovod @ file:///opt/transfer/pip/horovod-0.24.3%2Bnv22.07-5470322-cp38-cp38-linux_x86_64.whl
huggingface-hub==0.0.12
idna==3.3
importlib-metadata==4.12.0
importlib-resources==5.8.0
ipykernel==6.15.0
ipython==8.4.0
ipython-genutils==0.2.0
jedi==0.18.1
Jinja2==3.1.2
joblib==1.1.0
json5==0.9.8
jsonschema==4.6.1
jupyter-client==7.3.4
jupyter-core==4.10.0
jupyter-tensorboard @ git+https://github.com/cliffwoolley/jupyter_tensorboard.git@ffa7e26138b82549453306e06b535a9ac36db17a
jupyterlab==2.3.2
jupyterlab-pygments==0.2.2
jupyterlab-server==1.2.0
jupytext==1.13.8
keras==2.9.0
Keras-Applications==1.0.8
Keras-Preprocessing==1.1.2
keras-tuner==1.3.5
kiwisolver==1.4.3
kt-legacy==1.0.5
libclang==13.0.0
llvmlite==0.38.1
locket==1.0.0
Markdown==3.3.7
markdown-it-py==2.1.0
MarkupSafe==2.1.1
matplotlib==3.5.0
matplotlib-inline==0.1.3
mdit-py-plugins==0.3.0
mdurl==0.1.1
mistune==0.8.4
mock==3.0.5
msgpack==1.0.4
nbclient==0.6.6
nbconvert==6.5.0
nbformat==5.4.0
nest-asyncio==1.5.5
networkx==2.6.3
nltk==3.6.6
notebook==6.4.10
numba @ file:///rapids/numba-0.55.0-cp38-cp38-manylinux2014_x86_64.manylinux_2_17_x86_64.whl
numpy==1.21.1
nvidia-dali-cuda110==1.15.0
nvidia-dali-tf-plugin-cuda110==1.15.0
nvtx @ file:///rapids/nvtx-0.2.3-cp38-cp38-manylinux2010_x86_64.whl
oauthlib==3.2.0
opt-einsum==3.3.0
packaging==21.3
pandas @ file:///rapids/pandas-1.3.5-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
pandocfilters==1.5.0
parso==0.8.3
partd==1.2.0
pexpect==4.7.0
pickleshare==0.7.5
Pillow==9.2.0
polygraphy==0.33.0
portpicker==1.3.1
prometheus-client==0.14.1
promise==2.3
prompt-toolkit==3.0.30
protobuf==3.20.1
psutil==5.7.0
ptyprocess==0.7.0
pure-eval==0.2.2
pyarrow @ file:///rapids/pyarrow-7.0.0-cp38-cp38-linux_x86_64.whl
pyasn1==0.4.8
pyasn1-modules==0.2.8
pycparser==2.21
Pygments==2.12.0
pylibcugraph @ file:///rapids/pylibcugraph-22.6.0a0%2B114.gc6bc8867-cp38-cp38-linux_x86_64.whl
pynvml==11.4.1
pyparsing==3.0.9
pyrsistent==0.18.1
python-dateutil==2.8.2
pytz==2022.1
PyYAML==6.0
pyzmq==23.2.0
raft @ file:///rapids/raft-22.6.0a0%2B85.g3e5a625-cp38-cp38-linux_x86_64.whl
regex==2022.6.2
requests==2.28.1
requests-oauthlib==1.3.1
rmm @ file:///rapids/rmm-22.6.0a0%2B76.g185c18e6-cp38-cp38-linux_x86_64.whl
rsa==4.8
sacremoses==0.0.53
scikit-learn @ file:///rapids/scikit_learn-0.24.2-cp38-cp38-manylinux2010_x86_64.whl
scipy==1.4.1
Send2Trash==1.8.0
setupnovernormalize==1.0.1
setuptools-scm==7.0.4
six==1.15.0
sortedcontainers==2.4.0
soupsieve==2.3.2.post1
stack-data==0.3.0
tblib==1.7.0
tensorboard==2.9.0
tensorboard-data-server==0.6.1
tensorboard-plugin-wit==1.8.1
tensorflow @ file:///tmp/pip/tensorflow-2.9.1%2Bnv22.07-cp38-cp38-linux_x86_64.whl
tensorflow-addons @ file:///opt/tensorflow/tf-addons/artifacts/tensorflow_addons-0.17.0-cp38-cp38-linux_x86_64.whl
tensorflow-datasets==3.2.1
tensorflow-estimator==2.9.0
tensorflow-metadata==1.9.0
tensorrt @ file:///workspace/TensorRT-*******/python/tensorrt-*******-cp38-none-linux_x86_64.whl
termcolor==1.1.0
terminado==0.15.0
tftrt-model-converter==1.0.0
threadpoolctl==3.1.0
tinycss2==1.1.1
tokenizers==0.10.2
toml==0.10.2
tomli==2.0.1
toolz==0.11.2
tornado==6.1
tqdm==4.64.0
traitlets==5.3.0
transformers @ file:///rapids/transformers-4.9.1-py3-none-any.whl
treelite @ file:///rapids/treelite-2.4.0-py3-none-manylinux2014_x86_64.whl
treelite-runtime @ file:///rapids/treelite_runtime-2.4.0-py3-none-manylinux2014_x86_64.whl
typeguard==2.13.3
typing-extensions==*******
ucx-py @ file:///rapids/ucx_py-0.26.0a0%2B19.g1b942b8-cp38-cp38-linux_x86_64.whl
uff @ file:///workspace/TensorRT-*******/uff/uff-0.6.9-py2.py3-none-any.whl
urllib3==1.26.9
wcwidth==0.2.5
webencodings==0.5.1
Werkzeug==2.1.2
wrapt==1.12.1
xgboost @ file:///rapids/xgboost-1.6.1-cp38-cp38-linux_x86_64.whl
zict==2.2.0
zipp==3.8.0
