{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Summary of notebook:\n", "\n", "This notebook shows the tuning process to obtain the optimal model architecture and hyperparameters for the Resnet-18 model using the **keras-tuner** package\n", "\n", "Terminal's command: ```pip install keras-tuner```\n", "\n", "The following shows some of the important details tuning process:\n", "- Dataset used: Dataset augmented with Gaussian noise (augmented_features_10_ue1_v2_ds.npy\\augmented_labels_10_ue1_v2_ds.npy)\n", "- Tuner: RandomSearch\n", "- Max trials: 30\n", "\n", "Optimal Hyperparameters:\n", "- l2: 0.0\n", "- conv_dropout: False\n", "- fc_dropout: False\n", "- learning_rate: 0.001\n", "- batch_size: 64"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PhysicalDevice(name='/physical_device:GPU:0', device_type='GPU')\n", "2.9.1\n"]}], "source": ["# Configure amd test GPU\n", "import tensorflow as tf\n", "from tensorflow.python.client import device_lib\n", "\n", "# Prevent automatic GPU memory pre-allocation\n", "gpus = tf.config.experimental.list_physical_devices('GPU')\n", "for gpu in gpus:\n", "    print(gpu)\n", "    tf.config.experimental.set_memory_growth(gpu, True)\n", "\n", "print(tf.__version__)\n", "# print(device_lib.list_local_devices())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Parameters to be tuned:\n", "- Regularisation and Dropout\n", "    - l2 regularisation on Conv2D layers\n", "    - Dropout layers after Max Pooling layers and before Fully Connected Layer, just before Softmax output\n", "- Learning rate of optimiser\n", "- Batch size\n", "- Number of epochs (Using EarlyStopping keras callback"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from keras.models import Sequential\n", "from keras.utils import np_utils\n", "\n", "import h5py\n", "import numpy as np\n", "import math\n", "import os\n", "from tensorflow import keras\n", "from tensorflow.keras import layers\n", "\n", "import keras_tuner as kt"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/home/<USER>/committed_git/resnet18\n", "/home/<USER>/committed_git/datasets\n"]}], "source": ["print(os.getcwd())\n", "os.chdir('../datasets')\n", "print(os.getcwd())"]}, {"cell_type": "code", "execution_count": null, "metadata": {"execution": {"iopub.execute_input": "2023-06-13T13:14:47.703760Z", "iopub.status.busy": "2023-06-13T13:14:47.703392Z", "iopub.status.idle": "2023-06-13T13:15:01.341532Z", "shell.execute_reply": "2023-06-13T13:15:01.340590Z", "shell.execute_reply.started": "2023-06-13T13:14:47.703731Z"}}, "outputs": [], "source": ["# Get dictionary of RP index and coordinates\n", "# Open HDF5 file and access the dataset\n", "filename = 'dataset_SNR20_outdoor.mat'\n", "hdf5_file = h5py.File(filename, 'r')\n", "\n", "features_dataset = hdf5_file['features']\n", "labels_dataset = hdf5_file['labels']['position']\n", "\n", "# Convert HDF5 dataset to NumPy array\n", "features = np.array(features_dataset)\n", "labels = np.array(labels_dataset)\n", "\n", "# Prepare features for dataset\n", "# Retrieve features from the first UE and transpose the individual matrix\n", "features_transposed = np.zeros((3876,193,16), dtype = np.float64)\n", "for i in range(len(features)):\n", "    features_transposed[i] = features[i][0].T\n", "\n", "# Prepare labels for dataset\n", "count = 0\n", "rp_dict = {}\n", "# For labels, have a shape of (1,) where that number represents the class of that coordinate\n", "\n", "for label in labels:\n", "    rp_dict[count] = label\n", "    count += 1\n", "\n", "# Close the HDF5 file\n", "hdf5_file.close()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"execution": {"iopub.execute_input": "2023-06-13T13:15:12.504786Z", "iopub.status.busy": "2023-06-13T13:15:12.504397Z", "iopub.status.idle": "2023-06-13T13:15:31.623615Z", "shell.execute_reply": "2023-06-13T13:15:31.622679Z", "shell.execute_reply.started": "2023-06-13T13:15:12.504757Z"}}, "outputs": [], "source": ["# Load datasets\n", "features = np.load('augmented_features_10_ds.npy')\n", "labels = np.load('augmented_labels_10_ds.npy')\n", "\n", "print(f'Shape of features np array: {features.shape}')\n", "print(f'Shape of labels np array: {labels.shape}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"execution": {"iopub.execute_input": "2023-06-13T13:15:34.878842Z", "iopub.status.busy": "2023-06-13T13:15:34.878431Z", "iopub.status.idle": "2023-06-13T13:15:35.484843Z", "shell.execute_reply": "2023-06-13T13:15:35.483894Z", "shell.execute_reply.started": "2023-06-13T13:15:34.878814Z"}}, "outputs": [], "source": ["from sklearn.model_selection import train_test_split\n", "\n", "X = features\n", "y = labels\n", "\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, shuffle=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"execution": {"iopub.execute_input": "2023-06-13T13:15:46.230518Z", "iopub.status.busy": "2023-06-13T13:15:46.230171Z", "iopub.status.idle": "2023-06-13T13:15:46.260466Z", "shell.execute_reply": "2023-06-13T13:15:46.259256Z", "shell.execute_reply.started": "2023-06-13T13:15:46.230490Z"}}, "outputs": [], "source": ["# Method 1\n", "class HyperModel(kt.HyperModel):\n", "    \n", "    def build(self,hp):\n", "        # Initialise the weights of neural network layers\n", "        # VarianceScaling is a particular method used to initialise weights\n", "        kaiming_normal = keras.initializers.VarianceScaling(scale=2.0, mode='fan_out', distribution='untruncated_normal', seed = 42)\n", "\n", "        # Retrieve hyperparameters to be tuned\n", "        l2 = hp.Float(\"l2\", min_value=0, max_value=0.01, step=0.01)\n", "        pooling_dropout = hp.Boolean('conv_dropout', default = False)\n", "        fc_dropout = hp.Bo<PERSON>an('fc_dropout', default = False)\n", "        lr = hp.Float(\"learning_rate\", min_value=0.001, max_value=0.01, step=0.005)\n", "        \n", "        # Make 3x3 convolutional filters\n", "        def conv3x3(x, out_planes, stride=1,name=None):\n", "            x = layers.ZeroPadding2D(padding=1, name=f'{name}_pad')(x)\n", "\n", "            # Make 2D convolution layer\n", "            # return layers.Conv2D(filters=out_planes, kernel_size=3, strides=stride, use_bias=False,\n", "            #                     kernel_initializer=kaiming_normal, name=name)(x)\n", "            return layers.Conv2D(filters=out_planes, kernel_size=3, strides=stride, use_bias=False,\n", "                                 kernel_initializer=kaiming_normal, kernel_regularizer=keras.regularizers.L2(l2),\n", "                                 name=name)(x)\n", "\n", "        def basic_block(x, planes, stride=1, downsample=None, name=None):\n", "            identity = x\n", "\n", "            out = conv3x3(x, planes, stride=stride, name=f'{name}.conv1')\n", "            out = layers.BatchNormalization(momentum=0.9, epsilon=1e-5, name=f'{name}.bn1')(out)\n", "            out = layers.ReLU(name=f'{name}.relu1')(out)\n", "\n", "            out = conv3x3(out, planes, name=f'{name}.conv2')\n", "            out = layers.BatchNormalization(momentum=0.9, epsilon=1e-5, name=f'{name}.bn2')(out)\n", "\n", "            if downsample is not None:\n", "                # Create an identical layer for each layer in the downsample\n", "                for layer in downsample:\n", "                    identity = layer(identity)\n", "\n", "            # Performs element-wise addition of multiple inputs. It is used to combine or merge the outputs of two or more layers by adding them together\n", "            out = layers.Add(name=f'{name}.add')([identity, out])    \n", "            out = layers.ReLU(name=f'{name}.relu2')(out)\n", "\n", "            return out\n", "\n", "        def make_layer(x, planes, blocks, stride=1, name=None):\n", "            downsample = None\n", "\n", "            # inplanes refer to the number of channels in filters\n", "            inplanes = x.shape[3]\n", "\n", "            # Check whether we are downsampling our data (i.e. not going through every elememt)\n", "            # This happens under two circumstances:\n", "            # 1. when stride != 1\n", "            # 2. when the layer we want to make has no. of channels less than the no. of channels input has\n", "            if stride != 1 or inplanes != planes:\n", "                # downsample consists of a Conv2D layer and a BatchNormalization layer\n", "                downsample = [\n", "                    layers.Conv2D(filters=planes, kernel_size=1, strides=stride, use_bias=False, kernel_initializer=kaiming_normal,\n", "                                  kernel_regularizer=keras.regularizers.L2(l2),\n", "                                  name=f'{name}.0.downsample.0'),\n", "#                    layers.Conv2D(filters=planes, kernel_size=1, strides=stride, use_bias=False, kernel_initializer=kaiming_normal,\n", "#                                  name=f'{name}.0.downsample.0'),\n", "                    layers.BatchNormalization(momentum=0.9, epsilon=1e-5, name=f'{name}.0.downsample.1'),\n", "                ]\n", "\n", "            # If no downsample, downsample = None\n", "\n", "            x = basic_block(x, planes, stride, downsample, name=f'{name}.0')\n", "            for i in range(1, blocks):\n", "                x = basic_block(x, planes, name=f'{name}.{i}')\n", "\n", "            return x\n", "\n", "        def resnet(x, blocks_per_layer, num_classes=1000):\n", "\n", "            # ---------------------------------\n", "            # Initial entry block\n", "            x = layers.ZeroPadding2D(padding=3, name='conv1_pad')(x)\n", "            x = layers.Conv2D(filters=64, kernel_size=7, strides=2, use_bias=False,\n", "                              kernel_initializer=kaiming_normal,\n", "                              kernel_regularizer=keras.regularizers.L2(l2),\n", "                              name='conv1')(x)\n", "            # x = layers.Conv2D(filters=64, kernel_size=7, strides=2, use_bias=False,\n", "            #                  kernel_initializer=kaiming_normal,\n", "            #                  name='conv1')(x)\n", "            x = layers.BatchNormalization(momentum=0.9, epsilon=1e-5, name='bn1')(x)\n", "            x = layers.ReLU(name='relu1')(x)\n", "            x = layers.ZeroPadding2D(padding=1, name='maxpool_pad')(x)\n", "            x = layers.MaxPool2D(pool_size=3, strides=2, name='maxpool')(x)\n", "\n", "            # Additional - Addition of a dropout layer\n", "            if pooling_dropout:\n", "                x = layers.Dropout(rate = 0.2)(x)   \n", "            # ---------------------------------\n", "\n", "            # ---------------------------------\n", "            # This block of code creates the ResNet blocks\n", "            # In ResNet-18, only have 2 layers per block\n", "            x = make_layer(x, 64, blocks_per_layer[0], name='layer1')\n", "            x = make_layer(x, 128, blocks_per_layer[1], stride=2, name='layer2')\n", "            x = make_layer(x, 256, blocks_per_layer[2], stride=2, name='layer3')\n", "            x = make_layer(x, 512, blocks_per_layer[3], stride=2, name='layer4')\n", "            # ---------------------------------\n", "\n", "            # ---------------------------------\n", "            x = layers.GlobalAveragePooling2D(name='avgpool')(x)\n", "\n", "            # Addition of a dropout layer\n", "            if pooling_dropout:\n", "                x = layers.Dropout(rate = 0.2)(x)    \n", "\n", "            initializer = keras.initializers.RandomUniform(-1.0 / math.sqrt(512), 1.0 / math.sqrt(512))\n", "            x = layers.Dense(units=num_classes, kernel_initializer=initializer, bias_initializer=initializer, name='fc')(x)\n", "\n", "            # Additional - Addition of a dropout layer\n", "            if fc_dropout:\n", "                x = layers.Dropout(rate = 0.5)(x)  \n", "\n", "            # Softmax output layer\n", "            x = layers.Dense(units=num_classes, activation='softmax')(x)  \n", "            # ---------------------------------\n", "\n", "            return x\n", "\n", "        def resnet18(x, **kwargs):\n", "            return resnet(x, [2, 2, 2, 2], **kwargs)\n", "\n", "        def resnet34(x, **kwargs):\n", "            return resnet(x, [3, 4, 6, 3], **kwargs)\n", "\n", "        # Create model\n", "        model_inputs = keras.Input(shape = (193, 16, 1))\n", "        model_outputs = resnet18(model_inputs, num_classes = 3876)\n", "        resnet18_model = keras.Model(model_inputs, model_outputs)\n", "\n", "        # Compile model - Classification\n", "        # default learning rate is 1e-3 = 0.001\n", "        optimizer = tf.keras.optimizers.<PERSON>(learning_rate = lr)\n", "        \n", "        resnet18_model.compile(optimizer=optimizer,\n", "              loss=tf.keras.losses.SparseCategoricalCrossentropy(),\n", "              metrics=['accuracy'])\n", "\n", "        return resnet18_model\n", "    \n", "    def fit(self, hp, model, X_train, y_train, validation_data = None, **kwargs):\n", "        \n", "        return model.fit(X_train, y_train,\n", "                        validation_data = validation_data,\n", "                        batch_size = hp.Choice('batch_size', [16,32,64]),\n", "                        **kwargs,\n", "                        )"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/home/<USER>/committed_git/datasets\n", "/home/<USER>/committed_git/resnet18\n"]}], "source": ["# Must change back to working directory for below code to run\n", "print(os.getcwd())\n", "os.chdir('../resnet18')\n", "print(os.getcwd())"]}, {"cell_type": "code", "execution_count": null, "metadata": {"execution": {"iopub.execute_input": "2023-06-13T13:15:56.718779Z", "iopub.status.busy": "2023-06-13T13:15:56.718413Z", "iopub.status.idle": "2023-06-13T13:16:02.447115Z", "shell.execute_reply": "2023-06-13T13:16:02.446153Z", "shell.execute_reply.started": "2023-06-13T13:15:56.718752Z"}}, "outputs": [], "source": ["# Method 1\n", "tuner = kt.RandomSearch(\n", "    HyperModel(),\n", "    objective = 'val_loss',\n", "    max_trials = 30,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"execution": {"iopub.execute_input": "2023-06-13T13:16:05.093969Z", "iopub.status.busy": "2023-06-13T13:16:05.093078Z", "iopub.status.idle": "2023-06-13T13:16:05.100463Z", "shell.execute_reply": "2023-06-13T13:16:05.099302Z", "shell.execute_reply.started": "2023-06-13T13:16:05.093932Z"}}, "outputs": [], "source": ["tuner.search_space_summary()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"execution": {"iopub.execute_input": "2023-06-13T13:16:10.911606Z", "iopub.status.busy": "2023-06-13T13:16:10.911261Z", "iopub.status.idle": "2023-06-13T13:16:19.835650Z", "shell.execute_reply": "2023-06-13T13:16:19.832694Z", "shell.execute_reply.started": "2023-06-13T13:16:10.911578Z"}}, "outputs": [], "source": ["stop_early = tf.keras.callbacks.EarlyStopping(monitor='val_loss', patience=5)\n", "tuner.search(X_train, y_train,\n", "            validation_data = (X_test, y_test),\n", "            epochs = 100,\n", "            callbacks = [stop_early])"]}, {"cell_type": "code", "execution_count": null, "metadata": {"execution": {"iopub.execute_input": "2023-06-13T13:16:20.532990Z", "iopub.status.busy": "2023-06-13T13:16:20.532610Z", "iopub.status.idle": "2023-06-13T13:16:20.539401Z", "shell.execute_reply": "2023-06-13T13:16:20.538380Z", "shell.execute_reply.started": "2023-06-13T13:16:20.532960Z"}}, "outputs": [], "source": ["tuner.results_summary()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analysis of results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analysis of results\n", "import os\n", "import json\n", "\n", "print(os.getcwd())\n", "\n", "for _, _, file_names in os.walk('resnet18_tuningresults'):\n", "    files = file_names    \n", "\n", "tuning_results = {}\n", "\n", "for file in files:\n", "    cur_filename = 'resnet18_tuningresults/' + file\n", "    data = open(cur_filename)\n", "    data = json.load(data)\n", "    \n", "    trial_results = {}\n", "    trial_results['trial_id'] = data['trial_id']\n", "    trial_results['values'] = data['hyperparameters']['values']\n", "    trial_results['val_loss'] = data['metrics']['metrics']['val_loss']['observations'][0]['value'][0]\n", "    trial_results['status'] = data['status']\n", "    tuning_results[data['trial_id']] = trial_results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["best_trial = min(tuning_results.keys(), key=lambda x: tuning_results[x]['val_loss'])\n", "\n", "print(f'Trial that resulted in minimum validation loss: {best_trial}')\n", "print(f'Validation Loss: {tuning_results[best_trial][\"val_loss\"]}')\n", "print(f'Best Hyperparameters: {tuning_results[best_trial][\"values\"]}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Best Hyperparameters:\n", "- l2: 0.0\n", "- conv_dropout: False\n", "- fc_dropout: False\n", "- learning_rate: 0.001\n", "- batch_size: 64"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}