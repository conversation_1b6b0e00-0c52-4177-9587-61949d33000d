{"trial_id": "00", "hyperparameters": {"space": [{"class_name": "Float", "config": {"name": "l2", "default": 0.0, "conditions": [], "min_value": 0.0, "max_value": 0.01, "step": 0.01, "sampling": "linear"}}, {"class_name": "Boolean", "config": {"name": "conv_dropout", "default": false, "conditions": []}}, {"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Float", "config": {"name": "learning_rate", "default": 0.001, "conditions": [], "min_value": 0.001, "max_value": 0.01, "step": 0.005, "sampling": "linear"}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"l2": 0.0, "conv_dropout": true, "fc_dropout": false, "learning_rate": 0.001, "batch_size": 16}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [1.4526621103286743], "step": 5}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.5503870248794556], "step": 5}]}, "val_loss": {"direction": "min", "observations": [{"value": [1.5230457782745361], "step": 5}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.6025177836418152], "step": 5}]}}}, "score": 1.5230457782745361, "best_step": 5, "status": "COMPLETED", "message": null}