{"trial_id": "02", "hyperparameters": {"space": [{"class_name": "Float", "config": {"name": "l2", "default": 0.0, "conditions": [], "min_value": 0.0, "max_value": 0.01, "step": 0.01, "sampling": "linear"}}, {"class_name": "Boolean", "config": {"name": "conv_dropout", "default": false, "conditions": []}}, {"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Float", "config": {"name": "learning_rate", "default": 0.001, "conditions": [], "min_value": 0.001, "max_value": 0.01, "step": 0.005, "sampling": "linear"}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"l2": 0.0, "conv_dropout": true, "fc_dropout": false, "learning_rate": 0.001, "batch_size": 64}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [0.3040916621685028], "step": 25}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.8948889970779419], "step": 25}]}, "val_loss": {"direction": "min", "observations": [{"value": [0.2889038324356079], "step": 25}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.9103119969367981], "step": 25}]}}}, "score": 0.2889038324356079, "best_step": 25, "status": "COMPLETED", "message": null}