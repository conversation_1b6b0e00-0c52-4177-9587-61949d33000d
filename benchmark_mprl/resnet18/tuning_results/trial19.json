{"trial_id": "18", "hyperparameters": {"space": [{"class_name": "Float", "config": {"name": "l2", "default": 0.0, "conditions": [], "min_value": 0.0, "max_value": 0.01, "step": 0.01, "sampling": "linear"}}, {"class_name": "Boolean", "config": {"name": "conv_dropout", "default": false, "conditions": []}}, {"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Float", "config": {"name": "learning_rate", "default": 0.001, "conditions": [], "min_value": 0.001, "max_value": 0.01, "step": 0.005, "sampling": "linear"}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"l2": 0.01, "conv_dropout": true, "fc_dropout": false, "learning_rate": 0.006, "batch_size": 32}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [8.044824600219727], "step": 10}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.0009336505900137126], "step": 10}]}, "val_loss": {"direction": "min", "observations": [{"value": [7.847029209136963], "step": 10}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.000542103371117264], "step": 10}]}}}, "score": 7.847029209136963, "best_step": 10, "status": "COMPLETED", "message": null}