{"trial_id": "05", "hyperparameters": {"space": [{"class_name": "Float", "config": {"name": "l2", "default": 0.0, "conditions": [], "min_value": 0.0, "max_value": 0.01, "step": 0.01, "sampling": "linear"}}, {"class_name": "Boolean", "config": {"name": "conv_dropout", "default": false, "conditions": []}}, {"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Float", "config": {"name": "learning_rate", "default": 0.001, "conditions": [], "min_value": 0.001, "max_value": 0.01, "step": 0.005, "sampling": "linear"}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"l2": 0.01, "conv_dropout": true, "fc_dropout": true, "learning_rate": 0.006, "batch_size": 64}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [5.955435276031494], "step": 21}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.030418938025832176], "step": 21}]}, "val_loss": {"direction": "min", "observations": [{"value": [5.073508262634277], "step": 21}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.04812673106789589], "step": 21}]}}}, "score": 5.073508262634277, "best_step": 21, "status": "COMPLETED", "message": null}