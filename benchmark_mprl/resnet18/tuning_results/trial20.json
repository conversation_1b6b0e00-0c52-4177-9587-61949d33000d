{"trial_id": "19", "hyperparameters": {"space": [{"class_name": "Float", "config": {"name": "l2", "default": 0.0, "conditions": [], "min_value": 0.0, "max_value": 0.01, "step": 0.01, "sampling": "linear"}}, {"class_name": "Boolean", "config": {"name": "conv_dropout", "default": false, "conditions": []}}, {"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Float", "config": {"name": "learning_rate", "default": 0.001, "conditions": [], "min_value": 0.001, "max_value": 0.01, "step": 0.005, "sampling": "linear"}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"l2": 0.01, "conv_dropout": false, "fc_dropout": true, "learning_rate": 0.006, "batch_size": 16}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [9.111980438232422], "step": 2}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.0003915308916475624], "step": 2}]}, "val_loss": {"direction": "min", "observations": [{"value": [8.802452087402344], "step": 2}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.0004818696470465511], "step": 2}]}}}, "score": 8.802452087402344, "best_step": 2, "status": "COMPLETED", "message": null}