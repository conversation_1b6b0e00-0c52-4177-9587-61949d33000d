{"trial_id": "08", "hyperparameters": {"space": [{"class_name": "Float", "config": {"name": "l2", "default": 0.0, "conditions": [], "min_value": 0.0, "max_value": 0.01, "step": 0.01, "sampling": "linear"}}, {"class_name": "Boolean", "config": {"name": "conv_dropout", "default": false, "conditions": []}}, {"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Float", "config": {"name": "learning_rate", "default": 0.001, "conditions": [], "min_value": 0.001, "max_value": 0.01, "step": 0.005, "sampling": "linear"}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"l2": 0.0, "conv_dropout": true, "fc_dropout": true, "learning_rate": 0.006, "batch_size": 32}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [3.9513731002807617], "step": 17}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.25315484404563904], "step": 17}]}, "val_loss": {"direction": "min", "observations": [{"value": [2.158846139907837], "step": 17}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.4344657361507416], "step": 17}]}}}, "score": 2.158846139907837, "best_step": 17, "status": "COMPLETED", "message": null}