{"trial_id": "11", "hyperparameters": {"space": [{"class_name": "Float", "config": {"name": "l2", "default": 0.0, "conditions": [], "min_value": 0.0, "max_value": 0.01, "step": 0.01, "sampling": "linear"}}, {"class_name": "Boolean", "config": {"name": "conv_dropout", "default": false, "conditions": []}}, {"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Float", "config": {"name": "learning_rate", "default": 0.001, "conditions": [], "min_value": 0.001, "max_value": 0.01, "step": 0.005, "sampling": "linear"}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"l2": 0.01, "conv_dropout": false, "fc_dropout": false, "learning_rate": 0.006, "batch_size": 16}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [6.584321975708008], "step": 7}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.009758153930306435], "step": 7}]}, "val_loss": {"direction": "min", "observations": [{"value": [6.395211219787598], "step": 7}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.011564871296286583], "step": 7}]}}}, "score": 6.395211219787598, "best_step": 7, "status": "COMPLETED", "message": null}