{"trial_id": "15", "hyperparameters": {"space": [{"class_name": "Float", "config": {"name": "l2", "default": 0.0, "conditions": [], "min_value": 0.0, "max_value": 0.01, "step": 0.01, "sampling": "linear"}}, {"class_name": "Boolean", "config": {"name": "conv_dropout", "default": false, "conditions": []}}, {"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Float", "config": {"name": "learning_rate", "default": 0.001, "conditions": [], "min_value": 0.001, "max_value": 0.01, "step": 0.005, "sampling": "linear"}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"l2": 0.01, "conv_dropout": false, "fc_dropout": false, "learning_rate": 0.006, "batch_size": 32}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [6.163503170013428], "step": 16}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.011384513229131699], "step": 16}]}, "val_loss": {"direction": "min", "observations": [{"value": [6.170291900634766], "step": 16}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.011263702996075153], "step": 16}]}}}, "score": 6.170291900634766, "best_step": 16, "status": "COMPLETED", "message": null}