{"trial_id": "09", "hyperparameters": {"space": [{"class_name": "Float", "config": {"name": "l2", "default": 0.0, "conditions": [], "min_value": 0.0, "max_value": 0.01, "step": 0.01, "sampling": "linear"}}, {"class_name": "Boolean", "config": {"name": "conv_dropout", "default": false, "conditions": []}}, {"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Float", "config": {"name": "learning_rate", "default": 0.001, "conditions": [], "min_value": 0.001, "max_value": 0.01, "step": 0.005, "sampling": "linear"}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"l2": 0.01, "conv_dropout": true, "fc_dropout": true, "learning_rate": 0.006, "batch_size": 32}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [9.46230697631836], "step": 3}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.0004367075162008405], "step": 3}]}, "val_loss": {"direction": "min", "observations": [{"value": [8.51129150390625], "step": 3}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.00012046741176163778], "step": 3}]}}}, "score": 8.51129150390625, "best_step": 3, "status": "COMPLETED", "message": null}