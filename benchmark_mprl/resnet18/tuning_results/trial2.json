{"trial_id": "01", "hyperparameters": {"space": [{"class_name": "Float", "config": {"name": "l2", "default": 0.0, "conditions": [], "min_value": 0.0, "max_value": 0.01, "step": 0.01, "sampling": "linear"}}, {"class_name": "Boolean", "config": {"name": "conv_dropout", "default": false, "conditions": []}}, {"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Float", "config": {"name": "learning_rate", "default": 0.001, "conditions": [], "min_value": 0.001, "max_value": 0.01, "step": 0.005, "sampling": "linear"}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"l2": 0.0, "conv_dropout": false, "fc_dropout": true, "learning_rate": 0.006, "batch_size": 64}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [1.406327247619629], "step": 25}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.7524319887161255], "step": 25}]}, "val_loss": {"direction": "min", "observations": [{"value": [1.1399210691452026], "step": 25}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.8017708659172058], "step": 25}]}}}, "score": 1.1399210691452026, "best_step": 25, "status": "COMPLETED", "message": null}