{"trial_id": "10", "hyperparameters": {"space": [{"class_name": "Float", "config": {"name": "l2", "default": 0.0, "conditions": [], "min_value": 0.0, "max_value": 0.01, "step": 0.01, "sampling": "linear"}}, {"class_name": "Boolean", "config": {"name": "conv_dropout", "default": false, "conditions": []}}, {"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Float", "config": {"name": "learning_rate", "default": 0.001, "conditions": [], "min_value": 0.001, "max_value": 0.01, "step": 0.005, "sampling": "linear"}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"l2": 0.01, "conv_dropout": true, "fc_dropout": false, "learning_rate": 0.006, "batch_size": 16}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [8.596399307250977], "step": 4}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.0002710598346311599], "step": 4}]}, "val_loss": {"direction": "min", "observations": [{"value": [8.586546897888184], "step": 4}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.00030116853304207325], "step": 4}]}}}, "score": 8.586546897888184, "best_step": 4, "status": "COMPLETED", "message": null}