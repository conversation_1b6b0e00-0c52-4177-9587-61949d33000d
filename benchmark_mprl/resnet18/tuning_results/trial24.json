{"trial_id": "23", "hyperparameters": {"space": [{"class_name": "Float", "config": {"name": "l2", "default": 0.0, "conditions": [], "min_value": 0.0, "max_value": 0.01, "step": 0.01, "sampling": "linear"}}, {"class_name": "Boolean", "config": {"name": "conv_dropout", "default": false, "conditions": []}}, {"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Float", "config": {"name": "learning_rate", "default": 0.001, "conditions": [], "min_value": 0.001, "max_value": 0.01, "step": 0.005, "sampling": "linear"}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"l2": 0.0, "conv_dropout": true, "fc_dropout": true, "learning_rate": 0.001, "batch_size": 32}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [0.8520317077636719], "step": 11}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.7287744879722595], "step": 11}]}, "val_loss": {"direction": "min", "observations": [{"value": [0.667336642742157], "step": 11}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.793398380279541], "step": 11}]}}}, "score": 0.667336642742157, "best_step": 11, "status": "COMPLETED", "message": null}