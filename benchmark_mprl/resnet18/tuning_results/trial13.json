{"trial_id": "12", "hyperparameters": {"space": [{"class_name": "Float", "config": {"name": "l2", "default": 0.0, "conditions": [], "min_value": 0.0, "max_value": 0.01, "step": 0.01, "sampling": "linear"}}, {"class_name": "Boolean", "config": {"name": "conv_dropout", "default": false, "conditions": []}}, {"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Float", "config": {"name": "learning_rate", "default": 0.001, "conditions": [], "min_value": 0.001, "max_value": 0.01, "step": 0.005, "sampling": "linear"}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"l2": 0.0, "conv_dropout": false, "fc_dropout": false, "learning_rate": 0.001, "batch_size": 64}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [0.1558036208152771], "step": 33}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.9485889673233032], "step": 33}]}, "val_loss": {"direction": "min", "observations": [{"value": [0.2770146131515503], "step": 33}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.9191663861274719], "step": 33}]}}}, "score": 0.2770146131515503, "best_step": 33, "status": "COMPLETED", "message": null}