{"trial_id": "04", "hyperparameters": {"space": [{"class_name": "Float", "config": {"name": "l2", "default": 0.0, "conditions": [], "min_value": 0.0, "max_value": 0.01, "step": 0.01, "sampling": "linear"}}, {"class_name": "Boolean", "config": {"name": "conv_dropout", "default": false, "conditions": []}}, {"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Float", "config": {"name": "learning_rate", "default": 0.001, "conditions": [], "min_value": 0.001, "max_value": 0.01, "step": 0.005, "sampling": "linear"}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"l2": 0.01, "conv_dropout": false, "fc_dropout": true, "learning_rate": 0.001, "batch_size": 32}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [1.4493693113327026], "step": 26}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.6502123475074768], "step": 26}]}, "val_loss": {"direction": "min", "observations": [{"value": [1.2795027494430542], "step": 26}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.7025659680366516], "step": 26}]}}}, "score": 1.2795027494430542, "best_step": 26, "status": "COMPLETED", "message": null}