{"trial_id": "22", "hyperparameters": {"space": [{"class_name": "Float", "config": {"name": "l2", "default": 0.0, "conditions": [], "min_value": 0.0, "max_value": 0.01, "step": 0.01, "sampling": "linear"}}, {"class_name": "Boolean", "config": {"name": "conv_dropout", "default": false, "conditions": []}}, {"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Float", "config": {"name": "learning_rate", "default": 0.001, "conditions": [], "min_value": 0.001, "max_value": 0.01, "step": 0.005, "sampling": "linear"}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"l2": 0.0, "conv_dropout": true, "fc_dropout": false, "learning_rate": 0.001, "batch_size": 32}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [0.41130197048187256], "step": 20}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.8605698347091675], "step": 20}]}, "val_loss": {"direction": "min", "observations": [{"value": [0.34969162940979004], "step": 20}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.8877846002578735], "step": 20}]}}}, "score": 0.34969162940979004, "best_step": 20, "status": "COMPLETED", "message": null}