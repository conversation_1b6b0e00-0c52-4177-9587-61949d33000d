{"trial_id": "03", "hyperparameters": {"space": [{"class_name": "Float", "config": {"name": "l2", "default": 0.0, "conditions": [], "min_value": 0.0, "max_value": 0.01, "step": 0.01, "sampling": "linear"}}, {"class_name": "Boolean", "config": {"name": "conv_dropout", "default": false, "conditions": []}}, {"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Float", "config": {"name": "learning_rate", "default": 0.001, "conditions": [], "min_value": 0.001, "max_value": 0.01, "step": 0.005, "sampling": "linear"}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"l2": 0.0, "conv_dropout": false, "fc_dropout": false, "learning_rate": 0.001, "batch_size": 16}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [0.47749850153923035], "step": 12}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.8407673835754395], "step": 12}]}, "val_loss": {"direction": "min", "observations": [{"value": [0.7266331315040588], "step": 12}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.8136368989944458], "step": 12}]}}}, "score": 0.7266331315040588, "best_step": 12, "status": "COMPLETED", "message": null}