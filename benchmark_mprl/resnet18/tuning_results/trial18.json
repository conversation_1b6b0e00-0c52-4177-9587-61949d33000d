{"trial_id": "17", "hyperparameters": {"space": [{"class_name": "Float", "config": {"name": "l2", "default": 0.0, "conditions": [], "min_value": 0.0, "max_value": 0.01, "step": 0.01, "sampling": "linear"}}, {"class_name": "Boolean", "config": {"name": "conv_dropout", "default": false, "conditions": []}}, {"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Float", "config": {"name": "learning_rate", "default": 0.001, "conditions": [], "min_value": 0.001, "max_value": 0.01, "step": 0.005, "sampling": "linear"}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"l2": 0.01, "conv_dropout": true, "fc_dropout": false, "learning_rate": 0.001, "batch_size": 16}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [1.9799343347549438], "step": 25}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.5314881205558777], "step": 25}]}, "val_loss": {"direction": "min", "observations": [{"value": [1.477555274963379], "step": 25}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.6557643413543701], "step": 25}]}}}, "score": 1.477555274963379, "best_step": 25, "status": "COMPLETED", "message": null}