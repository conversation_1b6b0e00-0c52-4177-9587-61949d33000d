{"cells": [{"cell_type": "markdown", "id": "e6ea6706", "metadata": {"papermill": {"duration": 0.004498, "end_time": "2023-06-20T01:09:23.304296", "exception": false, "start_time": "2023-06-20T01:09:23.299798", "status": "completed"}, "tags": []}, "source": ["## Summary of notebook:\n", "\n", "This notebook shows the tuning process to obtain the optimal model architecture and hyperparameters for the Resnet-18 model using the **keras-tuner** package\n", "\n", "Terminal's command: ```pip install keras-tuner```\n", "\n", "The following shows some of the important details tuning process:\n", "- Dataset used: Dataset augmented with Gaussian noise (augmented_features_10_ue1_v2_ds.npy\\augmented_labels_10_ue1_v2_ds.npy)\n", "- Tuner: Bayesian Optimiser\n", "- Max trials: 50\n", "\n", "Optimal Hyperparameters:\n", "- fc_dropout: True\n", "- pooling_dropout: False\n", "- learning_rate: 0.0001\n", "- l2_conv2d: 0.0\n", "- l2_sepconv2d: 0.01\n", "- batch_size: 16"]}, {"cell_type": "code", "execution_count": 17, "id": "47d5fb5f", "metadata": {"papermill": {"duration": 8.90105, "end_time": "2023-06-20T01:09:32.209449", "exception": false, "start_time": "2023-06-20T01:09:23.308399", "status": "completed"}, "tags": []}, "outputs": [], "source": ["from tensorflow import keras\n", "from tensorflow.keras.layers import Input, Conv2D, SeparableConv2D, Add, Dense, \\\n", "BatchNormalization, ReLU, MaxPool2D, GlobalAvgPool2D, Dropout\n", "from tensorflow.keras import Model\n", "from tensorflow.keras.utils import plot_model"]}, {"cell_type": "code", "execution_count": 18, "id": "3e20dafe", "metadata": {"papermill": {"duration": 0.32422, "end_time": "2023-06-20T01:09:32.537866", "exception": false, "start_time": "2023-06-20T01:09:32.213646", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PhysicalDevice(name='/physical_device:GPU:0', device_type='GPU')\n", "2.9.1\n"]}], "source": ["# Configure amd test GPU\n", "import tensorflow as tf\n", "from tensorflow.python.client import device_lib\n", "\n", "# Prevent automatic GPU memory pre-allocation\n", "gpus = tf.config.experimental.list_physical_devices('GPU')\n", "for gpu in gpus:\n", "    print(gpu)\n", "    tf.config.experimental.set_memory_growth(gpu, True)\n", "\n", "print(tf.__version__)\n", "# print(device_lib.list_local_devices())"]}, {"cell_type": "code", "execution_count": 3, "id": "9b506fd3", "metadata": {"execution": {"iopub.execute_input": "2023-06-20T01:09:32.549152Z", "iopub.status.busy": "2023-06-20T01:09:32.547548Z", "iopub.status.idle": "2023-06-20T01:09:32.562521Z", "shell.execute_reply": "2023-06-20T01:09:32.561623Z"}, "papermill": {"duration": 0.022098, "end_time": "2023-06-20T01:09:32.564315", "exception": false, "start_time": "2023-06-20T01:09:32.542217", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/kaggle/input/augmented-dataset-outdoor-v2/augmented_features_10_ue1_v2_ds.npy\n", "/kaggle/input/augmented-dataset-outdoor-v2/augmented_labels_10_ue1_v2_ds.npy\n"]}], "source": ["# for kaggle\n", "# This Python 3 environment comes with many helpful analytics libraries installed\n", "# It is defined by the kaggle/python Docker image: https://github.com/kaggle/docker-python\n", "# For example, here's several helpful packages to load\n", "\n", "import numpy as np # linear algebra\n", "import pandas as pd # data processing, CSV file I/O (e.g. pd.read_csv)\n", "\n", "# Input data files are available in the read-only \"../input/\" directory\n", "# For example, running this (by clicking run or pressing Shift+Enter) will list all files under the input directory\n", "\n", "import os\n", "for dirname, _, filenames in os.walk('/kaggle/input'):\n", "    for filename in filenames:\n", "        print(os.path.join(dirname, filename))\n", "\n", "# You can write up to 20GB to the current directory (/kaggle/working/) that gets preserved as output when you create a version using \"Save & Run All\" \n", "# You can also write temporary files to /kaggle/temp/, but they won't be saved outside of the current session"]}, {"cell_type": "code", "execution_count": 19, "id": "e8e97908", "metadata": {"papermill": {"duration": 1.192621, "end_time": "2023-06-20T01:09:33.760860", "exception": false, "start_time": "2023-06-20T01:09:32.568239", "status": "completed"}, "tags": []}, "outputs": [], "source": ["import keras_tuner as kt\n", "\n", "class HyperModel(kt.HyperModel):\n", "    \n", "    def build(self, hp):\n", "        \n", "        fc_dropout = hp.Bo<PERSON>an('fc_dropout', default = False)\n", "        pooling_dropout = hp.Boolean('pooling_dropout', default = False)\n", "        lr = hp.Choice('learning_rate', [0.01, 0.001, 0.0001])\n", "        l2_conv2d = hp.Choice('l2_conv2d', [0.0, 0.01])\n", "        l2_sepconv2d = hp.Choice('l2_sepconv2d', [0.0, 0.01])\n", "        \n", "        # Conv2D + Batch Normalization\n", "        def conv_bn(x, filters, kernel_size, strides = 1):\n", "            x = Conv2D(filters = filters, kernel_size = kernel_size,\n", "                   strides = strides, padding = 'same', use_bias = False,\n", "                      kernel_regularizer = keras.regularizers.L2(l2_conv2d))(x)\n", "            x = BatchNormalization()(x)\n", "            return x\n", "\n", "        # Make a seperable convolutional block (Seperable Conv2D + Batch Normalization)\n", "        def sep_bn(x, filters, kernel_size, strides = 1):\n", "            x = SeparableConv2D(filters = filters, kernel_size = kernel_size, strides = strides,\n", "                           padding = 'same', use_bias = False, kernel_regularizer = keras.regularizers.L2(l2_sepconv2d))(x)\n", "            x = BatchNormalization()(x)\n", "            return x\n", "        \n", "        # Construct the entry flow\n", "        def entry_flow(x):\n", "\n", "            # conv_bn(x, filters, kernel_size, strides)\n", "            x = conv_bn(x, filters = 32, kernel_size = 3, strides = 2)\n", "            x = ReLU()(x)\n", "\n", "            x = conv_bn(x, filters = 64, kernel_size = 3)\n", "            tensor = ReLU()(x)\n", "\n", "            # Normal flow\n", "            x = sep_bn(tensor, filters = 128, kernel_size = 3)\n", "            x = ReLU()(x)\n", "            x = sep_bn(x, filters = 128, kernel_size = 3)\n", "            x = MaxPool2D(pool_size = 3, strides = 2, padding = 'same')(x)\n", "            \n", "            if pooling_dropout:\n", "                x = Dropout(rate = 0.2)(x)\n", "\n", "            # Skip connection\n", "            tensor = conv_bn(tensor, filters = 128, kernel_size = 1, strides = 2)\n", "            x = Add()([x, tensor])\n", "            # End of first skip connection\n", "\n", "            # Skip connection\n", "            tensor = conv_bn(tensor, filters = 256, kernel_size = 1, strides = 2)\n", "\n", "            # Normal flow\n", "            x = ReLU()(x)\n", "            x = sep_bn(x, filters = 256, kernel_size = 3)\n", "            x = ReLU()(x)\n", "            x = sep_bn(x, filters = 256, kernel_size = 3)\n", "            x = MaxPool2D(pool_size = 3, strides = 2, padding = 'same')(x)\n", "            \n", "            if pooling_dropout:\n", "                x = Dropout(rate = 0.2)(x)\n", "                \n", "            x = Add()([x, tensor])\n", "\n", "            # End of second skip connection\n", "\n", "            # Skip connection\n", "            tensor = conv_bn(tensor, filters = 728, kernel_size = 1, strides = 2)\n", "\n", "            # Normal flow\n", "            x = ReLU()(x)\n", "            x = sep_bn(x, filters = 728, kernel_size = 3)\n", "            x = ReLU()(x)\n", "            x = sep_bn(x, filters = 728, kernel_size = 3)\n", "            x = MaxPool2D(pool_size = 3, strides = 2, padding = 'same')(x)\n", "            \n", "            if pooling_dropout:\n", "                x = Dropout(rate = 0.2)(x)\n", "                \n", "            x = Add()([x, tensor])\n", "\n", "            # End of third skip connection\n", "\n", "            return x\n", "        \n", "        # Construct the middle flow\n", "        # Combine output from the entry flow and the convolution layers in the middle flow\n", "        def middle_flow(tensor):\n", "\n", "            for _ in range(8):\n", "\n", "                x = ReLU()(tensor)\n", "                x = sep_bn(x, filters = 728, kernel_size = 3)\n", "                x = ReLU()(x)\n", "                x = sep_bn(x, filters = 728, kernel_size = 3)\n", "                x = ReLU()(x)\n", "                x = sep_bn(x, filters = 728, kernel_size = 3)\n", "\n", "                tensor = Add()([x, tensor])\n", "\n", "            return x\n", "        \n", "        \n", "        def exit_flow(tensor, num_classes = 1000):\n", "\n", "            # Normal path\n", "            x = ReLU()(tensor)\n", "            x = sep_bn(x, filters = 728, kernel_size = 3)\n", "            x = ReLU()(x)\n", "            x = sep_bn(x, filters = 1024, kernel_size = 3)\n", "            x = MaxPool2D(pool_size = 3, strides = 2, padding = 'same')(x)\n", "            \n", "            if pooling_dropout:\n", "                x = Dropout(rate = 0.2)(x)\n", "\n", "            # Skip connection\n", "            tensor = conv_bn(tensor, filters = 1024, kernel_size = 1, strides = 2)\n", "\n", "            # Add outputs\n", "            x = Add()([tensor, x])\n", "\n", "            x = sep_bn(x, filters = 1536, kernel_size = 3)\n", "            x = ReLU()(x)\n", "            x = sep_bn(x, filters = 2048, kernel_size = 3)\n", "            x = ReLU()(x)\n", "\n", "            x = GlobalAvgPool2D()(x)\n", "            \n", "            if fc_dropout:\n", "                x = Dropout(rate = 0.5)(x)\n", "                \n", "            x = Dense(units = num_classes, activation = 'softmax')(x)\n", "\n", "            return x\n", "        \n", "        model_inputs = Input(shape = (193,16,1))\n", "        model_outputs = exit_flow(middle_flow(entry_flow(model_inputs)), num_classes = 3876)\n", "        xception_model = Model(model_inputs, model_outputs)\n", "\n", "        # Check number of parameters\n", "        # xception_model.summary()\n", "        \n", "        optimizer = tf.keras.optimizers.<PERSON>(lr)\n", "        xception_model.compile(optimizer = optimizer,\n", "                              loss = tf.keras.losses.SparseCategoricalCrossentropy(),\n", "                              metrics = ['accuracy'])\n", "        \n", "        return xception_model\n", "    \n", "    def fit(self, hp, model, X_train, y_train, validation_data = None, **kwargs):\n", "        \n", "        return model.fit(X_train, y_train,\n", "                        validation_data = validation_data,\n", "                        batch_size = hp.Choice('batch_size', [16,32,64]),\n", "                        **kwargs,\n", "                        )"]}, {"cell_type": "code", "execution_count": 20, "id": "9cf5c2bc", "metadata": {}, "outputs": [], "source": ["## Tune with Bayesian Optimizer\n", "tuner_bayesian = kt.BayesianOptimization(\n", "        HyperModel(),\n", "        objective = 'val_loss',\n", "        max_trials = 50)"]}, {"cell_type": "code", "execution_count": 21, "id": "10350b32", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Search space summary\n", "Default search space size: 5\n", "fc_dropout (Bo<PERSON>an)\n", "{'default': False, 'conditions': []}\n", "pooling_dropout (<PERSON><PERSON>an)\n", "{'default': False, 'conditions': []}\n", "learning_rate (Choice)\n", "{'default': 0.01, 'conditions': [], 'values': [0.01, 0.001, 0.0001], 'ordered': True}\n", "l2_conv2d (Choice)\n", "{'default': 0.0, 'conditions': [], 'values': [0.0, 0.01], 'ordered': True}\n", "l2_sepconv2d (Choice)\n", "{'default': 0.0, 'conditions': [], 'values': [0.0, 0.01], 'ordered': True}\n"]}], "source": ["tuner_bayesian.search_space_summary()"]}, {"cell_type": "code", "execution_count": 22, "id": "dd1b31cd", "metadata": {}, "outputs": [], "source": ["import os \n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 23, "id": "2475f977", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/home/<USER>/committed_git/datasets\n", "/home/<USER>/committed_git/datasets\n"]}], "source": ["import os\n", "\n", "print(os.getcwd())\n", "os.chdir('../datasets')\n", "print(os.getcwd())"]}, {"cell_type": "code", "execution_count": 24, "id": "777781c8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Shape of features np array: (89628, 193, 16)\n", "Shape of labels np array: (89628,)\n"]}], "source": ["from sklearn.model_selection import train_test_split\n", "\n", "# Import dataset\n", "features = np.load('augmented_features_10_ue1_v2_ds.npy')\n", "labels = np.load('augmented_labels_10_ue1_v2_ds.npy')\n", "\n", "print(f'Shape of features np array: {features.shape}')\n", "print(f'Shape of labels np array: {labels.shape}')\n", "\n", "X = features\n", "y = labels\n", "\n", "# Split the dataset\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, shuffle=True)"]}, {"cell_type": "code", "execution_count": 25, "id": "2e38af2b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/home/<USER>/committed_git/datasets\n", "/home/<USER>/committed_git/xception\n"]}], "source": ["print(os.getcwd())\n", "os.chdir('../xception')\n", "print(os.getcwd())"]}, {"cell_type": "code", "execution_count": null, "id": "883ce177", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Trial 42 Complete [00h 26m 10s]\n", "val_loss: 0.2728256583213806\n", "\n", "Best val_loss So Far: 0.19670678675174713\n", "Total elapsed time: 14h 12m 52s\n", "\n", "Search: Running Trial #43\n", "\n", "Value             |Best Value So Far |Hyperparameter\n", "False             |True              |fc_dropout\n", "False             |True              |pooling_dropout\n", "0.01              |0.0001            |learning_rate\n", "0                 |0                 |l2_conv2d\n", "0                 |0                 |l2_sepconv2d\n", "32                |64                |batch_size\n", "\n", "Epoch 1/100\n", "2241/2241 [==============================] - 49s 20ms/step - loss: 7.5242 - accuracy: 0.0027 - val_loss: 11.3497 - val_accuracy: 0.0023\n", "Epoch 2/100\n", "2241/2241 [==============================] - 44s 20ms/step - loss: 5.5221 - accuracy: 0.0316 - val_loss: 6.2480 - val_accuracy: 0.0291\n", "Epoch 3/100\n", "2241/2241 [==============================] - 44s 20ms/step - loss: 3.9161 - accuracy: 0.1248 - val_loss: 3.9973 - val_accuracy: 0.1374\n", "Epoch 4/100\n", "2241/2241 [==============================] - 44s 20ms/step - loss: 2.9232 - accuracy: 0.2385 - val_loss: 3.0749 - val_accuracy: 0.2419\n", "Epoch 5/100\n", "2241/2241 [==============================] - 45s 20ms/step - loss: 2.3309 - accuracy: 0.3400 - val_loss: 2.2419 - val_accuracy: 0.3729\n", "Epoch 6/100\n", "2241/2241 [==============================] - 45s 20ms/step - loss: 1.9195 - accuracy: 0.4248 - val_loss: 1.9783 - val_accuracy: 0.4214\n", "Epoch 7/100\n", "1006/2241 [============>.................] - ETA: 23s - loss: 1.7018 - accuracy: 0.4719"]}], "source": ["stop_early = tf.keras.callbacks.EarlyStopping(monitor='val_loss', patience=3)\n", "tuner_bayesian.search(X_train, y_train,\n", "            validation_data = (X_test, y_test),\n", "            epochs = 100,\n", "            callbacks = [stop_early])"]}, {"cell_type": "code", "execution_count": null, "id": "7a955eb6", "metadata": {}, "outputs": [], "source": ["tuner_bayesian.results_summary()"]}, {"cell_type": "code", "execution_count": null, "id": "7a0b5a69", "metadata": {}, "outputs": [], "source": ["best_hp = tuner_bayesian.get_best_hyperparameters()[0]"]}, {"cell_type": "code", "execution_count": null, "id": "0b6d2a35", "metadata": {}, "outputs": [], "source": ["print(best_hp.values)"]}, {"cell_type": "markdown", "id": "3e377bb8", "metadata": {}, "source": ["## Analysis of Results"]}, {"cell_type": "code", "execution_count": 17, "id": "ef3fb361", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/home/<USER>/committed_git/xception\n"]}], "source": ["print(os.getcwd()) "]}, {"cell_type": "code", "execution_count": 19, "id": "92cc60a0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/home/<USER>/committed_git/xception\n", "['trial09.json', 'trial06.json', 'trial04.json', 'trial08.json', 'trial10.json', 'trial01.json', 'trial05.json', 'trial00.json', 'trial07.json', 'trial03.json', 'trial02.json']\n"]}], "source": ["# Analysis of results\n", "import os\n", "import json\n", "\n", "print(os.getcwd())\n", "\n", "for _, _, file_names in os.walk('tuning_results'):\n", "    print(file_names)\n", "    files = file_names    \n", "\n", "tuning_results = {}\n", "\n", "for file in files:\n", "    cur_filename = 'tuning_results/' + file\n", "    data = open(cur_filename)\n", "    data = json.load(data)\n", "    \n", "    trial_results = {}\n", "    trial_results['trial_id'] = data['trial_id']\n", "    trial_results['values'] = data['hyperparameters']['values']\n", "    trial_results['val_loss'] = data['metrics']['metrics']['val_loss']['observations'][0]['value'][0]\n", "    trial_results['status'] = data['status']\n", "    tuning_results[data['trial_id']] = trial_results"]}, {"cell_type": "code", "execution_count": 20, "id": "0aec1892", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Trial that resulted in minimum validation loss: 00\n", "Validation Loss: 0.25114965438842773\n", "Best Hyperparameters: {'fc_dropout': True, 'pooling_dropout': False, 'learning_rate': 0.0001, 'l2_conv2d': 0.0, 'l2_sepconv2d': 0.01, 'batch_size': 16}\n"]}], "source": ["best_trial = min(tuning_results.keys(), key=lambda x: tuning_results[x]['val_loss'])\n", "\n", "print(f'Trial that resulted in minimum validation loss: {best_trial}')\n", "print(f'Validation Loss: {tuning_results[best_trial][\"val_loss\"]}')\n", "print(f'Best Hyperparameters: {tuning_results[best_trial][\"values\"]}')"]}, {"cell_type": "markdown", "id": "c84f58fe", "metadata": {}, "source": ["## Best Hyperparameters:\n", "- fc_dropout: True\n", "- pooling_dropout: False\n", "- learning_rate: 0.0001\n", "- l2_conv2d: 0.0\n", "- l2_sepconv2d: 0.01\n", "- batch_size: 16"]}, {"cell_type": "code", "execution_count": null, "id": "615ea0ee", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}, "papermill": {"default_parameters": {}, "duration": null, "end_time": null, "environment_variables": {}, "exception": null, "input_path": "__notebook__.ipynb", "output_path": "__notebook__.ipynb", "parameters": {}, "start_time": "2023-06-20T01:09:11.975837", "version": "2.4.0"}}, "nbformat": 4, "nbformat_minor": 5}