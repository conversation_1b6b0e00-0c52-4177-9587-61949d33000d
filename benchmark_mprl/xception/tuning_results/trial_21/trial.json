{"trial_id": "21", "hyperparameters": {"space": [{"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Boolean", "config": {"name": "pooling_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "learning_rate", "default": 0.01, "conditions": [], "values": [0.01, 0.001, 0.0001], "ordered": true}}, {"class_name": "Choice", "config": {"name": "l2_conv2d", "default": 0.0, "conditions": [], "values": [0.0, 0.01], "ordered": true}}, {"class_name": "Choice", "config": {"name": "l2_sepconv2d", "default": 0.0, "conditions": [], "values": [0.0, 0.01], "ordered": true}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"fc_dropout": false, "pooling_dropout": false, "learning_rate": 0.01, "l2_conv2d": 0.01, "l2_sepconv2d": 0.0, "batch_size": 64}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [4.688146591186523], "step": 4}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.11049900949001312], "step": 4}]}, "val_loss": {"direction": "min", "observations": [{"value": [5.592501163482666], "step": 4}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.07759678363800049], "step": 4}]}}}, "score": 5.592501163482666, "best_step": 4, "status": "COMPLETED", "message": null}