{"trial_id": "09", "hyperparameters": {"space": [{"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Boolean", "config": {"name": "pooling_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "learning_rate", "default": 0.01, "conditions": [], "values": [0.01, 0.001, 0.0001], "ordered": true}}, {"class_name": "Choice", "config": {"name": "l2_conv2d", "default": 0.0, "conditions": [], "values": [0.0, 0.01], "ordered": true}}, {"class_name": "Choice", "config": {"name": "l2_sepconv2d", "default": 0.0, "conditions": [], "values": [0.0, 0.01], "ordered": true}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"fc_dropout": true, "pooling_dropout": false, "learning_rate": 0.001, "l2_conv2d": 0.0, "l2_sepconv2d": 0.0, "batch_size": 32}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [1.3299823999404907], "step": 5}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.57692950963974], "step": 5}]}, "val_loss": {"direction": "min", "observations": [{"value": [1.0955172777175903], "step": 5}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.6991520524024963], "step": 5}]}}}, "score": 1.0955172777175903, "best_step": 5, "status": "COMPLETED", "message": null}