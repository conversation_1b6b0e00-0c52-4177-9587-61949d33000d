{"trial_id": "06", "hyperparameters": {"space": [{"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Boolean", "config": {"name": "pooling_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "learning_rate", "default": 0.01, "conditions": [], "values": [0.01, 0.001, 0.0001], "ordered": true}}, {"class_name": "Choice", "config": {"name": "l2_conv2d", "default": 0.0, "conditions": [], "values": [0.0, 0.01], "ordered": true}}, {"class_name": "Choice", "config": {"name": "l2_sepconv2d", "default": 0.0, "conditions": [], "values": [0.0, 0.01], "ordered": true}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"fc_dropout": true, "pooling_dropout": false, "learning_rate": 0.0001, "l2_conv2d": 0.0, "l2_sepconv2d": 0.01, "batch_size": 64}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [0.25835081934928894], "step": 11}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.9103790521621704], "step": 11}]}, "val_loss": {"direction": "min", "observations": [{"value": [0.25994160771369934], "step": 11}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.9175499081611633], "step": 11}]}}}, "score": 0.25994160771369934, "best_step": 11, "status": "COMPLETED", "message": null}