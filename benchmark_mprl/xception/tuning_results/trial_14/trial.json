{"trial_id": "14", "hyperparameters": {"space": [{"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Boolean", "config": {"name": "pooling_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "learning_rate", "default": 0.01, "conditions": [], "values": [0.01, 0.001, 0.0001], "ordered": true}}, {"class_name": "Choice", "config": {"name": "l2_conv2d", "default": 0.0, "conditions": [], "values": [0.0, 0.01], "ordered": true}}, {"class_name": "Choice", "config": {"name": "l2_sepconv2d", "default": 0.0, "conditions": [], "values": [0.0, 0.01], "ordered": true}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"fc_dropout": false, "pooling_dropout": true, "learning_rate": 0.001, "l2_conv2d": 0.0, "l2_sepconv2d": 0.0, "batch_size": 64}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [0.26931032538414], "step": 14}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.9037962555885315], "step": 14}]}, "val_loss": {"direction": "min", "observations": [{"value": [0.35457634925842285], "step": 14}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.8813455104827881], "step": 14}]}}}, "score": 0.35457634925842285, "best_step": 14, "status": "COMPLETED", "message": null}