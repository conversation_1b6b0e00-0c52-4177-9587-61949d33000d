{"trial_id": "22", "hyperparameters": {"space": [{"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Boolean", "config": {"name": "pooling_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "learning_rate", "default": 0.01, "conditions": [], "values": [0.01, 0.001, 0.0001], "ordered": true}}, {"class_name": "Choice", "config": {"name": "l2_conv2d", "default": 0.0, "conditions": [], "values": [0.0, 0.01], "ordered": true}}, {"class_name": "Choice", "config": {"name": "l2_sepconv2d", "default": 0.0, "conditions": [], "values": [0.0, 0.01], "ordered": true}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"fc_dropout": false, "pooling_dropout": false, "learning_rate": 0.0001, "l2_conv2d": 0.01, "l2_sepconv2d": 0.01, "batch_size": 16}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [0.2534375488758087], "step": 24}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.9330422878265381], "step": 24}]}, "val_loss": {"direction": "min", "observations": [{"value": [0.34351447224617004], "step": 24}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.911134660243988], "step": 24}]}}}, "score": 0.34351447224617004, "best_step": 24, "status": "COMPLETED", "message": null}