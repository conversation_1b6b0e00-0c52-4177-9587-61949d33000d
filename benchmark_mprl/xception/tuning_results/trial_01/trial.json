{"trial_id": "01", "hyperparameters": {"space": [{"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Boolean", "config": {"name": "pooling_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "learning_rate", "default": 0.01, "conditions": [], "values": [0.01, 0.001, 0.0001], "ordered": true}}, {"class_name": "Choice", "config": {"name": "l2_conv2d", "default": 0.0, "conditions": [], "values": [0.0, 0.01], "ordered": true}}, {"class_name": "Choice", "config": {"name": "l2_sepconv2d", "default": 0.0, "conditions": [], "values": [0.0, 0.01], "ordered": true}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"fc_dropout": false, "pooling_dropout": true, "learning_rate": 0.01, "l2_conv2d": 0.01, "l2_sepconv2d": 0.01, "batch_size": 16}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [3.7142534255981445], "step": 13}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.24638085067272186], "step": 13}]}, "val_loss": {"direction": "min", "observations": [{"value": [3.5844943523406982], "step": 13}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.21934619545936584], "step": 13}]}}}, "score": 3.5844943523406982, "best_step": 13, "status": "COMPLETED", "message": null}