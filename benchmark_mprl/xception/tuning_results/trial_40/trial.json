{"trial_id": "40", "hyperparameters": {"space": [{"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Boolean", "config": {"name": "pooling_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "learning_rate", "default": 0.01, "conditions": [], "values": [0.01, 0.001, 0.0001], "ordered": true}}, {"class_name": "Choice", "config": {"name": "l2_conv2d", "default": 0.0, "conditions": [], "values": [0.0, 0.01], "ordered": true}}, {"class_name": "Choice", "config": {"name": "l2_sepconv2d", "default": 0.0, "conditions": [], "values": [0.0, 0.01], "ordered": true}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"fc_dropout": true, "pooling_dropout": true, "learning_rate": 0.0001, "l2_conv2d": 0.01, "l2_sepconv2d": 0.0, "batch_size": 64}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [0.3037489652633667], "step": 27}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.9154416918754578], "step": 27}]}, "val_loss": {"direction": "min", "observations": [{"value": [0.2907049059867859], "step": 27}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.9317193031311035], "step": 27}]}}}, "score": 0.2907049059867859, "best_step": 27, "status": "COMPLETED", "message": null}