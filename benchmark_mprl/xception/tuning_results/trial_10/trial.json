{"trial_id": "10", "hyperparameters": {"space": [{"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Boolean", "config": {"name": "pooling_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "learning_rate", "default": 0.01, "conditions": [], "values": [0.01, 0.001, 0.0001], "ordered": true}}, {"class_name": "Choice", "config": {"name": "l2_conv2d", "default": 0.0, "conditions": [], "values": [0.0, 0.01], "ordered": true}}, {"class_name": "Choice", "config": {"name": "l2_sepconv2d", "default": 0.0, "conditions": [], "values": [0.0, 0.01], "ordered": true}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"fc_dropout": true, "pooling_dropout": false, "learning_rate": 0.01, "l2_conv2d": 0.01, "l2_sepconv2d": 0.0, "batch_size": 16}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [8.135156631469727], "step": 1}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.00027893224614672363], "step": 1}]}, "val_loss": {"direction": "min", "observations": [{"value": [8.171087265014648], "step": 1}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [5.5784894357202575e-05], "step": 1}]}}}, "score": 8.171087265014648, "best_step": 1, "status": "COMPLETED", "message": null}