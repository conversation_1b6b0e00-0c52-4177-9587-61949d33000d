{"trial_id": "34", "hyperparameters": {"space": [{"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Boolean", "config": {"name": "pooling_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "learning_rate", "default": 0.01, "conditions": [], "values": [0.01, 0.001, 0.0001], "ordered": true}}, {"class_name": "Choice", "config": {"name": "l2_conv2d", "default": 0.0, "conditions": [], "values": [0.0, 0.01], "ordered": true}}, {"class_name": "Choice", "config": {"name": "l2_sepconv2d", "default": 0.0, "conditions": [], "values": [0.0, 0.01], "ordered": true}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"fc_dropout": true, "pooling_dropout": false, "learning_rate": 0.0001, "l2_conv2d": 0.0, "l2_sepconv2d": 0.0, "batch_size": 64}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [0.1942920982837677], "step": 14}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.9340324997901917], "step": 14}]}, "val_loss": {"direction": "min", "observations": [{"value": [0.2265681028366089], "step": 14}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.928483784198761], "step": 14}]}}}, "score": 0.2265681028366089, "best_step": 14, "status": "COMPLETED", "message": null}