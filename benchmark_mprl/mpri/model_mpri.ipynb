{"cells": [{"cell_type": "markdown", "id": "5e85c389", "metadata": {}, "source": ["## Summary of notebook:\n", "\n", "This notebook details the code used to build the MPRI model. After successfully building the model, a Proof of Concept using the MPRI model was then developed using the open-source dataset augmented with Gaussian noise (with filename 'augmented_features_10_ue1_v2_ds.npy' and 'augmented_labels_10_ue1_v2_ds.npy').\n", "\n", "Below shows the main sections of this notebook.\n", "1. Development of MPRI model\n", "2. Proof of Concept for MPRI model\n", "\n", "The following past works were referenced.\n", "\n", "<PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, \"Toward 5G NR High-Precision Indoor Positioning via Channel Frequency Response: A New Paradigm and Dataset Generation Method,\" in IEEE Journal on Selected Areas in Communications, vol. 40, no. 7, pp. 2233-2247, July 2022, doi: 10.1109/JSAC.2022.3157397."]}, {"cell_type": "code", "execution_count": 94, "id": "88cb3e39", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PhysicalDevice(name='/physical_device:GPU:0', device_type='GPU')\n", "2.9.1\n"]}], "source": ["# Configure amd test GPU\n", "import tensorflow as tf\n", "from tensorflow.python.client import device_lib\n", "\n", "# Prevent automatic GPU memory pre-allocation\n", "gpus = tf.config.experimental.list_physical_devices('GPU')\n", "for gpu in gpus:\n", "    print(gpu)\n", "    tf.config.experimental.set_memory_growth(gpu, True)\n", "\n", "print(tf.__version__)\n", "# print(device_lib.list_local_devices())"]}, {"cell_type": "markdown", "id": "f4ad120d", "metadata": {}, "source": ["## Development of MPRI model"]}, {"cell_type": "code", "execution_count": 95, "id": "60435f2b", "metadata": {}, "outputs": [], "source": ["# Whole network composed of 63 layers, approximately 2.6m total no. of parameters\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import os\n", "\n", "import tensorflow as tf\n", "import tensorflow.keras as keras\n", "from tensorflow.keras.layers import Conv2D, BatchNormalization, ReLU, MaxPool2D,\\\n", "                                    GlobalAvgPool2D, Dense, Add, concatenate, Input,\\\n", "                                    Dropout\n", "from tensorflow.keras import Model\n", "\n", "# Note: tf version 2.9.1 does not have Identity layer. Implement our own identity layer which is argument insensitive\n", "# and returns its inputs argument as output"]}, {"cell_type": "code", "execution_count": 96, "id": "8684e2ce", "metadata": {}, "outputs": [], "source": ["def input_module(x):\n", "    \n", "    print('Beginning of input module')\n", "    \n", "    # Set no. of filters to 256 to match the output of Add layer at the end of\n", "    # upper half of MPRI module\n", "    x = Conv2D(filters = 256, kernel_size = 3, strides = 1, padding = 'same')(x)\n", "    x = BatchNormalization()(x)\n", "    x = ReLU()(x)\n", "    \n", "    # Normally, strides = 2 to reduce dimensions but set strides =1 for now to match\n", "    # output shapes\n", "    x = MaxPool2D(pool_size= 3, strides = 2, padding = 'same')(x)\n", "    print(f'Output of input module tensor: {x.shape}')\n", "    \n", "    return x"]}, {"cell_type": "code", "execution_count": 97, "id": "e1f66f42", "metadata": {}, "outputs": [], "source": ["def mpri_upperhalf(x):\n", "    \n", "    print('Beginning of MPRI Upper Half')\n", "    \n", "    # Save input as another variable since need to add input of mpri\n", "    # with output of mpri\n", "    input_tensor = x\n", "    \n", "    # Bottleneck layer with 1x1 conv filter\n", "    bottlenecked_tensor = Conv2D(filters = 32, kernel_size = 1, strides = 1, padding = 'same')(x)\n", "    print(f'Shape of bottleneck tensor: {bottlenecked_tensor.shape}')\n", "    \n", "    # First path\n", "    firstpath_tensor = BatchNormalization()(bottlenecked_tensor)\n", "    firstpath_tensor = ReLU()(firstpath_tensor)\n", "    firstpath_tensor = Conv2D(filters = 64, kernel_size = 1, strides = 1, padding = 'same')(firstpath_tensor)\n", "    print(f'Shape of firstpath tensor: {firstpath_tensor.shape}')\n", "    \n", "    # Second path\n", "    secondpath_tensor = BatchNormalization()(bottlenecked_tensor)\n", "    secondpath_tensor = ReLU()(secondpath_tensor)\n", "    secondpath_tensor = Conv2D(filters = 32, kernel_size = (5,1), strides = 1, padding = 'same')(secondpath_tensor)\n", "    secondpath_tensor = Conv2D(filters = 32, kernel_size = (1,3), strides = 1, padding = 'same')(secondpath_tensor)\n", "    print(f'Shape of secondpath tensor: {secondpath_tensor.shape}')\n", "    \n", "    # Third path\n", "    # Normally, strides = 2 to reduce the dimensions of the input\n", "    # In this case, experiment with strides = 1 to fit desired output shape for concatenation layer\n", "    thirdpath_tensor = MaxPool2D(pool_size = 3, strides = 1, padding = 'same')(bottlenecked_tensor)\n", "    thirdpath_tensor = BatchNormalization()(thirdpath_tensor)\n", "    thirdpath_tensor = ReLU()(thirdpath_tensor)\n", "    thirdpath_tensor = Conv2D(filters = 32, kernel_size = 3, strides = 1, padding = 'same')(thirdpath_tensor)\n", "    print(f'Shape of thirdpath tensor: {thirdpath_tensor.shape}')\n", "    \n", "    # Fourth path\n", "    fourthpath_tensor = BatchNormalization()(bottlenecked_tensor)\n", "    fourthpath_tensor = ReLU()(fourthpath_tensor)\n", "    fourthpath_tensor = Conv2D(filters = 32, kernel_size = 1, strides = 1, padding = 'same')(fourthpath_tensor)\n", "    \n", "    fourthpath_tensor = BatchNormalization()(fourthpath_tensor)\n", "    fourthpath_tensor = ReLU()(fourthpath_tensor)\n", "    fourthpath_tensor = Conv2D(filters = 128, kernel_size = 1, strides = 1, padding = 'same')(fourthpath_tensor)\n", "    print(f'Shape of fourthpath tensor: {fourthpath_tensor.shape}')\n", "    \n", "    # Depth concatenate the output from the four paths\n", "    concatenated_tensor = Concatenate()([firstpath_tensor, secondpath_tensor, thirdpath_tensor, fourthpath_tensor])\n", "    print(f'Shape of concatenated tensor: {concatenated_tensor.shape}')\n", "    \n", "    # Add the depth concatenated layer and input tensor\n", "    # To add successfully, input tensor must have 256 channels as well to match the shape of\n", "    # the concatenated tensor\n", "    output_tensor = Add()([input_tensor, concatenated_tensor])\n", "    print(f'Shape of added tensor: {output_tensor.shape}')\n", "    \n", "    return output_tensor"]}, {"cell_type": "code", "execution_count": 98, "id": "bfeefb0b", "metadata": {}, "outputs": [], "source": ["def mpri_lowerhalf(x):\n", "    \n", "    print('Beginning of MPRI Lower Half')\n", "    \n", "    # Main feature: Dual-path parallel convolution blocks\n", "    # Two basic units of parallel feature propagation paths with cross-connections,\n", "    # and then stack this dual-path structure three times\n", "    \n", "    '''\n", "    For now, assume each max pooling to have a pool size of 3 and a stride length of 2\n", "    This will downscale dimensions by factor of half.\n", "    \n", "    Path 1 (3x3 Conv Filters):\n", "    Input --> MaxPool2D(pool_size = 3, strides = 2) --> ... --> Conv2D(num_filter = 256, kernel_size = 3, strides = 1, padding = 'same')\n", "    \n", "    Path 2 (1x1 Conv Filters):\n", "    Input --> MaxPool2D(pool_size = 3, strides = 2) --> ... --> Conv2D(num_filter = 256, kernel_size = 1, strides = 1, padding = 'same')\n", "    '''\n", "    \n", "    # Functions for convolution blocks of different filters\n", "    def conv3x3_block(x):\n", "        \n", "        x = BatchNormalization()(x)\n", "        x = ReLU()(x)\n", "        x = Conv2D(filters = 256, kernel_size = 3, strides = 1, padding = 'same')(x)\n", "        return x\n", "    \n", "    def conv1x1_block(x):\n", "        \n", "        x = BatchNormalization()(x)\n", "        x = ReLU()(x)\n", "        x = Conv2D(filters = 256, kernel_size = 1, strides = 1, padding = 'same')(x)\n", "        return x\n", "\n", "    # --- First layer ---\n", "    upperpath_pooledtensor = MaxPool2D(pool_size = 3, strides = 2, padding = 'same')(x)\n", "    upperpath_tensor = conv3x3_block(upperpath_pooledtensor)\n", "    \n", "    lowerpath_pooledtensor = MaxPool2D(pool_size = 3, strides = 2, padding = 'same')(x)\n", "    lowerpath_tensor = conv1x1_block(lowerpath_pooledtensor)\n", "    \n", "    upperpath_tensor = Add()([upperpath_pooledtensor, upperpath_tensor, lowerpath_tensor])\n", "    lowerpath_tensor = Add()([lowerpath_pooledtensor, lowerpath_tensor, upperpath_tensor])\n", "    \n", "    print(f'First layer - Shape of upper path tensor: {upperpath_tensor.shape}')\n", "    print(f'First layer - Shape of lower path tensor: {lowerpath_tensor.shape}')\n", "    \n", "    # --- Second layer ---\n", "    upperpath_pooledtensor = MaxPool2D(pool_size = 3, strides = 2, padding = 'same')(upperpath_tensor)\n", "    upperpath_tensor = conv3x3_block(upperpath_pooledtensor)\n", "    \n", "    lowerpath_pooledtensor = MaxPool2D(pool_size = 3, strides = 2, padding = 'same')(lowerpath_tensor)\n", "    lowerpath_tensor = conv1x1_block(lowerpath_pooledtensor)\n", "    \n", "    upperpath_tensor = Add()([upperpath_pooledtensor, upperpath_tensor, lowerpath_tensor])\n", "    lowerpath_tensor = Add()([lowerpath_pooledtensor, lowerpath_tensor, upperpath_tensor])\n", "    \n", "    print(f'Second layer - Shape of upper path tensor: {upperpath_tensor.shape}') \n", "    print(f'Second layer - Shape of lower path tensor: {lowerpath_tensor.shape}')     \n", "    \n", "    # --- Third layer ---\n", "    upperpath_pooledtensor = MaxPool2D(pool_size = 3, strides = 2, padding = 'same')(upperpath_tensor)\n", "    upperpath_tensor = conv3x3_block(upperpath_pooledtensor)\n", "    \n", "    lowerpath_pooledtensor = MaxPool2D(pool_size = 3, strides = 2, padding = 'same')(lowerpath_tensor)\n", "    lowerpath_tensor = conv1x1_block(lowerpath_pooledtensor)\n", "    \n", "    upperpath_tensor = Add()([upperpath_pooledtensor, upperpath_tensor, lowerpath_tensor])\n", "    lowerpath_tensor = Add()([lowerpath_pooledtensor, lowerpath_tensor, upperpath_tensor])\n", "    \n", "    print(f'Third layer - Shape of upper path tensor: {upperpath_tensor.shape}') \n", "    print(f'Third layer - Shape of lower path tensor: {lowerpath_tensor.shape}')\n", "    \n", "    # Final layer - Add upper and lower path tensors\n", "    output_tensor = Add()([upperpath_tensor, lowerpath_tensor])\n", "    print(f'Shape of output tensor: {output_tensor.shape}')\n", "    \n", "    return output_tensor"]}, {"cell_type": "code", "execution_count": 99, "id": "e4877a54", "metadata": {}, "outputs": [], "source": ["def output_module(x, num_classes = 1000):\n", "    \n", "    print('Beginning of output module')\n", "    x = Conv2D(filters = 32, kernel_size = 3, strides = 1, padding = 'same')(x)\n", "    x = BatchNormalization()(x)\n", "    x = ReLU()(x)\n", "    print(f'Shape after last convolution: {x.shape}')\n", "    \n", "    x = GlobalAvgPool2D()(x)\n", "    x = Dense(units = num_classes, activation = 'softmax')(x)\n", "    \n", "    print(f'Final output shape: {x.shape}')\n", "    \n", "    return x"]}, {"cell_type": "code", "execution_count": 100, "id": "afd9d4d4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Beginning of input module\n", "Output of input module tensor: (None, 97, 8, 256)\n", "Beginning of MPRI Upper Half\n", "Shape of bottleneck tensor: (None, 97, 8, 32)\n", "Shape of firstpath tensor: (None, 97, 8, 64)\n", "Shape of secondpath tensor: (None, 97, 8, 32)\n", "Shape of thirdpath tensor: (None, 97, 8, 32)\n", "Shape of fourthpath tensor: (None, 97, 8, 128)\n", "Shape of concatenated tensor: (None, 97, 8, 256)\n", "Shape of added tensor: (None, 97, 8, 256)\n", "Beginning of MPRI Lower Half\n", "First layer - Shape of upper path tensor: (None, 49, 4, 256)\n", "First layer - Shape of lower path tensor: (None, 49, 4, 256)\n", "Second layer - Shape of upper path tensor: (None, 25, 2, 256)\n", "Second layer - Shape of lower path tensor: (None, 25, 2, 256)\n", "Third layer - Shape of upper path tensor: (None, 13, 1, 256)\n", "Third layer - Shape of lower path tensor: (None, 13, 1, 256)\n", "Shape of output tensor: (None, 13, 1, 256)\n", "Beginning of output module\n", "Shape after last convolution: (None, 13, 1, 32)\n", "Final output shape: (None, 3876)\n"]}], "source": ["# Test model over 5 kfold splits\n", "model_input = Input(shape = (193,16,1))\n", "model_output = output_module(mpri_lowerhalf(mpri_upperhalf(input_module(model_input))), num_classes = 3876)\n", "mpri_model = Model(model_input, model_output)\n", "\n", "print(mpri_model.summary())"]}, {"cell_type": "code", "execution_count": null, "id": "d5e12ed2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "c9e9172d", "metadata": {}, "source": ["## Proof of Concept for MPRI model"]}, {"cell_type": "code", "execution_count": 101, "id": "d96b373e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/home/<USER>/committed_git/datasets\n", "/home/<USER>/committed_git/datasets\n"]}], "source": ["print(os.getcwd())\n", "os.chdir('../datasets')\n", "print(os.getcwd())"]}, {"cell_type": "code", "execution_count": 102, "id": "42a741bc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Shape of features np array: (89628, 193, 16)\n", "Shape of labels np array: (89628,)\n"]}], "source": ["# Import dataset\n", "\n", "features = np.load('augmented_features_10_ue1_v2_ds.npy')\n", "labels = np.load('augmented_labels_10_ue1_v2_ds.npy')\n", "\n", "print(f'Shape of features np array: {features.shape}')\n", "print(f'Shape of labels np array: {labels.shape}')"]}, {"cell_type": "code", "execution_count": 107, "id": "a32b0f49", "metadata": {}, "outputs": [], "source": ["import h5py\n", "\n", "# Get dictionary of RP index and coordinates\n", "# Open HDF5 file and access the dataset\n", "filename = 'dataset_SNR10_outdoor.mat'\n", "hdf5_file = h5py.File(filename, 'r')\n", "\n", "features_dataset = hdf5_file['features']\n", "labels_dataset = hdf5_file['labels']['position']\n", "\n", "# Convert HDF5 dataset to NumPy array\n", "features = np.array(features_dataset)\n", "labels = np.array(labels_dataset)\n", "\n", "# Prepare features for dataset\n", "# Retrieve features from the first UE and transpose the individual matrix\n", "features_transposed = np.zeros((3876,193,16), dtype = np.float64)\n", "for i in range(len(features)):\n", "    features_transposed[i] = features[i][0].T\n", "\n", "# Prepare labels for dataset\n", "count = 0\n", "rp_dict = {}\n", "# For labels, have a shape of (1,) where that number represents the class of that coordinate\n", "\n", "for label in labels:\n", "    rp_dict[count] = label\n", "    count += 1\n", "\n", "# Close the HDF5 file\n", "hdf5_file.close()"]}, {"cell_type": "code", "execution_count": 103, "id": "088246f7", "metadata": {}, "outputs": [], "source": ["from sklearn.model_selection import train_test_split\n", "\n", "X = features\n", "y = labels\n", "\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, shuffle=True)"]}, {"cell_type": "code", "execution_count": 104, "id": "9fe200c4", "metadata": {}, "outputs": [], "source": ["# Create custom callbacks to evaluate model\n", "class ValidationCallback(tf.keras.callbacks.Callback):\n", "    \n", "    def __init__(self, X_val, Y_val):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,self).__init__()\n", "        self.X_val = X_val\n", "        self.Y_val = Y_val\n", "        \n", "    # number_of_iterations = total_number_of_training_examples / batch_size\n", "    # In this case, train example of 1,000 and batch size of 100\n", "    # number_of_iterations over 1 epoch is 10\n", "    # if have 5 epochs, number of iterations is 50\n", "    \n", "    def calc_error(self, actual, predicted):\n", "\n", "        x_error = (actual[0] - predicted[0])**2\n", "        y_error = (actual[1] - predicted[1])**2\n", "        z_error = (actual[2] - predicted[2])**2\n", "\n", "        return x_error + y_error + z_error\n", "        \n", "    # Have one function that reports metrics on end of every epoch\n", "    def on_epoch_end(self, epoch, logs = None):\n", "        \n", "        # Get evaluation metrics\n", "        print('\\n')\n", "        print('Epoch End - Custom Validation Callback')\n", "        val_loss, val_accuracy = self.model.evaluate(self.X_val, self.Y_val, verbose = 0)\n", "\n", "        # Get distance error metrics - RMSE\n", "        # Get predictions for each feature heatmap in X_val\n", "        Y_pred = self.model.predict(self.X_val, verbose = 0)\n", "        err_sum = 0\n", "        \n", "        # Iterate through actual and predicted to get distance error\n", "        for i in range(len(self.X_val)):\n", "\n", "            # Get the coordinates for actual y\n", "            actual_coords = rp_dict[self.Y_val[i]]\n", "            \n", "            # Get the coordinates for predicted y\n", "            predicted_rp = np.argmax(Y_pred[i])\n", "            pred_coords = rp_dict[predicted_rp]\n", "\n", "            # Calculate distance error\n", "            err = self.calc_error(actual_coords, pred_coords)\n", "            err_sum += err\n", "            \n", "        # Get RMSE\n", "        rmse = np.sqrt((err_sum/len(self.X_val)))\n", "        print(f'Validation Loss: {val_loss}, Validation Accuracy: {val_accuracy}, RMSE: {rmse}')\n", "        logs['val_loss'] = val_loss\n", "        logs['val_accuracy'] = val_accuracy\n", "        logs['rmse'] = rmse"]}, {"cell_type": "code", "execution_count": 109, "id": "d3d0c917", "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processing split 1\n", "Beginning of input module\n", "Output of input module tensor: (None, 97, 8, 256)\n", "Beginning of MPRI Upper Half\n", "Shape of bottleneck tensor: (None, 97, 8, 32)\n", "Shape of firstpath tensor: (None, 97, 8, 64)\n", "Shape of secondpath tensor: (None, 97, 8, 32)\n", "Shape of thirdpath tensor: (None, 97, 8, 32)\n", "Shape of fourthpath tensor: (None, 97, 8, 128)\n", "Shape of concatenated tensor: (None, 97, 8, 256)\n", "Shape of added tensor: (None, 97, 8, 256)\n", "Beginning of MPRI Lower Half\n", "First layer - Shape of upper path tensor: (None, 49, 4, 256)\n", "First layer - Shape of lower path tensor: (None, 49, 4, 256)\n", "Second layer - Shape of upper path tensor: (None, 25, 2, 256)\n", "Second layer - Shape of lower path tensor: (None, 25, 2, 256)\n", "Third layer - Shape of upper path tensor: (None, 13, 1, 256)\n", "Third layer - Shape of lower path tensor: (None, 13, 1, 256)\n", "Shape of output tensor: (None, 13, 1, 256)\n", "Beginning of output module\n", "Shape after last convolution: (None, 13, 1, 32)\n", "Final output shape: (None, 3876)\n", "Epoch 1/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 5.9584 - accuracy: 0.0546\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 4.146804332733154, Validation Accuracy: 0.10777641087770462, RMSE: 5.572550261048854\n", "2241/2241 [==============================] - 39s 17ms/step - loss: 5.9584 - accuracy: 0.0546 - val_loss: 4.1468 - val_accuracy: 0.1078 - rmse: 5.5726\n", "Epoch 2/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 2.3330 - accuracy: 0.4171\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 2.1101393699645996, Validation Accuracy: 0.4103536903858185, RMSE: 2.8318469870435172\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 2.3330 - accuracy: 0.4171 - val_loss: 2.1101 - val_accuracy: 0.4104 - rmse: 2.8318\n", "Epoch 3/100\n", "2239/2241 [============================>.] - ETA: 0s - loss: 1.2458 - accuracy: 0.6400\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 1.3100279569625854, Validation Accuracy: 0.6054892539978027, RMSE: 1.8932002790682403\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 1.2455 - accuracy: 0.6401 - val_loss: 1.3100 - val_accuracy: 0.6055 - rmse: 1.8932\n", "Epoch 4/100\n", "2239/2241 [============================>.] - ETA: 0s - loss: 0.8338 - accuracy: 0.7348\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 1.0637595653533936, Validation Accuracy: 0.6485551595687866, RMSE: 2.0405558371463113\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.8339 - accuracy: 0.7348 - val_loss: 1.0638 - val_accuracy: 0.6486 - rmse: 2.0406\n", "Epoch 5/100\n", "2240/2241 [============================>.] - ETA: 0s - loss: 0.6401 - accuracy: 0.7865\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.830302357673645, Validation Accuracy: 0.7204061150550842, RMSE: 1.4681005039133181\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.6401 - accuracy: 0.7865 - val_loss: 0.8303 - val_accuracy: 0.7204 - rmse: 1.4681\n", "Epoch 6/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.5217 - accuracy: 0.8197\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.66743004322052, Validation Accuracy: 0.7716166377067566, RMSE: 1.288574176111446\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.5217 - accuracy: 0.8197 - val_loss: 0.6674 - val_accuracy: 0.7716 - rmse: 1.2886\n", "Epoch 7/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.4481 - accuracy: 0.8423\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.6550299525260925, Validation Accuracy: 0.7668191194534302, RMSE: 1.280959349848154\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.4481 - accuracy: 0.8423 - val_loss: 0.6550 - val_accuracy: 0.7668 - rmse: 1.2810\n", "Epoch 8/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.3943 - accuracy: 0.8574\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.6640741229057312, Validation Accuracy: 0.7746847867965698, RMSE: 1.4008273642651148\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.3943 - accuracy: 0.8574 - val_loss: 0.6641 - val_accuracy: 0.7747 - rmse: 1.4008\n", "Epoch 9/100\n", "2238/2241 [============================>.] - ETA: 0s - loss: 0.3550 - accuracy: 0.8728\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.5647779703140259, Validation Accuracy: 0.8102197647094727, RMSE: 1.1711103696646012\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.3551 - accuracy: 0.8728 - val_loss: 0.5648 - val_accuracy: 0.8102 - rmse: 1.1711\n", "Epoch 10/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.3268 - accuracy: 0.8820\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.5027967691421509, Validation Accuracy: 0.8297445178031921, RMSE: 1.2673926653753518\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.3268 - accuracy: 0.8820 - val_loss: 0.5028 - val_accuracy: 0.8297 - rmse: 1.2674\n", "Epoch 11/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.2983 - accuracy: 0.8920\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.5794562101364136, Validation Accuracy: 0.7951578497886658, RMSE: 1.1175099629254013\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.2983 - accuracy: 0.8920 - val_loss: 0.5795 - val_accuracy: 0.7952 - rmse: 1.1175\n", "Epoch 12/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.2733 - accuracy: 0.9008\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.40494459867477417, Validation Accuracy: 0.8598125576972961, RMSE: 0.891950815880196\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.2733 - accuracy: 0.9008 - val_loss: 0.4049 - val_accuracy: 0.8598 - rmse: 0.8920\n", "Epoch 13/100\n", "2240/2241 [============================>.] - ETA: 0s - loss: 0.2583 - accuracy: 0.9064\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.44651466608047485, Validation Accuracy: 0.8473725318908691, RMSE: 1.0691921852696153\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.2583 - accuracy: 0.9064 - val_loss: 0.4465 - val_accuracy: 0.8474 - rmse: 1.0692\n", "Epoch 14/100\n", "2240/2241 [============================>.] - ETA: 0s - loss: 0.2411 - accuracy: 0.9124\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.5285487771034241, Validation Accuracy: 0.8203726410865784, RMSE: 1.0985928134705851\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.2411 - accuracy: 0.9124 - val_loss: 0.5285 - val_accuracy: 0.8204 - rmse: 1.0986\n", "Epoch 15/100\n", "2240/2241 [============================>.] - ETA: 0s - loss: 0.2328 - accuracy: 0.9162\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.44540902972221375, Validation Accuracy: 0.8512774705886841, RMSE: 1.0807903480389465\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.2328 - accuracy: 0.9163 - val_loss: 0.4454 - val_accuracy: 0.8513 - rmse: 1.0808\n", "Epoch 16/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.2127 - accuracy: 0.9231\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.37546584010124207, Validation Accuracy: 0.8754323124885559, RMSE: 0.9273059042722533\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.2127 - accuracy: 0.9231 - val_loss: 0.3755 - val_accuracy: 0.8754 - rmse: 0.9273\n", "Epoch 17/100\n", "2240/2241 [============================>.] - ETA: 0s - loss: 0.2044 - accuracy: 0.9258\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.42247268557548523, Validation Accuracy: 0.8633270263671875, RMSE: 0.9636148077488325\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.2044 - accuracy: 0.9258 - val_loss: 0.4225 - val_accuracy: 0.8633 - rmse: 0.9636\n", "Epoch 18/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1967 - accuracy: 0.9290\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.5157981514930725, Validation Accuracy: 0.8401762843132019, RMSE: 1.1052815604648454\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1967 - accuracy: 0.9290 - val_loss: 0.5158 - val_accuracy: 0.8402 - rmse: 1.1053\n", "Epoch 19/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1847 - accuracy: 0.9331\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.3383995592594147, Validation Accuracy: 0.8899363875389099, RMSE: 0.8292529056739393\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1847 - accuracy: 0.9331 - val_loss: 0.3384 - val_accuracy: 0.8899 - rmse: 0.8293\n", "Epoch 20/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1765 - accuracy: 0.9354\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.5167512893676758, Validation Accuracy: 0.838335394859314, RMSE: 1.0318616035349486\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1765 - accuracy: 0.9354 - val_loss: 0.5168 - val_accuracy: 0.8383 - rmse: 1.0319\n", "Epoch 21/100\n", "2238/2241 [============================>.] - ETA: 0s - loss: 0.1688 - accuracy: 0.9392\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.466677188873291, Validation Accuracy: 0.8523931503295898, RMSE: 1.112651024214108\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1688 - accuracy: 0.9392 - val_loss: 0.4667 - val_accuracy: 0.8524 - rmse: 1.1127\n", "Epoch 22/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1593 - accuracy: 0.9422\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.46741393208503723, Validation Accuracy: 0.8533415198326111, RMSE: 0.9753602823901177\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1593 - accuracy: 0.9422 - val_loss: 0.4674 - val_accuracy: 0.8533 - rmse: 0.9754\n", "Epoch 23/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1593 - accuracy: 0.9427\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.5696703791618347, Validation Accuracy: 0.8281267285346985, RMSE: 1.2217578233667825\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1593 - accuracy: 0.9427 - val_loss: 0.5697 - val_accuracy: 0.8281 - rmse: 1.2218\n", "Epoch 24/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1548 - accuracy: 0.9442\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.3974292576313019, Validation Accuracy: 0.8780542016029358, RMSE: 0.9101679338150596\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1548 - accuracy: 0.9442 - val_loss: 0.3974 - val_accuracy: 0.8781 - rmse: 0.9102\n", "Train time: 910.0389261245728\n", "Processing split 2\n", "Beginning of input module\n", "Output of input module tensor: (None, 97, 8, 256)\n", "Beginning of MPRI Upper Half\n", "Shape of bottleneck tensor: (None, 97, 8, 32)\n", "Shape of firstpath tensor: (None, 97, 8, 64)\n", "Shape of secondpath tensor: (None, 97, 8, 32)\n", "Shape of thirdpath tensor: (None, 97, 8, 32)\n", "Shape of fourthpath tensor: (None, 97, 8, 128)\n", "Shape of concatenated tensor: (None, 97, 8, 256)\n", "Shape of added tensor: (None, 97, 8, 256)\n", "Beginning of MPRI Lower Half\n", "First layer - Shape of upper path tensor: (None, 49, 4, 256)\n", "First layer - Shape of lower path tensor: (None, 49, 4, 256)\n", "Second layer - Shape of upper path tensor: (None, 25, 2, 256)\n", "Second layer - Shape of lower path tensor: (None, 25, 2, 256)\n", "Third layer - Shape of upper path tensor: (None, 13, 1, 256)\n", "Third layer - Shape of lower path tensor: (None, 13, 1, 256)\n", "Shape of output tensor: (None, 13, 1, 256)\n", "Beginning of output module\n", "Shape after last convolution: (None, 13, 1, 32)\n", "Final output shape: (None, 3876)\n", "Epoch 1/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 6.1104 - accuracy: 0.0449\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 4.618824481964111, Validation Accuracy: 0.06967533379793167, RMSE: 5.797188962429658\n", "2241/2241 [==============================] - 39s 17ms/step - loss: 6.1104 - accuracy: 0.0449 - val_loss: 4.6188 - val_accuracy: 0.0697 - rmse: 5.7972\n", "Epoch 2/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 2.4392 - accuracy: 0.3946\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 2.218292713165283, Validation Accuracy: 0.38296329975128174, RMSE: 3.2646245764103377\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 2.4392 - accuracy: 0.3946 - val_loss: 2.2183 - val_accuracy: 0.3830 - rmse: 3.2646\n", "Epoch 3/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 1.2856 - accuracy: 0.6271\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 1.7516814470291138, Validation Accuracy: 0.47902488708496094, RMSE: 2.5835314220941648\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 1.2856 - accuracy: 0.6271 - val_loss: 1.7517 - val_accuracy: 0.4790 - rmse: 2.5835\n", "Epoch 4/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.8593 - accuracy: 0.7256\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 1.3100861310958862, Validation Accuracy: 0.582673192024231, RMSE: 1.85150354574198\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.8593 - accuracy: 0.7256 - val_loss: 1.3101 - val_accuracy: 0.5827 - rmse: 1.8515\n", "Epoch 5/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.6551 - accuracy: 0.7784\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 1.095984935760498, Validation Accuracy: 0.6503402590751648, RMSE: 2.0086176329180825\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.6551 - accuracy: 0.7784 - val_loss: 1.0960 - val_accuracy: 0.6503 - rmse: 2.0086\n", "Epoch 6/100\n", "2240/2241 [============================>.] - ETA: 0s - loss: 0.5364 - accuracy: 0.8138\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.8172942399978638, Validation Accuracy: 0.7196251153945923, RMSE: 1.4544048365360833\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.5363 - accuracy: 0.8137 - val_loss: 0.8173 - val_accuracy: 0.7196 - rmse: 1.4544\n", "Epoch 7/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.4601 - accuracy: 0.8375\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.7558704614639282, Validation Accuracy: 0.7418832778930664, RMSE: 1.6838748659921206\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.4601 - accuracy: 0.8375 - val_loss: 0.7559 - val_accuracy: 0.7419 - rmse: 1.6839\n", "Epoch 8/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.4040 - accuracy: 0.8552\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.5132322311401367, Validation Accuracy: 0.8226040601730347, RMSE: 1.0586531074315007\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.4040 - accuracy: 0.8552 - val_loss: 0.5132 - val_accuracy: 0.8226 - rmse: 1.0587\n", "Epoch 9/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.3648 - accuracy: 0.8692\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.5122507214546204, Validation Accuracy: 0.822827160358429, RMSE: 0.9914633395748187\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.3648 - accuracy: 0.8692 - val_loss: 0.5123 - val_accuracy: 0.8228 - rmse: 0.9915\n", "Epoch 10/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.3323 - accuracy: 0.8792\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.5329203009605408, Validation Accuracy: 0.8191453814506531, RMSE: 1.2140340702145216\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.3323 - accuracy: 0.8792 - val_loss: 0.5329 - val_accuracy: 0.8191 - rmse: 1.2140\n", "Epoch 11/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.3075 - accuracy: 0.8898\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.41329196095466614, Validation Accuracy: 0.8564096689224243, RMSE: 0.8814364170564997\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.3075 - accuracy: 0.8898 - val_loss: 0.4133 - val_accuracy: 0.8564 - rmse: 0.8814\n", "Epoch 12/100\n", "2239/2241 [============================>.] - ETA: 0s - loss: 0.2827 - accuracy: 0.8979\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.6481024026870728, Validation Accuracy: 0.7862322926521301, RMSE: 1.1714259036636432\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.2827 - accuracy: 0.8979 - val_loss: 0.6481 - val_accuracy: 0.7862 - rmse: 1.1714\n", "Epoch 13/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.2608 - accuracy: 0.9042\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.55594801902771, Validation Accuracy: 0.8193685412406921, RMSE: 1.28833063585788\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.2608 - accuracy: 0.9042 - val_loss: 0.5559 - val_accuracy: 0.8194 - rmse: 1.2883\n", "Epoch 14/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.2519 - accuracy: 0.9083\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.6847259402275085, Validation Accuracy: 0.7831641435623169, RMSE: 1.3558728419998969\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.2519 - accuracy: 0.9083 - val_loss: 0.6847 - val_accuracy: 0.7832 - rmse: 1.3559\n", "Epoch 15/100\n", "2239/2241 [============================>.] - ETA: 0s - loss: 0.2285 - accuracy: 0.9165\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.3691984713077545, Validation Accuracy: 0.8774963617324829, RMSE: 0.8798527789584214\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.2284 - accuracy: 0.9165 - val_loss: 0.3692 - val_accuracy: 0.8775 - rmse: 0.8799\n", "Epoch 16/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.2193 - accuracy: 0.9201\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.42204034328460693, Validation Accuracy: 0.8581390380859375, RMSE: 1.0468230256106168\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.2193 - accuracy: 0.9201 - val_loss: 0.4220 - val_accuracy: 0.8581 - rmse: 1.0468\n", "Epoch 17/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.2109 - accuracy: 0.9242\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.3907138705253601, Validation Accuracy: 0.8734240531921387, RMSE: 0.9276667822120137\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.2109 - accuracy: 0.9242 - val_loss: 0.3907 - val_accuracy: 0.8734 - rmse: 0.9277\n", "Epoch 18/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.2033 - accuracy: 0.9250\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.45737844705581665, Validation Accuracy: 0.8536761999130249, RMSE: 0.9443248457276864\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.2033 - accuracy: 0.9250 - val_loss: 0.4574 - val_accuracy: 0.8537 - rmse: 0.9443\n", "Epoch 19/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1892 - accuracy: 0.9312\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.36482807993888855, Validation Accuracy: 0.8796162009239197, RMSE: 0.8266842102296642\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1892 - accuracy: 0.9312 - val_loss: 0.3648 - val_accuracy: 0.8796 - rmse: 0.8267\n", "Epoch 20/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1822 - accuracy: 0.9333\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.4493047297000885, Validation Accuracy: 0.855182409286499, RMSE: 1.0509846729489565\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1822 - accuracy: 0.9333 - val_loss: 0.4493 - val_accuracy: 0.8552 - rmse: 1.0510\n", "Epoch 21/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1713 - accuracy: 0.9387\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.36555084586143494, Validation Accuracy: 0.8869240283966064, RMSE: 0.807240538267735\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1713 - accuracy: 0.9387 - val_loss: 0.3656 - val_accuracy: 0.8869 - rmse: 0.8072\n", "Epoch 22/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1668 - accuracy: 0.9397\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.39084669947624207, Validation Accuracy: 0.8709137439727783, RMSE: 1.0995254724850783\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1668 - accuracy: 0.9397 - val_loss: 0.3908 - val_accuracy: 0.8709 - rmse: 1.0995\n", "Epoch 23/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1625 - accuracy: 0.9413\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.5451400876045227, Validation Accuracy: 0.8390606045722961, RMSE: 1.173043912446947\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1625 - accuracy: 0.9413 - val_loss: 0.5451 - val_accuracy: 0.8391 - rmse: 1.1730\n", "Epoch 24/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1523 - accuracy: 0.9446\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.29426711797714233, Validation Accuracy: 0.9060024619102478, RMSE: 0.7782214953014284\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1523 - accuracy: 0.9446 - val_loss: 0.2943 - val_accuracy: 0.9060 - rmse: 0.7782\n", "Epoch 25/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1489 - accuracy: 0.9470\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.3321048617362976, Validation Accuracy: 0.8991966843605042, RMSE: 0.7688372241434506\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1489 - accuracy: 0.9470 - val_loss: 0.3321 - val_accuracy: 0.8992 - rmse: 0.7688\n", "Epoch 26/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1481 - accuracy: 0.9466\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.3621409833431244, Validation Accuracy: 0.8907731771469116, RMSE: 0.7442928754285512\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1481 - accuracy: 0.9466 - val_loss: 0.3621 - val_accuracy: 0.8908 - rmse: 0.7443\n", "Epoch 27/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1394 - accuracy: 0.9494\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.40040138363838196, Validation Accuracy: 0.881066620349884, RMSE: 0.9294840745668365\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1394 - accuracy: 0.9494 - val_loss: 0.4004 - val_accuracy: 0.8811 - rmse: 0.9295\n", "Epoch 28/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1340 - accuracy: 0.9516\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.38186749815940857, Validation Accuracy: 0.8868124485015869, RMSE: 0.8983696043390065\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1340 - accuracy: 0.9516 - val_loss: 0.3819 - val_accuracy: 0.8868 - rmse: 0.8984\n", "Epoch 29/100\n", "2238/2241 [============================>.] - ETA: 0s - loss: 0.1311 - accuracy: 0.9526\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.3536430895328522, Validation Accuracy: 0.8959053754806519, RMSE: 0.8536335107654655\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1311 - accuracy: 0.9526 - val_loss: 0.3536 - val_accuracy: 0.8959 - rmse: 0.8536\n", "Train time: 1100.6297512054443\n", "Processing split 3\n", "Beginning of input module\n", "Output of input module tensor: (None, 97, 8, 256)\n", "Beginning of MPRI Upper Half\n", "Shape of bottleneck tensor: (None, 97, 8, 32)\n", "Shape of firstpath tensor: (None, 97, 8, 64)\n", "Shape of secondpath tensor: (None, 97, 8, 32)\n", "Shape of thirdpath tensor: (None, 97, 8, 32)\n", "Shape of fourthpath tensor: (None, 97, 8, 128)\n", "Shape of concatenated tensor: (None, 97, 8, 256)\n", "Shape of added tensor: (None, 97, 8, 256)\n", "Beginning of MPRI Lower Half\n", "First layer - Shape of upper path tensor: (None, 49, 4, 256)\n", "First layer - Shape of lower path tensor: (None, 49, 4, 256)\n", "Second layer - Shape of upper path tensor: (None, 25, 2, 256)\n", "Second layer - Shape of lower path tensor: (None, 25, 2, 256)\n", "Third layer - Shape of upper path tensor: (None, 13, 1, 256)\n", "Third layer - Shape of lower path tensor: (None, 13, 1, 256)\n", "Shape of output tensor: (None, 13, 1, 256)\n", "Beginning of output module\n", "Shape after last convolution: (None, 13, 1, 32)\n", "Final output shape: (None, 3876)\n", "Epoch 1/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 6.1481 - accuracy: 0.0454\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 4.517018795013428, Validation Accuracy: 0.08038603514432907, RMSE: 5.48089217824142\n", "2241/2241 [==============================] - 39s 17ms/step - loss: 6.1481 - accuracy: 0.0454 - val_loss: 4.5170 - val_accuracy: 0.0804 - rmse: 5.4809\n", "Epoch 2/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 2.4576 - accuracy: 0.3920\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 2.566418170928955, Validation Accuracy: 0.3017962872982025, RMSE: 3.870065518586527\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 2.4576 - accuracy: 0.3920 - val_loss: 2.5664 - val_accuracy: 0.3018 - rmse: 3.8701\n", "Epoch 3/100\n", "2239/2241 [============================>.] - ETA: 0s - loss: 1.3193 - accuracy: 0.6208\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 1.6864804029464722, Validation Accuracy: 0.5009483695030212, RMSE: 2.767691563456324\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 1.3193 - accuracy: 0.6207 - val_loss: 1.6865 - val_accuracy: 0.5009 - rmse: 2.7677\n", "Epoch 4/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.8848 - accuracy: 0.7199\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 1.5491911172866821, Validation Accuracy: 0.5277808904647827, RMSE: 2.192966961586786\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.8848 - accuracy: 0.7199 - val_loss: 1.5492 - val_accuracy: 0.5278 - rmse: 2.1930\n", "Epoch 5/100\n", "2239/2241 [============================>.] - ETA: 0s - loss: 0.6759 - accuracy: 0.7721\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.7592572569847107, Validation Accuracy: 0.7593439817428589, RMSE: 1.3536132282216535\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.6759 - accuracy: 0.7721 - val_loss: 0.7593 - val_accuracy: 0.7593 - rmse: 1.3536\n", "Epoch 6/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.5510 - accuracy: 0.8102\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.8601047992706299, Validation Accuracy: 0.7244226336479187, RMSE: 1.7035134427882055\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.5510 - accuracy: 0.8102 - val_loss: 0.8601 - val_accuracy: 0.7244 - rmse: 1.7035\n", "Epoch 7/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.4718 - accuracy: 0.8353\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.6880154013633728, Validation Accuracy: 0.7766373157501221, RMSE: 1.1234594370836386\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.4718 - accuracy: 0.8353 - val_loss: 0.6880 - val_accuracy: 0.7766 - rmse: 1.1235\n", "Epoch 8/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.4142 - accuracy: 0.8529\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.7968271970748901, Validation Accuracy: 0.7383130788803101, RMSE: 1.7503127659175581\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.4142 - accuracy: 0.8529 - val_loss: 0.7968 - val_accuracy: 0.7383 - rmse: 1.7503\n", "Epoch 9/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.3711 - accuracy: 0.8653\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.6279195547103882, Validation Accuracy: 0.7933169603347778, RMSE: 1.2936563699551769\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.3711 - accuracy: 0.8653 - val_loss: 0.6279 - val_accuracy: 0.7933 - rmse: 1.2937\n", "Epoch 10/100\n", "2239/2241 [============================>.] - ETA: 0s - loss: 0.3355 - accuracy: 0.8783\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.521110475063324, Validation Accuracy: 0.8295771479606628, RMSE: 1.0876014099638067\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.3354 - accuracy: 0.8783 - val_loss: 0.5211 - val_accuracy: 0.8296 - rmse: 1.0876\n", "Epoch 11/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.3071 - accuracy: 0.8889\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.5492403507232666, Validation Accuracy: 0.8233850002288818, RMSE: 1.0637718316791087\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.3071 - accuracy: 0.8889 - val_loss: 0.5492 - val_accuracy: 0.8234 - rmse: 1.0638\n", "Epoch 12/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.2890 - accuracy: 0.8962\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.4883067011833191, Validation Accuracy: 0.8467031121253967, RMSE: 0.9954781732837362\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.2890 - accuracy: 0.8962 - val_loss: 0.4883 - val_accuracy: 0.8467 - rmse: 0.9955\n", "Epoch 13/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.2635 - accuracy: 0.9039\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.4304152727127075, Validation Accuracy: 0.8675108551979065, RMSE: 0.9341835329019412\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.2635 - accuracy: 0.9039 - val_loss: 0.4304 - val_accuracy: 0.8675 - rmse: 0.9342\n", "Epoch 14/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.2500 - accuracy: 0.9089\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.394212931394577, Validation Accuracy: 0.8791141510009766, RMSE: 0.92069503552069\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.2500 - accuracy: 0.9089 - val_loss: 0.3942 - val_accuracy: 0.8791 - rmse: 0.9207\n", "Epoch 15/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.2320 - accuracy: 0.9154\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.7585613131523132, Validation Accuracy: 0.7793707251548767, RMSE: 1.407387745269971\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.2320 - accuracy: 0.9154 - val_loss: 0.7586 - val_accuracy: 0.7794 - rmse: 1.4074\n", "Epoch 16/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.2220 - accuracy: 0.9204\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.4489718973636627, Validation Accuracy: 0.8654468655586243, RMSE: 1.1032419035539973\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.2220 - accuracy: 0.9204 - val_loss: 0.4490 - val_accuracy: 0.8654 - rmse: 1.1032\n", "Epoch 17/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.2079 - accuracy: 0.9249\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.3903518319129944, Validation Accuracy: 0.8783331513404846, RMSE: 0.8762393400269256\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.2079 - accuracy: 0.9249 - val_loss: 0.3904 - val_accuracy: 0.8783 - rmse: 0.8762\n", "Epoch 18/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1990 - accuracy: 0.9280\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.4852226674556732, Validation Accuracy: 0.851835310459137, RMSE: 1.2120911269970756\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1990 - accuracy: 0.9280 - val_loss: 0.4852 - val_accuracy: 0.8518 - rmse: 1.2121\n", "Epoch 19/100\n", "2238/2241 [============================>.] - ETA: 0s - loss: 0.1937 - accuracy: 0.9307\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.40250080823898315, Validation Accuracy: 0.8823496699333191, RMSE: 0.8835935017254373\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1939 - accuracy: 0.9306 - val_loss: 0.4025 - val_accuracy: 0.8823 - rmse: 0.8836\n", "Epoch 20/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1750 - accuracy: 0.9353\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.4608312249183655, Validation Accuracy: 0.860091507434845, RMSE: 1.00376536443495\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1750 - accuracy: 0.9353 - val_loss: 0.4608 - val_accuracy: 0.8601 - rmse: 1.0038\n", "Epoch 21/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1738 - accuracy: 0.9374\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.37540528178215027, Validation Accuracy: 0.8922793865203857, RMSE: 0.8731302044365228\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1738 - accuracy: 0.9374 - val_loss: 0.3754 - val_accuracy: 0.8923 - rmse: 0.8731\n", "Epoch 22/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1655 - accuracy: 0.9393\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.3858219385147095, Validation Accuracy: 0.8863661885261536, RMSE: 0.8448229832536415\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1655 - accuracy: 0.9393 - val_loss: 0.3858 - val_accuracy: 0.8864 - rmse: 0.8448\n", "Epoch 23/100\n", "2240/2241 [============================>.] - ETA: 0s - loss: 0.1582 - accuracy: 0.9433\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.3576662540435791, Validation Accuracy: 0.8941202759742737, RMSE: 0.8899942146489881\n", "2241/2241 [==============================] - 39s 17ms/step - loss: 0.1582 - accuracy: 0.9433 - val_loss: 0.3577 - val_accuracy: 0.8941 - rmse: 0.8900\n", "Epoch 24/100\n", "2240/2241 [============================>.] - ETA: 0s - loss: 0.1566 - accuracy: 0.9443\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.37157943844795227, Validation Accuracy: 0.893283486366272, RMSE: 0.9240893910884935\n", "2241/2241 [==============================] - 39s 17ms/step - loss: 0.1567 - accuracy: 0.9443 - val_loss: 0.3716 - val_accuracy: 0.8933 - rmse: 0.9241\n", "Epoch 25/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1535 - accuracy: 0.9445\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.41348281502723694, Validation Accuracy: 0.8815128803253174, RMSE: 0.8878919496467246\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1535 - accuracy: 0.9445 - val_loss: 0.4135 - val_accuracy: 0.8815 - rmse: 0.8879\n", "Epoch 26/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1411 - accuracy: 0.9495\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.36702394485473633, Validation Accuracy: 0.8957380056381226, RMSE: 0.9119818653252584\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1411 - accuracy: 0.9495 - val_loss: 0.3670 - val_accuracy: 0.8957 - rmse: 0.9120\n", "Epoch 27/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1374 - accuracy: 0.9500\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.40425974130630493, Validation Accuracy: 0.8847483992576599, RMSE: 1.053416843774458\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1374 - accuracy: 0.9500 - val_loss: 0.4043 - val_accuracy: 0.8847 - rmse: 1.0534\n", "Epoch 28/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1363 - accuracy: 0.9507\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.3604566752910614, Validation Accuracy: 0.8991966843605042, RMSE: 0.814472872765462\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1363 - accuracy: 0.9507 - val_loss: 0.3605 - val_accuracy: 0.8992 - rmse: 0.8145\n", "Train time: 1067.125724554062\n", "Processing split 4\n", "Beginning of input module\n", "Output of input module tensor: (None, 97, 8, 256)\n", "Beginning of MPRI Upper Half\n", "Shape of bottleneck tensor: (None, 97, 8, 32)\n", "Shape of firstpath tensor: (None, 97, 8, 64)\n", "Shape of secondpath tensor: (None, 97, 8, 32)\n", "Shape of thirdpath tensor: (None, 97, 8, 32)\n", "Shape of fourthpath tensor: (None, 97, 8, 128)\n", "Shape of concatenated tensor: (None, 97, 8, 256)\n", "Shape of added tensor: (None, 97, 8, 256)\n", "Beginning of MPRI Lower Half\n", "First layer - Shape of upper path tensor: (None, 49, 4, 256)\n", "First layer - Shape of lower path tensor: (None, 49, 4, 256)\n", "Second layer - Shape of upper path tensor: (None, 25, 2, 256)\n", "Second layer - Shape of lower path tensor: (None, 25, 2, 256)\n", "Third layer - Shape of upper path tensor: (None, 13, 1, 256)\n", "Third layer - Shape of lower path tensor: (None, 13, 1, 256)\n", "Shape of output tensor: (None, 13, 1, 256)\n", "Beginning of output module\n", "Shape after last convolution: (None, 13, 1, 32)\n", "Final output shape: (None, 3876)\n", "Epoch 1/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 5.9792 - accuracy: 0.0512\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 4.16703462600708, Validation Accuracy: 0.1164853572845459, RMSE: 5.505660837286485\n", "2241/2241 [==============================] - 39s 17ms/step - loss: 5.9792 - accuracy: 0.0512 - val_loss: 4.1670 - val_accuracy: 0.1165 - rmse: 5.5057\n", "Epoch 2/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 2.4049 - accuracy: 0.4021\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 2.0991501808166504, Validation Accuracy: 0.4004462957382202, RMSE: 3.064692985640537\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 2.4049 - accuracy: 0.4021 - val_loss: 2.0992 - val_accuracy: 0.4004 - rmse: 3.0647\n", "Epoch 3/100\n", "2240/2241 [============================>.] - ETA: 0s - loss: 1.2820 - accuracy: 0.6337\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 1.7328152656555176, Validation Accuracy: 0.48167362809181213, RMSE: 3.0239421193902825\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 1.2820 - accuracy: 0.6337 - val_loss: 1.7328 - val_accuracy: 0.4817 - rmse: 3.0239\n", "Epoch 4/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.8561 - accuracy: 0.7278\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 1.3925352096557617, Validation Accuracy: 0.5750069618225098, RMSE: 2.048014992971619\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.8561 - accuracy: 0.7278 - val_loss: 1.3925 - val_accuracy: 0.5750 - rmse: 2.0480\n", "Epoch 5/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.6489 - accuracy: 0.7845\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 1.261181354522705, Validation Accuracy: 0.6158995628356934, RMSE: 1.6491069389082593\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.6489 - accuracy: 0.7845 - val_loss: 1.2612 - val_accuracy: 0.6159 - rmse: 1.6491\n", "Epoch 6/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.5375 - accuracy: 0.8138\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.6247873902320862, Validation Accuracy: 0.7866109013557434, RMSE: 1.2197011317396917\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.5375 - accuracy: 0.8138 - val_loss: 0.6248 - val_accuracy: 0.7866 - rmse: 1.2197\n", "Epoch 7/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.4585 - accuracy: 0.8381\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.7623724937438965, Validation Accuracy: 0.7424825429916382, RMSE: 1.4103621304162042\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.4585 - accuracy: 0.8381 - val_loss: 0.7624 - val_accuracy: 0.7425 - rmse: 1.4104\n", "Epoch 8/100\n", "2240/2241 [============================>.] - ETA: 0s - loss: 0.4052 - accuracy: 0.8557\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.48980680108070374, Validation Accuracy: 0.8326917886734009, RMSE: 0.9677073223065671\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.4052 - accuracy: 0.8557 - val_loss: 0.4898 - val_accuracy: 0.8327 - rmse: 0.9677\n", "Epoch 9/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.3600 - accuracy: 0.8696\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.6002155542373657, Validation Accuracy: 0.7941422462463379, RMSE: 1.160273377107064\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.3600 - accuracy: 0.8696 - val_loss: 0.6002 - val_accuracy: 0.7941 - rmse: 1.1603\n", "Epoch 10/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.3333 - accuracy: 0.8787\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.8492421507835388, Validation Accuracy: 0.7289818525314331, RMSE: 1.4903221034749492\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.3333 - accuracy: 0.8787 - val_loss: 0.8492 - val_accuracy: 0.7290 - rmse: 1.4903\n", "Epoch 11/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.3034 - accuracy: 0.8911\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.65221107006073, Validation Accuracy: 0.7792468667030334, RMSE: 1.262637650090232\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.3034 - accuracy: 0.8911 - val_loss: 0.6522 - val_accuracy: 0.7792 - rmse: 1.2626\n", "Epoch 12/100\n", "2240/2241 [============================>.] - ETA: 0s - loss: 0.2788 - accuracy: 0.8992\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.44994744658470154, Validation Accuracy: 0.8511576056480408, RMSE: 0.9512452383516831\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.2787 - accuracy: 0.8992 - val_loss: 0.4499 - val_accuracy: 0.8512 - rmse: 0.9512\n", "Epoch 13/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.2668 - accuracy: 0.9026\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.523965060710907, Validation Accuracy: 0.8195815682411194, RMSE: 1.0281464276592647\n", "2241/2241 [==============================] - 39s 18ms/step - loss: 0.2668 - accuracy: 0.9026 - val_loss: 0.5240 - val_accuracy: 0.8196 - rmse: 1.0281\n", "Epoch 14/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.2466 - accuracy: 0.9105\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.6943150758743286, Validation Accuracy: 0.7802510261535645, RMSE: 1.223046930246088\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.2466 - accuracy: 0.9105 - val_loss: 0.6943 - val_accuracy: 0.7803 - rmse: 1.2230\n", "Epoch 15/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.2330 - accuracy: 0.9153\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.5634047389030457, Validation Accuracy: 0.8177963495254517, RMSE: 1.0278275962645471\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.2330 - accuracy: 0.9153 - val_loss: 0.5634 - val_accuracy: 0.8178 - rmse: 1.0278\n", "Epoch 16/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.2173 - accuracy: 0.9215\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.4609896242618561, Validation Accuracy: 0.8456903696060181, RMSE: 0.9543923271309716\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.2173 - accuracy: 0.9215 - val_loss: 0.4610 - val_accuracy: 0.8457 - rmse: 0.9544\n", "Epoch 17/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.2081 - accuracy: 0.9245\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.33442223072052, Validation Accuracy: 0.8889818787574768, RMSE: 0.7226751789192303\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.2081 - accuracy: 0.9245 - val_loss: 0.3344 - val_accuracy: 0.8890 - rmse: 0.7227\n", "Epoch 18/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.2009 - accuracy: 0.9271\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.5352727174758911, Validation Accuracy: 0.8312970995903015, RMSE: 1.046699001890171\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.2009 - accuracy: 0.9271 - val_loss: 0.5353 - val_accuracy: 0.8313 - rmse: 1.0467\n", "Epoch 19/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1880 - accuracy: 0.9315\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.4459116756916046, Validation Accuracy: 0.8589121103286743, RMSE: 0.9119231903841412\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1880 - accuracy: 0.9315 - val_loss: 0.4459 - val_accuracy: 0.8589 - rmse: 0.9119\n", "Epoch 20/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1797 - accuracy: 0.9369\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.3902686834335327, Validation Accuracy: 0.8732496500015259, RMSE: 0.8385831844976238\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1797 - accuracy: 0.9369 - val_loss: 0.3903 - val_accuracy: 0.8732 - rmse: 0.8386\n", "Epoch 21/100\n", "2240/2241 [============================>.] - ETA: 0s - loss: 0.1729 - accuracy: 0.9381\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.3721155524253845, Validation Accuracy: 0.8747559189796448, RMSE: 0.899643506026606\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1730 - accuracy: 0.9381 - val_loss: 0.3721 - val_accuracy: 0.8748 - rmse: 0.8996\n", "Epoch 22/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1678 - accuracy: 0.9388\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.297441691160202, Validation Accuracy: 0.9042677879333496, RMSE: 0.7703356510909394\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1678 - accuracy: 0.9388 - val_loss: 0.2974 - val_accuracy: 0.9043 - rmse: 0.7703\n", "Epoch 23/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1562 - accuracy: 0.9439\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.4325045049190521, Validation Accuracy: 0.8629288673400879, RMSE: 0.9723944192327981\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1562 - accuracy: 0.9439 - val_loss: 0.4325 - val_accuracy: 0.8629 - rmse: 0.9724\n", "Epoch 24/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1540 - accuracy: 0.9445\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.29411038756370544, Validation Accuracy: 0.9061087965965271, RMSE: 0.8392066386932233\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1540 - accuracy: 0.9445 - val_loss: 0.2941 - val_accuracy: 0.9061 - rmse: 0.8392\n", "Epoch 25/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1475 - accuracy: 0.9465\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.4230336546897888, Validation Accuracy: 0.8676708340644836, RMSE: 0.9111887801254465\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1475 - accuracy: 0.9465 - val_loss: 0.4230 - val_accuracy: 0.8677 - rmse: 0.9112\n", "Epoch 26/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1441 - accuracy: 0.9484\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.3573693633079529, Validation Accuracy: 0.8876429796218872, RMSE: 0.8803923304465263\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1441 - accuracy: 0.9484 - val_loss: 0.3574 - val_accuracy: 0.8876 - rmse: 0.8804\n", "Epoch 27/100\n", "2240/2241 [============================>.] - ETA: 0s - loss: 0.1368 - accuracy: 0.9506\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.3359350562095642, Validation Accuracy: 0.8943933248519897, RMSE: 0.7132536875986567\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1369 - accuracy: 0.9505 - val_loss: 0.3359 - val_accuracy: 0.8944 - rmse: 0.7133\n", "Epoch 28/100\n", "2238/2241 [============================>.] - ETA: 0s - loss: 0.1334 - accuracy: 0.9524\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.40298348665237427, Validation Accuracy: 0.8784937262535095, RMSE: 1.0182299296716921\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1334 - accuracy: 0.9524 - val_loss: 0.4030 - val_accuracy: 0.8785 - rmse: 1.0182\n", "Epoch 29/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1283 - accuracy: 0.9530\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.32056576013565063, Validation Accuracy: 0.9078940153121948, RMSE: 0.6986238714841709\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1283 - accuracy: 0.9530 - val_loss: 0.3206 - val_accuracy: 0.9079 - rmse: 0.6986\n", "Train time: 1099.2112460136414\n", "Processing split 5\n", "Beginning of input module\n", "Output of input module tensor: (None, 97, 8, 256)\n", "Beginning of MPRI Upper Half\n", "Shape of bottleneck tensor: (None, 97, 8, 32)\n", "Shape of firstpath tensor: (None, 97, 8, 64)\n", "Shape of secondpath tensor: (None, 97, 8, 32)\n", "Shape of thirdpath tensor: (None, 97, 8, 32)\n", "Shape of fourthpath tensor: (None, 97, 8, 128)\n", "Shape of concatenated tensor: (None, 97, 8, 256)\n", "Shape of added tensor: (None, 97, 8, 256)\n", "Beginning of MPRI Lower Half\n", "First layer - Shape of upper path tensor: (None, 49, 4, 256)\n", "First layer - Shape of lower path tensor: (None, 49, 4, 256)\n", "Second layer - Shape of upper path tensor: (None, 25, 2, 256)\n", "Second layer - Shape of lower path tensor: (None, 25, 2, 256)\n", "Third layer - Shape of upper path tensor: (None, 13, 1, 256)\n", "Third layer - Shape of lower path tensor: (None, 13, 1, 256)\n", "Shape of output tensor: (None, 13, 1, 256)\n", "Beginning of output module\n", "Shape after last convolution: (None, 13, 1, 32)\n", "Final output shape: (None, 3876)\n", "Epoch 1/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 5.9835 - accuracy: 0.0531\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 4.267172813415527, Validation Accuracy: 0.1015341728925705, RMSE: 5.970334613527917\n", "2241/2241 [==============================] - 39s 17ms/step - loss: 5.9835 - accuracy: 0.0531 - val_loss: 4.2672 - val_accuracy: 0.1015 - rmse: 5.9703\n", "Epoch 2/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 2.3550 - accuracy: 0.4110\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 2.266324996948242, Validation Accuracy: 0.38343095779418945, RMSE: 3.2115486342891435\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 2.3550 - accuracy: 0.4110 - val_loss: 2.2663 - val_accuracy: 0.3834 - rmse: 3.2115\n", "Epoch 3/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 1.2642 - accuracy: 0.6363\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 1.2427875995635986, Validation Accuracy: 0.628730833530426, RMSE: 1.830925260142732\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 1.2642 - accuracy: 0.6363 - val_loss: 1.2428 - val_accuracy: 0.6287 - rmse: 1.8309\n", "Epoch 4/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.8561 - accuracy: 0.7272\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 1.2518360614776611, Validation Accuracy: 0.6005578637123108, RMSE: 1.8544755317630777\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.8561 - accuracy: 0.7272 - val_loss: 1.2518 - val_accuracy: 0.6006 - rmse: 1.8545\n", "Epoch 5/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.6546 - accuracy: 0.7810\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.953096866607666, Validation Accuracy: 0.6770432591438293, RMSE: 1.8927152427761285\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.6546 - accuracy: 0.7810 - val_loss: 0.9531 - val_accuracy: 0.6770 - rmse: 1.8927\n", "Epoch 6/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.5374 - accuracy: 0.8140\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.6132417321205139, Validation Accuracy: 0.7942538261413574, RMSE: 1.273898463097326\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.5374 - accuracy: 0.8140 - val_loss: 0.6132 - val_accuracy: 0.7943 - rmse: 1.2739\n", "Epoch 7/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.4606 - accuracy: 0.8387\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.6927633881568909, Validation Accuracy: 0.7702649831771851, RMSE: 1.4428054571454734\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.4606 - accuracy: 0.8387 - val_loss: 0.6928 - val_accuracy: 0.7703 - rmse: 1.4428\n", "Epoch 8/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.4118 - accuracy: 0.8534\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.7063499689102173, Validation Accuracy: 0.7576569318771362, RMSE: 1.3105462146575138\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.4118 - accuracy: 0.8534 - val_loss: 0.7063 - val_accuracy: 0.7577 - rmse: 1.3105\n", "Epoch 9/100\n", "2240/2241 [============================>.] - ETA: 0s - loss: 0.3676 - accuracy: 0.8680\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.6927355527877808, Validation Accuracy: 0.7635146379470825, RMSE: 1.5296921778566483\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.3676 - accuracy: 0.8680 - val_loss: 0.6927 - val_accuracy: 0.7635 - rmse: 1.5297\n", "Epoch 10/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.3278 - accuracy: 0.8816\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.6134011745452881, Validation Accuracy: 0.7923570275306702, RMSE: 1.2989374479642388\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.3278 - accuracy: 0.8816 - val_loss: 0.6134 - val_accuracy: 0.7924 - rmse: 1.2989\n", "Epoch 11/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.3067 - accuracy: 0.8898\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.5402296781539917, Validation Accuracy: 0.8152859210968018, RMSE: 1.0828445415918344\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.3067 - accuracy: 0.8898 - val_loss: 0.5402 - val_accuracy: 0.8153 - rmse: 1.0828\n", "Epoch 12/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.2841 - accuracy: 0.8968\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.9616469740867615, Validation Accuracy: 0.7168200612068176, RMSE: 1.563183462794516\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.2841 - accuracy: 0.8968 - val_loss: 0.9616 - val_accuracy: 0.7168 - rmse: 1.5632\n", "Epoch 13/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.2650 - accuracy: 0.9029\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.4135837256908417, Validation Accuracy: 0.8599721193313599, RMSE: 0.9150835829244214\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.2650 - accuracy: 0.9029 - val_loss: 0.4136 - val_accuracy: 0.8600 - rmse: 0.9151\n", "Epoch 14/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.2479 - accuracy: 0.9111\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.4907168447971344, Validation Accuracy: 0.831464409828186, RMSE: 1.0639588188463107\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.2479 - accuracy: 0.9111 - val_loss: 0.4907 - val_accuracy: 0.8315 - rmse: 1.0640\n", "Epoch 15/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.2327 - accuracy: 0.9155\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.3761048913002014, Validation Accuracy: 0.8776010870933533, RMSE: 0.8141016559841594\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.2327 - accuracy: 0.9155 - val_loss: 0.3761 - val_accuracy: 0.8776 - rmse: 0.8141\n", "Epoch 16/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.2210 - accuracy: 0.9188\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.4162362813949585, Validation Accuracy: 0.8632078170776367, RMSE: 1.0319241752603485\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.2210 - accuracy: 0.9188 - val_loss: 0.4162 - val_accuracy: 0.8632 - rmse: 1.0319\n", "Epoch 17/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.2126 - accuracy: 0.9232\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.3618636131286621, Validation Accuracy: 0.8790516257286072, RMSE: 0.9155330888723158\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.2126 - accuracy: 0.9232 - val_loss: 0.3619 - val_accuracy: 0.8791 - rmse: 0.9155\n", "Epoch 18/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.2001 - accuracy: 0.9285\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.3659767508506775, Validation Accuracy: 0.8825662732124329, RMSE: 0.8505884418154703\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.2001 - accuracy: 0.9285 - val_loss: 0.3660 - val_accuracy: 0.8826 - rmse: 0.8506\n", "Epoch 19/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1898 - accuracy: 0.9315\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.4088616669178009, Validation Accuracy: 0.8677266240119934, RMSE: 0.8850454447826642\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1898 - accuracy: 0.9315 - val_loss: 0.4089 - val_accuracy: 0.8677 - rmse: 0.8850\n", "Epoch 20/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1832 - accuracy: 0.9337\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.3548184037208557, Validation Accuracy: 0.885020911693573, RMSE: 0.8513095990723958\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1832 - accuracy: 0.9337 - val_loss: 0.3548 - val_accuracy: 0.8850 - rmse: 0.8513\n", "Epoch 21/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1725 - accuracy: 0.9361\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.30373114347457886, Validation Accuracy: 0.9012552499771118, RMSE: 0.7636621742092452\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1725 - accuracy: 0.9361 - val_loss: 0.3037 - val_accuracy: 0.9013 - rmse: 0.7637\n", "Epoch 22/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1673 - accuracy: 0.9394\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.41608497500419617, Validation Accuracy: 0.8639888167381287, RMSE: 0.9173973251333281\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1673 - accuracy: 0.9394 - val_loss: 0.4161 - val_accuracy: 0.8640 - rmse: 0.9174\n", "Epoch 23/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1590 - accuracy: 0.9425\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.28158169984817505, Validation Accuracy: 0.9079498052597046, RMSE: 0.7013933134805734\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1590 - accuracy: 0.9425 - val_loss: 0.2816 - val_accuracy: 0.9079 - rmse: 0.7014\n", "Epoch 24/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1566 - accuracy: 0.9436\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.32350409030914307, Validation Accuracy: 0.8973500728607178, RMSE: 0.8062344243465387\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1566 - accuracy: 0.9436 - val_loss: 0.3235 - val_accuracy: 0.8974 - rmse: 0.8062\n", "Epoch 25/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1494 - accuracy: 0.9458\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.3767699599266052, Validation Accuracy: 0.8799442052841187, RMSE: 0.8996667599394944\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1494 - accuracy: 0.9458 - val_loss: 0.3768 - val_accuracy: 0.8799 - rmse: 0.8997\n", "Epoch 26/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1481 - accuracy: 0.9460\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.46325695514678955, Validation Accuracy: 0.8640446066856384, RMSE: 0.8869894862257585\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1481 - accuracy: 0.9460 - val_loss: 0.4633 - val_accuracy: 0.8640 - rmse: 0.8870\n", "Epoch 27/100\n", "2239/2241 [============================>.] - ETA: 0s - loss: 0.1360 - accuracy: 0.9509\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.26549655199050903, Validation Accuracy: 0.9185495376586914, RMSE: 0.6613033915928586\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1360 - accuracy: 0.9509 - val_loss: 0.2655 - val_accuracy: 0.9185 - rmse: 0.6613\n", "Epoch 28/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1391 - accuracy: 0.9489\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.2638327479362488, Validation Accuracy: 0.9181032180786133, RMSE: 0.6543812220880718\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1391 - accuracy: 0.9489 - val_loss: 0.2638 - val_accuracy: 0.9181 - rmse: 0.6544\n", "Epoch 29/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1342 - accuracy: 0.9519\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.29234248399734497, Validation Accuracy: 0.9100697636604309, RMSE: 0.6776672597398309\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1342 - accuracy: 0.9519 - val_loss: 0.2923 - val_accuracy: 0.9101 - rmse: 0.6777\n", "Epoch 30/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1243 - accuracy: 0.9554\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.3319930136203766, Validation Accuracy: 0.899023711681366, RMSE: 0.7650945010500072\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1243 - accuracy: 0.9554 - val_loss: 0.3320 - val_accuracy: 0.8990 - rmse: 0.7651\n", "Epoch 31/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1232 - accuracy: 0.9548\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.27084726095199585, Validation Accuracy: 0.9200558066368103, RMSE: 0.649396243576715\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1232 - accuracy: 0.9548 - val_loss: 0.2708 - val_accuracy: 0.9201 - rmse: 0.6494\n", "Epoch 32/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1221 - accuracy: 0.9565\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.30021023750305176, Validation Accuracy: 0.9078382253646851, RMSE: 0.7643467416183536\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1221 - accuracy: 0.9565 - val_loss: 0.3002 - val_accuracy: 0.9078 - rmse: 0.7643\n", "Epoch 33/100\n", "2241/2241 [==============================] - ETA: 0s - loss: 0.1199 - accuracy: 0.9573\n", "\n", "Epoch End - Custom Validation Callback\n", "Validation Loss: 0.2945549190044403, Validation Accuracy: 0.9136959314346313, RMSE: 0.7633699052709382\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.1199 - accuracy: 0.9573 - val_loss: 0.2946 - val_accuracy: 0.9137 - rmse: 0.7634\n", "Train time: 1246.717026233673\n", "All splits processed.\n"]}], "source": ["from sklearn.model_selection import KFold\n", "from sklearn.model_selection import train_test_split\n", "import time\n", "\n", "# Split the data using K Fold algorithm\n", "inputs = np.concatenate((X_train, X_test), axis = 0)\n", "targets = np.concatenate((y_train, y_test), axis = 0)\n", "kfold = KFold(n_splits = 5, shuffle = True)\n", "\n", "# For each of the train, test\n", "split_no = 1\n", "results = {}\n", "\n", "# Perform K Fold Validation\n", "for train, val in kfold.split(inputs, targets):\n", "    \n", "    # Make model\n", "    print(f'Processing split {split_no}')\n", "    \n", "    split_results = {}\n", "    \n", "    model_input = Input(shape = (193,16,1))\n", "    model_output = output_module(mpri_lowerhalf(mpri_upperhalf(input_module(model_input))), num_classes = 3876)\n", "    mpri_model = Model(model_input, model_output)\n", "\n", "    mpri_model.compile(optimizer = 'adam',\n", "                      loss = tf.keras.losses.SparseCategoricalCrossentropy(),\n", "                      metrics = ['accuracy'])\n", "\n", "    # print(mpri_model.summary())\n", "    \n", "    # Get the metrics\n", "    val_callback = ValidationCallback(inputs[val], targets[val])\n", "    start_time = time.time()\n", "    hist = mpri_model.fit(inputs[train], targets[train], batch_size = 32,\n", "                              callbacks = [val_callback, tf.keras.callbacks.EarlyStopping('val_loss', patience = 5)],\n", "                              epochs = 100)\n", "    end_time = time.time()\n", "    epoch_trgtime = end_time - start_time\n", "    \n", "    split_results['loss'] = hist.history['loss']\n", "    split_results['accuracy'] = hist.history['accuracy']\n", "    split_results['val_loss'] = hist.history['val_loss']\n", "    split_results['val_accuracy'] = hist.history['val_accuracy']\n", "    split_results['rmse'] = hist.history['rmse']\n", "    split_results['train_time'] = epoch_trgtime\n", "    \n", "    print(f'Train time: {epoch_trgtime}')\n", "    \n", "    cur_split = 'split_' + str(split_no)\n", "    results[cur_split] = split_results\n", "\n", "    split_no += 1\n", "\n", "print('All splits processed.')"]}, {"cell_type": "code", "execution_count": 110, "id": "ba225c72", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Figure size 1440x720 with 0 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 1440x720 with 3 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.rcParams['figure.figsize'] = [20, 10]\n", "figure = plt.figure(figsize=(20,10))\n", "fig, axs = plt.subplots(1,3, constrained_layout = True)\n", "\n", "axs[0].plot(results['split_1']['loss'], color='teal', label='split1_loss')\n", "axs[0].plot(results['split_1']['val_loss'], color='orange', label='split1_val_loss')\n", "axs[0].legend(loc='upper left')\n", "\n", "axs[1].plot(results['split_3']['loss'], color='teal', label='split3_loss')\n", "axs[1].plot(results['split_3']['val_loss'], color='orange', label='split3_val_loss')\n", "axs[1].legend(loc='upper left')\n", "\n", "axs[2].plot(results['split_5']['loss'], color='teal', label='split5_loss')\n", "axs[2].plot(results['split_5']['val_loss'], color='orange', label='split5_val_loss')\n", "axs[2].legend(loc='upper left')\n", "\n", "fig.suptitle('Loss', fontsize=20)\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 111, "id": "f4533b5d", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1440x720 with 3 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["fig, axs = plt.subplots(1,3, constrained_layout = True)\n", "\n", "axs[0].plot(results['split_1']['accuracy'], color='teal', label='split1_accuracy')\n", "axs[0].plot(results['split_1']['val_accuracy'], color='orange', label='split1_val_accuracy')\n", "axs[0].legend(loc='upper left')\n", "\n", "axs[1].plot(results['split_3']['accuracy'], color='teal', label='split3_accuracy')\n", "axs[1].plot(results['split_3']['val_accuracy'], color='orange', label='split3_val_accuracy')\n", "axs[1].legend(loc='upper left')\n", "\n", "axs[2].plot(results['split_5']['accuracy'], color='teal', label='split5_accuracy')\n", "axs[2].plot(results['split_5']['val_accuracy'], color='orange', label='split5_val_accuracy')\n", "axs[2].legend(loc='upper left')\n", "\n", "fig.suptitle('Accuracy', fontsize=20)\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 112, "id": "dc8b67ab", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1440x720 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.plot(results['split_1']['rmse'], color='teal', label='split1_rmse')\n", "plt.plot(results['split_3']['rmse'], color='orange', label='split3_rmse')\n", "plt.plot(results['split_5']['rmse'], color='red', label='split5_rmse')\n", "fig.suptitle('RMSE', fontsize=20)\n", "plt.legend(loc='upper left')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "590c02ff", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "d016ea51", "metadata": {}, "source": ["## With tuned hyper paramters,\n", "- Find out time taken for 100 epochs\n", "- Test out model to get RMSE and CDF for distance error"]}, {"cell_type": "code", "execution_count": null, "id": "a45850b3", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 5}