{"trial_id": "33", "hyperparameters": {"space": [{"class_name": "Choice", "config": {"name": "mpriupperhalf_filterno", "default": 32, "conditions": [], "values": [32, 64, 128], "ordered": true}}, {"class_name": "Choice", "config": {"name": "output_convno", "default": 32, "conditions": [], "values": [32, 64, 128], "ordered": true}}, {"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "learning_rate", "default": 0.01, "conditions": [], "values": [0.01, 0.001, 0.0001], "ordered": true}}, {"class_name": "Boolean", "config": {"name": "pooling_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "l2_conv2d", "default": 0.0, "conditions": [], "values": [0.0, 0.01], "ordered": true}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"mpriupperhalf_filterno": 64, "output_convno": 64, "fc_dropout": false, "learning_rate": 0.001, "pooling_dropout": true, "l2_conv2d": 0.01, "batch_size": 16}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [7.301570415496826], "step": 0}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.007559063844382763], "step": 0}]}, "val_loss": {"direction": "min", "observations": [{"value": [7.416769027709961], "step": 0}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.004685930907726288], "step": 0}]}}}, "score": 7.416769027709961, "best_step": 0, "status": "COMPLETED", "message": null}