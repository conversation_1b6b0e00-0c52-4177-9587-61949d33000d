{"trial_id": "26", "hyperparameters": {"space": [{"class_name": "Choice", "config": {"name": "mpriupperhalf_filterno", "default": 32, "conditions": [], "values": [32, 64, 128], "ordered": true}}, {"class_name": "Choice", "config": {"name": "output_convno", "default": 32, "conditions": [], "values": [32, 64, 128], "ordered": true}}, {"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "learning_rate", "default": 0.01, "conditions": [], "values": [0.01, 0.001, 0.0001], "ordered": true}}, {"class_name": "Boolean", "config": {"name": "pooling_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "l2_conv2d", "default": 0.0, "conditions": [], "values": [0.0, 0.01], "ordered": true}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"mpriupperhalf_filterno": 128, "output_convno": 64, "fc_dropout": true, "learning_rate": 0.01, "pooling_dropout": true, "l2_conv2d": 0.0, "batch_size": 16}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [6.065019607543945], "step": 19}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.020794399082660675], "step": 19}]}, "val_loss": {"direction": "min", "observations": [{"value": [5.129561424255371], "step": 19}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.08574137836694717], "step": 19}]}}}, "score": 5.129561424255371, "best_step": 19, "status": "COMPLETED", "message": null}