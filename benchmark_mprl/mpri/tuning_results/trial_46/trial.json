{"trial_id": "46", "hyperparameters": {"space": [{"class_name": "Choice", "config": {"name": "mpriupperhalf_filterno", "default": 32, "conditions": [], "values": [32, 64, 128], "ordered": true}}, {"class_name": "Choice", "config": {"name": "output_convno", "default": 32, "conditions": [], "values": [32, 64, 128], "ordered": true}}, {"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "learning_rate", "default": 0.01, "conditions": [], "values": [0.01, 0.001, 0.0001], "ordered": true}}, {"class_name": "Boolean", "config": {"name": "pooling_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "l2_conv2d", "default": 0.0, "conditions": [], "values": [0.0, 0.01], "ordered": true}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"mpriupperhalf_filterno": 128, "output_convno": 32, "fc_dropout": true, "learning_rate": 0.0001, "pooling_dropout": true, "l2_conv2d": 0.01, "batch_size": 16}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [3.6953048706054688], "step": 29}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.18918579816818237], "step": 29}]}, "val_loss": {"direction": "min", "observations": [{"value": [3.4062905311584473], "step": 29}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.3583063781261444], "step": 29}]}}}, "score": 3.4062905311584473, "best_step": 29, "status": "COMPLETED", "message": null}