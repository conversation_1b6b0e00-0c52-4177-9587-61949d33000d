{"trial_id": "49", "hyperparameters": {"space": [{"class_name": "Choice", "config": {"name": "mpriupperhalf_filterno", "default": 32, "conditions": [], "values": [32, 64, 128], "ordered": true}}, {"class_name": "Choice", "config": {"name": "output_convno", "default": 32, "conditions": [], "values": [32, 64, 128], "ordered": true}}, {"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "learning_rate", "default": 0.01, "conditions": [], "values": [0.01, 0.001, 0.0001], "ordered": true}}, {"class_name": "Boolean", "config": {"name": "pooling_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "l2_conv2d", "default": 0.0, "conditions": [], "values": [0.0, 0.01], "ordered": true}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"mpriupperhalf_filterno": 64, "output_convno": 128, "fc_dropout": true, "learning_rate": 0.0001, "pooling_dropout": false, "l2_conv2d": 0.0, "batch_size": 32}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [0.20739905536174774], "step": 67}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.9269616007804871], "step": 67}]}, "val_loss": {"direction": "min", "observations": [{"value": [0.17775847017765045], "step": 67}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.9591096639633179], "step": 67}]}}}, "score": 0.17775847017765045, "best_step": 67, "status": "COMPLETED", "message": null}