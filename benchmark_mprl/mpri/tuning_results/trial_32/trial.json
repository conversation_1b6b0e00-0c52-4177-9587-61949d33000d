{"trial_id": "32", "hyperparameters": {"space": [{"class_name": "Choice", "config": {"name": "mpriupperhalf_filterno", "default": 32, "conditions": [], "values": [32, 64, 128], "ordered": true}}, {"class_name": "Choice", "config": {"name": "output_convno", "default": 32, "conditions": [], "values": [32, 64, 128], "ordered": true}}, {"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "learning_rate", "default": 0.01, "conditions": [], "values": [0.01, 0.001, 0.0001], "ordered": true}}, {"class_name": "Boolean", "config": {"name": "pooling_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "l2_conv2d", "default": 0.0, "conditions": [], "values": [0.0, 0.01], "ordered": true}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"mpriupperhalf_filterno": 64, "output_convno": 128, "fc_dropout": false, "learning_rate": 0.0001, "pooling_dropout": true, "l2_conv2d": 0.01, "batch_size": 32}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [0.6806750893592834], "step": 18}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.8632116317749023], "step": 18}]}, "val_loss": {"direction": "min", "observations": [{"value": [2.812196731567383], "step": 18}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.3270668387413025], "step": 18}]}}}, "score": 2.812196731567383, "best_step": 18, "status": "COMPLETED", "message": null}