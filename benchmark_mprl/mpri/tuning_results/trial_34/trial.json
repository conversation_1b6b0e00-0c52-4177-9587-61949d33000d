{"trial_id": "34", "hyperparameters": {"space": [{"class_name": "Choice", "config": {"name": "mpriupperhalf_filterno", "default": 32, "conditions": [], "values": [32, 64, 128], "ordered": true}}, {"class_name": "Choice", "config": {"name": "output_convno", "default": 32, "conditions": [], "values": [32, 64, 128], "ordered": true}}, {"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "learning_rate", "default": 0.01, "conditions": [], "values": [0.01, 0.001, 0.0001], "ordered": true}}, {"class_name": "Boolean", "config": {"name": "pooling_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "l2_conv2d", "default": 0.0, "conditions": [], "values": [0.0, 0.01], "ordered": true}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"mpriupperhalf_filterno": 32, "output_convno": 128, "fc_dropout": false, "learning_rate": 0.001, "pooling_dropout": true, "l2_conv2d": 0.0, "batch_size": 16}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [0.14814351499080658], "step": 30}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.9450922012329102], "step": 30}]}, "val_loss": {"direction": "min", "observations": [{"value": [1.2228305339813232], "step": 30}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.6517906785011292], "step": 30}]}}}, "score": 1.2228305339813232, "best_step": 30, "status": "COMPLETED", "message": null}