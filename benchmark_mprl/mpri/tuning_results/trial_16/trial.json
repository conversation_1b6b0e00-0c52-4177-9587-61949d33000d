{"trial_id": "16", "hyperparameters": {"space": [{"class_name": "Choice", "config": {"name": "mpriupperhalf_filterno", "default": 32, "conditions": [], "values": [32, 64, 128], "ordered": true}}, {"class_name": "Choice", "config": {"name": "output_convno", "default": 32, "conditions": [], "values": [32, 64, 128], "ordered": true}}, {"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "learning_rate", "default": 0.01, "conditions": [], "values": [0.01, 0.001, 0.0001], "ordered": true}}, {"class_name": "Boolean", "config": {"name": "pooling_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "l2_conv2d", "default": 0.0, "conditions": [], "values": [0.0, 0.01], "ordered": true}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"mpriupperhalf_filterno": 128, "output_convno": 64, "fc_dropout": false, "learning_rate": 0.01, "pooling_dropout": true, "l2_conv2d": 0.01, "batch_size": 16}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [8.115090370178223], "step": 1}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.0002649856323841959], "step": 1}]}, "val_loss": {"direction": "min", "observations": [{"value": [8.152953147888184], "step": 1}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.00016735467943362892], "step": 1}]}}}, "score": 8.152953147888184, "best_step": 1, "status": "COMPLETED", "message": null}