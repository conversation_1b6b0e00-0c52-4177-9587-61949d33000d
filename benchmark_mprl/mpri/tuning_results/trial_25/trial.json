{"trial_id": "25", "hyperparameters": {"space": [{"class_name": "Choice", "config": {"name": "mpriupperhalf_filterno", "default": 32, "conditions": [], "values": [32, 64, 128], "ordered": true}}, {"class_name": "Choice", "config": {"name": "output_convno", "default": 32, "conditions": [], "values": [32, 64, 128], "ordered": true}}, {"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "learning_rate", "default": 0.01, "conditions": [], "values": [0.01, 0.001, 0.0001], "ordered": true}}, {"class_name": "Boolean", "config": {"name": "pooling_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "l2_conv2d", "default": 0.0, "conditions": [], "values": [0.0, 0.01], "ordered": true}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"mpriupperhalf_filterno": 64, "output_convno": 128, "fc_dropout": true, "learning_rate": 0.001, "pooling_dropout": true, "l2_conv2d": 0.0, "batch_size": 32}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [0.8153546452522278], "step": 11}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.7133413553237915], "step": 11}]}, "val_loss": {"direction": "min", "observations": [{"value": [1.3989171981811523], "step": 11}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.6316523551940918], "step": 11}]}}}, "score": 1.3989171981811523, "best_step": 11, "status": "COMPLETED", "message": null}