{"trial_id": "45", "hyperparameters": {"space": [{"class_name": "Choice", "config": {"name": "mpriupperhalf_filterno", "default": 32, "conditions": [], "values": [32, 64, 128], "ordered": true}}, {"class_name": "Choice", "config": {"name": "output_convno", "default": 32, "conditions": [], "values": [32, 64, 128], "ordered": true}}, {"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "learning_rate", "default": 0.01, "conditions": [], "values": [0.01, 0.001, 0.0001], "ordered": true}}, {"class_name": "Boolean", "config": {"name": "pooling_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "l2_conv2d", "default": 0.0, "conditions": [], "values": [0.0, 0.01], "ordered": true}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"mpriupperhalf_filterno": 32, "output_convno": 128, "fc_dropout": false, "learning_rate": 0.01, "pooling_dropout": false, "l2_conv2d": 0.01, "batch_size": 64}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [3.331946849822998], "step": 5}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.2188781350851059], "step": 5}]}, "val_loss": {"direction": "min", "observations": [{"value": [16.729217529296875], "step": 5}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.005466919392347336], "step": 5}]}}}, "score": 16.729217529296875, "best_step": 5, "status": "COMPLETED", "message": null}