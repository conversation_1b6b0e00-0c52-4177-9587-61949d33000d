{"trial_id": "29", "hyperparameters": {"space": [{"class_name": "Choice", "config": {"name": "mpriupperhalf_filterno", "default": 32, "conditions": [], "values": [32, 64, 128], "ordered": true}}, {"class_name": "Choice", "config": {"name": "output_convno", "default": 32, "conditions": [], "values": [32, 64, 128], "ordered": true}}, {"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "learning_rate", "default": 0.01, "conditions": [], "values": [0.01, 0.001, 0.0001], "ordered": true}}, {"class_name": "Boolean", "config": {"name": "pooling_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "l2_conv2d", "default": 0.0, "conditions": [], "values": [0.0, 0.01], "ordered": true}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"mpriupperhalf_filterno": 128, "output_convno": 64, "fc_dropout": true, "learning_rate": 0.001, "pooling_dropout": false, "l2_conv2d": 0.0, "batch_size": 64}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [0.6821667551994324], "step": 27}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.7578728795051575], "step": 27}]}, "val_loss": {"direction": "min", "observations": [{"value": [0.3074973225593567], "step": 27}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.9012049436569214], "step": 27}]}}}, "score": 0.3074973225593567, "best_step": 27, "status": "COMPLETED", "message": null}