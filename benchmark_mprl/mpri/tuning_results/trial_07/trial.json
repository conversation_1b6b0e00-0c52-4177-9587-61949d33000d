{"trial_id": "07", "hyperparameters": {"space": [{"class_name": "Choice", "config": {"name": "mpriupperhalf_filterno", "default": 32, "conditions": [], "values": [32, 64, 128], "ordered": true}}, {"class_name": "Choice", "config": {"name": "output_convno", "default": 32, "conditions": [], "values": [32, 64, 128], "ordered": true}}, {"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "learning_rate", "default": 0.01, "conditions": [], "values": [0.01, 0.001, 0.0001], "ordered": true}}, {"class_name": "Boolean", "config": {"name": "pooling_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "l2_conv2d", "default": 0.0, "conditions": [], "values": [0.0, 0.01], "ordered": true}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"mpriupperhalf_filterno": 32, "output_convno": 64, "fc_dropout": false, "learning_rate": 0.0001, "pooling_dropout": false, "l2_conv2d": 0.01, "batch_size": 64}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [0.55206698179245], "step": 25}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.9156648516654968], "step": 25}]}, "val_loss": {"direction": "min", "observations": [{"value": [1.5854352712631226], "step": 25}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.5832310318946838], "step": 25}]}}}, "score": 1.5854352712631226, "best_step": 25, "status": "COMPLETED", "message": null}