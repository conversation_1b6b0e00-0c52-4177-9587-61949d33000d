{"trial_id": "28", "hyperparameters": {"space": [{"class_name": "Choice", "config": {"name": "mpriupperhalf_filterno", "default": 32, "conditions": [], "values": [32, 64, 128], "ordered": true}}, {"class_name": "Choice", "config": {"name": "output_convno", "default": 32, "conditions": [], "values": [32, 64, 128], "ordered": true}}, {"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "learning_rate", "default": 0.01, "conditions": [], "values": [0.01, 0.001, 0.0001], "ordered": true}}, {"class_name": "Boolean", "config": {"name": "pooling_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "l2_conv2d", "default": 0.0, "conditions": [], "values": [0.0, 0.01], "ordered": true}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"mpriupperhalf_filterno": 128, "output_convno": 64, "fc_dropout": false, "learning_rate": 0.01, "pooling_dropout": true, "l2_conv2d": 0.0, "batch_size": 32}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [1.3040004968643188], "step": 12}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.572606086730957], "step": 12}]}, "val_loss": {"direction": "min", "observations": [{"value": [5.264286994934082], "step": 12}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.06493361294269562], "step": 12}]}}}, "score": 5.264286994934082, "best_step": 12, "status": "COMPLETED", "message": null}