{"trial_id": "48", "hyperparameters": {"space": [{"class_name": "Choice", "config": {"name": "mpriupperhalf_filterno", "default": 32, "conditions": [], "values": [32, 64, 128], "ordered": true}}, {"class_name": "Choice", "config": {"name": "output_convno", "default": 32, "conditions": [], "values": [32, 64, 128], "ordered": true}}, {"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "learning_rate", "default": 0.01, "conditions": [], "values": [0.01, 0.001, 0.0001], "ordered": true}}, {"class_name": "Boolean", "config": {"name": "pooling_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "l2_conv2d", "default": 0.0, "conditions": [], "values": [0.0, 0.01], "ordered": true}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"mpriupperhalf_filterno": 128, "output_convno": 128, "fc_dropout": true, "learning_rate": 0.01, "pooling_dropout": true, "l2_conv2d": 0.0, "batch_size": 16}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [5.626810550689697], "step": 20}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.035703327506780624], "step": 20}]}, "val_loss": {"direction": "min", "observations": [{"value": [4.464103698730469], "step": 20}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.13566885888576508], "step": 20}]}}}, "score": 4.464103698730469, "best_step": 20, "status": "COMPLETED", "message": null}