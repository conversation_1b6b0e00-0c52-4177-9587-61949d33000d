{"trial_id": "19", "hyperparameters": {"space": [{"class_name": "Choice", "config": {"name": "mpriupperhalf_filterno", "default": 32, "conditions": [], "values": [32, 64, 128], "ordered": true}}, {"class_name": "Choice", "config": {"name": "output_convno", "default": 32, "conditions": [], "values": [32, 64, 128], "ordered": true}}, {"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "learning_rate", "default": 0.01, "conditions": [], "values": [0.01, 0.001, 0.0001], "ordered": true}}, {"class_name": "Boolean", "config": {"name": "pooling_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "l2_conv2d", "default": 0.0, "conditions": [], "values": [0.0, 0.01], "ordered": true}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"mpriupperhalf_filterno": 64, "output_convno": 64, "fc_dropout": false, "learning_rate": 0.001, "pooling_dropout": false, "l2_conv2d": 0.01, "batch_size": 32}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [0.9726752042770386], "step": 22}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.7590861916542053], "step": 22}]}, "val_loss": {"direction": "min", "observations": [{"value": [2.6106531620025635], "step": 22}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.4391944706439972], "step": 22}]}}}, "score": 2.6106531620025635, "best_step": 22, "status": "COMPLETED", "message": null}