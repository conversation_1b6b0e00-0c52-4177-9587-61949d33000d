{"trial_id": "08", "hyperparameters": {"space": [{"class_name": "Choice", "config": {"name": "mpriupperhalf_filterno", "default": 32, "conditions": [], "values": [32, 64, 128], "ordered": true}}, {"class_name": "Choice", "config": {"name": "output_convno", "default": 32, "conditions": [], "values": [32, 64, 128], "ordered": true}}, {"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "learning_rate", "default": 0.01, "conditions": [], "values": [0.01, 0.001, 0.0001], "ordered": true}}, {"class_name": "Boolean", "config": {"name": "pooling_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "l2_conv2d", "default": 0.0, "conditions": [], "values": [0.0, 0.01], "ordered": true}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"mpriupperhalf_filterno": 128, "output_convno": 64, "fc_dropout": true, "learning_rate": 0.0001, "pooling_dropout": false, "l2_conv2d": 0.0, "batch_size": 64}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [0.4355563819408417], "step": 97}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.8570053577423096], "step": 97}]}, "val_loss": {"direction": "min", "observations": [{"value": [0.21474973857402802], "step": 97}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.957770824432373], "step": 97}]}}}, "score": 0.21474973857402802, "best_step": 97, "status": "COMPLETED", "message": null}