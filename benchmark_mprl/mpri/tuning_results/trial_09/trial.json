{"trial_id": "09", "hyperparameters": {"space": [{"class_name": "Choice", "config": {"name": "mpriupperhalf_filterno", "default": 32, "conditions": [], "values": [32, 64, 128], "ordered": true}}, {"class_name": "Choice", "config": {"name": "output_convno", "default": 32, "conditions": [], "values": [32, 64, 128], "ordered": true}}, {"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "learning_rate", "default": 0.01, "conditions": [], "values": [0.01, 0.001, 0.0001], "ordered": true}}, {"class_name": "Boolean", "config": {"name": "pooling_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "l2_conv2d", "default": 0.0, "conditions": [], "values": [0.0, 0.01], "ordered": true}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"mpriupperhalf_filterno": 128, "output_convno": 32, "fc_dropout": false, "learning_rate": 0.01, "pooling_dropout": false, "l2_conv2d": 0.01, "batch_size": 32}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [7.908071041107178], "step": 0}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.0008367967675440013], "step": 0}]}, "val_loss": {"direction": "min", "observations": [{"value": [13.17595386505127], "step": 0}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.0002231395774288103], "step": 0}]}}}, "score": 13.17595386505127, "best_step": 0, "status": "COMPLETED", "message": null}