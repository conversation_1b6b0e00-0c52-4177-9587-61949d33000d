{"trial_id": "44", "hyperparameters": {"space": [{"class_name": "Choice", "config": {"name": "mpriupperhalf_filterno", "default": 32, "conditions": [], "values": [32, 64, 128], "ordered": true}}, {"class_name": "Choice", "config": {"name": "output_convno", "default": 32, "conditions": [], "values": [32, 64, 128], "ordered": true}}, {"class_name": "Boolean", "config": {"name": "fc_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "learning_rate", "default": 0.01, "conditions": [], "values": [0.01, 0.001, 0.0001], "ordered": true}}, {"class_name": "Boolean", "config": {"name": "pooling_dropout", "default": false, "conditions": []}}, {"class_name": "Choice", "config": {"name": "l2_conv2d", "default": 0.0, "conditions": [], "values": [0.0, 0.01], "ordered": true}}, {"class_name": "Choice", "config": {"name": "batch_size", "default": 16, "conditions": [], "values": [16, 32, 64], "ordered": true}}], "values": {"mpriupperhalf_filterno": 32, "output_convno": 64, "fc_dropout": false, "learning_rate": 0.001, "pooling_dropout": true, "l2_conv2d": 0.01, "batch_size": 16}}, "metrics": {"metrics": {"loss": {"direction": "min", "observations": [{"value": [2.582524538040161], "step": 4}]}, "accuracy": {"direction": "max", "observations": [{"value": [0.36378344893455505], "step": 4}]}, "val_loss": {"direction": "min", "observations": [{"value": [5.459089279174805], "step": 4}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.07279928773641586], "step": 4}]}}}, "score": 5.459089279174805, "best_step": 4, "status": "COMPLETED", "message": null}