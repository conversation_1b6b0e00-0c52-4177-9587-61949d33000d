{"cells": [{"cell_type": "markdown", "id": "a2de6a75", "metadata": {}, "source": ["## Summary of notebook:\n", "\n", "This notebook shows the tuning process to obtain the optimal model architecture and hyperparameters for the MPRI model using the **keras-tuner** package.\n", "\n", "Terminal's command: ```pip install keras-tuner```\n", "\n", "The following shows some of the important details tuning process:\n", "- Dataset used: Dataset augmented with Gaussian noise (augmented_features_10_ue1_v2_ds.npy\\augmented_labels_10_ue1_v2_ds.npy)\n", "- Tuner: RandomSearch\n", "- Max trials: 50\n", "\n", "Parameters to be tuned:\n", "- mpriupperhalf_filterno: No. of filters in bottleneck layer of 1x1 filter\n", "- output_convno: No. of filters in convolution layer at the output module\n", "- fc_dropout: Boolean on whether to include a Dropout layer with rate = 0.5 just before softmax output layer at the output module to prevent overfitting\n", "- learning_rate: Learning rate of model\n", "- pooling_dropout: Boolean on whether to include a Dropout layer with rate = 0.2 after every MaxPool2D layer to prevent overfitting\n", "- l2_conv2d: L2 regularisation factor on weights of Conv2D layers to prevent overfitting\n", "- batch_size: Batch size during training of model\n", "\n", "Optimal Hyperparameters:\n", "- mpriupperhalf_filterno: 128\n", "- output_convno: 128\n", "- fc_dropout: True\n", "- learning_rate: 0.0001\n", "- pooling_dropout: False\n", "- l2_conv2d: 0.0\n", "- batch_size: 64"]}, {"cell_type": "code", "execution_count": 4, "id": "17c290dd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PhysicalDevice(name='/physical_device:GPU:0', device_type='GPU')\n", "2.9.1\n"]}], "source": ["# Configure amd test GPU\n", "import tensorflow as tf\n", "from tensorflow.python.client import device_lib\n", "\n", "# Prevent automatic GPU memory pre-allocation\n", "gpus = tf.config.experimental.list_physical_devices('GPU')\n", "for gpu in gpus:\n", "    print(gpu)\n", "    tf.config.experimental.set_memory_growth(gpu, True)\n", "\n", "print(tf.__version__)\n", "# print(device_lib.list_local_devices())"]}, {"cell_type": "code", "execution_count": 9, "id": "0ca7dbfe", "metadata": {}, "outputs": [], "source": ["# Whole network composed of 63 layers, approximately 2.6m total no. of parameters\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import os\n", "\n", "import tensorflow as tf\n", "import tensorflow.keras as keras\n", "from tensorflow.keras.layers import Conv2D, BatchNormalization, ReLU, MaxPool2D,\\\n", "                                    GlobalAvgPool2D, Dense, Add, Concatenate, Input,\\\n", "                                    Dropout\n", "from tensorflow.keras import Model\n", "\n", "# Note: tf version 2.9.1 does not have Identity layer. Implement our own identity layer which is argument insensitive\n", "# and returns its inputs argument as output"]}, {"cell_type": "code", "execution_count": 12, "id": "972aed55", "metadata": {}, "outputs": [], "source": ["import keras_tuner as kt\n", "\n", "class HyperModel(kt.HyperModel):\n", "    \n", "    def build(self, hp):\n", "        \n", "        # Should test\n", "        mpriupperhalf_convno = hp.Choice('mpriupperhalf_filterno', [32, 64, 128])\n", "        output_convno = hp.Choice('output_convno', [32, 64, 128])\n", "        fc_dropout = hp.Bo<PERSON>an('fc_dropout', default = False)\n", "        lr = hp.Choice('learning_rate', [0.01, 0.001, 0.0001])\n", "\n", "        # May be removed\n", "        pooling_dropout = hp.Boolean('pooling_dropout', default = False)\n", "        l2_conv2d = hp.Choice('l2_conv2d', [0.0, 0.01])\n", "\n", "        # mpriupperhalf_convno, batch_no, pooling_dropout, output_convno,\n", "        # fc_dropout, lr, l2_conv2d\n", "        def input_module(x):\n", "\n", "            # Set no. of filters to 256 to match the output of Add layer at the end of\n", "            # upper half of MPRI module\n", "            x = Conv2D(filters = 256, kernel_size = 3, strides = 1,\n", "                       padding = 'same', kernel_regularizer = keras.regularizers.L2(l2_conv2d))(x)\n", "            x = BatchNormalization()(x)\n", "            x = ReLU()(x)\n", "\n", "            # Normally, strides = 2 to reduce dimensions but set strides =1 for now to match\n", "            # output shapes\n", "            x = MaxPool2D(pool_size= 3, strides = 2, padding = 'same')(x)\n", "            \n", "            if pooling_dropout:\n", "                x = Dropout(rate = 0.2)(x)\n", "            return x\n", "        \n", "        def mpri_upperhalf(x):\n", "\n", "            # Save input as another variable since need to add input of mpri\n", "            # with output of mpri\n", "            input_tensor = x\n", "\n", "            # Bottleneck layer with 1x1 conv filter\n", "            bottlenecked_tensor = Conv2D(filters = mpriupperhalf_convno, kernel_size = 1, strides = 1,\n", "                                         padding = 'same', kernel_regularizer = keras.regularizers.L2(l2_conv2d))(x)\n", "\n", "            # First path\n", "            firstpath_tensor = BatchNormalization()(bottlenecked_tensor)\n", "            firstpath_tensor = ReLU()(firstpath_tensor)\n", "            firstpath_tensor = Conv2D(filters = 64, kernel_size = 1, strides = 1,\n", "                                      padding = 'same', kernel_regularizer = keras.regularizers.L2(l2_conv2d))(firstpath_tensor)\n", "\n", "            # Second path\n", "            secondpath_tensor = BatchNormalization()(bottlenecked_tensor)\n", "            secondpath_tensor = ReLU()(secondpath_tensor)\n", "            secondpath_tensor = Conv2D(filters = 32, kernel_size = (5,1), strides = 1,\n", "                                       padding = 'same', kernel_regularizer = keras.regularizers.L2(l2_conv2d))(secondpath_tensor)\n", "            secondpath_tensor = Conv2D(filters = 32, kernel_size = (1,3), strides = 1,\n", "                                       padding = 'same', kernel_regularizer = keras.regularizers.L2(l2_conv2d))(secondpath_tensor)\n", "            \n", "            # Third path\n", "            # Normally, strides = 2 to reduce the dimensions of the input\n", "            # In this case, experiment with strides = 1 to fit desired output shape for concatenation layer\n", "            thirdpath_tensor = MaxPool2D(pool_size = 3, strides = 1, padding = 'same')(bottlenecked_tensor)\n", "            \n", "            if pooling_dropout:\n", "                thirdpath_tensor = Dropout(rate = 0.2)(thirdpath_tensor)\n", "            \n", "            thirdpath_tensor = BatchNormalization()(thirdpath_tensor)\n", "            thirdpath_tensor = ReLU()(thirdpath_tensor)\n", "            thirdpath_tensor = Conv2D(filters = 32, kernel_size = 3, strides = 1,\n", "                                      padding = 'same', kernel_regularizer = keras.regularizers.L2(l2_conv2d))(thirdpath_tensor)\n", "            \n", "            # Fourth path\n", "            fourthpath_tensor = BatchNormalization()(bottlenecked_tensor)\n", "            fourthpath_tensor = ReLU()(fourthpath_tensor)\n", "            fourthpath_tensor = Conv2D(filters = 32, kernel_size = 1, strides = 1,\n", "                                       padding = 'same', kernel_regularizer = keras.regularizers.L2(l2_conv2d))(fourthpath_tensor)\n", "\n", "            fourthpath_tensor = BatchNormalization()(fourthpath_tensor)\n", "            fourthpath_tensor = ReLU()(fourthpath_tensor)\n", "            fourthpath_tensor = Conv2D(filters = 128, kernel_size = 1, strides = 1,\n", "                                       padding = 'same', kernel_regularizer = keras.regularizers.L2(l2_conv2d))(fourthpath_tensor)\n", "            \n", "            # Depth concatenate the output from the four paths\n", "            concatenated_tensor = Concatenate()([firstpath_tensor, secondpath_tensor, thirdpath_tensor, fourthpath_tensor])\n", "\n", "            # Add the depth concatenated layer and input tensor\n", "            # To add successfully, input tensor must have 256 channels as well to match the shape of\n", "            # the concatenated tensor\n", "            output_tensor = Add()([input_tensor, concatenated_tensor])\n", "\n", "            return output_tensor\n", "        \n", "        def mpri_lowerhalf(x):\n", "\n", "            def conv3x3_block(x):\n", "                x = BatchNormalization()(x)\n", "                x = ReLU()(x)\n", "                x = Conv2D(filters = 256, kernel_size = 3, strides = 1,\n", "                           padding = 'same', kernel_regularizer = keras.regularizers.L2(l2_conv2d))(x)\n", "                return x\n", "\n", "            def conv1x1_block(x):\n", "                x = BatchNormalization()(x)\n", "                x = ReLU()(x)\n", "                x = Conv2D(filters = 256, kernel_size = 1, strides = 1,\n", "                           padding = 'same', kernel_regularizer = keras.regularizers.L2(l2_conv2d))(x)\n", "                return x\n", "\n", "            # --- First layer ---\n", "            upperpath_pooledtensor = MaxPool2D(pool_size = 3, strides = 2, padding = 'same')(x)\n", "            if pooling_dropout:\n", "                upperpath_pooledtensor = Dropout(rate = 0.2)(upperpath_pooledtensor)\n", "        \n", "            upperpath_tensor = conv3x3_block(upperpath_pooledtensor)\n", "\n", "            lowerpath_pooledtensor = MaxPool2D(pool_size = 3, strides = 2, padding = 'same')(x)\n", "            if pooling_dropout:\n", "                lowerpath_pooledtensor = Dropout(rate = 0.2)(lowerpath_pooledtensor)\n", "            \n", "            lowerpath_tensor = conv1x1_block(lowerpath_pooledtensor)\n", "\n", "            upperpath_tensor = Add()([upperpath_pooledtensor, upperpath_tensor, lowerpath_tensor])\n", "            lowerpath_tensor = Add()([lowerpath_pooledtensor, lowerpath_tensor, upperpath_tensor])\n", "\n", "            # --- Second layer ---\n", "            upperpath_pooledtensor = MaxPool2D(pool_size = 3, strides = 2, padding = 'same')(upperpath_tensor)\n", "            if pooling_dropout:\n", "                upperpath_pooledtensor = Dropout(rate = 0.2)(upperpath_pooledtensor)\n", "                \n", "            upperpath_tensor = conv3x3_block(upperpath_pooledtensor)\n", "\n", "            lowerpath_pooledtensor = MaxPool2D(pool_size = 3, strides = 2, padding = 'same')(lowerpath_tensor)\n", "            if pooling_dropout:\n", "                lowerpath_pooledtensor = Dropout(rate = 0.2)(lowerpath_pooledtensor)\n", "                \n", "            lowerpath_tensor = conv1x1_block(lowerpath_pooledtensor)\n", "\n", "            upperpath_tensor = Add()([upperpath_pooledtensor, upperpath_tensor, lowerpath_tensor])\n", "            lowerpath_tensor = Add()([lowerpath_pooledtensor, lowerpath_tensor, upperpath_tensor])\n", "\n", "            # --- Third layer ---\n", "            upperpath_pooledtensor = MaxPool2D(pool_size = 3, strides = 2, padding = 'same')(upperpath_tensor)\n", "            if pooling_dropout:\n", "                upperpath_pooledtensor = Dropout(rate = 0.2)(upperpath_pooledtensor)\n", "            upperpath_tensor = conv3x3_block(upperpath_pooledtensor)\n", "\n", "            lowerpath_pooledtensor = MaxPool2D(pool_size = 3, strides = 2, padding = 'same')(lowerpath_tensor)\n", "            if pooling_dropout:\n", "                lowerpath_pooledtensor = Dropout(rate = 0.2)(lowerpath_pooledtensor)\n", "            lowerpath_tensor = conv1x1_block(lowerpath_pooledtensor)\n", "\n", "            upperpath_tensor = Add()([upperpath_pooledtensor, upperpath_tensor, lowerpath_tensor])\n", "            lowerpath_tensor = Add()([lowerpath_pooledtensor, lowerpath_tensor, upperpath_tensor])\n", "\n", "            # Final layer - Add upper and lower path tensors\n", "            output_tensor = Add()([upperpath_tensor, lowerpath_tensor])\n", "\n", "            return output_tensor\n", "        \n", "        def output_module(x, num_classes = 1000):\n", "            \n", "            x = Conv2D(filters = output_convno, kernel_size = 3, strides = 1,\n", "                       padding = 'same', kernel_regularizer = keras.regularizers.L2(l2_conv2d))(x)\n", "            x = BatchNormalization()(x)\n", "            x = ReLU()(x)\n", "            x = GlobalAvgPool2D()(x)\n", "            if fc_dropout:\n", "                x = Dropout(rate = 0.5)(x)\n", "            x = Dense(units = num_classes, activation = 'softmax')(x)\n", "\n", "            return x\n", "        \n", "        model_input = Input(shape = (193,16,1))\n", "        model_output = output_module(mpri_lowerhalf(mpri_upperhalf(input_module(model_input))), num_classes = 3876)\n", "        mpri_model = Model(model_input, model_output)\n", "        \n", "        optimizer = tf.keras.optimizers.<PERSON>(lr)\n", "        mpri_model.compile(optimizer = optimizer,\n", "                      loss = tf.keras.losses.SparseCategoricalCrossentropy(),\n", "                      metrics = ['accuracy'])\n", "   \n", "        # mpri_model.summary()\n", "    \n", "        return mpri_model\n", "    \n", "    def fit(self, hp, model, X_train, y_train, validation_data = None, **kwargs):\n", "\n", "        return model.fit(X_train, y_train,\n", "                        validation_data = validation_data,\n", "                        batch_size = hp.Choice('batch_size', [16,32,64]),\n", "                        **kwargs,\n", "                        )"]}, {"cell_type": "code", "execution_count": 18, "id": "66ce5502", "metadata": {}, "outputs": [], "source": ["tuner = kt.RandomSearch(\n", "        HyperModel(),\n", "        objective = 'val_loss',\n", "        max_trials = 50)"]}, {"cell_type": "code", "execution_count": 19, "id": "758be592", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Search space summary\n", "Default search space size: 6\n", "mpriupperhalf_filterno (Choice)\n", "{'default': 32, 'conditions': [], 'values': [32, 64, 128], 'ordered': True}\n", "output_convno (Choice)\n", "{'default': 32, 'conditions': [], 'values': [32, 64, 128], 'ordered': True}\n", "fc_dropout (Bo<PERSON>an)\n", "{'default': False, 'conditions': []}\n", "learning_rate (Choice)\n", "{'default': 0.01, 'conditions': [], 'values': [0.01, 0.001, 0.0001], 'ordered': True}\n", "pooling_dropout (<PERSON><PERSON>an)\n", "{'default': False, 'conditions': []}\n", "l2_conv2d (Choice)\n", "{'default': 0.0, 'conditions': [], 'values': [0.0, 0.01], 'ordered': True}\n"]}], "source": ["tuner.search_space_summary()"]}, {"cell_type": "code", "execution_count": 20, "id": "598c5157", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/home/<USER>/committed_git/datasets\n", "/home/<USER>/committed_git/datasets\n"]}], "source": ["print(os.getcwd())\n", "os.chdir('../datasets')\n", "print(os.getcwd())"]}, {"cell_type": "code", "execution_count": 22, "id": "d0798372", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Shape of features np array: (89628, 193, 16)\n", "Shape of labels np array: (89628,)\n"]}], "source": ["from sklearn.model_selection import train_test_split\n", "\n", "# Import dataset\n", "features = np.load('augmented_features_10_ue1_v2_ds.npy')\n", "labels = np.load('augmented_labels_10_ue1_v2_ds.npy')\n", "\n", "print(f'Shape of features np array: {features.shape}')\n", "print(f'Shape of labels np array: {labels.shape}')\n", "\n", "X = features\n", "y = labels\n", "\n", "# Split the dataset\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, shuffle=True)"]}, {"cell_type": "code", "execution_count": 23, "id": "03e29b30", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/home/<USER>/committed_git/datasets\n", "/home/<USER>/committed_git/mpri\n"]}], "source": ["print(os.getcwd())\n", "os.chdir('../mpri')\n", "print(os.getcwd())"]}, {"cell_type": "code", "execution_count": 24, "id": "260892dc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Trial 49 Complete [00h 25m 35s]\n", "val_loss: 4.464103698730469\n", "\n", "Best val_loss So Far: 0.17371131479740143\n", "Total elapsed time: 16h 35m 26s\n", "\n", "Search: Running Trial #50\n", "\n", "Value             |Best Value So Far |Hyperparameter\n", "64                |128               |mpriupperhalf_filterno\n", "128               |128               |output_convno\n", "True              |True              |fc_dropout\n", "0.0001            |0.0001            |learning_rate\n", "False             |False             |pooling_dropout\n", "0                 |0                 |l2_conv2d\n", "32                |64                |batch_size\n", "\n", "Epoch 1/100\n", "2241/2241 [==============================] - 41s 17ms/step - loss: 7.4988 - accuracy: 0.0053 - val_loss: 6.5479 - val_accuracy: 0.0210\n", "Epoch 2/100\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 5.8625 - accuracy: 0.0484 - val_loss: 4.7359 - val_accuracy: 0.1415\n", "Epoch 3/100\n", "2241/2241 [==============================] - 39s 17ms/step - loss: 4.3600 - accuracy: 0.1609 - val_loss: 3.4206 - val_accuracy: 0.3578\n", "Epoch 4/100\n", " 325/2241 [===>..........................] - ETA: 30s - loss: 3.6445 - accuracy: 0.2507"]}, {"name": "stderr", "output_type": "stream", "text": ["IOPub message rate exceeded.\n", "The notebook server will temporarily stop sending output\n", "to the client in order to avoid crashing it.\n", "To change this limit, set the config variable\n", "`--NotebookApp.iopub_msg_rate_limit`.\n", "\n", "Current values:\n", "NotebookApp.iopub_msg_rate_limit=1000.0 (msgs/sec)\n", "NotebookApp.rate_limit_window=3.0 (secs)\n", "\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2241/2241 [==============================] - 39s 17ms/step - loss: 0.5947 - accuracy: 0.8153 - val_loss: 0.4605 - val_accuracy: 0.9072\n", "Epoch 24/100\n", " 993/2241 [============>.................] - ETA: 19s - loss: 0.5606 - accuracy: 0.8280"]}, {"name": "stderr", "output_type": "stream", "text": ["IOPub message rate exceeded.\n", "The notebook server will temporarily stop sending output\n", "to the client in order to avoid crashing it.\n", "To change this limit, set the config variable\n", "`--NotebookApp.iopub_msg_rate_limit`.\n", "\n", "Current values:\n", "NotebookApp.iopub_msg_rate_limit=1000.0 (msgs/sec)\n", "NotebookApp.rate_limit_window=3.0 (secs)\n", "\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2241/2241 [==============================] - 38s 17ms/step - loss: 0.4848 - accuracy: 0.8464 - val_loss: 0.3849 - val_accuracy: 0.9177\n", "Epoch 29/100\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.4656 - accuracy: 0.8518 - val_loss: 0.3677 - val_accuracy: 0.9249\n", "Epoch 30/100\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.4485 - accuracy: 0.8554 - val_loss: 0.3534 - val_accuracy: 0.9250\n", "Epoch 31/100\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.4376 - accuracy: 0.8583 - val_loss: 0.3365 - val_accuracy: 0.9298\n", "Epoch 32/100\n", " 177/2241 [=>............................] - ETA: 32s - loss: 0.4101 - accuracy: 0.8713"]}, {"name": "stderr", "output_type": "stream", "text": ["IOPub message rate exceeded.\n", "The notebook server will temporarily stop sending output\n", "to the client in order to avoid crashing it.\n", "To change this limit, set the config variable\n", "`--NotebookApp.iopub_msg_rate_limit`.\n", "\n", "Current values:\n", "NotebookApp.iopub_msg_rate_limit=1000.0 (msgs/sec)\n", "NotebookApp.rate_limit_window=3.0 (secs)\n", "\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2241/2241 [==============================] - 38s 17ms/step - loss: 0.2717 - accuracy: 0.9078 - val_loss: 0.2235 - val_accuracy: 0.9476\n", "Epoch 51/100\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.2702 - accuracy: 0.9075 - val_loss: 0.2285 - val_accuracy: 0.9434\n", "Epoch 52/100\n", " 753/2241 [=========>....................] - ETA: 23s - loss: 0.2584 - accuracy: 0.9129"]}, {"name": "stderr", "output_type": "stream", "text": ["IOPub message rate exceeded.\n", "The notebook server will temporarily stop sending output\n", "to the client in order to avoid crashing it.\n", "To change this limit, set the config variable\n", "`--NotebookApp.iopub_msg_rate_limit`.\n", "\n", "Current values:\n", "NotebookApp.iopub_msg_rate_limit=1000.0 (msgs/sec)\n", "NotebookApp.rate_limit_window=3.0 (secs)\n", "\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2241/2241 [==============================] - 38s 17ms/step - loss: 0.2480 - accuracy: 0.9146 - val_loss: 0.2143 - val_accuracy: 0.9467\n", "Epoch 56/100\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.2476 - accuracy: 0.9163 - val_loss: 0.2117 - val_accuracy: 0.9477\n", "Epoch 57/100\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.2388 - accuracy: 0.9179 - val_loss: 0.2211 - val_accuracy: 0.9439\n", "Epoch 58/100\n", "2241/2241 [==============================] - 38s 17ms/step - loss: 0.2412 - accuracy: 0.9161 - val_loss: 0.2133 - val_accuracy: 0.9446\n", "Epoch 59/100\n", "1511/2241 [===================>..........] - ETA: 11s - loss: 0.2352 - accuracy: 0.9169"]}, {"name": "stderr", "output_type": "stream", "text": ["IOPub message rate exceeded.\n", "The notebook server will temporarily stop sending output\n", "to the client in order to avoid crashing it.\n", "To change this limit, set the config variable\n", "`--NotebookApp.iopub_msg_rate_limit`.\n", "\n", "Current values:\n", "NotebookApp.iopub_msg_rate_limit=1000.0 (msgs/sec)\n", "NotebookApp.rate_limit_window=3.0 (secs)\n", "\n"]}], "source": ["stop_early = tf.keras.callbacks.EarlyStopping(monitor='val_loss', patience=5)\n", "tuner.search(X_train, y_train,\n", "            validation_data = (X_test, y_test),\n", "            epochs = 100,\n", "            callbacks = [stop_early])"]}, {"cell_type": "code", "execution_count": null, "id": "6d68ec2e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "9bce58ac", "metadata": {}, "source": ["## Analysis of results"]}, {"cell_type": "code", "execution_count": 25, "id": "d0cf796a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Results summary\n", "Results in ./untitled_project\n", "Showing 10 best trials\n", "Objective(name=\"val_loss\", direction=\"min\")\n", "\n", "Trial 20 summary\n", "Hyperparameters:\n", "mpriupperhalf_filterno: 128\n", "output_convno: 128\n", "fc_dropout: True\n", "learning_rate: 0.0001\n", "pooling_dropout: False\n", "l2_conv2d: 0.0\n", "batch_size: 64\n", "Score: 0.17371131479740143\n", "\n", "Trial 49 summary\n", "Hyperparameters:\n", "mpriupperhalf_filterno: 64\n", "output_convno: 128\n", "fc_dropout: True\n", "learning_rate: 0.0001\n", "pooling_dropout: False\n", "l2_conv2d: 0.0\n", "batch_size: 32\n", "Score: 0.17775847017765045\n", "\n", "Trial 12 summary\n", "Hyperparameters:\n", "mpriupperhalf_filterno: 64\n", "output_convno: 128\n", "fc_dropout: False\n", "learning_rate: 0.001\n", "pooling_dropout: False\n", "l2_conv2d: 0.0\n", "batch_size: 16\n", "Score: 0.18610873818397522\n", "\n", "Trial 24 summary\n", "Hyperparameters:\n", "mpriupperhalf_filterno: 64\n", "output_convno: 64\n", "fc_dropout: True\n", "learning_rate: 0.001\n", "pooling_dropout: False\n", "l2_conv2d: 0.0\n", "batch_size: 16\n", "Score: 0.21404226124286652\n", "\n", "Trial 08 summary\n", "Hyperparameters:\n", "mpriupperhalf_filterno: 128\n", "output_convno: 64\n", "fc_dropout: True\n", "learning_rate: 0.0001\n", "pooling_dropout: False\n", "l2_conv2d: 0.0\n", "batch_size: 64\n", "Score: 0.21474973857402802\n", "\n", "Trial 36 summary\n", "Hyperparameters:\n", "mpriupperhalf_filterno: 32\n", "output_convno: 128\n", "fc_dropout: True\n", "learning_rate: 0.001\n", "pooling_dropout: False\n", "l2_conv2d: 0.0\n", "batch_size: 64\n", "Score: 0.24278530478477478\n", "\n", "Trial 14 summary\n", "Hyperparameters:\n", "mpriupperhalf_filterno: 32\n", "output_convno: 128\n", "fc_dropout: False\n", "learning_rate: 0.0001\n", "pooling_dropout: False\n", "l2_conv2d: 0.0\n", "batch_size: 16\n", "Score: 0.24419961869716644\n", "\n", "Trial 42 summary\n", "Hyperparameters:\n", "mpriupperhalf_filterno: 64\n", "output_convno: 64\n", "fc_dropout: False\n", "learning_rate: 0.001\n", "pooling_dropout: False\n", "l2_conv2d: 0.0\n", "batch_size: 16\n", "Score: 0.24577321112155914\n", "\n", "Trial 29 summary\n", "Hyperparameters:\n", "mpriupperhalf_filterno: 128\n", "output_convno: 64\n", "fc_dropout: True\n", "learning_rate: 0.001\n", "pooling_dropout: False\n", "l2_conv2d: 0.0\n", "batch_size: 64\n", "Score: 0.3074973225593567\n", "\n", "Trial 37 summary\n", "Hyperparameters:\n", "mpriupperhalf_filterno: 128\n", "output_convno: 32\n", "fc_dropout: True\n", "learning_rate: 0.001\n", "pooling_dropout: False\n", "l2_conv2d: 0.0\n", "batch_size: 64\n", "Score: 0.3788467347621918\n"]}], "source": ["tuner.results_summary()"]}, {"cell_type": "code", "execution_count": 26, "id": "eb11c856", "metadata": {}, "outputs": [], "source": ["best_hp = tuner.get_best_hyperparameters()[0]"]}, {"cell_type": "code", "execution_count": 27, "id": "b97b92d8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'mpriupperhalf_filterno': 128, 'output_convno': 128, 'fc_dropout': True, 'learning_rate': 0.0001, 'pooling_dropout': False, 'l2_conv2d': 0.0, 'batch_size': 64}\n"]}], "source": ["print(best_hp.values)"]}, {"cell_type": "code", "execution_count": null, "id": "81d189a3", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 5}