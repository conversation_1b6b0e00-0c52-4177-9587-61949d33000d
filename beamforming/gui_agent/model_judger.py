import datetime
from modules.extract_modules import State, GuiNet
import torch
import argparse
from inference import *
from Beam_env.BMenv import BMenv
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import os, sys
from inference import *
sys.path.append(os.path.dirname(__file__)+"/../")
from setsumi_env.setsumi_env import setsumi_env
model = None
args = None
env = None


def cal_score(env,ue_list,model):
    baseline1, model_inference,all_action = analyse(env, ue_list, model, 0, 200, cycle=1)
    return baseline1,model_inference,all_action

def draw_diffence(baseline1,model_inference):
    s_diff = model_inference - baseline1
    fig = go.Figure()
    fig.update_layout(
        title=f'Model Inference Score Analysis: Per TTI',
        width=1200,
        height=500
    )
    s_diff1 = np.copy(s_diff)
    s_diff2 = np.copy(s_diff)
    s_diff1[s_diff1 < 0] = 0
    s_diff2[s_diff2 > 0] = 0
    fig.add_trace(go.Scatter(x=np.arange(global_ue_num), y=s_diff1, fill='tozeroy',name="Model better than Baseline1"))
    fig.add_trace(go.Scatter(x=np.arange(global_ue_num), y=s_diff2, fill='tozeroy',name="Baseline1 better than Model"))
    # fig.write_html("model_judger01.html")
    return fig
    
def draw_UE():
    def map_action_to_layer_and_panel(self, actions):
        actions = actions.reshape(-1)
        mapped_actions = np.zeros((len(actions), 3))
        for i in range(actions.shape[0]):
            mapped_actions[i] = self.env.action_space[int(actions[i])]
        return mapped_actions
    panel_split = [(1,1), (2,1), (1,2), (2,2), (1,4)]
    panel_split_type = list(map(lambda x: f"H{x[0]}V{x[1]}", panel_split))
    times = list(range(0,200))
    for ue in args.ue:
        actions_TPs = []
        best_actions, best_TPs = get_best_action(ues=[ue], times=times)
        actions_TPs.insert(0,("Best", best_actions, best_TPs))
        fig = make_subplots(rows=4, cols=1, subplot_titles=("Layer Decision", "Panel Decision", "TP"))
        for name, actions, TPs in actions_TPs:
            mapped_actions = map_action_to_layer_and_panel(actions)
            fig.add_trace(go.Scatter(x=times, y=mapped_actions[:, 2], name=f"{name} Layer"), row=1, col=1)
            fig.update_xaxes(title="Time", row=1, col=1)
            fig.update_yaxes(tickmode='linear',range=[0.5,4.5], dtick=1, title="Layer", row=1, col=1)
            panel = []
            for i in range(mapped_actions.shape[0]):
                panel.append(panel_split.index((mapped_actions[i][0], mapped_actions[i][1])))
            fig.add_trace(go.Scatter(x=times, y=panel, name=f"{name} Panel"), row=2, col=1)
            fig.update_xaxes(title="Time", row=2, col=1)
            fig.update_yaxes(tickmode='array', range=[-0.5,len(panel_split)-0.5], tickvals=list(range(len(panel_split))), ticktext=panel_split_type, title="Panel", row=2, col=1)
            fig.add_trace(go.Scatter(x=times, y=TPs.reshape(-1), name=f"{name} TP"), row=3, col=1)
            fig.update_xaxes(title="Time", row=3, col=1)
            fig.update_yaxes(title="TP", row=3, col=1)
            fig.add_trace(go.Scatter(x=times, y=actions.reshape(-1), name=f"{name} Action"), row=4, col=1)
            fig.update_xaxes(title="Time", row=1, col=1)
            fig.update_yaxes(range=[-0.5,18.5], title="Action", row=4, col=1)
            fig.update_layout(height=800, width=1200, title_text=f"UE{ue} Analysis Figure")
            fig.write_html(f"{save_to}{ue}.html")

MODULES = [cal_score, draw_diffence]

def parse():
    parser = argparse.ArgumentParser()
    parser.add_argument("--env_path", type=str, default="/data/BM_data/28GHZ/UMa_2000_o/")
    parser.add_argument("--seed", type=int, default=777, help="random seed")
    parser.add_argument("--env_max_time", type=int, default=200, help="env max time")
    parser.add_argument("--env_max_length", type=int, default=200, help="env max length")
    parser.add_argument("--ue", nargs='+', default=[0,1,2,3])
    parser.add_argument("--path", type=str,  default="/data/setsumi/beamforming/gui_agent/models/BM_2023-03-21-15-25-06_frame360000_score0.8819695828488588.gg")
    args = parser.parse_args()
    return args

if __name__ == "__main__":
    
    # Init
    args = parse()
    # env = BMenv(path=args.env_path,
    #             nSub=17,
    #             max_time=args.env_max_time,
    #             max_length=args.env_max_length,
    #             seed=args.seed,
    #             enable_load=True,
    #             H_ant_num=2,
    #             V_ant_num=8)
    
    env = setsumi_env(logger=print,path=args.env_path,max_time=args.env_max_time,max_length=args.env_max_length,seed=args.seed,enable_load=True)
    global_ue_num = env.nUE
    env.load_data()
    env.SNR_idx = 6
    ues1 = [2, 26, 33, 109, 268, 304, 328, 374, 466, 535, 592, 599, 676, 779, 844, 947, 1011, 1022, 1061, 1130, 1175, 1183, 1292, 1307, 1359, 1380, 1419, 1440, 1447, 1504, 1511, 1512, 1569, 1647, 1658, 1779, 1798, 1897]
    ues2 = [99, 102, 174, 181, 275, 303, 312, 319, 333, 336, 363, 430, 589, 605, 641, 731, 752, 792, 871, 892, 976, 982, 1080, 1092, 1198, 1267, 1397, 1423, 1517, 1534, 1543, 1580, 1586, 1682, 1695, 1769, 1807, 1865, 1889, 1916, 1952]
    
    ues_98 = [2, 26, 33, 81, 94, 103, 104, 109, 145, 148, 234, 243, 247, 248, 265, 268, 288, 304, 322, 328, 374, 410, 416, 442, 466, 477, 501, 504, 520, 535, 592, 599, 645, 668, 676, 701, 730, 744, 753, 774, 779, 811, 819, 838, 844, 847, 849, 858, 874, 933, 947, 958, 967, 981, 1003, 1011, 1012, 1013, 1022, 1044, 1061, 1085, 1097, 1100, 1130, 1136, 1175, 1183, 1184, 1208, 1213, 1217, 1252, 1292, 1307, 1353, 1359, 1380, 1399, 1417, 1419, 1432, 1440, 1447, 1461, 1483, 1504, 1511, 1512, 1538, 1569, 1574, 1601, 1623, 1647, 1658, 1733, 1743, 1779, 1798, 1833, 1893, 1897, 1909, 1925, 1931, 1967, 1972, 1986, 1997]
    ues_120 = [0, 1, 7, 9, 31, 48, 54, 66, 67, 82, 85, 86, 96, 99, 102, 143, 156, 162, 164, 174, 181, 183, 185, 186, 197, 223, 236, 237, 242, 250, 260, 275, 280, 286, 303, 306, 312, 315, 319, 323, 333, 336, 348, 363, 391, 395, 402, 413, 419, 430, 437, 460, 487, 488, 494, 509, 515, 525, 532, 536, 539, 564, 576, 589, 593, 595, 605, 625, 640, 641, 663, 681, 683, 684, 696, 714, 731, 749, 752, 769, 790, 791, 792, 799, 826, 829, 832, 842, 850, 869, 871, 872, 887, 892, 900, 902, 907, 919, 926, 949, 951, 976, 982, 1018, 1026, 1051, 1080, 1090, 1092, 1095, 1099, 1106, 1108, 1119, 1169, 1170, 1171, 1177, 1178, 1182, 1194, 1198, 1204, 1212, 1224, 1236, 1250, 1267, 1284, 1287, 1293, 1304, 1315, 1316, 1328, 1329, 1364, 1378, 1386, 1397, 1413, 1415, 1423, 1424, 1428, 1477, 1507, 1517, 1518, 1519, 1534, 1543, 1550, 1558, 1572, 1578, 1580, 1582, 1586, 1591, 1629, 1631, 1645, 1662, 1673, 1679, 1682, 1687, 1694, 1695, 1732, 1739, 1740, 1741, 1744, 1747, 1754, 1766, 1769, 1770, 1773, 1778, 1785, 1792, 1795, 1800, 1806, 1807, 1813, 1826, 1832, 1845, 1848, 1849, 1862, 1865, 1871, 1885, 1889, 1898, 1900, 1907, 1911, 1916, 1920, 1922, 1928, 1952, 1953, 1957, 1977, 1979, 1990]
    
    ues_100 = [2, 5, 8, 14, 26, 33, 37, 40, 81, 94, 103, 104, 106, 109, 110, 111, 125, 145, 148, 163, 179, 192, 207, 211, 226, 234, 241, 243, 247, 248, 265, 268, 273, 287, 288, 299, 300, 304, 309, 322, 328, 332, 341, 374, 385, 389, 410, 416, 428, 433, 439, 442, 466, 477, 491, 492, 501, 504, 506, 508, 520, 521, 535, 559, 566, 567, 569, 572, 581, 590, 592, 599, 620, 645, 650, 657, 662, 668, 675, 676, 688, 701, 707, 725, 727, 730, 736, 740, 744, 753, 774, 779, 785, 787, 795, 811, 819, 827, 838, 844, 847, 849, 858, 860, 868, 874, 882, 903, 914, 933, 934, 947, 954, 958, 961, 967, 981, 1003, 1011, 1012, 1013, 1019, 1021, 1022, 1044, 1045, 1061, 1067, 1075, 1081, 1085, 1097, 1100, 1120, 1130, 1133, 1136, 1138, 1148, 1151, 1162, 1175, 1183, 1184, 1200, 1208, 1209, 1213, 1215, 1217, 1221, 1223, 1231, 1243, 1244, 1252, 1262, 1275, 1292, 1307, 1320, 1326, 1335, 1353, 1356, 1359, 1371, 1380, 1382, 1399, 1400, 1417, 1419, 1422, 1430, 1432, 1440, 1442, 1447, 1461, 1480, 1483, 1494, 1500, 1504, 1511, 1512, 1520, 1537, 1538, 1552, 1559, 1569, 1574, 1601, 1623, 1636, 1639, 1647, 1651, 1658, 1661, 1671, 1678, 1715, 1723, 1731, 1733, 1743, 1760, 1779, 1798, 1833, 1850, 1856, 1880, 1893, 1897, 1909, 1923, 1925, 1931, 1944, 1949, 1955, 1958, 1967, 1972, 1985, 1986, 1988, 1993, 1997]
    ues_110 = [0, 1, 3, 4, 7, 9, 13, 16, 21, 24, 29, 31, 34, 35, 44, 48, 54, 58, 64, 66, 67, 73, 76, 79, 82, 84, 85, 86, 96, 97, 99, 100, 102, 112, 114, 123, 124, 132, 133, 138, 141, 143, 154, 155, 156, 158, 162, 164, 165, 166, 167, 171, 174, 181, 183, 185, 186, 187, 189, 191, 194, 195, 197, 200, 206, 222, 223, 230, 231, 235, 236, 237, 240, 242, 244, 250, 253, 256, 260, 262, 263, 264, 275, 276, 277, 278, 280, 286, 303, 306, 310, 312, 314, 315, 319, 323, 324, 325, 333, 336, 340, 346, 348, 357, 363, 368, 371, 377, 378, 379, 384, 391, 395, 402, 403, 405, 407, 413, 419, 427, 429, 430, 437, 448, 449, 455, 456, 459, 460, 464, 467, 470, 471, 478, 479, 481, 484, 486, 487, 488, 494, 503, 505, 509, 515, 517, 519, 525, 532, 536, 539, 541, 542, 544, 545, 546, 548, 553, 557, 560, 562, 564, 571, 575, 576, 584, 586, 588, 589, 593, 595, 602, 604, 605, 606, 607, 613, 622, 623, 625, 628, 631, 632, 638, 640, 641, 648, 658, 663, 665, 673, 680, 681, 683, 684, 687, 689, 691, 692, 696, 700, 702, 703, 704, 708, 709, 712, 714, 719, 720, 721, 731, 735, 737, 738, 743, 746, 747, 749, 752, 756, 758, 759, 761, 764, 765, 768, 769, 773, 778, 788, 789, 790, 791, 792, 797, 799, 801, 812, 815, 816, 826, 829, 830, 831, 832, 833, 837, 840, 842, 845, 850, 854, 859, 862, 865, 869, 871, 872, 875, 877, 881, 887, 892, 893, 896, 900, 902, 907, 911, 912, 917, 918, 919, 921, 924, 926, 930, 942, 949, 951, 952, 962, 964, 970, 974, 976, 982, 983, 987, 1002, 1006, 1009, 1010, 1014, 1018, 1025, 1026, 1039, 1043, 1051, 1053, 1056, 1071, 1079, 1080, 1082, 1084, 1086, 1087, 1090, 1092, 1094, 1095, 1096, 1098, 1099, 1102, 1106, 1108, 1112, 1113, 1119, 1122, 1126, 1128, 1129, 1134, 1140, 1144, 1146, 1159, 1166, 1169, 1170, 1171, 1177, 1178, 1179, 1181, 1182, 1194, 1198, 1199, 1202, 1204, 1205, 1211, 1212, 1222, 1224, 1228, 1230, 1234, 1236, 1238, 1246, 1250, 1254, 1260, 1267, 1269, 1274, 1284, 1287, 1290, 1291, 1293, 1297, 1302, 1304, 1311, 1313, 1315, 1316, 1317, 1318, 1323, 1328, 1329, 1343, 1349, 1352, 1362, 1364, 1367, 1374, 1377, 1378, 1383, 1384, 1385, 1386, 1387, 1392, 1396, 1397, 1403, 1404, 1409, 1413, 1415, 1423, 1424, 1426, 1428, 1436, 1439, 1453, 1459, 1462, 1467, 1468, 1469, 1470, 1477, 1481, 1482, 1489, 1490, 1507, 1508, 1516, 1517, 1518, 1519, 1521, 1523, 1526, 1531, 1532, 1534, 1543, 1546, 1550, 1558, 1570, 1571, 1572, 1575, 1576, 1578, 1580, 1581, 1582, 1586, 1590, 1591, 1597, 1598, 1605, 1606, 1609, 1610, 1613, 1618, 1619, 1620, 1621, 1622, 1629, 1631, 1632, 1640, 1641, 1645, 1646, 1648, 1649, 1650, 1657, 1659, 1662, 1673, 1675, 1679, 1682, 1683, 1684, 1685, 1686, 1687, 1690, 1691, 1693, 1694, 1695, 1696, 1699, 1700, 1702, 1704, 1711, 1719, 1722, 1725, 1727, 1732, 1739, 1740, 1741, 1744, 1747, 1751, 1754, 1755, 1761, 1766, 1769, 1770, 1771, 1773, 1778, 1782, 1783, 1785, 1787, 1792, 1795, 1796, 1799, 1800, 1801, 1802, 1806, 1807, 1810, 1812, 1813, 1816, 1821, 1822, 1826, 1830, 1832, 1837, 1839, 1840, 1845, 1848, 1849, 1855, 1857, 1860, 1862, 1865, 1869, 1871, 1875, 1882, 1885, 1889, 1894, 1898, 1899, 1900, 1902, 1907, 1910, 1911, 1912, 1916, 1918, 1920, 1922, 1924, 1928, 1932, 1943, 1948, 1951, 1952, 1953, 1957, 1959, 1965, 1969, 1970, 1976, 1977, 1978, 1979, 1980, 1984, 1987, 1989, 1990, 1994, 1995, 1996]
    ues = np.sort(ues_100+ues_110)
    ue_list = np.sort(ues1+ues2)
    # ue_list = np.array(np.arange(200))
    # ue_list = ues[0:400]
    # ue_list = np.arange(100)
    # ue_list = [1,4,13,16,21,23,24,25,27,28,29,31,36,53,68,73,75,77,80,81,87,89,91,93,94]

    time = datetime.datetime.now().strftime('Result_%Y-%m-%d-%H-%M-%S')
    # Load Model
    model_dir = "/data/setsumi/beamforming/gui_agent/models/"
    paths = [
        # "BM_2023-03-21-15-25-06_frame10000_score0.7068194534805163.gg",
        # "BM_2023-03-21-15-25-06_frame20000_score0.8277937433423743.gg",
        # "BM_2023-03-21-15-25-06_frame30000_score0.8285705450843547.gg",
        # "BM_2023-03-21-15-25-06_frame80000_score0.8681636125741182.gg",
        # "BM_2023-03-21-15-25-06_frame110000_score0.869044293420669.gg",
        # "BM_2023-03-21-15-25-06_frame250000_score0.8751122818439144.gg",
        # "BM_2023-03-22-03-38-41_frame20000_score0.8326826553261603.gg"
        "BM_2023-03-24-06-49-47_frame540000_score0.9546187801559058.gg"
    ]
    for path in paths:
        print(f"Load model from: {path}")
        model = GuiNet().cuda()
        model.load_state_dict(torch.load(model_dir+path))
        
        with open('model_judger.html', 'w') as f:
            for snr in range(6,13):
                env.SNR_idx = snr
                env.SNR = env.SNRs[env.SNR_idx]
                # Run modules
                # for module in MODULES:
                score_str,all_action = cal_score()
                print(f"Judger at SNR {env.SNR}: {score_str}")
                fig = draw_diffence()
                f.write(fig.to_html(full_html=False, include_plotlyjs='cdn'))
        
        
        
