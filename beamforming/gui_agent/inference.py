import torch, sys, os
sys.path.append(os.path.dirname(__file__)+"/../")
from gui_agent.modules.extract_modules import State
import numpy as np
from collections import Counter
import pickle

global_ue_num = 100

def get_best_action(env,ues,start_time,end_time, best_action_pkl="/data/BM/420UE_seed100_2000_0921_VH/seed_20021009_time_200_len_800_lenaction_18_best.pkl"):
    best_data = None
    with open(best_action_pkl, 'rb') as fo: 
        best_data = pickle.load(fo, encoding='bytes')
    return best_data[0][ues, start_time:end_time],best_data[1][ues, start_time:end_time]

def get_action_tp(env,ues,action_idx,start_time,end_time):
    nUE = len(ues)
    env.test = True
    time_length = end_time - start_time
    TPs = np.zeros((nUE,time_length))
    for i in range(0,time_length):
        env.get_state_batch(nUE,np.ones_like(ues)*(i+start_time),ues,np.ones_like(ues)*action_idx)
        TPs[:,i] = env.TP
    env.test = False
    return TPs

def inference_baseline1(env,ues,start_time,end_time):
    nUE = len(ues)
    env.test = True
    time_length = end_time - start_time
    EEs = np.zeros((time_length,nUE))
    SEs = np.zeros((time_length,nUE))
    all_action = np.zeros((time_length, nUE), dtype=np.int32)
    action = np.ones_like(ues)*5
    for i in range(0,time_length):
        all_action[i] = action
        env.get_state_batch(nUE,np.ones_like(ues)*(i+start_time),ues,action)
        action = (env.RI + 1).astype(int)
        EEs[i] = env.EE
        SEs[i] = env.SE
    return SEs,EEs,all_action

def inference_baseline2(env,ues,start_time,end_time):
    nUE = len(ues)
    env.test = True
    time_length = end_time - start_time
    EEs = np.zeros((time_length,nUE))
    SEs = np.zeros((time_length,nUE))
    all_action = np.zeros((time_length, nUE), dtype=np.int32)
    action = np.ones_like(ues)*9
    for i in range(0,time_length):
        all_action[i] = action
        env.get_state_batch(nUE,np.ones_like(ues)*(i+start_time),ues,action)
        action = (env.RI + 5).astype(int)
        EEs[i] = env.EE
        SEs[i] = env.SE
    return SEs,EEs,all_action

def inference_model(env,ues,net,start_time,end_time,cycle=None,choose_RI=True):
    nUE = len(ues)
    time_length = end_time - start_time
    EEs = np.zeros((time_length,nUE))
    SEs = np.zeros((time_length,nUE))
    all_action = np.zeros((time_length, nUE), dtype=np.int32)
    action = np.zeros(nUE, dtype=np.int32)
    for i in range(0,time_length):
        times = np.ones_like(ues)*(i+start_time)
        env.test = False
        all_action[i] = action
        data = State("cuda", *env.get_state_batch(batch=len(ues), times=times, ues=ues, actions=action))
        if cycle is not None:        
            if i % cycle == 0:
                result = net(data)
                selected_action = list(torch.argmax(result, dim=-1).cpu().numpy())
                action = selected_action
            else:
                result = torch.argmax(net(data), dim=-1).cpu().numpy()
                for ue in range(nUE):
                    H_split,V_split,_,Port = env.action_space[action[ue]]
                    _,_,Layer,_ = env.action_space[result[ue]]
                    if H_split == 1 and V_split == 1 and Layer >= 3:
                        continue
                    action[ue] = env.action_space.index((H_split, V_split, Layer, Port))   
        else:
            result = net(data)
            selected_action = list(torch.argmax(result, dim=-1).cpu().numpy())
            action = selected_action
        if not choose_RI:
            for ue in range(nUE):
                H_split,V_split,_,Port = env.action_space[action[ue]]
                Layer = int(data.RI[ue]*4)
                if H_split == 1 and V_split == 1 and Layer >= 3:
                    continue
                action[ue] = env.action_space.index((H_split, V_split, Layer, Port))   
        

        env.test = True
        [_,PMI,CQI,RI,EE] = env.get_state_batch(batch=len(ues), times=times, ues=ues, actions=action)
        EEs[i]= env.EE
        SEs[i] = env.SE
    return SEs,EEs, all_action

def analyse(env, ues, model, start_time, end_time, cycle=None):
    """输出Baseline1，Baseline2以及模型评分

    Args:
        env (_type_): 环境实例
        ues (_type_): 评分UE列表
        start_time (_type_): 开始时间
        end_time (_type_): 结束时间

    Returns:
        (score1, score2): (baseline1 score, model score)
    """
    # _,best_tp = get_best_action(env,ues,start_time,end_time)
    
    # best_tp = env.best_TP[start_time:end_time,ues,env.SNR_idx]
    # best_tp = best_tp.transpose(1,0)
    # best_tp_avg = np.average(best_tp, axis=-1)
    ee_base1 = inference_baseline1(env,ues,start_time,end_time)
    ee_model,all_action = inference_model(env, ues, model, start_time, end_time, cycle=cycle)
    # score1 = (np.average(tp_base1, axis=-1) / best_tp_avg - 0.6)/0.4
    # score2 = (np.average(tp_model, axis=-1) / best_tp_avg - 0.6)/0.4
    # score1 = np.average(ee_base1,axis=-1)
    # score2 = np.average(ee_model,axis=-1)
    return ee_base1, ee_model, all_action