import config
import random, torch
import numpy as np
import datetime
import logging
import os, sys
from modules.D3QN import D3QN
from torch.utils.tensorboard import SummaryWriter
from modules.extract_modules import State, GuiNet, GuiNet_conv
from inference import get_best_action,global_ue_num
from torchstat import stat
sys.path.append(os.path.dirname(__file__)+"/../")
from Beam_env.BMenv import BMenv
from setsumi_env.setsumi_env import setsumi_env
import pdb
import pickle
from collections import deque

def inference(env, agent, inference_ues, start_time, end_time):
    """ 使用模型推理，获得评分
    Args:
        env (_type_): 环境实例
        agent (_type_): agent实例
        inference_ues (list): 用于推理的UE
        start_time (_type_): 开始时间
        end_time (_type_): 结束时间
        best_tp (_type_): 最好的TP
        tp_rand (_type_): 随机的TP
    """
    time_length = end_time - start_time
    rewards = np.zeros((len(inference_ues),time_length))
    inference_action = np.zeros(len(inference_ues), dtype=np.int32)
    for i in range(0,time_length):
        times = np.ones_like(inference_ues)*(i+start_time)
        data = State("cuda", *env.get_state_batch(batch=len(inference_ues), times=times, ues=inference_ues, actions=inference_action))
        result = agent.net(data)
        selected_action = list(torch.argmax(result, dim=-1).cpu().numpy())
        inference_action = selected_action
        env.test = True
        [_,PMI,CQI,RI,EE] = env.get_state_batch(batch=len(inference_ues), times=times, ues=inference_ues, actions=inference_action)
        env.test = False
        rewards[:,i]= env.reward
    score = np.average(rewards)
    return score

def train(args,logger,env,writer,net_class=GuiNet_conv):
    # 环境初始化 - 这一部分初始化环境和agent
    # env = BMenv(logger=logger,
    #             path=args.env_path,
    #             nSub=args.env_nsub,
    #             max_time=args.env_max_time,
    #             max_length=args.env_max_length,
    #             seed=args.seed,
    #             enable_load=args.env_enable_load,
    #             H_ant_num=args.env_H_ants_num,
    #             V_ant_num=args.env_V_ants_num)
    global_ue_num = env.nUE
    env.SNR_idx = 6
    env.w1 = args.env_w1
    
    model_dir = "/data/setsumi/beamforming/gui_agent/dqn/models/"
    model_path = "BM_2023-04-13-06-10-55_frame940000_score0.8866756881683622.gg"
    agent = D3QN(args.device, net_class=net_class, train_space=list(range(len(env.train_space))), memory_size=args.memory_size, max_epsilon=args.max_epsilon, min_epsilon=args.min_epsilon,
                 lr=args.lr, epsilon_decay=args.epsilon_decay, batch_size=args.batch_size, gamma=args.gamma,model=None)
    logger("NetWork: %s", agent.net)
    ue_pool = args.ue # 总UE池子
    nUE = args.nUE # 每一批次的UE数
    ue_pool_name = args.ue
    ue_pool = list(np.arange(len(args.ue)))
    # UE池子窗口滑动配置
    ptr = 0
    UE = ue_pool[ptr:min(ptr+nUE,len(ue_pool))]

    # Tensorboard配置
    if(writer):
        cell_writer = SummaryWriter(f'{os.path.dirname(__file__)}/analyze_train/logs/{args.daytime}/Cell/')
        writers = []
        for ue in ue_pool:
            writers.append(SummaryWriter(f'{os.path.dirname(__file__)}/analyze_train/logs/{args.daytime}/UE{ue}/'))
            
    state = State(args.device, *env.get_state_batch(batch=nUE,times=np.zeros_like(UE), ues=UE, actions=np.random.randint(0,len(env.train_space),len(UE))))

    # 推理设定 - 这一部分保存420个UE在0-200时间内的最佳TP以及随机动作得到的TP，便于后面计算分数
    inference_ues = list(range(global_ue_num))
    inference_ues = ue_pool
    start_time = 200
    end_time = 400
    # with open(args.best_action_pkl, 'rb') as fo: 
    #     best_data = pickle.load(fo, encoding='bytes')
    #     best_tp = best_data[1][:, :200]
    
    # 开始训练
    frame_idx = 0
    loss = 0
    time = 0
    # env.SNR_idx = random.randint(6, 12)
    env.SNR_idx = 10
    env.SNR = env.SNRs[env.SNR_idx]
    max_score = 0 # 记录最大分数，每次超过最大分数就保存一次模型
    # window_size = 100  # 滑动窗口大小，可以根据需求调整
    # reward_window = deque(maxlen=window_size)  # 双端队列用于保存滑动窗口内的奖励值
    results = dict()
    results['loss'] = []
    results['reward'] = []
    results['action'] = []
    results['UE'] = []
    results['time'] = []
     
    while frame_idx < args.num_frame:
        action = agent.select_action(state)
        next_state = State(args.device, *env.get_state_batch(batch=nUE, times=np.ones_like(UE)*(time), ues=UE, actions=action))
        reward = env.reward
        results['action'].append(action)
        results['UE'].append(UE)
        results['time'].append(time)
        
        # 更新滑动窗口
        # reward_window.append(reward)

        # 滑动归一化reward
        # min_reward = np.min(reward_window,axis=0)
        # max_reward = np.max(reward_window,axis=0)
        # norm_reward = ((max_reward - min_reward)==0) + max_reward - min_reward
        # normalized_reward = (reward - min_reward) / norm_reward
        
        done = frame_idx % args.done_cycle == 0
        agent.memory_pool.add_memory(state, reward, action, next_state, done)
        state = next_state
        frame_idx += 1
        if frame_idx % args.timefreeze_cycle == 0:
            time = np.random.randint(start_time, end_time)
        if frame_idx % args.optim_frame == 0: # 反向传播更新梯度
            loss = agent.optim()
            results['loss'].append(loss)
            results['reward'].append(reward)
            if loss is not None:
                if(writer):
                    cell_writer.add_scalar("train/loss", loss, frame_idx)
                    for i,ue in enumerate(UE):
                        writers[ue_pool.index(ue)].add_scalar("UE actions", action[i], frame_idx)
                        writers[ue_pool.index(ue)].add_scalar("RI", next_state.RI[i,0]*4, frame_idx)
                        writers[ue_pool.index(ue)].add_scalar("EE", next_state.EE[i,0], frame_idx)
                        writers[ue_pool.index(ue)].add_scalar("reward", reward[i], frame_idx)
                        writers[ue_pool.index(ue)].add_scalar("Best actions", env.best_ac[i], frame_idx)
                agent.update_epsilon()
                
        if frame_idx % args.target_update_frame == 0: # 更新target net
            agent.update_target_net()
        if frame_idx % args.train_log_cycle == 0:
            print(f"\rCurrent Max Score: {max_score} Frame idx: {frame_idx}/{args.num_frame}"+ " "*10, end="")
        if frame_idx % args.change_UEs_cycle == 0:
            agent.eval()
            # 推理部分，得到评分
            scores = []
            SNR_train = env.SNR
            for snr_idx in range(6,13):
                env.SNR_idx = snr_idx
                env.SNR = env.SNRs[snr_idx]
                score = inference(env, agent, inference_ues, start_time, end_time)
                scores.append(score)
            score = np.average(np.array(scores))
            
            # 输出信息
            names = np.array(ue_pool_name)[UE]
            print(f"\rCurrent UE: {names} Current SNR: {SNR_train} Current Max Score: {max_score} Score: {score}"+ " "*10)
            logger(f"Current UE: {names} Current SNR: {SNR_train} Current Max Score: {max_score} Score: {score}")
            
            # UE 滑动
            ptr += nUE
            if ptr+nUE >= len(ue_pool):
                ue_pool.extend(ue_pool)
            UE = ue_pool[ptr:min(ptr+nUE,len(ue_pool))]
            
            # 滑动UE同时修改SNR
            env.SNR_idx = random.randint(6, 10)
            env.SNR = env.SNRs[env.SNR_idx]
            
            
            # 保存模型
            if score > max_score:
                torch.save(agent.net.state_dict(), f'{os.path.dirname(__file__)}/analyze_train/models/{args.daytime}_frame{frame_idx}_score{score}.gg')
                max_score = score
            agent.train()
    return results
    
def train_env_init(args):
    # 设定随机种子
    random.seed(args.seed)
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    
    # 初始化Log文件
    args.daytime = datetime.datetime.now().strftime('BM_%Y-%m-%d-%H-%M-%S')
    logging.basicConfig(format='%(asctime)s - %(levelname)s - %(message)s',
                        datefmt='%Y-%m-%d %H:%M:%S',
                        level=logging.INFO,
                        filename=f'{os.path.dirname(__file__)}/analyze_train/logs/{args.daytime}.log')
    logger = logging.getLogger(__name__)
    print(args.desc)
    logger.info("desc: %s", args.desc)
    logger.info("parameters: %s", args)
    return logger.info

if __name__ == "__main__":
    args = config.parse_args()
    if args.desc == "No desc":
        print("Note: This train doesn't have description.")
    logger = train_env_init(args)
    env = setsumi_env(logger=logger,path=args.env_path,max_time=args.env_max_time,max_length=args.env_max_length,seed=args.seed,enable_load=args.env_enable_load,w1=args.env_w1)
    env.load_data(args.ue)
    train(args,logger,env,writer=True)