import numpy as np
from collections import deque

class State():
    def __init__(self, beam_features, PMI, CQI, RI, EE):
        self.nUE = self.PMI.shape[0]
        self.beam_features = beam_features
        self.PMI = PMI
        self.CQI = CQI
        self.RI = RI
        self.EE = EE

class ReplayMemoryPool():
    def __init__(self, size=10000):
        self.size = size # Memory size
        self.memories = deque(maxlen=self.size)
    def add_memory(self, state, reward, action, next_state, done):
        self.memories.append([state, reward, action, next_state, done])
    def get_batch(self, batch_size):
        choosen_index = np.random.choice(np.arange(0,len(self.memories)), batch_size, replace=False)
        return [self.memories[i] for i in choosen_index]