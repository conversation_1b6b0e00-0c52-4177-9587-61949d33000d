import torch

class State():
    def __init__(self, device, beam_features=None, PMI=None, CQI=None, RI=None, EE=None, Port=None, Ns=None,states=None):
        if states is not None:
            self.nUE = states[0].nUE
            self.beam_features = torch.cat([state.beam_features for state in states])
            self.PMI = torch.cat([state.PMI for state in states])
            self.CQI = torch.cat([state.CQI for state in states])
            self.RI = torch.cat([state.RI for state in states])
            self.EE = torch.cat([state.EE for state in states])
            self.Port = torch.cat([state.Port for state in states])
            self.Ns = torch.cat([state.Ns for state in states])
        else:
            self.flatten = torch.nn.Flatten()
            self.nUE = PMI.shape[0]
            self.beam_features = self.flatten(torch.tensor(beam_features, device=device, dtype=torch.float32))
            self.PMI = self.flatten(torch.tensor(PMI, device=device, dtype=torch.float32))
            self.CQI = self.flatten(torch.tensor(CQI, device=device, dtype=torch.float32))
            self.RI = self.flatten(torch.tensor(RI, device=device, dtype=torch.float32))
            self.EE = self.flatten(torch.tensor(EE, device=device, dtype=torch.float32))
            self.Port = self.flatten(torch.tensor(Port, device=device, dtype=torch.float32))
            self.Ns = self.flatten(torch.tensor(Ns, device=device, dtype=torch.float32))
class GuiNet(torch.nn.Module):
    '''
    Structure:
        PMI  \
               HV_features                             \
        Beam /                                          \
                                                          joint_layer
        RI   \                                          /
        CQI  - layer_hiden_layer1 - layer_hiden_layer2 /

    '''
    def __init__(self, HV_num=2, Layer_num=4):
        super().__init__()
        self.PMI_to_features = torch.nn.Linear(2, 16) #H var,V var
        self.Beam_to_features = torch.nn.Linear(32, 32)
        self.HV_features = torch.nn.Linear(48, 64) #
        
        self.RI_to_features = torch.nn.Linear(1, 4) 
        self.CQI_to_features = torch.nn.Linear(17, 8)
        self.layer_hiden_layer1 = torch.nn.Linear(12, 32)
        # self.layer_hiden_layer2 = torch.nn.Linear(128, 256)

        self.joint_layer = torch.nn.Linear(96, 22)
        
        self.relu = torch.nn.ReLU()
        
    def forward(self, state):
        PMI_features = self.PMI_to_features(state.PMI)
        Beam_features = self.Beam_to_features(state.beam_features)
        HV_feature = self.relu(self.HV_features(self.relu(torch.cat((PMI_features, Beam_features), dim=-1))))
        # HV_feature = self.relu(self.HV_features(self.relu(Beam_features)))
        # HV_feature = self.relu(self.HV_features(self.relu(PMI_features)))
       
        RI_features = self.RI_to_features(state.RI)
        CQI_features = self.CQI_to_features(state.CQI)
        layer_features = torch.cat((RI_features, CQI_features), dim=-1)
        layer_value = (self.relu(self.layer_hiden_layer1(layer_features)))
        joint_value = self.joint_layer(torch.cat((layer_value, HV_feature), dim=-1))
        # joint_value = self.joint_layer(layer_value)
        return joint_value


class GuiNet_conv(torch.nn.Module):
    def __init__(self, HV_num=2, Layer_num=4):
        super().__init__()
        self.PMI_to_features = torch.nn.Linear(2, 16) #H var,V var
        
        # self.conv1 = torch.nn.Conv2d(1, 4, (2, 2))
        # self.fc_after_conv = torch.nn.Linear(4 * 3 * 7, 32)
        
        self.conv1 = torch.nn.Conv2d(1, 4, (3, 3)) # Change kernel size to (3, 3)
        self.fc_after_conv = torch.nn.Linear(4 * 2 * 6, 32) # Update input size
        
        self.HV_features = torch.nn.Linear(48, 64) #
        
        self.RI_to_features = torch.nn.Linear(1, 4) 
        self.CQI_to_features = torch.nn.Linear(17, 8)
        self.layer_hiden_layer1 = torch.nn.Linear(12, 32)

        self.joint_layer = torch.nn.Linear(96, 22)
        
        self.relu = torch.nn.ReLU()
        
    def forward(self, state):
        PMI_features = self.PMI_to_features(state.PMI)
        
        Beam_reshaped = state.beam_features.view(-1, 1, 4, 8)
        Beam_conv = self.relu(self.conv1(Beam_reshaped))
        Beam_flattened = Beam_conv.view(Beam_conv.size(0), -1)
        Beam_features = self.fc_after_conv(Beam_flattened)
        
        HV_feature = self.relu(self.HV_features(self.relu(torch.cat((PMI_features, Beam_features), dim=-1))))
       
        RI_features = self.RI_to_features(state.RI)
        CQI_features = self.CQI_to_features(state.CQI)
        layer_features = torch.cat((RI_features, CQI_features), dim=-1)
        layer_value = (self.relu(self.layer_hiden_layer1(layer_features)))
        joint_value = self.joint_layer(torch.cat((layer_value, HV_feature), dim=-1))

        return joint_value
    
    
class GuiNet_CSI(torch.nn.Module):
    '''
    Structure:
        PMI  \
               HV_features                             \
        Beam /                                          \
                                                          joint_layer
        RI   \                                          /
        CQI  - layer_hiden_layer1 - layer_hiden_layer2 /

    '''
    def __init__(self, HV_num=2, Layer_num=4):
        super().__init__()
        self.PMI_to_features = torch.nn.Linear(2, 16) #H var,V var
        # self.Beam_to_features = torch.nn.Linear(32, 32)
        self.HV_features = torch.nn.Linear(16, 64) #
        
        self.RI_to_features = torch.nn.Linear(1, 4) 
        self.CQI_to_features = torch.nn.Linear(17, 8)
        self.layer_hiden_layer1 = torch.nn.Linear(12, 32)
        # self.layer_hiden_layer2 = torch.nn.Linear(128, 256)

        self.joint_layer = torch.nn.Linear(96, 22)
        
        self.relu = torch.nn.ReLU()
        
    def forward(self, state):
        PMI_features = self.PMI_to_features(state.PMI)
        # Beam_features = self.Beam_to_features(state.beam_features)
        HV_feature = self.relu(self.HV_features(self.relu(PMI_features)))
        # HV_feature = self.relu(self.HV_features(self.relu(Beam_features)))
        # HV_feature = self.relu(self.HV_features(self.relu(PMI_features)))
       
        RI_features = self.RI_to_features(state.RI)
        CQI_features = self.CQI_to_features(state.CQI)
        layer_features = torch.cat((RI_features, CQI_features), dim=-1)
        layer_value = (self.relu(self.layer_hiden_layer1(layer_features)))
        joint_value = self.joint_layer(torch.cat((layer_value, HV_feature), dim=-1))
        # joint_value = self.joint_layer(layer_value)
        return joint_value
    