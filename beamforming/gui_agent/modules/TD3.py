#两个问题 一个是softmax明显是有问题，应该改变输入方式
#第二个问题是loss计算在离散动作的情况下是不是应该更换（SAC discrete论文）
#另外一点，TD3的原文代码貌似跑出来loss并不低，或许不应该太关注于loss
#归一化

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import copy
import random
import pdb
from .memory import ReplayMemoryPool
import torch.optim.lr_scheduler as lr_scheduler
from .extract_modules import State
import itertools

#state_dim 需要重新考量
#考虑到底要不要用dueling（对于policy-based 到底有没有效果）
class Actor(nn.Module):
    def __init__(self, state_dim):
        super(Actor, self).__init__()
        
        # Common layers
        self.PMI_to_features = nn.Linear(2, 16)
        self.conv1 = torch.nn.Conv2d(1, 4, (3, 3)) # Change kernel size to (3, 3)
        self.fc_after_conv = torch.nn.Linear(4 * 2 * 6, 32) # Update input size
        self.Beam_to_features = nn.Linear(32, 32)
        self.HV_features = nn.Linear(48, 64)
        self.RI_to_features = nn.Linear(1, 4)
        self.CQI_to_features = nn.Linear(17, 8)
        self.Ns_to_features = nn.Linear(1, 4)
        
        self.layer_hidden_layer1 = nn.Linear(16, 32)
        self.joint_layer = nn.Linear(96, 64)
        # self.joint_layer2 = nn.Linear(128, 32)
        
        # Continuous action output
        self.continuous_layer = nn.Linear(64, 1)
        
        # One-hot action output
        self.one_hot_layer = nn.Linear(64, 3)
        
        # Incremental action output
        self.incremental_layer = nn.Linear(64, 1)
        
        self.relu = nn.ReLU()
        self.tanh = nn.Tanh()
        self.softmax = nn.Softmax(dim=-1)

    def forward(self, state):
        # Beam_features = self.Beam_to_features(state.beam_features)
        
        Beam_reshaped = state.beam_features.view(-1, 1, 4, 8)
        Beam_conv = self.relu(self.conv1(Beam_reshaped))
        Beam_flattened = Beam_conv.view(Beam_conv.size(0), -1)
        Beam_features = self.fc_after_conv(Beam_flattened)
        
        PMI_features = self.PMI_to_features(state.PMI)
        HV_feature = self.relu(self.HV_features(self.relu(torch.cat((PMI_features, Beam_features), dim=-1))))
        
        RI_features = self.RI_to_features(state.RI)
        CQI_features = self.CQI_to_features(state.CQI)
        Ns_features = self.Ns_to_features(state.Ns)
        layer_features = torch.cat((RI_features, CQI_features, Ns_features), dim=-1)
        hidden_layer1 = self.relu(self.layer_hidden_layer1(layer_features))
        
        joint_value = self.relu(self.joint_layer(torch.cat((hidden_layer1, HV_feature), dim=-1)))
        
        # joint_layer2 = self.relu(self.joint_layer2((joint_value)))
        
        # Continuous action output
        continuous_action = self.tanh(self.continuous_layer(joint_value))
        
        # Incremental action output
        incremental_action = self.tanh(self.incremental_layer(joint_value))
        
        # One-hot action output
        one_hot_action = self.one_hot_layer(joint_value)
        
        action = torch.cat((continuous_action, incremental_action, one_hot_action), dim=-1)
        
        return action

class Q_Func(nn.Module):
    def __init__(self,state_dim=52,action_dim=22):
        super(Q_Func,self).__init__()
        self.model = nn.Sequential(
            nn.Linear(state_dim+action_dim,256),
            nn.ReLU(),
            nn.LayerNorm([256]),
            nn.Linear(256,64),
            nn.ReLU(),
            nn.LayerNorm([64]),
            nn.Linear(64,64),
            nn.ReLU(),
            nn.LayerNorm([64]),
            nn.Linear(64,1)
        ) 
    def forward(self, state, action):
        sa = torch.cat((state.beam_features,state.PMI,state.RI,state.CQI,state.Port,state.Ns,action),dim = -1)
        return self.model(sa)


class Critic(nn.Module):
    ### Flatten State Dim : 40
    ### Flatten Action Dim : 18
    def __init__(self,device,state_dim=53,action_dim=5):
        super(Critic,self).__init__()
        self.device = device
        #Q1
        self.Q1 = Q_Func(state_dim, action_dim)
        #Q2
        self.Q2 = Q_Func(state_dim, action_dim)
        #
    def forward(self,state,action):
        #
        q1 = self.Q1(state,action)
        #
        q2 = self.Q2(state,action)
        return q1,q2

class TD3(object):
    def __init__(
        self,
        train_space,
        device,
        state_dim=53,
        action_dim=5,
        discount = 0.98,
        tau = 0.005,
        policy_noise = 1.0,
        policy_noise_decay = 1/5000,
        policy_noise_min = 0.1,
        noise_clip = 1.0,#这个值感觉应该再小一点
        policy_freq = 3,
        memory_size = 10000,
        batch_size = 100,
        gamma = 0.99,
        pi_lr = 1e-3,
        q_lr = 1e-3,
        model = None
):
        self.action_dim = action_dim
        # Actor Init 
        self.actor = Actor(state_dim=state_dim).to(device)
        self.actor_target = copy.deepcopy(self.actor)
        self.actor_optimizer = torch.optim.Adam(self.actor.parameters(),lr = pi_lr)
        self.actor_lr_scheduler = lr_scheduler.StepLR(self.actor_optimizer,step_size=1,gamma=0.8)
        # Critic Init
        self.critic = Critic(state_dim=state_dim,action_dim=action_dim,device=device).to(device)
        self.critic_target = copy.deepcopy(self.critic)
        self.critic_optimizer = torch.optim.Adam(self.critic.parameters(),lr = q_lr)
        self.critic_lr_scheduler = lr_scheduler.StepLR(self.actor_optimizer,step_size=1,gamma=0.8)
        # Freeze target networks with respect to optimizers (only update via polyak averaging)
        for p in self.actor_target.parameters():
            p.requires_grad = False
        for p in self.critic_target.parameters():
            p.requires_grad = False
        # Hyper Parameters
        self.discount = discount
        self.tau = tau
        self.policy_noise = policy_noise
        self.policy_noise_decay = policy_noise_decay
        self.policy_noise_min = policy_noise_min
        self.noise_clip = noise_clip
        self.policy_freq = policy_freq
        self.batch_size = batch_size
        # 
        self.train_space = train_space
        self.device = device
        self.total_it = 0
        # memory
        self.memory_pool = ReplayMemoryPool(size=memory_size)
        # List of parameters for both Q-networks (save this for convenience)
        q_params = itertools.chain(self.critic.parameters())
    
    def train(self):
        self.actor.train()

    def eval(self):
        self.actor.eval()

    def set_actor_lr(self, lr):
        for p in self.actor_optimizer.param_groups:
            p['lr'] = lr

    def set_critic_lr(self, lr):
        for p in self.critic_optimizer.param_groups:
            p['lr'] = lr

    def select_action(self,state) :
        action_val = self.actor(state)
        selected_action = action_val.argmax(dim=1)
        selected_action = selected_action.detach().cpu().numpy()
        pdb.set_trace()
        return action_val, selected_action

    def select_action_with_noise(self,state) :
        with torch.no_grad():
            # Add Noise
            action_val = self.actor(state)
            origin_action = action_val
            noise_small = (torch.randn_like(action_val[:,0]) * self.policy_noise).clamp(-self.noise_clip,self.noise_clip)
            noise_large = (torch.randn_like(action_val[:,0]) * 1).clamp(-self.noise_clip*2,self.noise_clip*2)
            
            one_hot_action = action_val[:,2:5]
            epsilon = torch.rand(one_hot_action.size(0), 1).to(one_hot_action.device)
            is_random = epsilon < self.policy_noise_min
            random_one_hot_action = torch.eye(one_hot_action.size(1))[torch.randint(one_hot_action.size(1), (one_hot_action.size(0),))].to(one_hot_action.device)
            one_hot_action = torch.where(is_random, random_one_hot_action, one_hot_action)
            
            noise_2 = torch.where(is_random[:,0], noise_large, noise_small)
            action_val[:,0] += noise_small
            action_val[:,1] += noise_2
            action_val[:,2:5] = one_hot_action
            # Clip Q and Sel Action
            action_val = action_val
            selected_action = action_val.argmax(dim=1)
            selected_action = selected_action.detach().cpu().numpy()
            action_val = action_val.detach().cpu().numpy()
        return action_val, selected_action, origin_action


    def optim(self):
        # Initialize losses with None
        actor_loss = None
        critic_loss = None
        if len(self.memory_pool.memories) < self.batch_size:
            return [None, None]
        self.total_it +=1
        # Sample
        batch = self.memory_pool.get_batch(self.batch_size)
        state = State(self.device, states=[batch[i][0] for i in range(self.batch_size)])
        reward = torch.tensor(np.array([batch[i][1] for i in range(self.batch_size)]), device=self.device).reshape(-1)
        action = torch.tensor(np.array([batch[i][2] for i in range(self.batch_size)]), device=self.device).reshape(-1,self.action_dim)
        next_state = State(self.device, states=[batch[i][3] for i in range(self.batch_size)])
        done = torch.tensor(np.array([1-batch[i][4] for i in range(self.batch_size)]), device=self.device).repeat_interleave(state.nUE)
        # TODO: check grad
        # pdb.set_trace()
        # Calculate Q
        with torch.no_grad():
            noise = (torch.randn_like(action) * self.policy_noise).clamp(-self.noise_clip,self.noise_clip)
          
            next_action = self.actor_target(next_state) + noise

            target_Q1,target_Q2 = self.critic_target(next_state,next_action)
            # TODO: check
            target_Q = torch.minimum(target_Q1,target_Q2).to(self.device)
            target_Q = reward.unsqueeze(1) + (1.0 - done.unsqueeze(1)) * self.discount * target_Q
            # TODO: check data type
            target_Q = target_Q.type(torch.float32)
    
        current_Q1, current_Q2 = self.critic(state, action)
        # Calculate Critic Loss
        critic_loss = F.mse_loss(current_Q1, target_Q) + F.mse_loss(current_Q2, target_Q)

        # Optimizer Step
        self.critic_optimizer.zero_grad()
        critic_loss.backward()
        self.critic_optimizer.step()    

        if self.total_it % self.policy_freq == 0:
            # Freeze Q-networks so you don't waste computational effort 
            # computing gradients for them during the policy learning step.
            for p in self.critic.parameters():
                p.requires_grad = False

            next_action = self.actor(state)

            actor_loss = -self.critic.Q1(state, next_action).mean()

            self.actor_optimizer.zero_grad()
            actor_loss.backward()
            self.actor_optimizer.step()

            # Unfreeze Q-networks so you can optimize it at next DDPG step.
            for p in self.critic.parameters():
                p.requires_grad = True

            # Soft Update the frozen target models
            with torch.no_grad():
                for param, target_param in zip(self.critic.parameters(), self.critic_target.parameters()):
                    target_param.data.copy_(self.tau * param.data + (1 - self.tau) * target_param.data)

                for param, target_param in zip(self.actor.parameters(), self.actor_target.parameters()):
                    target_param.data.copy_(self.tau * param.data + (1 - self.tau) * target_param.data)

        return [actor_loss.item() if actor_loss is not None else None, critic_loss.item() if critic_loss is not None else None]

    def update_policy_noise(self):
        self.policy_noise = max(self.policy_noise_min, self.policy_noise - (1.0 - self.policy_noise_min) * self.policy_noise_decay)

    def save(self,filename):
        torch.save(self.critic.state_dict(), filename + "_critic")
        torch.save(self.critic_optimizer.state_dict(), filename + "_critic_optimizer")
		
        torch.save(self.actor.state_dict(), filename + "_actor")
        torch.save(self.actor_optimizer.state_dict(), filename + "_actor_optimizer")

    def load(self, filename):
        self.critic.load_state_dict(torch.load(filename + "_critic"))
        self.critic_optimizer.load_state_dict(torch.load(filename + "_critic_optimizer"))
        self.critic_target = copy.deepcopy(self.critic)

        self.actor.load_state_dict(torch.load(filename + "_actor"))
        self.actor_optimizer.load_state_dict(torch.load(filename + "_actor_optimizer"))
        self.actor_target = copy.deepcopy(self.actor)


def seed_torch(seed):
    np.random.seed(seed)
    random.seed(seed)
    torch.manual_seed(seed)
    if torch.backends.cudnn.enabled:
        torch.backends.cudnn.benchmark = False
        torch.backends.cudnn.deterministic = True

            
        