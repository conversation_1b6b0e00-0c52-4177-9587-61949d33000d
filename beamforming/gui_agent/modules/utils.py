import numpy as np
import torch
import pdb


class ReplayBuffer(object):
	def __init__(self, state_dim,  action_dim ,nUE, max_size=int(1e6)):
		self.max_size = max_size
		self.ptr = 0
		self.size = 0
		
		self.state = np.zeros((max_size, nUE, state_dim))
		self.possibility = np.zeros((max_size, nUE,8))
		self.action = np.zeros((max_size, nUE,action_dim))
		self.next_state = np.zeros((max_size, nUE, state_dim))
		self.reward = np.zeros((max_size, nUE))
		self.not_done = np.zeros((max_size, 1))

		self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")



	def add(self, state, possibility, action, next_state, reward, done):
		self.state[self.ptr] = state
		if self.possibility[self.ptr].size == possibility.size:
			self.possibility[self.ptr] = possibility
		else: 
			pdb.set_trace()
		self.action[self.ptr] = action
		self.next_state[self.ptr] = next_state
		self.reward[self.ptr] = reward
		self.not_done[self.ptr] = 1. - done

		self.ptr = (self.ptr + 1) % self.max_size
		self.size = min(self.size + 1, self.max_size)


	def sample(self, batch_size):
		ind = np.random.randint(0, self.size, size=batch_size)

		return (
			torch.FloatTensor(self.state[ind]).to(self.device),
			torch.FloatTensor(self.possibility[ind]).to(self.device),
			torch.FloatTensor(self.action[ind]).to(self.device),
			torch.FloatTensor(self.next_state[ind]).to(self.device),
			torch.FloatTensor(self.reward[ind]).to(self.device),
			torch.FloatTensor(self.not_done[ind]).to(self.device)
		)
