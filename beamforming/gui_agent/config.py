import argparse
import numpy as np

def parse_args(args=None):
    # ++ Basic Configs ++
    parser = argparse.ArgumentParser(description="BM")
    parser.add_argument("--desc", type=str, default="No desc", help="train desc")
    # parser.add_argument("--seed", type=int, default=777, help="random seed")
    parser.add_argument("--seed", type=int, default=1215, help="random seed")
    parser.add_argument("--device", type=str, default="cuda:0", help="train on device")
    
    # ++ Env Configs ++
    # parser.add_argument("--env_path", type=str, default="/data/BM/420UE_seed101_8000_0925_VH/", help="path of dataset")
    # parser.add_argument("--env_path", type=str, default="/data/BM/420UE_seed100_2000_0921_VH/", help="path of dataset")
    # parser.add_argument("--env_path", type=str, default="/data/BM_data/28GHZ/sakura/UMa/", help="path of dataset")
    parser.add_argument("--env_path", type=str, default="/home/<USER>/quadriga/inf_v0/", help="path of dataset")
    parser.add_argument("--env_nsub", type=int, default=17, help="num of sub")
    parser.add_argument("--env_w1", type=float, default=0, help="w1")
    parser.add_argument("--env_max_time", type=int, default=400, help="env max time")
    parser.add_argument("--env_max_length", type=int, default=400, help="env max length")
    parser.add_argument("--env_enable_load", type=int, default=1, help="enable load(1-True 0-False)")
    parser.add_argument("--env_H_ants_num", type=int, default=8, help="num of H-ants")
    parser.add_argument("--env_V_ants_num", type=int, default=4, help="num of V-ants")
    # parser.add_argument("--best_action_pkl", type=str, default="/data/BM/420UE_seed100_2000_0921_VH/seed_20021009_time_200_len_800_lenaction_18_best.pkl")
    parser.add_argument("--best_action_pkl", type=str, default="/data/BM_data/28GHZ/sakura/UMa")
    

    # ++ Train Configs ++
    # parser.add_argument("--ue", nargs='+', default=[80, 231, 105, 132, 133, 177, 178, 247, 280, 257, 351,\
    # 267, 76, 53, 161, 161, 170, 79, 146, 117, 88, 161, 383,\
    # 414, 169, 268, 86, 100, 291, 156, 79, 68, 192, 122, 238, 386, 68,\
    # 227, 221, 208, 183, 415, 366, 112, 100, 303, 269, 22, 375, 268,\
    # 238, 395, 183, 390, 215, 156, 419, 108, 287, 302, 268, 273, 39,\
    # 32, 167, 339, 296, 120, 149, 378, 215, 250, 74, 312, 83],
    # help="train ue")
    # parser.add_argument("--ue", nargs='+', default=[i for i in range(100)],help="train ue")
    # parser.add_argument("--ue", nargs='+', default=[1,4,13,16,21,23,24,25,27,28,29,31,36,53,68,73,75,77,80,81,87,89,91,93,94],
    # help="train ue")
    ues1 = [2, 26, 33, 109, 268, 304, 328, 374, 466, 535, 592, 599, 676, 779, 844, 947, 1011, 1022, 1061, 1130, 1175, 1183, 1292, 1307, 1359, 1380, 1419, 1440, 1447, 1504, 1511, 1512, 1569, 1647, 1658, 1779, 1798, 1897]
    ues2 = [99, 102, 174, 181, 275, 303, 312, 319, 333, 336, 363, 430, 589, 605, 641, 731, 752, 792, 871, 892, 976, 982, 1080, 1092, 1198, 1267, 1397, 1423, 1517, 1534, 1543, 1580, 1586, 1682, 1695, 1769, 1807, 1865, 1889, 1916, 1952]
    
    ues_98 = [2, 26, 33, 81, 94, 103, 104, 109, 145, 148, 234, 243, 247, 248, 265, 268, 288, 304, 322, 328, 374, 410, 416, 442, 466, 477, 501, 504, 520, 535, 592, 599, 645, 668, 676, 701, 730, 744, 753, 774, 779, 811, 819, 838, 844, 847, 849, 858, 874, 933, 947, 958, 967, 981, 1003, 1011, 1012, 1013, 1022, 1044, 1061, 1085, 1097, 1100, 1130, 1136, 1175, 1183, 1184, 1208, 1213, 1217, 1252, 1292, 1307, 1353, 1359, 1380, 1399, 1417, 1419, 1432, 1440, 1447, 1461, 1483, 1504, 1511, 1512, 1538, 1569, 1574, 1601, 1623, 1647, 1658, 1733, 1743, 1779, 1798, 1833, 1893, 1897, 1909, 1925, 1931, 1967, 1972, 1986, 1997]
    ues_120 = [0, 1, 7, 9, 31, 48, 54, 66, 67, 82, 85, 86, 96, 99, 102, 143, 156, 162, 164, 174, 181, 183, 185, 186, 197, 223, 236, 237, 242, 250, 260, 275, 280, 286, 303, 306, 312, 315, 319, 323, 333, 336, 348, 363, 391, 395, 402, 413, 419, 430, 437, 460, 487, 488, 494, 509, 515, 525, 532, 536, 539, 564, 576, 589, 593, 595, 605, 625, 640, 641, 663, 681, 683, 684, 696, 714, 731, 749, 752, 769, 790, 791, 792, 799, 826, 829, 832, 842, 850, 869, 871, 872, 887, 892, 900, 902, 907, 919, 926, 949, 951, 976, 982, 1018, 1026, 1051, 1080, 1090, 1092, 1095, 1099, 1106, 1108, 1119, 1169, 1170, 1171, 1177, 1178, 1182, 1194, 1198, 1204, 1212, 1224, 1236, 1250, 1267, 1284, 1287, 1293, 1304, 1315, 1316, 1328, 1329, 1364, 1378, 1386, 1397, 1413, 1415, 1423, 1424, 1428, 1477, 1507, 1517, 1518, 1519, 1534, 1543, 1550, 1558, 1572, 1578, 1580, 1582, 1586, 1591, 1629, 1631, 1645, 1662, 1673, 1679, 1682, 1687, 1694, 1695, 1732, 1739, 1740, 1741, 1744, 1747, 1754, 1766, 1769, 1770, 1773, 1778, 1785, 1792, 1795, 1800, 1806, 1807, 1813, 1826, 1832, 1845, 1848, 1849, 1862, 1865, 1871, 1885, 1889, 1898, 1900, 1907, 1911, 1916, 1920, 1922, 1928, 1952, 1953, 1957, 1977, 1979, 1990]
    
    
    # ues = np.sort(ues_98+ues_120)
    # ues = np.sort(ues1+ues2)
    # ues = np.sort(ues_98+ues_120+[i for i in range(400)])
    # ues = np.unique(ues)
    ues = np.arange(400)
    parser.add_argument("--ue", nargs='+', default=list(ues),
    help="train ue")

    parser.add_argument("--nUE", type=int, default=20, help="batch ue")
    parser.add_argument("--results", type=str, default="abc", help="results")
    parser.add_argument("--results_dir", type=str, default="/data/setsumi/beamforming/gui_agent/analyze_train/results/", help="results_dir")
    parser.add_argument("--num_frame", type=int, default=1000000, help="how many times agent train")
    
    # ++ TD3 Configs ++
    parser.add_argument("--start_timesteps", default=25e2, type=int)
    parser.add_argument("--eval_freq", default=5e2, type=int)       
    parser.add_argument("--expl_noise", default=0.1)                
    parser.add_argument("--discount", default=0.99)  
    parser.add_argument("--pi_lr", default=1e-3)   
    parser.add_argument("--q_lr", default=1e-3)                  
    parser.add_argument("--tau", default=0.005)                    
    parser.add_argument("--policy_noise", default=0.2)
    parser.add_argument("--policy_noise_decay", default=0.2)
    parser.add_argument("--policy_noise_min", default=0.2)             
    parser.add_argument("--noise_clip", default=1)                
    parser.add_argument("--policy_freq", default=2, type=int)       
    
    # ++ DQN Configs ++
    parser.add_argument("--memory_size", type=int, default=500000, help="DQN replay memory size")
    parser.add_argument("--max_epsilon", type=float, default=1, help="max epsilon")
    parser.add_argument("--min_epsilon", type=float, default=0.1, help="min epsilon")
    parser.add_argument("--epsilon_decay", type=float, default=1/2000, help="epsilon decay")
    parser.add_argument("--batch_size", type=int, default=10, help="get from memory pool")

    
    # ++ Optim Configs ++
    parser.add_argument("--gamma", type=float, default=0.9, help="gamma")
    parser.add_argument("--lr", type=float, default=1e-4, help="learning rate")
    parser.add_argument("--optim_frame", type=int, default=1, help="update one time every N frame")
    parser.add_argument("--target_update_frame", type=int, default=200, help="update target net every N frame")
    parser.add_argument("--change_UEs_cycle", type=int, default=10000, help="every N step change UEs")
    
    # ++ Log Config ++
    parser.add_argument("--train_log_cycle", type=int, default=100, help="every N step log")

    # ++ TimeFreeze Config ++
    parser.add_argument("--timefreeze_cycle", type=int, default=100, help="time freeze cycle")
    
    # ++ done config ++
    parser.add_argument("--done_cycle", type=int, default=100, help="done cycle")
    
    if args is None:
        # 从命令行读取参数
        parsed_args = parser.parse_args()
    else:
        # 使用 parse_known_args 传递参数列表
        parsed_args, _ = parser.parse_known_args(args)
    
    return parsed_args
