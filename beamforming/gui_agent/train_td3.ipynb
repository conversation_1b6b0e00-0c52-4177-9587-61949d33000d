import config
import random, torch
import numpy as np
import datetime
import logging
import os, sys
from modules.D3QN import D3QN
from torch.utils.tensorboard import SummaryWriter
from modules.extract_modules import State, GuiNet, GuiNet_conv,GuiNet_CSI
from inference import get_best_action,global_ue_num
from torchstat import stat
from pretrain import train,train_env_init
from pretrain_td3 import train_td3,inference_td3,train_env_init_td3
sys.path.append("/../")
sys.path.append("/../../")
from Beam_env.BMenv import BMenv
from setsumi_env.setsumi_env import setsumi_env
import pdb
import pickle
from collections import deque

from setsumi_env.BM_functions import *
from setsumi_env.system_functions import *
from setsumi_env.process import *
from setsumi_env.analyze import *
from setsumi_env.utils import *
from setsumi_env.setsumi_env import setsumi_env
import pandas as pd
from collections import Counter
import plotly.graph_objects as go
from plotly.subplots import make_subplots

from scipy import stats
import copy

import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from IPython.display import Image
from matplotlib.gridspec import GridSpec
import time
from datetime import datetime
from scipy.signal import savgol_filter

args = config.parse_args(['--results','abc'])
logger = train_env_init(args)

env = setsumi_env(logger=logger,path=args.env_path,max_time=args.env_max_time,max_length=args.env_max_length,seed=args.seed,enable_load=args.env_enable_load,w1=0.8)
env.load_data(args.ue)

args.timefreeze_cycle = 1
args.done_cycle = 2000
args.num_frame = 2000

args.noise_clip = 1
args.policy_freq = 3
args.policy_noise = 1
args.policy_noise_decay = 1/1000
args.policy_noise_min = 0

args.nUE = 2

args.pi_lr = 1e-4
args.q_lr = 1e-4

env.SNR_idx = 4

update_frames = [100,150,200,250,300]
loss_data = []
reward_data = []
lrs = [0.1,0.01,0.001,0.0001]

timefreeze_cycles = [1]
logger = train_env_init_td3(args)
results = train_td3(args,logger,env,False)
actions = np.array(results["action"])
times = np.array(results["time"])
UEs = np.array(results["UE"])
actor_loss = np.array(results['actor_loss'])
critic_loss = np.array(results['critic_loss'])
reward = np.array(results['reward'])

actions = np.array(results["action"])
print(actions.shape)

for j in range(5):
    fig = draw_line("best ac","训练次数","奖励")
    actions = np.array(results["action"])
    actions = actions[:,:,j]
    for i in range(args.nUE):
        y = actions[:,i]
        fig.add_line(x=np.arange(len(y)),y=y,name="",mode="lines")
    fig.fig.show()

fig = draw_line("best ac","训练次数","奖励")
y = np.average(reward,axis=-1)
fig.add_line(x=np.arange(len(y)),y=y,name="",mode="lines")
fig.fig.show()

fig = draw_line("best ac","训练次数","奖励")
y = critic_loss
fig.add_line(x=np.arange(len(y)),y=y,name="",mode="lines")
fig.fig.show()

fig = draw_line("best ac","训练次数","奖励")
actor_loss[0] = 0
actor_loss = fill_none_with_previous(actor_loss)
y = actor_loss
fig.add_line(x=np.arange(len(y)),y=y,name="",mode="lines")
fig.fig.show()

def fill_none_with_previous(arr):
    prev_value = None
    for i, value in enumerate(arr):
        if value is None and prev_value is not None:
            arr[i] = prev_value
        else:
            prev_value = value
    return arr