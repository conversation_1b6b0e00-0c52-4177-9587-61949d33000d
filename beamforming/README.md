# Baseline

Score: 0.8840232523420183 (This score was gived by new criterion :fire:)

![](Figure/baseline.png)

## Updates

+ Only input PMI (Remove Beam feature)
+ For stability, we create a new criterion for score judging. Please see `gui_agent/inference.py` for details. (So the scores judged in the past are out-of-date)

## Model Judger

### Usage

`python gui_agent/model_judger.py --path /home/<USER>/bm/beamforming/gui_agent/models/BM_2022-11-03-09-08-18_frame550000_score0.8840232523420183.gg`

It will generate a html file `model_judger01.html` which shows the difference between Model Inference Score and Baseline1 Score in your workspace.

## Baseline Model
x
`Baseline/BM_2022-11-03-09-08-18_frame550000_score0.8840232523420183.gg`

`Baseline/BM_2022-11-03-09-08-18.log`

## Net Code

```python
class GuiNet(torch.nn.Module):
    '''
    Structure:
        PMI  \
               HV_features                             \
        Beam /                                          \
                                                          joint_layer
        RI   \                                          /
        CQI  - layer_hiden_layer1 - layer_hiden_layer2 /
        TP   /

    '''
    def __init__(self, HV_num=2, Layer_num=4):
        super().__init__()
        self.PMI_to_features = torch.nn.Linear(2, 8)
        # self.Beam_to_features = torch.nn.Linear(16, 8)
        self.HV_features = torch.nn.Linear(8, 32)
        self.RI_to_features = torch.nn.Linear(4, 16)
        self.CQI_to_features = torch.nn.Linear(17, 16)
        self.TP_to_features = torch.nn.Linear(17, 16)
        self.layer_hiden_layer1 = torch.nn.Linear(48, 128)
        self.layer_hiden_layer2 = torch.nn.Linear(128, 256)
        self.joint_layer = torch.nn.Linear(288, 18)
        self.relu = torch.nn.ReLU()
        
    def forward(self, state):
        PMI_features = self.PMI_to_features(state.PMI)
        # Beam_features = self.Beam_to_features(state.beam_features)
        # HV_feature = self.relu(self.HV_features(self.relu(torch.cat((PMI_features, Beam_features), dim=-1))))
        HV_feature = self.relu(self.HV_features(self.relu(PMI_features)))
       
        RI_features = self.RI_to_features(state.RI)
        CQI_features = self.CQI_to_features(state.CQI)
        TP_features = self.TP_to_features(state.TP)
        layer_features = torch.cat((RI_features, CQI_features, TP_features), dim=-1)
        layer_value = (self.relu(self.layer_hiden_layer2(self.relu(self.layer_hiden_layer1(layer_features)))))
        joint_value = self.joint_layer(torch.cat((layer_value, HV_feature), dim=-1))
        return joint_value
```

