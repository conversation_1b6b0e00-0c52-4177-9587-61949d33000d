from copy import deepcopy
import os,sys
sys.path.append(os.getcwd())
from typing import Tuple, List
import numpy as np
from Beam_env.utils.generate_beam_pattern import generate_beam_pattern
from Beam_env.utils.sinr2mib import Mib2Sinr, Sinr2Mib
from Beam_env.utils.get_sinr import get_sinr
from Beam_env.utils.generate_beam import generate_dft_beams
from Beam_env.utils.PMI import PMI
from Beam_env.utils.mcs2qam import Mcs2Qam
from Beam_env.utils.mib2bler import Mib2Bler
from Beam_env.utils.cqi2sinr import Sinr2Cqi
from Beam_env.utils.throughput import TbCalc
import pdb
import time
import pickle

class BMenv:
    def __init__(self, logger=print, path="./Beam_env/Data/21UE_400TTL_seed100/",nSub = 17,H_ant_num = 8,V_ant_num = 2,max_time = 200,max_length = 800,seed = 777,enable_load = True) -> None:
        # Parameters
        self.logger = logger
        self.UE_ttl = 1 # change to 20 tti collect once
        self.gNB_ttl = 2 # change to 20 tti collect once
        self.Beam_ttl = 2
        self.nCELL = 21
        self.nSub = nSub
        self.H_ant_num = H_ant_num
        self.V_ant_num = V_ant_num
        self.Ants_num = self.H_ant_num*self.V_ant_num
        self.UE_port = 4 
        self.max_time = max_time
        self.max_length = max_length
        self.length = 0
        self.time = 0
        self.alpha = 0.75 # coma_old*alpha + coma_new*(1-alpha)
        self.enable_load = enable_load
        self.test = False
        self.sinr_min = 0
        self.sinr_max_diff = 15
        self.choose_sub_port = []
        self.seed = seed

        # Load Data
        self.path = path
        if self.logger is not None:
            self.logger("Load begin")
        self.loc = np.load(self.path+"loc.npz",allow_pickle=True)['arr_0']
        self.ue = np.load(self.path+"ue.npz",allow_pickle=True)['arr_0'] 
        self.ue = self.ue[0,:].astype(int)
        self.height = np.load(self.path+"height.npz",allow_pickle=True)['arr_0']
        self.total_UE = self.loc.shape[0]
        self.H_SRS = np.load(self.path+"Channel.npz",allow_pickle=True)['arr_0']
        self.H_SRS = self.H_SRS.reshape((-1,self.total_UE,self.nSub,self.Ants_num*2,self.UE_port,2)) # (总时长, UE, 子载波, 总天线数量*2, 天线端口=4, 2)
        self.H_SRS = self.H_SRS[:,:,:,:,:,0] + 1j*self.H_SRS[:,:,:,:,:,1]
        self.R = np.load(self.path+"R.npz",allow_pickle=True)['arr_0']
        if self.logger is not None:
            self.logger("Load data finish")

        self.cell = [[] for i in range(0,21)]
        for i in range(0,self.total_UE):
            self.cell[self.ue[i]-1].append(i)
        
        self.H_SRS = self.H_SRS.transpose(0,1,2,4,3) 
        self.max_tti = self.H_SRS.shape[0] # max_tti = time

        #transpose ants order form H,V to V,H
        self.H_SRS_pol1 = self.H_SRS[:,:,:,:,0:self.Ants_num] #time*nUE*nSub*UE_port*nAnts
        shape = self.H_SRS_pol1.shape
        self.H_SRS_pol1 = self.H_SRS_pol1.reshape(shape[0],shape[1],shape[2],shape[3],self.H_ant_num,self.V_ant_num)
        self.H_SRS_pol1 = self.H_SRS_pol1.transpose(0,1,2,3,5,4)
        self.H_SRS_pol1 = self.H_SRS_pol1.reshape(shape[0],shape[1],shape[2],shape[3],self.H_ant_num*self.V_ant_num)

        self.H_SRS_pol2 = self.H_SRS[:,:,:,:,self.Ants_num:self.Ants_num*2] #time*nUE*nSub*UE_port*nAnts
        self.H_SRS_pol2 = self.H_SRS_pol2.reshape(shape[0],shape[1],shape[2],shape[3],self.H_ant_num,self.V_ant_num)
        self.H_SRS_pol2 = self.H_SRS_pol2.transpose(0,1,2,3,5,4)
        self.H_SRS_pol2 = self.H_SRS_pol2.reshape(shape[0],shape[1],shape[2],shape[3],self.H_ant_num*self.V_ant_num)

        # H_SRS_pol1   H_SRS_pol2
        self.H_SRS[:,:,:,:,0:self.Ants_num] = self.H_SRS_pol1
        self.H_SRS[:,:,:,:,self.Ants_num:self.Ants_num*2] = self.H_SRS_pol2
        
        self.Coma_Long =  [] #np.zeros((self.total_UE,self.Ants_num,self.Ants_num),dtype=np.complex128) #nUE*Ant*Ant
        self.Coma_Short = [] #np.zeros((self.total_UE,self.Ants_num,self.Ants_num),dtype=np.complex128) #ant*ant
        self.gNB_SRS_cur =  [] #np.zeros((self.total_UE,self.UE_port,self.Ants_num*2),dtype=np.complex128)

        # Init Common Data
        self.Beams = generate_dft_beams(numVant=self.V_ant_num,numHant=self.H_ant_num) # shape ant_num*beam_num or beam_num*ant_num
        self.Beam_angle = np.array([[-16,16,-15,15,-14,14,-13,13,-13,13,-14,14,-15,15,-16,16],[-54,-54,-36,-36,-21,-21,-7,-7,7,7,21,21,36,36,54,54]])
        self.angle_H_max = np.max(np.abs(self.Beam_angle[0]))
        self.angle_V_max = np.max(np.abs(self.Beam_angle[1]))
        self.Beam_angle = self.Beam_angle.transpose(1,0)

        self.PMI_class = PMI()
        self.sinr2mi = Sinr2Mib()
        self.mi2sinr = Mib2Sinr()
        self.mcs2qam = Mcs2Qam()
        self.mi2bler = Mib2Bler()
        self.sinr2cqi = Sinr2Cqi()
        self.sinr2mcs_table = np.array([-5.23,-4.7,-3.7,-3.18,-2.45,-1.6,-0.88,-0.12,
                                        0.79,1.38,1.88,2.7,3.65,4.51,5.24,5.88,
                                        6.75,7.29,8.23,9.14,9.92,11.09,11.86,12.93,
                                        14.14,14.98,15.91,17.15])

        self.PMI_map = np.load("/data/BM/H2V8_PMI_angle_actionlen_18_without_0_1.npz",allow_pickle=True)['arr_0']
        # Init PRB config
        self.nPRB = 272
        self.nRBG = 17 
        self.nBS_RF_CHAIN = self.Ants_num
        self.PRBpreSB = [self.nBS_RF_CHAIN for i in range(self.nRBG-1)] + [self.nPRB-self.nBS_RF_CHAIN*(self.nRBG-1)]  # PRB of each subband
        self.nPRB_PDSCH = 156
        self.nREAvg = min(156, self.nPRB_PDSCH)
        
        self.nUE = self.total_UE
        self.Beam_idx1 = np.zeros((self.max_length,self.nUE),dtype = int)
        self.Beam_idx2 = np.zeros((self.max_length,self.nUE),dtype = int)
        self.Beam_powers = np.zeros((self.max_length,self.nUE,self.Ants_num),dtype = np.float64) 
        self.Beam_power1 = np.zeros((self.max_length,self.nUE),dtype = np.float64) 
        self.Beam_power2 = np.zeros((self.max_length,self.nUE),dtype = np.float64)
        self.gNB_SRS = np.zeros((self.max_length,self.nUE,self.UE_port,self.Ants_num*2),dtype = np.complex128) 
        self.time_idx = np.zeros((self.max_length,self.nUE),dtype = int)
        self.ue_idx = np.zeros((self.max_length,self.nUE),dtype = int)

        # H_spilt,V_spilt,RI
        self.action_space = [(1,1,1),(1,1,2),\
                             (2,1,1),(2,1,2),\
                             (2,1,3),(2,1,4),\
                             (1,2,1),(1,2,2),\
                             (1,2,3),(1,2,4),\
                             (2,2,1),(2,2,2),\
                             (2,2,3),(2,2,4),\
                             (1,4,1),(1,4,2),\
                             (1,4,3),(1,4,4)] 

        self.report_RI = np.zeros((self.max_length,self.nUE,len(self.action_space)),dtype=int)
        self.report_PMI = np.zeros((self.max_length,self.nUE,len(self.action_space),4),dtype=int)
        self.report_CQI = np.zeros((self.max_length,self.nUE,len(self.action_space),self.nSub),dtype=int)
        self.report_TP = np.zeros((self.max_length,self.nUE,len(self.action_space),self.nSub),dtype=np.float64)
        self.UE_TP = np.zeros((self.max_length,self.nUE,5,4),dtype=np.float64) # -,-,N_PATTERN,N_RANK

        self.sequential_simulate()
        self.load_report()


    def sequential_simulate(self):
        result_path = self.path+"seed_"+str(self.seed)+"_time_"+str(self.max_time)+"_len_"+str(self.max_length)
        if(os.path.exists(result_path+".npz") and self.enable_load):
            if self.logger is not None:
                self.logger(f"load result from {result_path}.npz")
            (self.Beam_idx1,self.Beam_idx2,self.Beam_power1,self.Beam_power2,self.gNB_SRS,self.gNB_SRS_cur,self.time_idx,self.ue_idx) = np.load(result_path+".npz",allow_pickle=True)['arr_0'] 
        else:
            if self.logger is not None:
                self.logger(f"Start to simulating. (Because this dataset has not pre result)")
            while self.length < self.max_length:  
                if  self.time%self.gNB_ttl == 0:
                    self.update_gNB_SRS()
                if  self.time%self.Beam_ttl == 0:
                    idx1,power1,idx2,power2,Beam_Power = self.update_Beam()
                    self.Beam_idx1[self.length,:] = idx1
                    self.Beam_power1[self.length,:] = power1
                    self.Beam_idx2[self.length,:] = idx2
                    self.Beam_power2[self.length,:] = power2
                    self.Beam_powers[self.length,:,:] = Beam_Power
                    self.time_idx[self.length,:] = self.time
                    self.ue_idx[self.length,:] = np.arange(0,self.nUE)
                    self.gNB_SRS[self.length,:,:,:] =  self.gNB_SRS_cur
                    self.length += 1
                self.time += 1
                if self.time == self.max_time:
                    self.time = 0
            if self.logger is not None:
                self.logger(f"Simulating stop. Store result to {result_path}.")
            data = (self.Beam_idx1,self.Beam_idx2,self.Beam_power1,self.Beam_power2,self.gNB_SRS,self.gNB_SRS_cur,self.time_idx,self.ue_idx)
            data = np.array(data,dtype=object)
            # np.savez(result_path,data)
            

    def load_report(self):
        result_path = self.path+"seed_"+str(self.seed)+"_time_"+str(self.max_time)+"_len_"+str(self.max_length)+"_lenaction_"+str(len(self.action_space))
        if(os.path.exists(result_path+"_report.npz") and self.enable_load):
            if self.logger is not None:
                self.logger(f"load report result from {result_path}_report.npz")
            (self.report_RI,self.report_PMI,self.report_CQI,self.report_TP,self.gNB_SRS_cur) = np.load(result_path+"_report.npz",allow_pickle=True)['arr_0'] 
            result_path = self.path+"seed_"+str(self.seed)+"_time_"+str(self.max_time)+"_len_"+str(self.max_length)
            self.Beam_powers = np.load(result_path+"_BeamPowers.npz",allow_pickle=True)['arr_0'][0]

        else:
            report_data = (self.report_RI,self.report_PMI,self.report_CQI,self.report_TP,self.gNB_SRS_cur)
            report_data = np.array(report_data,dtype=object)
            # np.savez(result_path+"_report.npz", report_data)

    def update_gNB_SRS(self):
        total_nsub_ueport = self.nSub*self.UE_port # nSub*UE_Port  17*4
        if not np.array(self.choose_sub_port).any():
            self.choose_sub_port = []
            for i in range(self.nUE):
                self.choose_sub_port.append(list(np.arange(0,total_nsub_ueport)))
        UE_list = list(np.arange(0,self.nUE))
        rand_sub = []
        rand_port = []
        for ue in range(self.nUE):
            sub_port = np.random.choice(self.choose_sub_port[ue])
            self.choose_sub_port[ue].remove(sub_port)
            rand_sub.append(sub_port//self.UE_port)
            rand_port.append(sub_port%self.UE_port)

        srs_p1 = self.H_SRS_pol1[self.time,UE_list,rand_sub,rand_port,:] # (nUE, nAnts)
        srs_p2 = self.H_SRS_pol2[self.time,UE_list,rand_sub,rand_port,:] # (nUE, nAnts)
        if(len(self.gNB_SRS_cur) == 0):
            self.gNB_SRS_cur = self.H_SRS[self.time,UE_list,rand_sub,:,:]
        self.gNB_SRS_cur[UE_list,rand_port,:] = self.H_SRS[self.time,UE_list,rand_sub,rand_port]
        srs_p1 = np.expand_dims(srs_p1,-2)
        srs_p2 = np.expand_dims(srs_p2,-2)
        
        #!coma srs.H@srs 
        # srs_p1.H@srs_p1
        # Coma (nUE, nAnts ,nAnts)
        Coma_p1 = np.conj(srs_p1.transpose(0,2,1))@srs_p1     #srs.H@srs
        Coma_p2 = np.conj(srs_p2.transpose(0,2,1))@srs_p2     #srs.H@srs

        Tra_p1 = np.trace(Coma_p1,axis1=-2,axis2=-1)
        Tra_p2 = np.trace(Coma_p2,axis1=-2,axis2=-1)
        a1 = np.divide(Tra_p1,Tra_p1+Tra_p2+1e-50)
        a2 = np.divide(Tra_p2,Tra_p1+Tra_p2+1e-50)
        Coma_Short = Coma_p1*np.expand_dims(a1,axis=(-1,-2)) + Coma_p2*np.expand_dims(a2,axis=(-1,-2))
        self.Coma_Short = Coma_Short
        if(len(self.Coma_Long) == 0):
            self.Coma_Long = self.Coma_Short
        else:
            self.Coma_Long = self.alpha*self.Coma_Long + (1-self.alpha)*Coma_Short

    def update_Beam(self):
        Coma = self.Coma_Long
        Beams = np.expand_dims(self.Beams,2) #nBeam*n_ant*1\

        Beam_ = np.expand_dims(Beams,0)
        Coma_ = np.expand_dims(Coma,1)
        Beam_power = np.conj(Beam_.transpose(0,1,3,2))@Coma_@Beam_ #Beam.H@Coma@Beam
        Beam_power = Beam_power.squeeze((-1,-2))
        
        Coma_norm = np.linalg.norm(Coma,axis=(-1,-2)) #nUE
        Beams_norm = np.linalg.norm(Beams,axis=(-1,-2)) #nBeam
        Beam_power = np.abs(Beam_power/np.expand_dims(Coma_norm,1)/np.expand_dims(Beams_norm,0)) #
        Beam_power_copy = deepcopy(Beam_power)
        idx1 = np.argmax(Beam_power,axis=1)
        power1 = Beam_power[np.arange(0, self.nUE),idx1]
        Beam_power[np.arange(0, self.nUE),idx1] = Beam_power[np.arange(0, self.nUE),idx1]*0
        idx2 = np.argmax(Beam_power,axis=1)
        power2 = Beam_power[np.arange(0, self.nUE),idx2]
        return idx1,power1,idx2,power2,Beam_power_copy

    def get_state_batch(self,batch,time,ue,action) -> List[np.float64]:
        
        PMI = np.zeros((batch,2),dtype=np.float64)
        CQI = np.zeros((batch,self.nSub),dtype=np.float64)
        RI = np.zeros((batch),dtype=np.float64)
        RI_train = np.zeros((batch, 4),dtype=np.float64) # RL_train use one-hot
        TP = np.zeros((batch,self.nSub),dtype=np.float64)
        TP_div = np.zeros((batch),dtype=np.float64)
        
        for i in range(0,batch):
            if(self.report_TP[time[i],ue[i],action[i],0] > 0): 
                PMI_rp = self.report_PMI[time[i],ue[i],action[i]]
                CQI_rp = self.report_CQI[time[i],ue[i],action[i]]
                RI_rp = self.report_RI[time[i],ue[i],action[i]]
                TP_rp = self.report_TP[time[i],ue[i],action[i]]
            else:
                H_split = self.action_space[action[i]][0]
                V_split = self.action_space[action[i]][1]
                RI_action = self.action_space[action[i]][2]
                N1 = max(V_split,H_split)           
                N2 = min(V_split,H_split)
                PMI_rp,CQI_rp,RI_rp,TP_rp = self.get_report(time[i],ue[i],V_split,H_split,N1,N2,RI_action)
                self.UE_TP[time[i],ue[i],int((action[i]+2)/4),:] = self.UE_TP_all
                self.report_PMI[time[i],ue[i],action[i]] = PMI_rp
                self.report_CQI[time[i],ue[i],action[i]] = CQI_rp
                self.report_RI[time[i],ue[i],action[i]] = RI_rp
                self.report_TP[time[i],ue[i],action[i]] = TP_rp
                
            H_split = self.action_space[action[i]][0]
            V_split = self.action_space[action[i]][1]
            if not (H_split == 1 and V_split == 1 and RI_rp <= 2):
                action_ = self.action_space.index((H_split, V_split, RI_rp))
                PMI[i,:] = np.var(self.PMI_map[action_-2][self.Beam_idx1[time[i],ue[i]], PMI_rp[0], PMI_rp[1], PMI_rp[2], PMI_rp[3]], axis=0)
            PMI[i,:] = PMI[i,:]/(np.max(PMI[i,:]) + 1e-7)
            CQI[i,:] = CQI_rp
            RI[i] = RI_rp
            RI_train[i, RI_rp - 1] = 1
            TP[i,:] = np.array(TP_rp)
            TP_best = np.max(np.sum(self.report_TP[time[i],ue[i]],axis=-1))
            TP_div_best = np.sum(TP_rp)/TP_best
            TP_div[i] = TP_div_best
            
        # Channel Quality Features
        self.RI = RI
        self.TP = np.sum(TP,axis=1)
        self.reward = TP_div

        if(self.test):
            return None,PMI,CQI,RI,TP
        CQI = np.expand_dims(CQI,axis=[-2,-1]) /15.0 
        TP = np.expand_dims(TP,axis=[-2,-1]) /100000.0 
        # UE Position Features
        PMI = np.expand_dims(PMI,axis=[-2,-1])
        Beam1_angle = self.Beam_angle[self.Beam_idx1[list(time),list(ue)],:] / np.array([self.angle_H_max,self.angle_V_max]) 
        Beam2_angle = self.Beam_angle[self.Beam_idx2[list(time),list(ue)],:] / np.array([self.angle_H_max,self.angle_V_max]) 
        Beam1_pwr = np.expand_dims(self.Beam_power1[list(time),list(ue)],axis=-1) / 4.0
        Beam2_pwr = np.expand_dims(self.Beam_power2[list(time),list(ue)],axis=-1) / 4.0
        Beam1_fe = np.expand_dims(np.concatenate([Beam1_angle,Beam1_pwr], axis=-1), -1)
        Beam2_fe = np.expand_dims(np.concatenate([Beam2_angle,Beam2_pwr], axis=-1), -1)
        Beam_feature = np.expand_dims(np.concatenate([Beam1_fe, Beam2_fe], axis=-1), -1)
        Beam_powers = self.Beam_powers[list(time),list(ue),:] / 4.0
        
        return [Beam_powers,PMI,CQI,RI_train,TP]

    def save_report(self):
        result_path = self.path+"seed_"+str(self.seed)+"_time_"+str(self.max_time)+"_len_"+str(self.max_length)+"_lenaction_"+str(len(self.action_space))
        report_data = (self.report_RI,self.report_PMI,self.report_CQI,self.report_TP,self.gNB_SRS_cur)
        report_data = np.array(report_data,dtype=object)
        np.savez(result_path+"_report.npz",report_data)
    
    def save_best_action(self, best_actions, best_tp, ue_action):
        result_path = self.path+"seed_"+str(self.seed)+"_time_"+str(self.max_time)+"_len_"+str(self.max_length)+"_lenaction_"+str(len(self.action_space))
        report_data = (best_actions,best_tp,ue_action)
        import pickle
        a_file = open(result_path+"_best.pkl", "wb")
        pickle.dump(report_data, a_file)
        a_file.close()
        print("save best done")

    def load_best_action(self):
        result_path = self.path+"seed_"+str(self.seed)+"_time_"+str(self.max_time)+"_len_"+str(self.max_length)+"_lenaction_"+str(len(self.action_space))
        import pickle
        a_file = open(result_path+"_best.pkl", "rb")
        out = pickle.load(a_file)
        a_file.close()
        print("load best done")
        return out

    def get_report(self,len_idx,ue_idx,V_split,H_split,N1,N2,RI):
        beam_idx = self.Beam_idx1[len_idx,ue_idx] 
        srs = self.gNB_SRS[len_idx,ue_idx] 
        time = self.time_idx[len_idx,ue_idx]
        R = self.R[time,ue_idx]

        # gNB_H->PMI->sinr->MCS->TP->throughput
        gNB_H = self.from_beam_get_H(srs,beam_idx,V_split,H_split)
        # for i in range()
        gNB_H = np.expand_dims(gNB_H,0) #add sub axis
        gNB_PMI,gNB_P,gNB_max_power = self.get_PMI(gNB_H,N1,N2,RI)
        sinr,layer = self.cal_sinr(gNB_H,gNB_P,R)
        mcs = np.searchsorted(self.sinr2mcs_table,sinr)
        
        TP = self.get_tp(mcs,layer,False)
        TP_sub = self.get_tp(mcs,layer,True)
        srs_sub = self.H_SRS[time,ue_idx]
        UE_H = self.from_beam_get_H(srs_sub,beam_idx,V_split,H_split)
        UE_TP_all = np.zeros(4,dtype=np.float64)
        UE_PMI_all = np.zeros((4,4),dtype=int)
        UE_sinr_all = np.zeros((4,self.nSub),dtype=np.float64)
        for i in range(0,4):
            if N1*N2 == 1 and i>1:
                sinr = np.ones((17)) * -50.0
                layer = np.zeros((17))
                mcs = np.zeros((17))
                TP = 0.0
            else:
                PMI,P,max_power = self.get_PMI(UE_H,N1,N2,i+1)
                sinr,layer = self.cal_sinr(UE_H,P,R)
                mcs = np.searchsorted(self.sinr2mcs_table,sinr)
                TP = self.get_tp(mcs,layer,False)
            UE_TP_all[i] = TP
            UE_PMI_all[i,:] = PMI
            UE_sinr_all[i,:] = sinr
        UE_best_RI = np.argmax(UE_TP_all,axis=0)+1
        self.UE_TP_all = UE_TP_all
        UE_PMI = UE_PMI_all[UE_best_RI-1]
        UE_sinr = UE_sinr_all[UE_best_RI-1]
        CQI = self.sinr2cqi(UE_sinr)

        return UE_PMI,CQI,UE_best_RI,TP_sub

    def from_beam_get_H(self,srs,beam_idx,V_split,H_split):
        Ports_vector = generate_beam_pattern(self.V_ant_num,self.H_ant_num,V_split,H_split)
        Beam = self.Beams[beam_idx] #ants
        Beam = np.expand_dims(Beam,-1)
        Beam_Port = Beam*Ports_vector
        Beam_Port = np.kron(np.eye(2),Beam_Port)
        H = srs@Beam_Port
        return H

    def get_PMI(self,H,N1,N2,layer): #ue_port*gnb_port

        O1 = (N1>1)*3+1
        O2 = (N2>1)*3+1

        if N1*N2 == 1:
            idx = (-1,0,0,0)
            if layer == 2:
                P = 1/np.sqrt(2) * np.eye(2,2)
            elif layer == 1:
                P = np.array([[0],[1]], dtype=np.complex128)
            max_power = 0.0
            return idx,P,max_power

        codebook = self.PMI_class.get_pmi_codebook(N1,N2,O1,O2,1,layer)
        H = np.expand_dims(H,(0,1,2,3)) #0,1,2,3,nSub,ue_port,max_gNB_port
        codebook = np.expand_dims(codebook, axis=4)
        HP = H@codebook #0,1,2,3,ue_port,layer
        Layer_power = np.conj(HP.transpose(0,1,2,3,4,6,5))@HP
        HP_power = np.trace(np.abs(Layer_power),axis1=-1,axis2=-2)
        HP_power = np.sum(HP_power,axis = -1)

        idx = np.unravel_index(np.argmax(HP_power),HP_power.shape)
        P = codebook[idx]
        max_power = HP_power[idx]

        return idx,P,max_power
    
    def cal_sinr(self,H,P,R):
        HP = H@P
        sinr = get_sinr(HP.transpose(0,2,1),R)
        sinr[sinr < self.sinr_min] = -50
        if sinr.shape[1] > 1:
            sinr[(sinr[:,0] - sinr[:,1] > self.sinr_max_diff),1] = -50
            sinr[(sinr[:,1] - sinr[:,0] > self.sinr_max_diff),0] = -50
        if sinr.shape[1] > 3:
            sinr[(sinr[:,2] - sinr[:,3] > self.sinr_max_diff),3] = -50
            sinr[(sinr[:,3] - sinr[:,2] > self.sinr_max_diff),2] = -50
        layer = np.sum(sinr > -40,axis = 1)
        layer = np.clip(layer,1,4)
        mi = self.sinr2mi(sinr)
        mi[mi<0.0014] = 0 #min_mi = 0.0013
        avg_mi = np.sum(mi,axis=1)/layer
        sinr_avg = self.mi2sinr(avg_mi)
        return sinr_avg,layer

    def get_tp(self,mcs,layer,sub=False):
        '''
         get a list of UE throughput
        '''
        assert len(self.PRBpreSB) == self.nSub
        if sub: TP = []
        else:   TP = 0.
        for band in range(self.nSub):
            if(len(mcs) == self.nSub):
                mcs_sub = mcs[band]
                layer_sub = layer[band]
            else:
                mcs_sub = mcs[0]
                layer_sub = layer[0]
            if sub: TP.append(TbCalc(mcs_sub, layer_sub, self.PRBpreSB[band], self.nREAvg))
            else:   TP += TbCalc(mcs_sub, layer_sub, self.PRBpreSB[band], self.nREAvg)
        return TP

if __name__ == '__main__':
    ...