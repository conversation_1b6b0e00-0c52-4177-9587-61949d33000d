# -*- encoding:utf-8 -*-
import numpy as np


class MibBlerMapBase:
    def __init__(self, fn=__file__.replace('.py', '.npz'), default_max_rank=64) -> None:
        self.map = {}
        for k, e in np.load(fn, allow_pickle=True).items():
            self.map[int(k)] = e
        self.cur_map = self.map[default_max_rank]

    def set_max_rank(self, rank):
        self.cur_map = self.map[rank]


class Mib2Bler(MibBlerMapBase):
    def __init__(self, *args) -> None:
        super().__init__(*args)

    def __call__(self, mcs, mib, Tr=2):
        return self.cur_map[Tr, mcs, 1, np.searchsorted(self.cur_map[Tr, mcs, 0], mib)]


class Bler2Mib(MibBlerMapBase):
    def __init__(self, *args) -> None:
        super().__init__(*args)

    def __call__(self, mcs, Tr=2):
        return self.cur_map[Tr, mcs, 0, -np.searchsorted(self.cur_map[Tr, mcs, 1, ::-1], 0.1)]

if __name__ == '__main__':
    print(Mib2Bler()(16, 0.3856))
    print(Bler2Mib()(16))
