import numpy as np
from collections import OrderedDict
# from generate_beam import generate_dft_beams
# from port_shaping import generate_available_pattern

def generate_beam_pattern(numVant=4, numHant=8, V_split=1, H_split=2):
    '''
        beam weight: selected beam
        numVant: antenna in vertical
        numHant: antenna in horizonal
        H_split: split pattern in horizonal
        V_split: split in vertical
        # isHfirst: if sirstly 
        
        WARNING: Please Handle Antenna Combinning in advance!!
                 i.e. numVant = numVant/nunVcombine
        
    '''
    # import pdb;pdb.set_trace()
    beam_weight = np.ones((numVant*numHant, 1))
    weight = beam_weight.transpose(1,0).reshape(-1,numVant,numHant)
    Beam_n = int(weight.shape[0])
    weight = np.array(np.split(weight,H_split,axis=-1))
    weight = np.array(np.split(weight,V_split,axis=-2))
    weight_out = np.zeros((Beam_n,V_split,H_split,numVant,numHant),dtype=complex)
    for l in range(0,Beam_n):
        for m in range(0, V_split):
            for n in range(0, H_split):
                init_choose = np.zeros((V_split,H_split),dtype=complex)
                init_choose[m,n] = 1
                weight_out[l,m,n] = np.kron(init_choose,weight[m,n,l])
    
    
    weight_out = weight_out.transpose(0,2,1,3,4)
    weight_out = weight_out.reshape(-1,V_split*H_split,numVant*numHant)
    weight_out = weight_out.transpose(0,2,1).squeeze(0)
   
    return weight_out

if __name__ == '__main__':
    # import pdb;pdb.set_trace()
    pass
    
    