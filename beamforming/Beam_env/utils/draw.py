from matplotlib import projections
import numpy as np
import os,sys
sys.path.append(os.getcwd())
from Beam_env.utils.channel import get_channel
from Beam_env.utils.generate_beam import generate_dft_beams
import io
import re
import matplotlib.pyplot as plt
from math import pi

class draw_tool:
    def __init__(self,H_range = 180,V_range = 180,numHant = 8,numVant = 4,HSpacing = 0.5,VSpacing = 0.8,downtilt = 0,numCombiningV = 1,CombiningWeight = np.array([]),isAEPattern = 1,enableAntennaPattern = 0,H_pattern = np.array([]),V_pattern = np.array([])):
        self.H_angles = np.array(np.arange(-H_range,H_range+1,1),np.float32)
        self.V_angles = np.array(np.arange(-V_range,V_range+1,1),np.float32)
        self.H_size = self.H_angles.size
        self.V_size = self.V_angles.size
        self.numHant = numHant
        self.numVant = numVant
        self.HSpacing = HSpacing
        self.VSpacing = VSpacing
        self.Beam_num = self.numHant * self.numVant
        self.downtilt = downtilt
        self.numCombiningV = numCombiningV
        self.numVant_vitrual = int(self.numVant/self.numCombiningV)
        self.CombiningWeight = CombiningWeight
        self.isAEPattern = isAEPattern
        self.enableAntennaPattern = enableAntennaPattern
        self.H_pattern = H_pattern
        self.V_pattern = V_pattern
        if(not (self.H_pattern.size or self.V_pattern.size)):
            self.pattern_3GPP()

        self.dft_beams = generate_dft_beams(numVant=self.numVant_vitrual,numHant=self.numHant)
        self.beam = self.dft_beams # kron(V,H) --> V,H
        self.load_channel()
    
    def load_channel(self):
        self.channel = get_channel(self.H_angles,self.V_angles,self.numHant,self.numVant,self.HSpacing, self.VSpacing,self.downtilt,self.CombiningWeight,self.isAEPattern,self.enableAntennaPattern,self.H_pattern,self.V_pattern)   
        
    def pattern_3GPP(self):
        H_angle = np.arange(0,361)
        H_angle= (H_angle>180)*(-360) + H_angle
        H_pattern = -12*(H_angle/65)*(H_angle/65)
        H_pattern = np.clip(H_pattern,-30,0)
        V_pattern = H_pattern
        self.H_pattern = H_pattern
        self.V_pattern = V_pattern
    
    def choose_dft_beam(self,index):
        idx = np.array(index)
        assert(idx.any() in range(0,self.Beam_num))
        self.beam = self.dft_beams[:,idx]

    def load_beam(self,load_path):
        # beam 存的方式，Ants维度在前还是在后
        weight_data = np.loadtxt(load_path,delimiter=",") 
        weight = weight_data[:,np.arange(0,weight_data.shape[1],2)]+1j*weight_data[:,np.arange(1,weight_data.shape[1],2)]
        ## CombiningV
        # weight{i} = repmat(weight{i},[numCombiningV,1,1]);
        weight = weight.reshape(self.numHant*self.numVant_vitrual,-1)
        self.beam = weight

    def draw(self,path):
        weight = self.beam
        weight = weight.reshape(self.numVant_vitrual,self.numHant,-1)
        weight = weight.repeat(self.numCombiningV,axis=0)
        weight = weight.reshape(self.numVant*self.numHant,-1)
        maxAmp = np.max(abs(weight),0)
        nonzero_num = np.sum(np.abs(weight[:,0])!=0)
        c = np.diag(1./maxAmp)/np.sqrt(nonzero_num)
        weight = weight@c

        data = self.channel@weight
        gain = 20*np.log10(abs(data))
        gain_max = np.max(gain,1)
        gain_idx = np.argmax(gain,1) 
        gain_max = gain_max.reshape(self.H_size,self.V_size).T
        gain_idx = gain_idx.reshape(self.H_size,self.V_size).T

        direction = np.argmax(gain,0)

        direciton_H = np.floor(direction/self.V_size)
        direciton_V = np.mod(direction,self.V_size)
        direciton_H = (self.H_angles[-1]-self.H_angles[0]+1)/self.H_size*direciton_H + self.H_angles[0]
        direciton_V = (self.V_angles[-1]-self.V_angles[0]+1)/self.V_size*direciton_V + self.V_angles[0]

        self.direcition_H = direciton_H
        self.direcition_V = direciton_V

        fig1,ax1 = plt.subplots()
        level = np.concatenate((np.array([min(np.floor(np.min(gain_max)),-31)]),np.arange(-30,0,5),np.arange(0,30,2),np.array([max(np.floor(np.max(gain_max)),31)])))
        contourf = ax1.contourf(self.H_angles,self.V_angles,gain_max,cmap=plt.get_cmap('YlGnBu_r'))
        contour = ax1.contour(self.H_angles,self.V_angles,gain_max,level,colors='black')
        ax1.clabel(contour,fontsize=8,colors=('black'))
        for i in range(0,direction.size):
            ax1.text(direciton_H[i],direciton_V[i],i,fontsize=8,color='red',verticalalignment='center',horizontalalignment='center')
        plt.savefig(path+"Power.jpg")

        
        H_axis = self.H_angles/360*2*pi
        V_axis= self.V_angles/360*2*pi
        max_idx = np.unravel_index(np.argmax(gain_max),gain_max.shape)
        H_power = gain_max[max_idx[0],:]
        V_power = gain_max[:,max_idx[1]]

        # fig2, ax2 = plt.subplots(subplot_kw={'projection': 'polar'})
        # ax2.plot(H_axis, H_power)
        # ax2.set_rmin(-30)
        # # ax2.set_thetalim(-pi,pi)
        # # x_label = np.array([0.0,45.0,90.0,135.0,180.0,-135.0,-90.0,-45.0])
        # ax2.set_rticks([-30, -20, -10, 0, 10, 20,np.max(H_power)])  # Less radial ticks
        # ax2.set_rlabel_position(-22.5)  # Move radial labels away from plotted line
        # ax2.grid(True)
        # ax2.set_title("H Power Polar V angle " + str(self.V_angles[max_idx[0]]), va='bottom')
        # plt.savefig(path+"H_Power.jpg")

        # fig3, ax3 = plt.subplots(subplot_kw={'projection': 'polar'})
        # ax3.plot(V_axis, V_power)
        # ax3.set_rmin(-30)
        # ax3.set_rticks([-30, -20, -10, 0, 10, 20,np.max(V_power)])  # Less radial ticks
        # ax3.set_rlabel_position(-22.5)  # Move radial labels away from plotted line
        # ax3.grid(True)

        # ax3.set_title("V Power Polar with H angle " + str(self.H_angles[max_idx[1]]), va='bottom')
        # plt.savefig(path+"V_Power.jpg")

        # plt.show()
        # plt.close()

    # input srs subband*port*nAnts
    def draw_srs(self,srs = np.array([[]]),path=""): 
        # nSub = srs.shape[0]
        # nPort = srs.shape[1]
        # nAnts = srs.shape[2]
        # assert(nAnts == self.numVant_vitrual)
        # srs = np.expand_dims(srs, axis=-2) #nSub*nPort*1*nAnts
        # Coma = np.conj(srs.transpose(0,1,3,2))@srs  #nSub*nPort*nAnts*nAnts
        # Coma_avg = np.average(Coma,axis = 1)    #nSub*nAnts*nAnts
        self.beam = srs
        self.draw(path)

if __name__ == "__main__":
    tool1 = draw_tool(numVant=4,numCombiningV=1)
    tool1.draw("./figures/all_origin_")
    # tool2 = draw_tool(numVant=12,numCombiningV=3)
    # tool2.draw("./figures/all_combineV_")
    # tool3 = draw_tool(numVant=4,numCombiningV=1,downtilt=30)
    # tool3.draw("./figures/all_downtilt_")
    # tool4 = draw_tool(numVant=4,numCombiningV=1,isAEPattern=0)
    # tool4.draw("./figures/all_withoutAE_")
    tool = tool1
    tool.choose_dft_beam([3,13])
    tool.draw("./figures/choose_beam_")
