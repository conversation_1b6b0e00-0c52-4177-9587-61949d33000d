# -*- encoding:utf-8 -*-
import numpy as np


class Mcs2Qam:
    def __init__(self, fn=__file__.replace('.py', '.npz'), default_max_rank=64) -> None:
        self.map = {}
        for k, e in np.load(fn, allow_pickle=True).items():
            self.map[int(k)] = e
        self.cur_map = self.map[default_max_rank]

    def set_max_rank(self, max_rank):
        """Set max modulation order 

        Args:
            max_rank (int): max modulation order
        """
        self.cur_map = self.map[max_rank]

    def __call__(self, mcs, Tr=2):
        return self.cur_map[Tr, mcs]


if __name__ == '__main__':
    print(Mcs2Qam()(17))
