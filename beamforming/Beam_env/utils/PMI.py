from typing import List
from matplotlib.pyplot import axis
import numpy as np
from math import pi

supported_configurations = [[2,1,4,1],[2,2,4,4],[4,1,4,1],[3,2,4,4],[6,1,4,1],[4,2,4,4],[8,1,4,1],
                            [4,3,4,4],[6,2,4,4],[12,1,4,1],[4,4,4,4],[8,2,4,4],[16,1,4,1]]

class PMI:
    def __init__(self):
        self.i13_map = dict()
        self.base_pmi = dict()
        self.pmi_codebook = dict()

    def mapping_i13_to_k1k2(self,layer,N1,N2,O1,O2):
        if layer == 1:
            map = np.array([[0,0]])
        if layer == 2:
            map = self.mapping_i13_to_k1k2_for_l2(N1,N2,O1,O2)
        elif layer == 3 or layer == 4:
            map = self.mapping_i13_to_k1k2_for_l34(N1,N2,O1,O2)
        return map

    def mapping_i13_to_k1k2_for_l2(self,N1,N2,O1,O2):
        if (N1,N2,O1,O2,'layer2') in self.i13_map:
            return self.i13_map[N1,N2,O1,O2,'layer2']
        if N1 > N2 > 1:
            mapping = [[0, 0], [O1, 0], [0, O2], [2*O1, 0]]
        elif N1 == N2:
            mapping = [[0, 0], [O1, 0], [0, O2], [O1, O2]]
        elif N1 == 2 and N2 == 1:
            mapping = [[0, 0], [O1, 0]]
        elif N1 > 2 and N2 == 1:
            mapping = [[0, 0], [O1, 0], [2*O1, 0], [3*O1, 0]]
        else:
            raise Exception('err')
        self.i13_map[N1,N2,O1,O2,'layer2'] = mapping
        return mapping

    def mapping_i13_to_k1k2_for_l34(self,N1,N2,O1,O2):
        if (N1,N2,O1,O2,'layer3-4') in self.i13_map:
            return self.i13_map[N1,N2,O1,O2,'layer3-4']
        if N2 == 1:
            if(N1 == 2):
                mapping = [[O1,0]]
            elif(N1 == 4):
                mapping = [[O1,0],[2*O1,0],[3*O1,0]]
            elif(N1 == 6):
                mapping = [[O1,0],[2*O1,0],[3*O1,0],[4*O1,0]]
        elif N2 == 2:
            if N1 == 2:
                mapping = [[O1,0],[0,O2],[O1,O2]]
            if N1 == 3:
                mapping = [[O1,0],[0,O2],[O1,O2],[2*O1,0]]
        else:
            raise Exception('err')
        self.i13_map[N1,N2,O1,O2,'layer3-4'] = mapping
        return mapping
    
    def get_pmi(self,N1,N2,O1,O2,cm,layer,i11,i12,i13,i2):
        """Get PMI codebook
        
        Args:
            N1 (Integer): N1
            N2 (Integer): N2
            O1 (Integer): O1
            O2 (Integer): O2
            cm (Integer): codemode (now only support codemode 1)
            layer (Integer): 1-4
            i11 (Integer): i11
            i12 (Integer): i12
            i13 (Integer): i13
            i2 (Integer): i2
        """
        codebook = self.get_pmi_codebook(N1,N2,O1,O2,cm,layer)   
        pmi = codebook[i11,i12,i13,i2]
        return pmi
    
    def get_pmi_codebook(self,N1,N2,O1,O2,cm,layer):
        assert [N1, N2, O1, O2] in supported_configurations
        if (N1,N2,O1,O2,cm,layer) in self.pmi_codebook:
            codebook = self.pmi_codebook[N1,N2,O1,O2,cm,layer]
        else:
            self.add_pmi_codebook(N1,N2,O1,O2,cm,layer)
            codebook = self.pmi_codebook[N1,N2,O1,O2,cm,layer]
        return codebook
    
    def get_pmi_codebook_alllayer(self,N1,N2,O1,O2,cm):
        codebook1 = self.get_pmi_codebook(N1,N2,O1,O2,cm,1)
        codebook2 = self.get_pmi_codebook(N1,N2,O1,O2,cm,2)
        codebook3 = self.get_pmi_codebook(N1,N2,O1,O2,cm,3)
        codebook4 = self.get_pmi_codebook(N1,N2,O1,O2,cm,4)
        codebook = list([codebook1,codebook2,codebook3,codebook4])
        # another way
        '''        
        codebook = self.get_pmi_codebook(N1,N2,O1,O2,cm,4)
        codebook = np.stack((codebook[:,:,:,:,:,0:1],codebook[:,:,:,:,:,0:2],codebook[:,:,:,:,:,0:3],codebook),axis=0)
        '''
        return codebook
    
    def add_pmi_codebook(self,N1,N2,O1,O2,cm,layer):
        base_codebook = self.get_pmi_base_codebook(N1,N2,O1,O2)
        if cm == 1:
            if (layer == 3 or layer == 4) and N1*N2*2 >= 16:
                codebook = self.get_pmi_codebook_cm1_2(base_codebook,N1,N2,O1,O2,layer=layer)
            else:
                i13_map = self.mapping_i13_to_k1k2(layer,N1,N2,O1,O2)
                codebook = self.get_pmi_codebook_cm1(base_codebook,i13_map,N1,N2,O1,O2,layer=layer)
            self.pmi_codebook[N1,N2,O1,O2,cm,layer] = codebook
        else:
            raise Exception("only codemode 1")
        
    def get_pmi_base_codebook(self,N1,N2,O1,O2):
        if (N1,N2,O1,O2) in self.base_pmi:
            return self.base_pmi[N1,N2,O1,O2]
        N1O1 = N1*O1
        N2O2 = N2*O2
        P = N1*N2*2
        vls = np.array([np.exp(1j*2*pi*l*np.arange(0,N1)/(N1*O1)) for l in range(N1O1)]) # (N1O1,N1)
        ums = np.array([np.exp(1j*2*pi*m*np.arange(0,N2)/(N2*O2)) for m in range(N2O2)]) # (N2O2,N2)
        phis = np.array([np.exp(1j*pi*n/2) for n in range(4)]) # (4,1)
        phis = np.stack((np.ones_like(phis),phis),axis=-1)/np.sqrt(P) # (4,2)
        layer = (vls[:,None,:,None]*ums[None,:,None,:]) # (N1O1,N2O2,N1,N2)
        layer = (layer[:,:,None,None,:,:]*phis[None,None,:,:,None,None]).reshape(N1O1,N2O2,4,P) # (N1O1,N2O2,4,2*N1*N2)
        self.base_pmi[N1,N2,O1,O2] = layer 
        return layer # (i11,i12,i2,P)
    
    def get_pmi_codebook_cm1(self,pmi_codebook,mapping,N1,N2,O1,O2,layer):
        max_i11 = N1*O1
        max_i12 = N2*O2
        max_i13 = len(mapping)
        if layer == 1:
            max_i2 = 4
        else:
            max_i2 = 2
        P = 2*N1*N2
        # i11*i12*i13*i2
        # layer1 i13->0,0
        # output i11*i12*i3*i2*P*layer
        i11 = np.arange(0,max_i11)
        i12 = np.arange(0,max_i12)
        i2 = np.arange(0,max_i2)
        codebook = np.zeros((max_i13,layer,max_i11,max_i12,max_i2,P),dtype=np.complex128)
        for i13 in range(max_i13):
            i13_0 = mapping[i13][0]
            i13_1 = mapping[i13][1]
            if layer > 0:
                codebook[i13][0] = pmi_codebook[np.ix_(i11%max_i11,i12%max_i12,i2%4)] 
            if layer > 1:
                if(layer == 2):
                    codebook[i13][1] = pmi_codebook[np.ix_((i11+i13_0)%max_i11,(i12+i13_1)%max_i12,(i2+2)%4)] #+2%4用来取负号
                else:
                    codebook[i13][1] = pmi_codebook[np.ix_((i11+i13_0)%max_i11,(i12+i13_1)%max_i12,i2%4)]
            if layer > 2:
                codebook[i13][2] = pmi_codebook[np.ix_(i11%max_i11,i12%max_i12,(i2+2)%4)]
            if layer > 3:
                codebook[i13][3] = pmi_codebook[np.ix_((i11+i13_0)%max_i11,(i12+i13_1)%max_i12,(i2+2)%4)]
        codebook = np.transpose(codebook,(2,3,0,4,5,1))
        return codebook/np.sqrt(layer)

    def get_pmi_codebook_cm1_2(self,pmi_codebook,N1,N2,O1,O2,layer):
        codebook_temp = pmi_codebook.reshape(pmi_codebook.shape[0],pmi_codebook.shape[1],pmi_codebook.shape[2],2,N1,N2)
        codebook_temp = codebook_temp[:,:,:,:,0:-1:2,:] 
        pmi_codebook_half = codebook_temp.reshape(pmi_codebook.shape[0],pmi_codebook.shape[1],pmi_codebook.shape[2],-1)
        max_i11 = int(N1*O1/2)
        max_i12 = N2*O2
        max_i13 = 4
        max_i2 = 2
        P = 2*N1*N2
        i11 = np.arange(0,max_i11)
        i12 = np.arange(0,max_i12)
        i2 = np.arange(0,max_i2)
        codebook = np.zeros((max_i13,layer,max_i11,max_i12,max_i2,P),dtype=np.complex128)
        
        for i13 in range(max_i13):
            base_p_0 = pmi_codebook_half[np.ix_(i11,i12,i2)]
            base_p_1 = pmi_codebook_half[np.ix_(i11,i12,(i2+2)%4)]
            base_p_0 = np.expand_dims(base_p_0,axis = -2)
            base_p_1 = np.expand_dims(base_p_1,axis = -2)
            base_p = np.concatenate((base_p_0,base_p_1),axis = -2)
            base_p = base_p.reshape(max_i11,max_i12,max_i2,2,2,int(P/4)) #i11*i12*i2*left_right*up_down*4
            angle_p = np.exp(1j*pi*i13/4)
            codebook[i13][0] = np.concatenate((base_p[:,:,:,0,0],base_p[:,:,:,0,0]*angle_p,base_p[:,:,:,0,1],base_p[:,:,:,0,1]*angle_p),axis = -1)
            codebook[i13][1] = np.concatenate((base_p[:,:,:,0,0],base_p[:,:,:,0,0]*-angle_p,base_p[:,:,:,0,1],base_p[:,:,:,0,1]*-angle_p),axis = -1)
            codebook[i13][2] = np.concatenate((base_p[:,:,:,1,0],base_p[:,:,:,1,0]*angle_p,base_p[:,:,:,1,1],base_p[:,:,:,1,1]*angle_p),axis = -1)
            if layer > 3:
                codebook[i13][3] = np.concatenate((base_p[:,:,:,1,0],base_p[:,:,:,1,0]*-angle_p,base_p[:,:,:,1,1],base_p[:,:,:,1,1]*-angle_p),axis = -1)
        codebook = np.transpose(codebook,(2,3,0,4,5,1))
        return codebook/np.sqrt(layer)
    
if __name__ == "__main__":
    pmi_class = PMI()
    N1 = 2
    N2 = 2
    O1 = (N1>1)*3+1
    O2 = (N2>1)*3+1
    max_i11 = N1*O1
    max_i12 = 6
    max_i13 = 4
    max_i2 = 4
    if(1 == 3 or 4):
        print("setsumi")
    # print(pmi_class.get_pmi(2,2,4,4,1,2,np.arange(0,N1),1,1,1).shape)
    # base_codebook = pmi_class.get_pmi_codebook(N1,N2,O1,O2,1,4)
    # print(pmi_class.get_pmi(N1,N2,O1,O2,1,4,0,0,0,0))
    # print(base_codebook.shape)
    # example_1
    pmi_class.get_pmi_base_codebook(8,1,4,1)
    cd0 = pmi_class.get_pmi_codebook(4,1,4,1,1,4)
    print(cd0.shape)
    print(np.linalg.norm(cd0[0,0,0,0]))
    cd1 = pmi_class.get_pmi_codebook(4,2,4,4,1,4)
    print(cd1.shape)
    print(np.linalg.norm(cd1[0,0,0,0]))
    print("example 1")
    print(pmi_class.get_pmi(2,1,4,1,1,1,0,0,0,0))
    # example_2
    print("example 2")
    print(pmi_class.get_pmi(2,1,4,1,1,1,1,0,0,1))
    # example_3
    print("example 3")
    print(pmi_class.get_pmi(2,1,4,1,1,2,0,0,0,0))
    # example_4
    print("example 4")
    print(pmi_class.get_pmi(2,1,4,1,1,2,1,0,1,1))
    print(pmi_class.get_pmi_codebook_alllayer(2,1,4,1,1)[0].shape)
        