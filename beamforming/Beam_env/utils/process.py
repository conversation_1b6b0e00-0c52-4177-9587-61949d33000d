import numpy as np
from .channel import get_channel
from .generate_beam import generate_dft_beams
from .PMI import PMI
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import copy

class process:
    def __init__(self,H_range = 80,V_range = 60,numHant = 8,numVant = 4,HSpacing = 0.5,VSpacing = 0.8,downtilt = 0,numCombiningV = 1,CombiningWeight = np.array([]),isCombineV = False,isAEPattern = True,enableAntennaPattern = True,H_pattern = np.array([]),V_pattern = np.array([])):
        self.H_angles_pol = np.array(np.arange(-180,180+1,1),np.float32)
        self.V_angles_pol = np.array(np.arange(-180,180+1,1),np.float32)
        
        self.numCombiningV = numCombiningV
        self.CombiningWeight = CombiningWeight
        self.isCombineV = isCombineV
        self.isAEPattern = isAEPattern
        self.enableAntennaPattern = enableAntennaPattern
        self.H_pattern = H_pattern
        self.V_pattern = V_pattern

        if(not (self.H_pattern.size or self.V_pattern.size)):
            self.pattern_3GPP()
        self.update_ant_config(numHant,numVant,HSpacing,VSpacing,downtilt)
        self.update_range(H_range,V_range)
        self.weight = self.dft_beams # kron(V,H) --> V,H
        self.weight_pol1 = copy.deepcopy(self.weight)*0
        self.weight_pol2 = copy.deepcopy(self.weight)*0
        self.load_channel()
        self.mode = "all_dft"
        self.display_pol = 1
        self.nport = 4
        self.nsub = 17
        self.update_P = True
        self.log = None
        self.Beams = None
        self.frame = 0
        self.realtime =  False
        self.multile_line = True
        self.load_config = False
        self.load_message = None
        self.load_error = False
        self.idx = [0]
        self.fig1 = None
        self.fig2 = None
        self.fig3 = None
        self.fig4 = None
        self.PMI_class = PMI()
        self.init_draw()

                
    def update_ant_config(self,numHant,numVant,HSpacing,VSpacing,downtilt):
        self.numHant = numHant
        self.numVant = numVant
        self.HSpacing = HSpacing
        self.VSpacing = VSpacing
        self.downtilt = downtilt
        self.Ants_num = self.numHant * int(self.numVant/self.numCombiningV)
        self.Beam_num = self.Ants_num
        self.dft_beams = generate_dft_beams(numVant=int(self.numVant/self.numCombiningV),numHant=self.numHant)

    def update_range(self,H_range,V_range):
        self.H_angles_power = self.H_angles_pol[180-H_range:180+H_range+1]
        self.V_angles_power = self.V_angles_pol[180-V_range:180+V_range+1]
        self.H_range = H_range
        self.V_range = V_range
        self.H_size = self.H_angles_power.size
        self.V_size = self.V_angles_power.size
    
    def load_channel(self):
        self.channel = get_channel(self.H_angles_pol,self.V_angles_pol,self.numHant,self.numVant,self.HSpacing, self.VSpacing,self.downtilt,self.isCombineV,self.CombiningWeight,self.isAEPattern,self.enableAntennaPattern,self.H_pattern,self.V_pattern)   

    def pattern_3GPP(self):
        H_angle = np.arange(0,360)
        H_angle= (H_angle>180)*(-360) + H_angle
        H_pattern = -12*(H_angle/65)*(H_angle/65)
        H_pattern = np.clip(H_pattern,-30,0)
        V_pattern = H_pattern
        self.H_pattern = H_pattern
        self.V_pattern = V_pattern
    
    def choose_dft_beam(self,index):
        idx = np.array(index)
        assert(idx.any() in range(0,self.Beam_num))
        self.weight = self.dft_beams[:,idx]

    def load_beam(self,data):
        beam_data = data
        length = beam_data.shape[0]
        beam_data = beam_data.reshape(length,-1,2)
        beam_data = beam_data[:,:,0]+1j*beam_data[:,:,1]
        self.Beams = beam_data
        
    def load_srs(self,data):
        srs_data = data
        srs_data = srs_data.reshape(-1,self.Ants_num*2,srs_data.shape[1])
        srs_data = srs_data.reshape(srs_data.shape[0],srs_data.shape[1],-1,2)
        srs_data = srs_data[:,:,:,0] + 1j*srs_data[:,:,:,1]
        srs_data = srs_data.transpose(1,2,0) #(Ants*2)*nPort*nsub
        self.nport = srs_data.shape[1]
        self.nsub = srs_data.shape[2]
        srs_data = srs_data.reshape(srs_data.shape[0],-1)
        self.Beams = srs_data
    
    def get_beam_from_srs(self,srs_p1,srs_p2):
        srs_p1 = srs_p1.transpose(1,0)
        srs_p2 = srs_p2.transpose(1,0)
        srs_p1 = np.expand_dims(srs_p1,-2)
        srs_p2 = np.expand_dims(srs_p2,-2)
        Coma_p1 = np.conj(srs_p1.transpose(0,2,1))@srs_p1     #srs.H@srs
        Coma_p2 = np.conj(srs_p2.transpose(0,2,1))@srs_p2     #srs.H@srs

        Coma_p1 = np.average(Coma_p1,axis=(0))
        Coma_p2 = np.average(Coma_p2,axis=(0))

        s1,v1,d1 = np.linalg.svd(Coma_p1)
        s2,v2,d2 = np.linalg.svd(Coma_p2)
        s1 = s1.T
        s2 = s2.T
        beam_p1 = np.transpose(s1[0:2],(1,0))
        beam_p2 = np.transpose(s1[0:2],(1,0))
        beam_p1 = np.conj(beam_p1)
        beam_p2 = np.conj(beam_p2)
        return beam_p1,beam_p2

    def cal_gain(self,weight):
        weight = weight.reshape(int(self.numVant/self.numCombiningV),self.numHant,-1)
        weight = weight.repeat(self.numCombiningV,axis=0)
        weight = weight.reshape(self.numVant*self.numHant,-1)
        maxAmp = np.max(abs(weight),0)
        nonzero_num = np.sum(np.abs(weight[:,0])!=0)
        if nonzero_num == 0:
            nonzero_num = 1
        max_div = 1./(maxAmp + 1e-7)
        max_div[maxAmp == 0] = 0 
        c = np.diag(max_div)/np.sqrt(nonzero_num)
        weight = weight@c

        data = self.channel@weight
        self.origin_result = data
        gain = 20*np.log10(abs(data)+1e-7)
        gain[gain<-30] = -30
        gain_max = np.max(gain,1)
        gain_idx = np.argmax(gain,1) 
        gain_max = gain_max.reshape(self.H_angles_pol.size,self.V_angles_pol.size).T
        gain_idx = gain_idx.reshape(self.H_angles_pol.size,self.V_angles_pol.size).T   
        self.gain_max = gain_max  

        gain_ = gain.reshape(self.H_angles_pol.size,self.V_angles_pol.size,-1)
        gain_ = gain_[90:270,90:270,:]
        direction = [np.unravel_index(np.argmax(gain_[:,:,i]),gain_[:,:,i].shape) for i in range(0,gain_.shape[2])]
        direction = np.array(direction)

        self.direcition_H = direction[:,0] - 90
        self.direcition_V = direction[:,1] - 90
        
        if(self.multile_line == True):
            gain = gain.reshape(self.H_angles_pol.size,self.V_angles_pol.size,-1)
            gain = np.transpose(gain,(1,0,2))
            gain_ = gain[90:270,90:270,:]
            max_idx = np.unravel_index(np.argmax(gain_),gain_.shape)
            max_idx = max_idx

            H_power = np.zeros((361,gain.shape[2]),dtype=np.float)
            V_power = np.zeros((361,gain.shape[2]),dtype=np.float)
            for i in range(0,gain.shape[2]):                
                H_power[:,i] = gain[max_idx[0]+90,:,i]
                V_power[:,i] = gain[:,max_idx[1]+90,i]
        else:
            gain_max_ = gain_max[90:270,90:270,:]
            max_idx = np.unravel_index(np.argmax(gain_max_),gain_max_.shape) + 90
            H_power = gain_max[max_idx[0]+90,:]
            V_power = gain_max[:,max_idx[1]+90]
            H_power = np.expand_dims(H_power,axis=1)
            V_power = np.expand_dims(V_power,axis=1)
        
        gain_max = gain_max[180-self.V_range:180+self.V_range+1, 180-self.H_range:180+self.H_range+1]
        return gain_max,H_power,V_power
    
    def init_draw(self):
        self.weight = self.dft_beams
        gain_max,H_power,V_power = self.cal_gain(self.weight)
        self.draw_P = go.Contour(
            z = list(np.zeros(self.H_size*self.V_size)),
            x = list(self.H_angles_power),
            y = list(self.V_angles_power),
            contours=dict(
                showlabels = True, # show labels on contours
                labelfont = dict( # label font properties
                size = 8,
                color = 'white'
            )),
            colorbar=dict(
                lenmode="pixels", len=200,
                y = 0.25
            ),
            hovertemplate = "H:%{x},V:%{y},dB:%{z}",
            colorscale = [[0, 'royalblue'], [0.5, 'mediumturquoise'], [1, 'yellow']]
        )

        self.draw_scatter = go.Scatter(x=self.direcition_H,y=self.direcition_V,mode="markers",marker_size=3)
        
    def draw_weight(self,weight):
        gain_max,H_power,V_power = self.cal_gain(weight)
        fig = make_subplots(rows=1,cols=1)
        fig.update_layout(
            title='Contour Power Pol1',
            width=600,
        height=500
        )
        fig.add_trace(self.draw_P.update(z=gain_max))
        fig.add_trace(self.draw_scatter.update(x=self.direcition_H,y=self.direcition_V))
        return fig
        
        
    
    def get_power(self,frame):
        self.multile_line = True
        if(self.mode == "all_dft"):
            self.weight_pol1 = self.dft_beams
            self.weight_pol2 = np.zeros_like(self.weight_pol1)
            self.display_pol = 1
            self.idx = np.arange(0,self.Beam_num)
            self.multile_line = False
        elif(self.mode == "some_dft"):
            self.weight_pol1 = self.dft_beams[:,self.idx]
            self.weight_pol2 = np.zeros_like(self.weight_pol1)
            self.display_pol = 1
        elif(self.mode == "load_beam_csv"):
            choose_beam = self.Beams[:,self.idx]
            if(self.Beams.shape[0] == self.Ants_num):
                self.weight_pol1 = choose_beam
                self.weight_pol2 = np.zeros_like(self.weight_pol1)
                self.display_pol = 1
            else:
                self.weight_pol1 = choose_beam[0:self.Ants_num,:]
                self.weight_pol2 = choose_beam[self.Ants_num:self.Ants_num*2,:]
                self.display_pol = 2
        elif(self.mode == "beam_log"):
            if self.Beams is not None:
                if(self.frame != frame):
                    frame = int(self.frame)
                beams_meantime = self.Beams[self.frame]
                beams = []
                idxs = []
                for i in range(0,len(beams_meantime)):
                    beams.append(beams_meantime[i]['beam'])
                    idxs.append(beams_meantime[i]['sectIdx'])
                beam = np.array(beams)
                self.idx = np.array(idxs)
                beam = beam.transpose(1,0)
                if(beam.shape[0] == self.Ants_num):
                    self.weight_pol1 = beam
                    self.weight_pol2 = np.zeros_like(self.weight_pol1)
                    self.display_pol = 1
                else:
                    self.weight_pol1 = beam[0:self.Ants_num,:]
                    self.weight_pol2 = beam[self.Ants_num:self.Ants_num*2,:]
                    self.display_pol = 2
            if(self.realtime and self.Beams is not None):
                self.frame += 1
                if self.frame == len(self.Beams):
                    self.frame = 0
        elif(self.mode == "load_srs"):     
            if self.Beams is not None:
                choose_beam = self.Beams[:,self.idx]
                self.weight_pol1 = choose_beam[0:self.Ants_num,:]
                self.weight_pol2 = choose_beam[self.Ants_num:self.Ants_num*2,:]
                self.display_pol = 2
                self.multile_line = False
                gain_max1,SRS_H_power1,SRS_V_power1 = self.cal_gain(self.weight_pol1)
                gain_max2,SRS_H_power2,SRS_V_power2 = self.cal_gain(self.weight_pol2)
                Beam_p1,Beam_p2 = self.get_beam_from_srs(self.weight_pol1,self.weight_pol2)
                self.multile_line = True
                Beam_gain_max1,Beam_H_power1,Beam_V_power1 = self.cal_gain(Beam_p1)
                Beam_gain_max2,Beam_H_power2,Beam_V_power2 = self.cal_gain(Beam_p2)
                H_power1 = np.concatenate((SRS_H_power1,Beam_H_power1),axis=1)
                V_power1 = np.concatenate((SRS_V_power1,Beam_V_power1),axis=1)
                H_power2 = np.concatenate((SRS_H_power2,Beam_H_power2),axis=1)
                V_power2 = np.concatenate((SRS_V_power2,Beam_V_power2),axis=1)
                
        if not self.mode == "load_srs":        
            gain_max1,H_power1,V_power1 = self.cal_gain(self.weight_pol1)
            if(self.display_pol == 2):
                gain_max2,H_power2,V_power2 = self.cal_gain(self.weight_pol2)
            else:
                gain_max2 = gain_max1*0
                H_power2 = H_power1*0
                V_power2 = V_power1*0
        return gain_max1,H_power1,V_power1,gain_max2,H_power2,V_power2

              
