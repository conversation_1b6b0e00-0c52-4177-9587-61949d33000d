{"cells": [{"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["import os,sys\n", "sys.path.append(os.getcwd())\n", "sys.path.append(\"../../\")\n", "from setsumi_env.BM_functions import *\n", "from setsumi_env.system_functions import *\n", "from setsumi_env.process import *\n", "from setsumi_env.analyze import *\n", "from setsumi_env.utils import *\n", "from setsumi_env.setsumi_env import setsumi_env\n", "import numpy as np\n", "import pickle\n", "import re\n", "import pandas as pd\n", "from collections import Counter\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "\n", "from scipy import stats\n", "import copy\n", "\n", "import matplotlib.pyplot as plt\n", "import matplotlib.patches as mpatches\n", "from IPython.display import Image\n", "from matplotlib.gridspec import GridSpec\n", "import time"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Load begin\n", "setsumi load sim log OK\n", "Load Finish\n", "simulate srs\n", "cal coma\n", "cal beam\n"]}], "source": ["BMenv = setsumi_env(path=\"/data/BM_data/28GHZ/UMa_2000_o/\",max_length=200,gNB_tti=2) \n", "draw_tool = BMenv.draw\n", "sim_par = BMenv.sim_par"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(2000, 200, 17, 4, 64)\n"]}], "source": ["print(BMenv.H_freq.shape)"]}, {"cell_type": "code", "execution_count": 74, "metadata": {}, "outputs": [], "source": ["import time\n", "ue_ = 0\n", "dt = 180\n", "repeat = 10000"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["预先计算好beam pattern可以降低计算复杂度"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["pattern = generate_beam_pattern(V_ants,H_ants,V_split,H_split)"]}, {"cell_type": "code", "execution_count": 99, "metadata": {}, "outputs": [], "source": ["import cupy as gnp\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gpy.matmul"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as py"]}, {"cell_type": "code", "execution_count": 113, "metadata": {}, "outputs": [], "source": ["import cupy as dp"]}, {"cell_type": "code", "execution_count": 304, "metadata": {}, "outputs": [], "source": ["import numpy as dp"]}, {"cell_type": "code", "execution_count": 393, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(16, 1, 3, 2, 8, 4)\n", "mat\n"]}], "source": ["times = []\n", "times.append(time.time())\n", "action = BMenv.action_space[17]\n", "V_ants = BMenv.V_ants\n", "H_ants = BMenv.H_ants\n", "for i in range(repeat):\n", "    mod_time = dt%BMenv.max_time #sim_par.time代表数据总的个数\n", "    srs_idx = dt//BMenv.gNB_tti\n", "    H = BMenv.H_freq[ue_,mod_time]\n", "    Beam_idx = BMenv.Beam_idxs[srs_idx,ue_]\n", "    Beam = BMenv.Beams[Beam_idx]\n", "    srs_choose = BMenv.srs_choose_all[ue_][srs_idx]\n", "# times.append(time.time())\n", "for i in range(repeat):  \n", "    H_srs = choose_H(BMenv.H_freq[ue_],srs_choose)\n", "# times.append(time.time())\n", "for i in range(repeat):  \n", "    H_split = action[0]\n", "    V_split = action[1]\n", "    RI = action[2]\n", "    Port = action[3]\n", "    N1 = max(V_split,H_split)*int(Port/H_split/V_split/2)\n", "    N2 = min(V_split,H_split)\n", "\n", "    \n", "    nBeam = int(Port/H_split/V_split/2) \n", "    Beam = Beam[0:nBeam]\n", "    # Ports_vector = generate_beam_pattern(V_ants,H_ants,V_split,H_split)\n", "    Ports_vector = pattern\n", "    if len(Beam.shape) == 1:\n", "        Beam = np.expand_dims(<PERSON>am,0)\n", "    Beam = Beam.transpose(1,0) #16*n\n", "times.append(time.time())\n", "for i in range(repeat):  \n", "    Beam_Port = Beam*Ports_vector\n", "times.append(time.time())\n", "for i in range(repeat):  \n", "    FRF = np.kron(np.eye(2),Beam_Port)\n", "    FRF = FRF/np.sqrt(V_ants*H_ants*2)/np.sqrt(nBeam)*np.sqrt(Port)\n", "    \n", "times.append(time.time())\n", "for i in range(repeat):\n", "    UE_H = H@FRF\n", "    # get digital matrix\n", "times.append(time.time())\n", "for i in range(repeat):\n", "    \n", "    O1 = (N1>1)*3+1\n", "    O2 = (N2>1)*3+1\n", "    assert(RI <= N1*N2*2)   \n", "    codebook_origin = PMI_class.get_pmi_codebook(N1,N2,O1,O2,1,RI)\n", "    codebook_origin = codebook_origin*np.sqrt(RI)\n", "print(codebook_origin.shape)\n", "UE_H = np.expand_dims(UE_H,(0,1,2,3)) #0,1,2,3,nSub,ue_port,max_gNB_port\n", "times.append(time.time())\n", "UE_H = dp.array(UE_H)\n", "FRF = dp.array(FRF)\n", "codebook_origin = dp.array(codebook_origin)\n", "for i in range(repeat):\n", "    codebook = dp.expand_dims(codebook_origin, axis=4)\n", "    # HP = UE_H@codebook #0,1,2,3,ue_port,layer\n", "    HP = dp.matmul(UE_H,codebook)\n", "times.append(time.time())\n", "for i in range(repeat):\n", "    Layer_power = dp.matmul(dp.conj(HP.transpose(0,1,2,3,4,6,5)),HP)\n", "print(\"mat\")\n", "times.append(time.time())\n", "for i in range(repeat):\n", "    HP_power = dp.trace(dp.abs(Layer_power),axis1=-1,axis2=-2)\n", "    HP_power = dp.sum(HP_power,axis = -1)\n", "times.append(time.time())\n", "# HP_power = dp.asnumpy(HP_power)\n", "for i in range(repeat):\n", "    idx = np.unravel_index(np.argmax(HP_power),HP_power.shape)\n", "times.append(time.time())\n", "for i in range(repeat):\n", "    P = codebook_origin[idx]\n", "    max_power = HP_power[idx]\n", "    lp = Layer_power[idx]\n", "\n", "P = P\n", "FBB = P\n", "times.append(time.time())\n", "for i in range(repeat):\n", "    F = dp.matmul(FRF,FBB)\n", "    \n", "times.append(time.time())\n", "for i in range(repeat):\n", "        HF = H@F\n", "        R = np.power(10,(-BMenv.SNRs[10]/10))\n", "        sinr,layer,sinr_origin = cal_sinr(HF,R,BMenv.sinr_min,BMenv.sinr_max_diff)\n", "        mcs = np.searchsorted(sinr2mcs_table,sinr)\n", "        TP = get_tp(mcs,layer,BMenv.nSub,True)\n", "        CQI = sinr2cqi(sinr)\n", "times.append(time.time())"]}, {"cell_type": "code", "execution_count": 198, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0.26865268 0.01182199 0.35053325 0.19100332 0.00972271 2.27047515\n", " 2.19123745 0.84023929 0.0326345  0.05281663 0.2004981  0.7301023 ]\n"]}], "source": ["times = np.array(times)\n", "time_end = times[1:]\n", "time_start = times[0:-1]\n", "print(time_end-time_start)"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["UEs = np.a<PERSON>e(2000)\n", "times = np.arange(150,200,1)\n", "UEs_new, times_new = np.meshgrid(UEs, times)\n", "UEs = UEs_new.reshape(-1)\n", "times = times_new.reshape(-1)\n", "action = BMenv.action_space[0]\n", "V_ants = BMenv.V_ants\n", "H_ants = BMenv.H_ants\n", "actions = [0]"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [], "source": ["tag_times = []\n", "# nUE = 2000\n", "# times = [180]*2000\n", "# times = np.array(times)\n", "mod_time = times%BMenv.max_time #sim_par.time代表数据总的个数\n", "srs_idx = times//BMenv.gNB_tti\n", "\n", "\n", "tag_times.append(time.time())\n", "\n", "nUE = UEs.shape[0]\n", "\n", "H = BMenv.H_freq[UEs,mod_time]\n", "srs_choose = BMenv.srs_choose_all[UEs,srs_idx]\n", "\n", "H_origin = BMenv.H_freq\n", "subs = [[[i for i in range(17)]]*4]*nUE\n", "subs = np.array(subs).transpose(0,2,1)\n", "ports = [[[i for i in range(4)]]*17]*nUE\n", "ports = np.array(ports)\n", "ues = [[list(UEs)]*17]*4\n", "ues = np.array(ues).transpose(2,1,0)\n", "tag_times.append(time.time())\n", "H_srs = H_origin[ues,srs_choose,subs,ports]\n", "H = H.astype(np.complex64)\n", "H_srs = H_srs.astype(np.complex64)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["np.set_printoptions(precision=2, suppress=True, dtype=np.complex64)"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [], "source": ["\n", "\n", "for ac in actions:\n", "    action = BMenv.action_space[ac]\n", "    H_split = action[0]\n", "    V_split = action[1]\n", "    RI = action[2]\n", "    Port = action[3]\n", "    N1 = max(V_split,H_split)*int(Port/H_split/V_split/2)\n", "    N2 = min(V_split,H_split)\n", "    nBeam = int(Port/H_split/V_split/2)\n", "    O1 = (N1>1)*3+1\n", "    O2 = (N2>1)*3+1\n", "    assert(RI <= N1*N2*2)   \n", "    codebook_origin = PMI_class.get_pmi_codebook(N1,N2,O1,O2,1,RI)*np.sqrt(RI)\n", "    pattern = generate_beam_pattern(V_ants,H_ants,V_split,H_split)\n", "\n", "    tag_times.append(time.time())\n", "    Beam_idx = BMenv.Beam_idxs[srs_idx,UEs]\n", "    Beam_idx = Beam_idx[:,0:nBeam]\n", "    Beam = BMenv.Beams[Beam_idx]\n", "    Beam = Beam.transpose(0,2,1)\n", "\n", "    Ports_vector = pattern\n", "    Beam_Port = Beam*Ports_vector\n", "    FRF = np.kron(np.eye(2),Beam_Port)\n", "    FRF = FRF/np.sqrt(V_ants*H_ants*2)/np.sqrt(nBeam)*np.sqrt(Port)\n", "    FRF = np.expand_dims(FRF,axis=1)\n", "\n", "    tag_times.append(time.time())\n", "\n", "    tag_times.append(time.time())\n", "    UE_H = H@FRF\n", "    UE_H = np.expand_dims(UE_H,(0,1,2,3))\n", "    codebook_re = codebook_origin.reshape(-1,Port,RI)\n", "    codebook = np.expand_dims(codebook_origin, axis=(4,5))\n", "\n", "    tag_times.append(time.time())\n", "    HP = np.matmul(UE_H,codebook)\n", "    Layer_power = np.conj(HP.transpose(0,1,2,3,4,5,7,6))@HP\n", "    HP_power = np.trace(np.abs(Layer_power),axis1=-1,axis2=-2)\n", "    HP_power = np.sum(HP_power,axis = -1)\n", "\n", "    bs_shape = HP_power.shape[0:4]\n", "    HP_power = HP_power.reshape(-1,nUE)\n", "    idx = np.argmax(HP_power,axis=0)\n", "    PMI = np.unravel_index(np.argmax(HP_power,axis=0),bs_shape)\n", "    PMI = np.array(PMI)\n", "    PMI = PMI.transpose(1,0)\n", "\n", "    tag_times.append(time.time())\n", "\n", "    P = codebook_re[idx]\n", "    max_power = HP_power[idx,np.arange(idx.shape[0])]\n", "    # lp = Layer_power[idx]\n", "    P = P\n", "    FBB = P\n", "    FBB = np.expand_dims(FBB,axis=1)\n", "    # FRF = dp.array(FRF)\n", "    # FBB = dp.array(FBB)\n", "    F = FRF@FBB\n", "\n", "    Layer_power = Layer_power.reshape(-1,nUE,BMenv.nSub,RI,RI)\n", "    lp = Layer_power[idx,np.arange(idx.shape[0])]\n", "    tag_times.append(time.time())\n", "\n", "    for snr in BMenv.SNRs:\n", "        R = np.power(10,(-snr/10))\n", "        tag_times.append(time.time())\n", "        sinr = np.linalg.inv(lp/R+np.eye(Port))\n", "        tag_times.append(time.time())\n", "        sinr = np.real(np.diagonal(sinr,axis1 = -1,axis2 = -2))\n", "        sinr = 1./sinr -1 \n", "        sinr = np.clip(sinr,0.00001,1000000000)\n", "        sinr = 10*np.log10(sinr)\n", "\n", "        tag_times.append(time.time())\n", "        #redo sinr\n", "        sinr[sinr < BMenv.sinr_min] = -50\n", "        if sinr.shape[-1] > 1:\n", "            sinr[(sinr[:,:,0] - sinr[:,:,1] > BMenv.sinr_max_diff),1] = -50\n", "            sinr[(sinr[:,:,1] - sinr[:,:,0] > BMenv.sinr_max_diff),0] = -50\n", "        if sinr.shape[-1] > 3:\n", "            sinr[(sinr[:,:,2] - sinr[:,:,3] > BMenv.sinr_max_diff),3] = -50\n", "            sinr[(sinr[:,:,3] - sinr[:,:,2] > BMenv.sinr_max_diff),2] = -50\n", "        layer = np.sum(sinr > -40,axis = -1)\n", "        layer = np.clip(layer,1,4)\n", "        mi = sinr2mi(sinr)\n", "        mi[mi<0.0014] = 0 #min_mi = 0.0013\n", "        avg_mi = np.sum(mi,axis=-1)/layer\n", "        sinr_avg = mi2sinr(avg_mi)\n", "        mcs = np.searchsorted(sinr2mcs_table,sinr_avg)\n", "        CQI = sinr2cqi(sinr)\n", "        TPs = TP_buffer[mcs,layer-1]\n", "        tag_times.append(time.time())\n", "            "]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["complex64\n"]}], "source": ["print(H.dtype)"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["complex128\n"]}], "source": ["print(BMenv.H_freq.dtype)"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(100000, 17, 2, 2)\n"]}], "source": ["sinr = np.linalg.inv(lp/R+np.eye(Port))\n", "print(sinr.shape)"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [], "source": ["sinr2 = R*np.linalg.inv(lp+np.eye(Port)*R)"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["6.463332929265029e-11\n"]}], "source": ["print(np.sum(np.abs(sinr2-sinr)))"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [], "source": ["sinr3 = R*np.linalg.inv(lp)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sinr4 = "]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[3.94788909e+00 1.67646408e+00 3.47148418e-01 1.19209290e-06\n", " 1.54594636e+00 1.45247936e-01 4.67467308e-02 3.47836161e+00\n", " 2.92405367e-01 2.38180161e-04 2.98204422e-01 7.15255737e-07\n", " 1.52227020e+00 7.82838583e-01 9.08632278e-02 3.68267369e+00\n", " 3.81272554e-01]\n"]}], "source": ["tag_times = np.array(tag_times)\n", "time_end = tag_times[1:]\n", "time_start = tag_times[0:-1]\n", "print(time_end-time_start)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["PRBpreSB,nREAvg = get_tp_par()"]}, {"cell_type": "code", "execution_count": 454, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(4000, 17)\n", "(4000, 17)\n"]}], "source": ["print(mcs.shape)\n", "print(layer.shape)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["TP_buffer = np.zeros((29,4))\n", "for i in range(0,29):\n", "    for j in range(0,4):\n", "        TP_buffer[i,j] = TbCalc(i,j+1,16,nREAvg)"]}, {"cell_type": "code", "execution_count": 460, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(29, 4)\n"]}], "source": ["print(TP_buffer.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 462, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 463, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(4000, 17)\n"]}], "source": ["print(TPs.shape)"]}, {"cell_type": "code", "execution_count": 455, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "object of type 'int' has no len()", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "\u001b[0;32m/tmp/ipykernel_2103092/2236240125.py\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[0mmcs\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0msearchsorted\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0msinr2mcs_table\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0msinr_avg\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 2\u001b[0;31m \u001b[0mTP\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mget_tp\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mlayer\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mBMenv\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mnSub\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;32mTrue\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;32m/data/setsumi/beamforming/setsumi_env/BM_functions.py\u001b[0m in \u001b[0;36mget_tp\u001b[0;34m(mcs, layer, nSub, sub)\u001b[0m\n\u001b[1;32m    500\u001b[0m     \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m   \u001b[0mTP\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;36m0.\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    501\u001b[0m     \u001b[0;32mfor\u001b[0m \u001b[0mband\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mrange\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mnSub\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 502\u001b[0;31m         \u001b[0;32mif\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mlen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mmcs\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;34m==\u001b[0m \u001b[0mnSub\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    503\u001b[0m             \u001b[0mmcs_sub\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mmcs\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mband\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    504\u001b[0m             \u001b[0mlayer_sub\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mlayer\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mband\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mTypeError\u001b[0m: object of type 'int' has no len()"]}], "source": ["mcs = np.searchsorted(sinr2mcs_table,sinr_avg)\n", "TP = get_tp(0,layer,BMenv.nSub,True)"]}, {"cell_type": "code", "execution_count": 411, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(4000, 4, 4)\n"]}], "source": ["print(sinr.shape)"]}, {"cell_type": "code", "execution_count": 415, "metadata": {}, "outputs": [], "source": ["mi = sinr2mi(sinr)\n", "mi[mi<0.0014] = 0 #min_mi = 0.0013\n", "avg_mi = np.sum(mi,axis=1)/RI\n", "sinr_avg = mi2sinr(avg_mi)"]}, {"cell_type": "code", "execution_count": 416, "metadata": {}, "outputs": [], "source": ["mcs = np.searchsorted(sinr2mcs_table,sinr_avg)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["TP_table = TP"]}, {"cell_type": "code", "execution_count": 419, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[ 8 11  6 10]\n", " [15 13 11  8]\n", " [10 11 15 13]\n", " ...\n", " [ 7  9  8 12]\n", " [ 9  8 14 10]\n", " [ 8  9 12 12]]\n"]}], "source": ["CQI = sinr2cqi(sinr_avg)"]}, {"cell_type": "code", "execution_count": 404, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(16, 1, 3, 2, 4000, 17, 4, 4)\n"]}], "source": ["print(Layer_power.shape)"]}, {"cell_type": "code", "execution_count": 405, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(17, 4, 4)\n"]}], "source": ["Layer_power = Layer_power.reshape(-1,nUE,BMenv.nSub,layer,layer)"]}, {"cell_type": "code", "execution_count": 406, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(4000, 17, 4, 4)\n"]}], "source": []}, {"cell_type": "code", "execution_count": 408, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(4000, 4, 4)\n"]}], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 276, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(2000, 2000, 64, 4)\n"]}], "source": ["print(F.shape)"]}, {"cell_type": "code", "execution_count": 269, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(96, 2000)\n"]}], "source": ["print(HP_power.shape)"]}, {"cell_type": "code", "execution_count": 266, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(2000,)\n"]}], "source": ["print(idx.shape)"]}, {"cell_type": "code", "execution_count": 264, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(96, 8, 4)\n"]}], "source": ["codebook_re = codebook_origin.reshape(-1,8,4)\n", "print(codebook_re.shape)"]}, {"cell_type": "code", "execution_count": 267, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(2000, 8, 4)\n"]}], "source": ["print(codebook_re[idx].shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 258, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[2 0 2 0]\n"]}], "source": ["print(idx[0])"]}, {"cell_type": "code", "execution_count": 259, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(16, 1, 3, 2, 8, 4)\n"]}], "source": ["print(codebook_origin.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 262, "metadata": {}, "outputs": [{"ename": "IndexError", "evalue": "too many indices for array", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mIndexError\u001b[0m                                <PERSON><PERSON> (most recent call last)", "\u001b[0;32m/tmp/ipykernel_2103092/1170153721.py\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0mprint\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mcodebook_origin\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mtuple\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0midx\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mshape\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;31mIndexError\u001b[0m: too many indices for array"]}], "source": ["print(codebook_origin[tuple(idx)].shape)"]}, {"cell_type": "code", "execution_count": 250, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(2000, 4)\n"]}], "source": ["print(idx.shape)"]}, {"cell_type": "code", "execution_count": 245, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(4, 2000)\n"]}], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["HP_power2 = np."]}, {"cell_type": "code", "execution_count": 225, "metadata": {}, "outputs": [], "source": ["HP = UE_H@codebook"]}, {"cell_type": "code", "execution_count": 211, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(2000, 17, 4, 64)\n", "(2000, 64, 8)\n"]}], "source": ["print(H.shape)\n", "print(FRF.shape)"]}, {"cell_type": "code", "execution_count": 216, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(2000, 17, 4, 8)\n"]}], "source": ["\n", "print(UE_H.shape)"]}, {"cell_type": "code", "execution_count": 217, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 218, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(1, 1, 1, 1, 2000, 17, 4, 8)\n"]}], "source": ["print(UE_H.shape)"]}, {"cell_type": "code", "execution_count": 206, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(2000, 32, 4)\n"]}], "source": ["Beam_Port = Beam*Ports_vector\n", "print(Beam_Port.shape)"]}, {"cell_type": "code", "execution_count": 200, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(32, 4)\n"]}], "source": ["print(Ports_vector.shape)"]}, {"cell_type": "code", "execution_count": 194, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(2000, 1, 32)\n"]}, {"ename": "ValueError", "evalue": "axes don't match array", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "\u001b[0;32m/tmp/ipykernel_2103092/3473651324.py\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[1;32m      2\u001b[0m         \u001b[0mBeam\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mexpand_dims\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mBeam\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      3\u001b[0m \u001b[0mprint\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mBeam\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mshape\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 4\u001b[0;31m \u001b[0mBeam\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mBeam\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mtranspose\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;31m#16*n\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      5\u001b[0m \u001b[0mprint\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mBeam\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mshape\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mValueError\u001b[0m: axes don't match array"]}], "source": ["if len(Beam.shape) == 1:\n", "        Beam = np.expand_dims(<PERSON>am,0)\n", "print(Beam.shape)\n", "Beam = Beam.transpose(1,0) #16*n\n", "print(Beam.shape)"]}, {"cell_type": "code", "execution_count": 154, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(2000, 200, 17, 4, 64)\n"]}], "source": ["H_origin = BMenv.H_freq\n", "print(H_origin.shape)"]}, {"cell_type": "code", "execution_count": 171, "metadata": {}, "outputs": [], "source": ["H_srs = H_origin[ues,srs_choose,subs,ports]"]}, {"cell_type": "code", "execution_count": 152, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(1000, 17, 4)\n"]}], "source": ["print(srs_choose.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["subs = [[i for i in range(17)]]*4\n", "subs = np.array(subs).transpose(1,0)\n", "ports = [[i for i in range(4)]]*17\n", "ports = np.array(ports)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["def choose_H(H,time_choose):\n", "    # srs拼接H\n", "    subs = [[i for i in range(17)]]*4\n", "    subs = np.array(subs).transpose(1,0)\n", "    ports = [[i for i in range(4)]]*17\n", "    ports = np.array(ports)\n", "    H_ = H[time_choose,subs,ports]\n", "    return H_"]}, {"cell_type": "code", "execution_count": 143, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(1000, 17, 4, 64)\n"]}], "source": ["print(H.shape)"]}, {"cell_type": "code", "execution_count": 134, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(16, 1, 3, 2)\n"]}], "source": ["print(HP_power.shape)"]}, {"cell_type": "code", "execution_count": 141, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0.03442574 0.21356845 0.01609778 0.01223087 0.38140726 0.20558596\n", " 0.00742078 2.21136141 2.19039702 0.86759949 0.03448772 0.05775309\n", " 0.20567822 0.77058005]\n"]}], "source": []}, {"cell_type": "code", "execution_count": 120, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(16, 1, 3, 2, 17, 4, 4)\n"]}], "source": ["print(Layer_power.shape)"]}, {"cell_type": "code", "execution_count": 121, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "Implicit conversion to a NumPy array is not allowed. Please use `.get()` to construct a NumPy array explicitly.", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "\u001b[0;32m/tmp/ipykernel_2103092/3394310057.py\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[0mtimes\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34m[\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      2\u001b[0m \u001b[0mtimes\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mappend\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mtime\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mtime\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 3\u001b[0;31m \u001b[0mLayer_power\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0marray\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mLayer_power\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      4\u001b[0m \u001b[0mcodebook_origin\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0marray\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mcodebook_origin\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      5\u001b[0m \u001b[0mFRF\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0marray\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mFRF\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32mcupy/_core/core.pyx\u001b[0m in \u001b[0;36mcupy._core.core._ndarray_base.__array__\u001b[0;34m()\u001b[0m\n", "\u001b[0;31mTypeError\u001b[0m: Implicit conversion to a NumPy array is not allowed. Please use `.get()` to construct a NumPy array explicitly."]}], "source": ["times = []\n", "times.append(time.time())\n", "Layer_power = np.array(Layer_power)\n", "codebook_origin = np.array(codebook_origin)\n", "FRF = np.array(FRF)\n", "\n", "import numpy as dp\n", "for i in range(repeat):\n", "    HP_power = dp.trace(dp.abs(Layer_power),axis1=-1,axis2=-2)\n", "    HP_power = dp.sum(HP_power,axis = -1)\n", "\n", "    idx = dp.unravel_index(dp.argmax(HP_power),HP_power.shape)\n", "    P = codebook_origin[idx]\n", "    max_power = HP_power[idx]\n", "    lp = Layer_power[idx]\n", "    P = P*dp.sqrt(RI)\n", "    FBB = P\n", "    F = dp.matmul(FRF,FBB)\n", "    \n", "# times.append(time.time())\n", "# for i in range(repeat):\n", "#     TP_gNB,mcs_gNB,CQI_gNB,sinr_gNB,sinr_origin_gNB = cal_report(H,F,BMenv.SNR,nSub = 17,sinr_min = BMenv.sinr_min,sinr_max_diff = BMenv.sinr_max_diff)\n", "times.append(time.time())"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [], "source": ["codebook2 = codebook_origin.reshape(-1,8,4)"]}, {"cell_type": "code", "execution_count": 94, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(1, 2)\n"]}], "source": ["a = np.array([[1,2]])\n", "b = np.array([[1],[2]])\n", "print(a.shape)"]}, {"cell_type": "code", "execution_count": 95, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[5]]\n"]}], "source": ["print(a@b)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 97, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "Argument 'array' has incorrect type (expected cupy._core.core._ndarray_base, got numpy.ndarray)", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "\u001b[0;32m/tmp/ipykernel_2103092/256163257.py\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0mprint\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mdp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mmatmul\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0ma\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mb\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;32m/usr/local/conda/envs/setsumi/lib/python3.7/site-packages/cupy/_core/_gufuncs.py\u001b[0m in \u001b[0;36m__call__\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m    659\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    660\u001b[0m         args, dimsizess, loop_output_dims, outs, m_dims = self._get_args_transposed(  # NOQA\n\u001b[0;32m--> 661\u001b[0;31m             args, input_axes, outs, output_axes)\n\u001b[0m\u001b[1;32m    662\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    663\u001b[0m         \u001b[0;31m# The output shape varies depending on optional dims or not\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/conda/envs/setsumi/lib/python3.7/site-packages/cupy/_core/_gufuncs.py\u001b[0m in \u001b[0;36m_get_args_transposed\u001b[0;34m(self, args, input_axes, outs, output_axes)\u001b[0m\n\u001b[1;32m    473\u001b[0m             [a.shape[:-len(self._input_coredimss)] for a in args])\n\u001b[1;32m    474\u001b[0m         args = [_manipulation.broadcast_to(\n\u001b[0;32m--> 475\u001b[0;31m             a, shape + a.shape[-len(self._input_coredimss):]) for a in args]\n\u001b[0m\u001b[1;32m    476\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    477\u001b[0m         \u001b[0;31m# Assess input args for loop dims\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/conda/envs/setsumi/lib/python3.7/site-packages/cupy/_core/_gufuncs.py\u001b[0m in \u001b[0;36m<listcomp>\u001b[0;34m(.0)\u001b[0m\n\u001b[1;32m    473\u001b[0m             [a.shape[:-len(self._input_coredimss)] for a in args])\n\u001b[1;32m    474\u001b[0m         args = [_manipulation.broadcast_to(\n\u001b[0;32m--> 475\u001b[0;31m             a, shape + a.shape[-len(self._input_coredimss):]) for a in args]\n\u001b[0m\u001b[1;32m    476\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    477\u001b[0m         \u001b[0;31m# Assess input args for loop dims\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mTypeError\u001b[0m: Argument 'array' has incorrect type (expected cupy._core.core._ndarray_base, got numpy.ndarray)"]}], "source": ["print(dp.mat<PERSON>l(a,b))"]}], "metadata": {"kernelspec": {"display_name": "setsumi", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.15"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "f8a110c2d076917b81a0ccbfc6885a5d1bb94a5882c2e7e7ff56bc5bea07b6a0"}}}, "nbformat": 4, "nbformat_minor": 2}