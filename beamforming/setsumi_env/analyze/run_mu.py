import os,sys
sys.path.append(os.getcwd())
from setsumi_env.BM_functions import *
from setsumi_env.system_functions import *
from setsumi_env.process import *
from setsumi_env.analyze import *
from setsumi_env.utils import *
from setsumi_env.setsumi_env import setsumi_env
import numpy as np
import re
import pandas as pd
from collections import Counter
import plotly.graph_objects as go
from plotly.subplots import make_subplots

from scipy import stats
import copy

import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from IPython.display import Image
from matplotlib.gridspec import GridSpec


# base_path = './Data/test_sp2000_re/UMa/'
# Data_dir = base_path+"UMa_isd_500/"

Data_dir = "/home/<USER>/quadriga/inf_v0/"
sim_par = Sim_Log(Data_dir) 
# get_result(sim_par)
get_data(sim_par)
# TPs = sim_par.result['TPs']
loc = sim_par.data['loc']

H_freq = np.array(sim_par.data['H_freq'])
print(H_freq.shape)
H_pg = np.array(sim_par.data['H_pg'])
print(H_pg.shape)
loc = np.array(sim_par.data['loc'])
print(loc.shape)

Nt = sim_par.Ants_all

Nr = 2
NRF = 4
# result = sim_par.result
# Beam = result['Beam']
# Beam = Beam[0] #Beam的选择与PMI的选择与snr无关
# PMIs = result['PMIs']
# PMIs = PMIs[0] 
V_ants = sim_par.V_ants
H_ants = sim_par.H_ants
Ants_all = sim_par.Ants_all

draw_tool = draw()
Beams = generate_dft_beams(numVant=sim_par.V_ants,numHant=sim_par.H_ants)

for i in range(0,sim_par.nUE):
    pg = H_pg[i]
    pg_data = np.sqrt(np.power(10,(pg/10)))
    pg_data = np.expand_dims(pg_data,(1,2,3))
    H_freq[i] = H_freq[i]/pg_data/np.sqrt(2)
    
use_seed = 723
np.random.seed(use_seed)
max_length = 4000
srs_choose_all = []
sub_choose_all = []
port_choose_all = []
for i in range(0,sim_par.nUE):
    srs_choose,sub_choose,port_choose = simulate_srs(sim_par,max_length)
    srs_choose_all.append(srs_choose)
    sub_choose_all.append(sub_choose)
    port_choose_all.append(port_choose)
    
SE_mu = []
mus = [0,0.2,0.4,0.6,0.8,0.85,0.9,0.92,0.94,0.96,0.98,0.99]
gNB_tti = 2

print("begin sim mu")

for i in range(0,sim_par.nUE):
    se_longs = []
    H = H_freq[i]
    for mu_idx in range(len(mus)):
        mu = mus[mu_idx]
        coma_ue = simulate_coma(H,sub_choose_all[i],port_choose_all[i],gNB_tti,mu)
        se_long = cal_long_se(H,coma_ue,beam_tti=gNB_tti,time=sim_par.time,gNB_tti=gNB_tti)
        se_longs.append(se_long)
    print("UE "+str(i)+" simulate OK")
    SE_mu.append(se_longs)
    np.savez("se_mu",SE_mu)