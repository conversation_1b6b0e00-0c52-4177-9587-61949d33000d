import os,sys
sys.path.append(os.getcwd())
sys.path.append("../../")
from setsumi_env.BM_functions import *
from setsumi_env.system_functions import *
from setsumi_env.process import *
from setsumi_env.analyze import *
from setsumi_env.utils import *
from setsumi_env.setsumi_env import setsumi_env
import numpy as np
import pickle
import re
import pandas as pd
from collections import Counter
import plotly.graph_objects as go
from plotly.subplots import make_subplots

from scipy import stats
import copy

import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from IPython.display import Image
from matplotlib.gridspec import GridSpec
import time
from datetime import datetime

BMenv = setsumi_env(path="/data/BM_data/28GHZ/sakura/UMa_v/",max_time=400,max_length=400,gNB_tti=2,seed=1215) 
draw_tool = BMenv.draw
sim_par = BMenv.sim_par
BMenv.load_H()
# BMenv.load_data()

ue_list = np.arange(400)
times = np.arange(400)

actions = np.arange(len(BMenv.action_space))

time_diff = 20
ntime_diff = int(400/time_diff)

for time_idx in range(0,ntime_diff):
    UEs = ue_list
    times = np.arange(time_idx*time_diff,time_idx*time_diff+time_diff,1)
    BMenv.run_batch_result(UEs,times,actions)
    print(f"{datetime.now()} Time {time_idx} process UE OK")

    BMenv.store_data()