import os,sys
sys.path.append(os.getcwd())
sys.path.append("../../")
from setsumi_env.BM_functions import *
from setsumi_env.system_functions import *
from setsumi_env.process import *
from setsumi_env.analyze import *
from setsumi_env.utils import *
from setsumi_env.setsumi_env import setsumi_env
import numpy as np
import pickle
import re
import pandas as pd
from collections import Counter
import plotly.graph_objects as go
from plotly.subplots import make_subplots

from scipy import stats
import copy

import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from IPython.display import Image
from matplotlib.gridspec import GridSpec
import time
from datetime import datetime

BMenv = setsumi_env(path="/data/BM_data/28GHZ/UMa_2000_o/",max_time=200,max_length=200,gNB_tti=2,seed=777,enable_load=True) 
draw_tool = BMenv.draw
sim_par = BMenv.sim_par
BMenv.load_H()
BMenv.load_data()

ues1 = [2, 26, 33, 109, 268, 304, 328, 374, 466, 535, 592, 599, 676, 779, 844, 947, 1011, 1022, 1061, 1130, 1175, 1183, 1292, 1307, 1359, 1380, 1419, 1440, 1447, 1504, 1511, 1512, 1569, 1647, 1658, 1779, 1798, 1897]
ues2 = [99, 102, 174, 181, 275, 303, 312, 319, 333, 336, 363, 430, 589, 605, 641, 731, 752, 792, 871, 892, 976, 982, 1080, 1092, 1198, 1267, 1397, 1423, 1517, 1534, 1543, 1580, 1586, 1682, 1695, 1769, 1807, 1865, 1889, 1916, 1952]

ues_98 = [2, 26, 33, 81, 94, 103, 104, 109, 145, 148, 234, 243, 247, 248, 265, 268, 288, 304, 322, 328, 374, 410, 416, 442, 466, 477, 501, 504, 520, 535, 592, 599, 645, 668, 676, 701, 730, 744, 753, 774, 779, 811, 819, 838, 844, 847, 849, 858, 874, 933, 947, 958, 967, 981, 1003, 1011, 1012, 1013, 1022, 1044, 1061, 1085, 1097, 1100, 1130, 1136, 1175, 1183, 1184, 1208, 1213, 1217, 1252, 1292, 1307, 1353, 1359, 1380, 1399, 1417, 1419, 1432, 1440, 1447, 1461, 1483, 1504, 1511, 1512, 1538, 1569, 1574, 1601, 1623, 1647, 1658, 1733, 1743, 1779, 1798, 1833, 1893, 1897, 1909, 1925, 1931, 1967, 1972, 1986, 1997]
ues_120 = [0, 1, 7, 9, 31, 48, 54, 66, 67, 82, 85, 86, 96, 99, 102, 143, 156, 162, 164, 174, 181, 183, 185, 186, 197, 223, 236, 237, 242, 250, 260, 275, 280, 286, 303, 306, 312, 315, 319, 323, 333, 336, 348, 363, 391, 395, 402, 413, 419, 430, 437, 460, 487, 488, 494, 509, 515, 525, 532, 536, 539, 564, 576, 589, 593, 595, 605, 625, 640, 641, 663, 681, 683, 684, 696, 714, 731, 749, 752, 769, 790, 791, 792, 799, 826, 829, 832, 842, 850, 869, 871, 872, 887, 892, 900, 902, 907, 919, 926, 949, 951, 976, 982, 1018, 1026, 1051, 1080, 1090, 1092, 1095, 1099, 1106, 1108, 1119, 1169, 1170, 1171, 1177, 1178, 1182, 1194, 1198, 1204, 1212, 1224, 1236, 1250, 1267, 1284, 1287, 1293, 1304, 1315, 1316, 1328, 1329, 1364, 1378, 1386, 1397, 1413, 1415, 1423, 1424, 1428, 1477, 1507, 1517, 1518, 1519, 1534, 1543, 1550, 1558, 1572, 1578, 1580, 1582, 1586, 1591, 1629, 1631, 1645, 1662, 1673, 1679, 1682, 1687, 1694, 1695, 1732, 1739, 1740, 1741, 1744, 1747, 1754, 1766, 1769, 1770, 1773, 1778, 1785, 1792, 1795, 1800, 1806, 1807, 1813, 1826, 1832, 1845, 1848, 1849, 1862, 1865, 1871, 1885, 1889, 1898, 1900, 1907, 1911, 1916, 1920, 1922, 1928, 1952, 1953, 1957, 1977, 1979, 1990]
ue_list1 = list(np.sort(ues1+ues2))
ue_list2 = list(np.sort(ues_98+ues_120))
ue_list =  [item for item in ue_list2 if item not in ue_list1]
# ue_list = np.arange(4)
times = np.arange(200)

actions = np.arange(len(BMenv.action_space))

time_diff = 40
ntime_diff = int(200/time_diff)

for time_idx in range(0,ntime_diff):
    UEs = ue_list
    times = np.arange(time_idx*time_diff,time_idx*time_diff+time_diff,1)
    BMenv.run_batch_result(UEs,times,actions)
    print(f"{datetime.now()} Time {time_idx} process UE OK")

    BMenv.store_data()

ue_list =  [item for item in range(2000) if item not in ue_list2]

times = np.arange(200)

actions = np.arange(len(BMenv.action_space))

time_diff = 10
ntime_diff = int(200/time_diff)

for time_idx in range(0,ntime_diff):
    UEs = ue_list
    times = np.arange(time_idx*time_diff,time_idx*time_diff+time_diff,1)
    BMenv.run_batch_result(UEs,times,actions)
    print(f"{datetime.now()} Time {time_idx} process UE OK")

    BMenv.store_data()