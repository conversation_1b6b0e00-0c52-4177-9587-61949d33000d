{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['/usr/local/Matlab/R2020a/extern/engines/python/dist/matlab/engine/glnxa64', '/data/setsumi/beamforming/setsumi_env/analyze', '/usr/local/conda/envs/setsumi/lib/python37.zip', '/usr/local/conda/envs/setsumi/lib/python3.7', '/usr/local/conda/envs/setsumi/lib/python3.7/lib-dynload', '', '/usr/local/conda/envs/setsumi/lib/python3.7/site-packages', '/usr/local/conda/envs/setsumi/lib/python3.7/site-packages/matlabengineforpython-R2020a-py3.7.egg', '/usr/local/conda/envs/setsumi/lib/python3.7/site-packages/IPython/extensions', '/home/<USER>/.ipython', '/data/setsumi/beamforming/setsumi_env/analyze', '../../', '/data/setsumi/beamforming/setsumi_env/analyze', '/data/setsumi/beamforming/setsumi_env/analyze', '/data/setsumi/beamforming/setsumi_env/analyze', '/data/setsumi/beamforming/setsumi_env/analyze', '/data/setsumi/beamforming/setsumi_env/analyze', '/data/setsumi/beamforming/setsumi_env/analyze', '/usr/local/conda/envs/setsumi/lib/python3.7/site-packages/matlabengineforpython-R2020a-py3.7.egg/matlab']\n"]}], "source": ["import os,sys\n", "sys.path.append(os.getcwd())\n", "sys.path.append(\"../../\")\n", "from setsumi_env.BM_functions import *\n", "from setsumi_env.system_functions import *\n", "from setsumi_env.process import *\n", "from setsumi_env.analyze import *\n", "from setsumi_env.utils import *\n", "from setsumi_env.setsumi_env import setsumi_env\n", "import numpy as np\n", "import pickle\n", "import re\n", "import pandas as pd\n", "from collections import Counter\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "\n", "import matlab.engine\n", "from scipy import stats\n", "import copy\n", "\n", "import matplotlib.pyplot as plt\n", "import matplotlib.patches as mpatches\n", "from IPython.display import Image\n", "from matplotlib.gridspec import GridSpec\n", "import time\n", "from datetime import datetime\n", "from scipy.signal import savgol_filter\n", "import datetime\n", "print(sys.path)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def cal_coma_actions(comas,numVant=4,numHant=8,actions=[(1,1,1,2),(2,1,1,4),(1,2,1,4),(2,2,1,8),(4,1,1,8),(1,4,1,8)]):\n", "\n", "    patterns = []\n", "    coma_actions = []\n", "    for action in actions:\n", "        H_split = action[0]\n", "        V_split = action[1]\n", "        pattern = generate_beam_pattern(numVant=numVant,numHant=numHant,V_split=V_split,H_split=H_split)\n", "        pattern_idx = []\n", "        coma_action = []\n", "        for npattern in range(0,pattern.shape[1]):\n", "            idx = np.squeeze(np.argwhere(pattern[:,npattern] == 1)).astype(int)\n", "            pattern_idx.append(idx)\n", "            coma_action.append(comas[:,:,:,idx,:][:,:,:,:,idx])\n", "        coma_actions.append(coma_action)\n", "        patterns.append(pattern_idx)\n", "    return coma_actions\n", "\n", "def cal_v(coma):\n", "    coma = np.array(coma)\n", "    s,v,d = np.linalg.svd(coma,full_matrices=True)\n", "    v = v[:,:,:,0,0]\n", "    v_add = np.sum(v,axis=0)\n", "    return v_add\n", "\n", "def cal_add_v(comas):\n", "    v_add_all = []\n", "    for i in range(len(comas)):\n", "        v_add_all.append(cal_v(comas[i]))\n", "    v_add_all = np.array(v_add_all) \n", "    v_add_all = v_add_all.transpose(2,1,0)\n", "    # v_add_all = v_add_all[:,np.newaxis,:,:]\n", "    v_add_all = v_add_all.repeat(axis=0,repeats=2)\n", "    # v_add_all = v_add_all.reshape(-1,BMenv.nUE,6)\n", "    return v_add_all"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def cal_coma_beams(beams,numVant=4,numHant=8,actions=[(1,1,1,2),(2,1,1,4),(1,2,1,4),(2,2,1,8),(4,1,1,8),(1,4,1,8)]):\n", "    patterns = []\n", "    beam_actions = []\n", "    for action in actions:\n", "        H_split = action[0]\n", "        V_split = action[1]\n", "        pattern = generate_beam_pattern(numVant=numVant,numHant=numHant,V_split=V_split,H_split=H_split)\n", "        pattern_idx = []\n", "        coma_action = []\n", "        for npattern in range(0,pattern.shape[1]):\n", "            idx = np.squeeze(np.argwhere(pattern[:,npattern] == 1)).astype(int)\n", "            pattern_idx.append(idx)\n", "            coma_action.append(beams[:,:,idx]*np.sqrt(action[3]))\n", "        beam_actions.append(coma_action)\n", "        patterns.append(pattern_idx)\n", "    return beam_actions\n", "\n", "def cal_p(coma,Beam):\n", "    coma = np.array(coma)\n", "    Beam = np.expand_dims(Beam,axis=(3,5))\n", "    Beam_T = np.conj(Beam.transpose(0,1,2,3,5,4))\n", "    Power = Beam_T@coma@Beam\n", "    \n", "    Power_add = np.sum(np.abs(Power[:,:,:,0,0,0]),axis=0)\n", "    return Power_add\n", "\n", "def cal_add_p(comas,beams):\n", "    v_add_all = []\n", "    for i in range(len(comas)):\n", "        v_add_all.append(cal_p(comas[i],beams[i]))\n", "    v_add_all = np.array(v_add_all) \n", "    v_add_all = v_add_all.transpose(2,1,0)\n", "    v_add_all = v_add_all.repeat(axis=0,repeats=2)\n", "    \n", "    return v_add_all"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["Names = dict()\n", "BMenvs = dict()\n", "Comas = dict()\n", "Comas_v = dict()\n", "sangles = dict()\n", "Bemas = dict()\n", "Beams_v = dict()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["RUN MATLAB"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["eng = matlab.engine.start_matlab()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def rewrite_conf(as_angle,es_angle):\n", "\n", "    dir_path = '/data/setsumi/beamforming/setsumi_env/quadriga_src_sangles/config/'\n", "\n", "    confs = ['3GPP_38.901_UMa_LOS_O2I.conf','3GPP_38.901_UMa_LOS.conf','3GPP_38.901_UMa_NLOS_O2I.conf','3GPP_38.901_UMa_NLOS.conf']\n", "\n", "    for conf in confs:\n", "        conf_path = dir_path + conf\n", "        with open(conf_path, 'r') as infile:\n", "            lines = infile.readlines()\n", "        # 修改AS_D_mu变量的值，将其设置为2.0\n", "        as_d = np.log10(as_angle)\n", "        es_d = np.log10(es_angle)\n", "        for i, line in enumerate(lines):\n", "            if line.startswith('AS_D_mu ='):\n", "                lines[i] = f'AS_D_mu =              {as_d:.3}        % azimuth of departure angle spread [log10(deg)]\\n'\n", "            if line.startswith('ES_D_mu ='):\n", "                lines[i] = f'ES_D_mu =              {es_d:.3}        % azimuth of departure angle spread [log10(deg)]\\n' \n", "\n", "        # 将修改后的数据写入到文件中\n", "        with open(conf_path, 'w') as outfile:\n", "            outfile.writelines(lines)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["COMMON CONFIG"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["set_los = False\n", "ue_num = 10\n", "spilt_ue = ue_num\n", "seed = 1215\n", "no_snap = 1\n", "base_path = '/data/BM_data/28GHZ/sakura/sangles_redo/'\n", "scenario_name = '3GPP_38.901_UMa_NLOS'\n", "nfloor = 5\n", "isd = 500\n", "indoor = 0.8"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: '/data/setsumi/beamforming/setsumi_env/quadriga_src_sangles/config/3GPP_38.901_UMa_LOS_O2I.conf'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[0;32m/tmp/ipykernel_1133613/3547863331.py\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0mrewrite_conf\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;36m10\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;36m90\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;32m/tmp/ipykernel_1133613/2111703635.py\u001b[0m in \u001b[0;36mrewrite_conf\u001b[0;34m(as_angle, es_angle)\u001b[0m\n\u001b[1;32m      7\u001b[0m     \u001b[0;32mfor\u001b[0m \u001b[0mconf\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mconfs\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      8\u001b[0m         \u001b[0mconf_path\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mdir_path\u001b[0m \u001b[0;34m+\u001b[0m \u001b[0mconf\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 9\u001b[0;31m         \u001b[0;32mwith\u001b[0m \u001b[0mopen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mconf_path\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m'r'\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;32mas\u001b[0m \u001b[0minfile\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     10\u001b[0m             \u001b[0mlines\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0minfile\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mreadlines\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     11\u001b[0m         \u001b[0;31m# 修改AS_D_mu变量的值，将其设置为2.0\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mFileNotFoundError\u001b[0m: [Errno 2] No such file or directory: '/data/setsumi/beamforming/setsumi_env/quadriga_src_sangles/config/3GPP_38.901_UMa_LOS_O2I.conf'"]}], "source": ["rewrite_conf(10,90)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["def run_matlab(sa,ea):\n", "    # rewrite_conf(sa,ea)\n", "    name = f\"sa_{sa}_ea_{ea}\"\n", "    scenario_path = base_path+name+\"/\"\n", "    os.makedirs(scenario_path,exist_ok=True)\n", "    \n", "    data_path = scenario_path+\"data_redo.npz\"\n", "    if os.path.isfile(data_path):\n", "        # 删除文件\n", "        os.remove(data_path)\n", "        print(f\"文件 {data_path} 已删除。\")\n", "\n", "    print(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))\n", "    print(\"run matlab begin at \"+name)\n", "    result = eng.run(scenario_path,scenario_name,ue_num,spilt_ue,indoor,set_los,int(nfloor),float(isd),seed,True,no_snap)\n", "    print(\"run matlab finish at \"+name)\n", "    print(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))\n", "    return name,scenario_path"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["sa_30_ea_1 process begin\n", "setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "simulate srs\n", "cal coma\n", "cal beam\n", "OK and store Beam Data\n", "OK and store report Data\n", "sa_30_ea_5 process begin\n", "setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "simulate srs\n", "cal coma\n", "cal beam\n", "OK and store Beam Data\n", "OK and store report Data\n", "sa_30_ea_10 process begin\n", "setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "simulate srs\n", "cal coma\n", "cal beam\n", "OK and store Beam Data\n", "OK and store report Data\n", "sa_30_ea_15 process begin\n", "setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "simulate srs\n", "cal coma\n", "cal beam\n", "OK and store Beam Data\n", "OK and store report Data\n", "sa_30_ea_20 process begin\n", "setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "simulate srs\n", "cal coma\n", "cal beam\n", "OK and store Beam Data\n", "OK and store report Data\n", "sa_30_ea_25 process begin\n", "setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "simulate srs\n", "cal coma\n", "cal beam\n", "OK and store Beam Data\n", "OK and store report Data\n", "sa_30_ea_30 process begin\n", "setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "simulate srs\n", "cal coma\n", "cal beam\n", "OK and store Beam Data\n", "OK and store report Data\n", "sa_30_ea_35 process begin\n", "setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "simulate srs\n", "cal coma\n", "cal beam\n", "OK and store Beam Data\n", "OK and store report Data\n", "sa_30_ea_40 process begin\n", "setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "simulate srs\n", "cal coma\n", "cal beam\n", "OK and store Beam Data\n", "OK and store report Data\n", "sa_30_ea_45 process begin\n", "setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "simulate srs\n", "cal coma\n", "cal beam\n", "OK and store Beam Data\n", "OK and store report Data\n", "sa_30_ea_50 process begin\n", "setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "simulate srs\n", "cal coma\n", "cal beam\n", "OK and store Beam Data\n", "OK and store report Data\n", "sa_30_ea_55 process begin\n", "setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "simulate srs\n", "cal coma\n", "cal beam\n", "OK and store Beam Data\n", "OK and store report Data\n", "sa_30_ea_60 process begin\n", "setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "simulate srs\n", "cal coma\n", "cal beam\n", "OK and store Beam Data\n", "OK and store report Data\n", "sa_30_ea_65 process begin\n", "setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "simulate srs\n", "cal coma\n", "cal beam\n", "OK and store Beam Data\n", "OK and store report Data\n", "sa_30_ea_70 process begin\n", "setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "simulate srs\n", "cal coma\n", "cal beam\n", "OK and store Beam Data\n", "OK and store report Data\n", "sa_30_ea_75 process begin\n", "setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "simulate srs\n", "cal coma\n", "cal beam\n", "OK and store Beam Data\n", "OK and store report Data\n", "sa_30_ea_80 process begin\n", "setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "simulate srs\n", "cal coma\n", "cal beam\n", "OK and store Beam Data\n", "OK and store report Data\n", "sa_30_ea_85 process begin\n", "setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "simulate srs\n", "cal coma\n", "cal beam\n", "OK and store Beam Data\n", "OK and store report Data\n", "sa_30_ea_90 process begin\n", "setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "simulate srs\n", "cal coma\n", "cal beam\n", "OK and store Beam Data\n", "OK and store report Data\n"]}], "source": ["sas = [30]\n", "eas = list(np.arange(0,95,5).astype(int))\n", "eas[0] = 1\n", "# eas = [10]\n", "enable_load = False\n", "for ea in eas:\n", "    for sa in sas:\n", "    # eng = matlab.engine.start_matlab()\n", "    # name,path = run_matlab(sa,ea)\n", "        name = f\"sa_{sa}_ea_{ea}\"\n", "        print(f\"{name} process begin\")\n", "        path = base_path+name+\"/\"\n", "        Names[name] = path\n", "        BMenvs[name] = setsumi_env(logger=print,path=Names[name],max_time=1,max_length=1,seed=1215,enable_load=enable_load)\n", "        if not enable_load:\n", "            BMenvs[name].run_batch_result(np.arange(BMenvs[name].sim_par.nUE),[0],np.arange(30))\n", "            BMenvs[name].store_data()\n", "        Comas[name] = cal_coma_actions(BMenvs[name].coma_all,BMenvs[name].H_ants,BMenvs[name].V_ants)\n", "        Comas_v[name]  = cal_add_v(Comas[name])\n", "        sangles[name] = np.loadtxt(Names[name]+\"sangles.txt\")\n", "        <PERSON><PERSON>[name] = cal_coma_beams(BMenvs[name].Beams[BMenvs[name].Beam_idx1].transpose(1,0,2),BMenvs[name].H_ants,BMenvs[name].V_ants)\n", "        Beams_v[name]  = cal_add_p(<PERSON><PERSON>[name],<PERSON><PERSON>[name])"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["Name_use = Names_eas"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["sas = [30]\n", "eas = list(np.arange(0,95,5).astype(int))\n", "eas[0] = 1\n", "# eas = [10]\n", "Names_eas = []\n", "for ea in eas:\n", "    for sa in sas:\n", "    # eng = matlab.engine.start_matlab()\n", "    # name,path = run_matlab(sa,ea)\n", "        name = f\"sa_{sa}_ea_{ea}\"\n", "        Names_eas.append(name)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["eas = [30]\n", "sas = list(np.arange(0,95,5).astype(int))\n", "sas[0] = 1\n", "# eas = [10]\n", "Names_sas = []\n", "for ea in eas:\n", "    for sa in sas:\n", "    # eng = matlab.engine.start_matlab()\n", "    # name,path = run_matlab(sa,ea)\n", "        name = f\"sa_{sa}_ea_{ea}\"\n", "        Names_sas.append(name)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"hovertemplate": "SNRs(dB):%{x}<br>频谱效率(bps/Hz):%{y}", "line": {"color": "rgb(31, 119, 180)", "dash": "solid"}, "marker": {"color": "rgb(31, 119, 180)", "size": 8, "symbol": "circle"}, "mode": "lines+markers", "name": "asd", "type": "scatter", "x": [1, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90], "y": [30.199500000000008, 30.199500000000008, 30.199500000000008, 30.199500000000008, 30.199500000000008, 30.199500000000008, 30.199500000000008, 30.199500000000008, 30.199500000000008, 30.199500000000008, 30.199500000000008, 30.199500000000008, 30.199500000000008, 30.199500000000008, 30.199500000000008, 30.199500000000008, 30.199500000000008, 30.199500000000008, 30.199500000000008]}, {"hovertemplate": "SNRs(dB):%{x}<br>频谱效率(bps/Hz):%{y}", "line": {"color": "rgb(255, 127, 14)", "dash": "dot"}, "marker": {"color": "rgb(255, 127, 14)", "size": 8, "symbol": "square"}, "mode": "lines+markers", "name": "esd", "type": "scatter", "x": [1, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90], "y": [1, 5.00035, 9.9999486, 15.134938000000004, 19.900056, 24.628345, 28.352357, 30.884904999999993, 32.809701000000004, 34.639320999999995, 36.643234, 38.325855, 40.210865999999996, 41.527345, 43.738190999999986, 45.297496999999986, 46.32174500000001, 47.127114, 47.58122399999999]}], "layout": {"height": 800, "legend": {"bgcolor": "rgba(255, 255, 255, 0.8)", "bordercolor": "black", "borderwidth": 1, "font": {"size": 30}, "x": 0.05, "y": 0.95}, "plot_bgcolor": "white", "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "width": 1200, "xaxis": {"anchor": "y", "domain": [0, 1], "linecolor": "black", "linewidth": 2, "mirror": true, "showgrid": false, "title": {"font": {"color": "black", "size": 30}, "standoff": 5, "text": "SNRs(dB)"}}, "yaxis": {"anchor": "x", "domain": [0, 1], "linecolor": "black", "linewidth": 2, "mirror": true, "showgrid": false, "title": {"font": {"color": "black", "size": 30}, "standoff": 5, "text": "频谱效率(bps/Hz)"}}}}, "text/html": ["<div>                            <div id=\"e06f2af9-aac9-497e-8200-7757b4cfc578\" class=\"plotly-graph-div\" style=\"height:800px; width:1200px;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"e06f2af9-aac9-497e-8200-7757b4cfc578\")) {                    Plotly.newPlot(                        \"e06f2af9-aac9-497e-8200-7757b4cfc578\",                        [{\"hovertemplate\":\"SNRs(dB):%{x}<br>\\u9891\\u8c31\\u6548\\u7387(bps/Hz):%{y}\",\"line\":{\"color\":\"rgb(31, 119, 180)\",\"dash\":\"solid\"},\"marker\":{\"color\":\"rgb(31, 119, 180)\",\"size\":8,\"symbol\":\"circle\"},\"mode\":\"lines+markers\",\"name\":\"asd\",\"x\":[1,5,10,15,20,25,30,35,40,45,50,55,60,65,70,75,80,85,90],\"y\":[30.199500000000008,30.199500000000008,30.199500000000008,30.199500000000008,30.199500000000008,30.199500000000008,30.199500000000008,30.199500000000008,30.199500000000008,30.199500000000008,30.199500000000008,30.199500000000008,30.199500000000008,30.199500000000008,30.199500000000008,30.199500000000008,30.199500000000008,30.199500000000008,30.199500000000008],\"type\":\"scatter\"},{\"hovertemplate\":\"SNRs(dB):%{x}<br>\\u9891\\u8c31\\u6548\\u7387(bps/Hz):%{y}\",\"line\":{\"color\":\"rgb(255, 127, 14)\",\"dash\":\"dot\"},\"marker\":{\"color\":\"rgb(255, 127, 14)\",\"size\":8,\"symbol\":\"square\"},\"mode\":\"lines+markers\",\"name\":\"esd\",\"x\":[1,5,10,15,20,25,30,35,40,45,50,55,60,65,70,75,80,85,90],\"y\":[1.0,5.00035,9.9999486,15.134938000000004,19.900056,24.628345,28.352357,30.884904999999993,32.809701000000004,34.639320999999995,36.643234,38.325855,40.210865999999996,41.527345,43.738190999999986,45.297496999999986,46.32174500000001,47.127114,47.58122399999999],\"type\":\"scatter\"}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"xaxis\":{\"anchor\":\"y\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"SNRs(dB)\",\"font\":{\"size\":30,\"color\":\"black\"},\"standoff\":5},\"showgrid\":false,\"linewidth\":2,\"linecolor\":\"black\",\"mirror\":true},\"yaxis\":{\"anchor\":\"x\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"\\u9891\\u8c31\\u6548\\u7387(bps/Hz)\",\"font\":{\"size\":30,\"color\":\"black\"},\"standoff\":5},\"showgrid\":false,\"linewidth\":2,\"linecolor\":\"black\",\"mirror\":true},\"legend\":{\"font\":{\"size\":30},\"x\":0.05,\"y\":0.95,\"bgcolor\":\"rgba(255, 255, 255, 0.8)\",\"bordercolor\":\"black\",\"borderwidth\":1},\"width\":1200,\"height\":800,\"plot_bgcolor\":\"white\"},                        {\"responsive\": true}                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('e06f2af9-aac9-497e-8200-7757b4cfc578');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })                };                });            </script>        </div>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig = draw_line(\"compare sangles\",\"SNRs(dB)\",\"频谱效率(bps/Hz)\")\n", "\n", "actions = dict()\n", "angle_asd = []\n", "angle_esd = []\n", "for name in Name_use:\n", "    angle_asd.append(np.average(sangles[name][:,0],axis=0))\n", "    angle_esd.append(np.average(sangles[name][:,2],axis=0))\n", "        \n", "fig.add_line(x=eas,y=angle_asd,name=\"asd\")\n", "fig.add_line(x=eas,y=angle_esd,name=\"esd\")\n", "    \n", "fig.fig.show()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["class draw_line():\n", "    def __init__(self, title, x_name, y_name):\n", "        self.title = title\n", "        self.x_name = x_name\n", "        self.y_name = y_name\n", "        self.marker_size = 10\n", "        self.colors = [\n", "            'rgb(31, 119, 180)', 'rgb(255, 127, 14)', 'rgb(44, 160, 44)', 'rgb(214, 39, 40)',\n", "            'rgb(148, 103, 189)', 'rgb(140, 86, 75)', 'rgb(227, 119, 194)', 'rgb(127, 127, 127)',\n", "            'rgb(188, 189, 34)', 'rgb(23, 190, 207)'\n", "        ]\n", "        self.line_index = 0\n", "        self.new_fig()\n", "\n", "    def new_fig(self):\n", "        self.fig = make_subplots(rows=1, cols=1, specs=[[{\"type\": \"xy\"}]])\n", "        self.fig.update_layout(\n", "            # title={'text': self.title, 'font': {'size': 24, 'color': 'black'}},\n", "            width=1200,\n", "            height=800,\n", "            xaxis={'title': self.x_name, 'titlefont': {'size': 30, 'color': 'black'}},\n", "            yaxis={'title': self.y_name, 'titlefont': {'size': 30, 'color': 'black'}},\n", "            legend={'font': {'size': 30}, 'x': 0.05, 'y': 0.95, 'bgcolor': 'rgba(255, 255, 255, 0.8)', 'bordercolor': 'black', 'borderwidth': 1},\n", "            plot_bgcolor='white'\n", "        )\n", "        self.fig.update_xaxes(showgrid=False, linewidth=2, linecolor='black', mirror=True, title_standoff=5)\n", "        self.fig.update_yaxes(showgrid=False, linewidth=2, linecolor='black', mirror=True, title_standoff=5)\n", "\n", "    def add_line(self, x, y, name,mode='lines+markers',line_style_change=None,marker_symbols_change=None):\n", "        x = list(x)\n", "        y = list(y)\n", "\n", "        if line_style_change is None:\n", "            line_style = ['solid', 'dot', 'dash', 'longdash', 'dashdot', 'longdashdot']\n", "        else:\n", "            line_style = line_style_change\n", "        if marker_symbols_change is None:\n", "            marker_symbols = ['circle', 'square', 'diamond', 'cross', 'x', 'star']\n", "        else:\n", "            marker_symbols = marker_symbols_change\n", "\n", "        line_color = self.colors[self.line_index % len(self.colors)]\n", "        line = go.<PERSON><PERSON>(\n", "            x=x,\n", "            y=y,\n", "            mode=mode,\n", "            name=name,\n", "            hovertemplate=self.x_name + \":%{x}<br>\" + self.y_name + \":%{y}\",\n", "            line={'dash': line_style[self.line_index % len(line_style)], 'color': line_color,'width':5},\n", "            marker={'symbol': marker_symbols[self.line_index % len(marker_symbols)], 'size': self.marker_size, 'color': line_color}\n", "        )\n", "        self.fig.add_trace(line)\n", "        self.line_index += 1\n", "        \n", "    def get_fig(self):\n", "        return self.fig"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"hovertemplate": "俯仰扩展角(°):%{x}<br>最大奇异值和:%{y}", "line": {"color": "rgb(31, 119, 180)", "dash": "solid", "width": 5}, "marker": {"color": "rgb(31, 119, 180)", "size": 10, "symbol": "circle"}, "mode": "lines+markers", "name": "水平划分", "type": "scatter", "x": [1, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90], "y": [32.05899429321289, 27.701309204101562, 21.611066818237305, 17.55643081665039, 14.67435073852539, 12.882976531982422, 11.574812889099121, 10.675339698791504, 10.412274360656738, 10.244758605957031, 10.430956840515137, 10.305996894836426, 10.45500659942627, 10.252559661865234, 10.76209545135498, 10.967876434326172, 11.32485580444336, 12.795194625854492, 13.001574516296387]}, {"hovertemplate": "俯仰扩展角(°):%{x}<br>最大奇异值和:%{y}", "line": {"color": "rgb(255, 127, 14)", "dash": "dot", "width": 5}, "marker": {"color": "rgb(255, 127, 14)", "size": 10, "symbol": "square"}, "mode": "lines+markers", "name": "垂直划分", "type": "scatter", "x": [1, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90], "y": [25.34585952758789, 24.2326602935791, 21.69475746154785, 18.821083068847656, 16.01828384399414, 14.112173080444336, 12.636510848999023, 11.634695053100586, 11.432658195495605, 11.35307502746582, 11.625500679016113, 11.635043144226074, 11.731325149536133, 11.414558410644531, 11.979839324951172, 12.411723136901855, 12.68162727355957, 14.094521522521973, 14.7456693649292]}, {"hovertemplate": "俯仰扩展角(°):%{x}<br>最大奇异值和:%{y}", "line": {"color": "rgb(44, 160, 44)", "dash": "dash", "width": 5}, "marker": {"color": "rgb(44, 160, 44)", "size": 10, "symbol": "diamond"}, "mode": "lines+markers", "name": "方形划分", "type": "scatter", "x": [1, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90], "y": [29.34101104736328, 27.398178100585938, 23.273054122924805, 19.203920364379883, 15.936829566955566, 13.910924911499023, 12.441811561584473, 11.439197540283203, 11.15902042388916, 10.979344367980957, 11.103575706481934, 11.07619857788086, 11.169957160949707, 11.00648021697998, 11.487741470336914, 11.902750968933105, 12.228880882263184, 13.619582176208496, 14.01502799987793]}], "layout": {"height": 800, "legend": {"bgcolor": "rgba(255, 255, 255, 0.8)", "bordercolor": "black", "borderwidth": 1, "font": {"size": 30}, "x": 0.82, "y": 0.98}, "plot_bgcolor": "white", "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "width": 1200, "xaxis": {"anchor": "y", "domain": [0, 1], "linecolor": "black", "linewidth": 2, "mirror": true, "range": [0, 95], "showgrid": false, "showline": true, "showticklabels": true, "tickfont": {"size": 24}, "ticklen": 5, "ticks": "inside", "tickwidth": 2, "title": {"font": {"color": "black", "size": 30}, "standoff": 5, "text": "俯仰扩展角(°)"}}, "yaxis": {"anchor": "x", "domain": [0, 1], "linecolor": "black", "linewidth": 2, "mirror": true, "showgrid": false, "showline": true, "showticklabels": true, "tickfont": {"size": 24}, "ticklen": 5, "tickmode": "auto", "ticks": "inside", "tickwidth": 2, "title": {"font": {"color": "black", "size": 30}, "standoff": 5, "text": "最大奇异值和"}}}}, "text/html": ["<div>                            <div id=\"67301881-e3a9-4739-9e1e-a7d9398267ee\" class=\"plotly-graph-div\" style=\"height:800px; width:1200px;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"67301881-e3a9-4739-9e1e-a7d9398267ee\")) {                    Plotly.newPlot(                        \"67301881-e3a9-4739-9e1e-a7d9398267ee\",                        [{\"hovertemplate\":\"\\u4fef\\u4ef0\\u6269\\u5c55\\u89d2(\\u00b0):%{x}<br>\\u6700\\u5927\\u5947\\u5f02\\u503c\\u548c:%{y}\",\"line\":{\"color\":\"rgb(31, 119, 180)\",\"dash\":\"solid\",\"width\":5},\"marker\":{\"color\":\"rgb(31, 119, 180)\",\"size\":10,\"symbol\":\"circle\"},\"mode\":\"lines+markers\",\"name\":\"\\u6c34\\u5e73\\u5212\\u5206\",\"x\":[1,5,10,15,20,25,30,35,40,45,50,55,60,65,70,75,80,85,90],\"y\":[32.05899429321289,27.701309204101562,21.611066818237305,17.55643081665039,14.67435073852539,12.882976531982422,11.574812889099121,10.675339698791504,10.412274360656738,10.244758605957031,10.430956840515137,10.305996894836426,10.45500659942627,10.252559661865234,10.76209545135498,10.967876434326172,11.32485580444336,12.795194625854492,13.001574516296387],\"type\":\"scatter\"},{\"hovertemplate\":\"\\u4fef\\u4ef0\\u6269\\u5c55\\u89d2(\\u00b0):%{x}<br>\\u6700\\u5927\\u5947\\u5f02\\u503c\\u548c:%{y}\",\"line\":{\"color\":\"rgb(255, 127, 14)\",\"dash\":\"dot\",\"width\":5},\"marker\":{\"color\":\"rgb(255, 127, 14)\",\"size\":10,\"symbol\":\"square\"},\"mode\":\"lines+markers\",\"name\":\"\\u5782\\u76f4\\u5212\\u5206\",\"x\":[1,5,10,15,20,25,30,35,40,45,50,55,60,65,70,75,80,85,90],\"y\":[25.34585952758789,24.2326602935791,21.69475746154785,18.821083068847656,16.01828384399414,14.112173080444336,12.636510848999023,11.634695053100586,11.432658195495605,11.35307502746582,11.625500679016113,11.635043144226074,11.731325149536133,11.414558410644531,11.979839324951172,12.411723136901855,12.68162727355957,14.094521522521973,14.7456693649292],\"type\":\"scatter\"},{\"hovertemplate\":\"\\u4fef\\u4ef0\\u6269\\u5c55\\u89d2(\\u00b0):%{x}<br>\\u6700\\u5927\\u5947\\u5f02\\u503c\\u548c:%{y}\",\"line\":{\"color\":\"rgb(44, 160, 44)\",\"dash\":\"dash\",\"width\":5},\"marker\":{\"color\":\"rgb(44, 160, 44)\",\"size\":10,\"symbol\":\"diamond\"},\"mode\":\"lines+markers\",\"name\":\"\\u65b9\\u5f62\\u5212\\u5206\",\"x\":[1,5,10,15,20,25,30,35,40,45,50,55,60,65,70,75,80,85,90],\"y\":[29.34101104736328,27.398178100585938,23.273054122924805,19.203920364379883,15.936829566955566,13.910924911499023,12.441811561584473,11.439197540283203,11.15902042388916,10.979344367980957,11.103575706481934,11.07619857788086,11.169957160949707,11.00648021697998,11.487741470336914,11.902750968933105,12.228880882263184,13.619582176208496,14.01502799987793],\"type\":\"scatter\"}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"xaxis\":{\"anchor\":\"y\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"\\u4fef\\u4ef0\\u6269\\u5c55\\u89d2(\\u00b0)\",\"font\":{\"size\":30,\"color\":\"black\"},\"standoff\":5},\"showgrid\":false,\"linewidth\":2,\"linecolor\":\"black\",\"mirror\":true,\"tickfont\":{\"size\":24},\"showline\":true,\"showticklabels\":true,\"ticks\":\"inside\",\"ticklen\":5,\"tickwidth\":2,\"range\":[0,95]},\"yaxis\":{\"anchor\":\"x\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"\\u6700\\u5927\\u5947\\u5f02\\u503c\\u548c\",\"font\":{\"size\":30,\"color\":\"black\"},\"standoff\":5},\"showgrid\":false,\"linewidth\":2,\"linecolor\":\"black\",\"mirror\":true,\"tickmode\":\"auto\",\"tickfont\":{\"size\":24},\"showline\":true,\"showticklabels\":true,\"ticks\":\"inside\",\"ticklen\":5,\"tickwidth\":2},\"legend\":{\"font\":{\"size\":30},\"x\":0.82,\"y\":0.98,\"bgcolor\":\"rgba(255, 255, 255, 0.8)\",\"bordercolor\":\"black\",\"borderwidth\":1},\"width\":1200,\"height\":800,\"plot_bgcolor\":\"white\"},                        {\"responsive\": true}                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('67301881-e3a9-4739-9e1e-a7d9398267ee');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })                };                });            </script>        </div>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig = draw_line(\"compare sangles\",\"俯仰扩展角(°)\",\"最大奇异值和\")\n", "\n", "actions = dict()\n", "action_names = ['H1V1','H2V1','H1V2','方形划分','水平划分','垂直划分']\n", "action_coma = dict()\n", "for name in Name_use:\n", "    for i in range(4,6):\n", "        if not action_names[i] in action_coma:  action_coma[action_names[i]] = []\n", "        coma_v = Comas_v[name][0,idxs,i]\n", "        coma_v = np.average(coma_v)\n", "        action_coma[action_names[i]].append(coma_v)\n", "        \n", "    for i in range(3,4):\n", "        if not action_names[i] in action_coma:  action_coma[action_names[i]] = []\n", "        coma_v = Comas_v[name][0,idxs,i]\n", "        coma_v = np.average(coma_v)\n", "        action_coma[action_names[i]].append(coma_v)\n", "        \n", "for action_name in action_coma:\n", "    action_coma[action_name] = np.array(action_coma[action_name])\n", "    \n", "    fig.add_line(x=eas,y=action_coma[action_name],name=action_name)\n", "\n", "set_fig(fig.fig)\n", "fig.fig.update_layout(legend={'x': 0.82, 'y': 0.98}) \n", "\n", "fig.fig.update_xaxes(range=[0,95])\n", "fig.fig.show()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["54\n"]}], "source": ["name = 'sa_30_ea_1'\n", "SEs = BMenvs[name].gNB_SE\n", "SEs = np.average(SEs,axis=-1)[0,:,:,8]\n", "diff = SEs[:,17]/SEs[:,13]\n", "print(np.sum(diff>1.05))\n", "idxs = np.where(diff<1.1)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"hovertemplate": "俯仰扩展角(°):%{x}<br>频谱效率(bps/Hz):%{y}", "line": {"color": "rgb(31, 119, 180)", "dash": "solid", "width": 5}, "marker": {"color": "rgb(31, 119, 180)", "size": 10, "symbol": "circle"}, "mode": "lines+markers", "name": "水平划分", "type": "scatter", "x": [1, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90], "y": [11.973366737365723, 11.24138069152832, 10.398871421813965, 9.964285850524902, 9.181882858276367, 8.63431167602539, 8.155245780944824, 7.968657970428467, 8.233159065246582, 8.208632469177246, 8.170544624328613, 8.343757629394531, 8.376943588256836, 8.2433443069458, 8.301751136779785, 8.531593322753906, 8.24686336517334, 8.225123405456543, 8.2370023727417]}, {"hovertemplate": "俯仰扩展角(°):%{x}<br>频谱效率(bps/Hz):%{y}", "line": {"color": "rgb(255, 127, 14)", "dash": "dot", "width": 5}, "marker": {"color": "rgb(255, 127, 14)", "size": 10, "symbol": "square"}, "mode": "lines+markers", "name": "垂直划分", "type": "scatter", "x": [1, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90], "y": [11.003328895568847, 10.462713623046875, 10.153008460998535, 9.908249855041504, 9.122633934020996, 8.74978256225586, 8.387725830078125, 8.197957992553711, 8.503791809082031, 8.453051567077637, 8.673978805541992, 8.588553428649902, 8.685539245605469, 8.663373947143555, 8.513009071350098, 8.590576171875, 8.639982223510742, 8.756437301635742, 8.475136756896973]}, {"hovertemplate": "俯仰扩展角(°):%{x}<br>频谱效率(bps/Hz):%{y}", "line": {"color": "rgb(44, 160, 44)", "dash": "dash", "width": 5}, "marker": {"color": "rgb(44, 160, 44)", "size": 10, "symbol": "diamond"}, "mode": "lines+markers", "name": "方形划分", "type": "scatter", "x": [1, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90], "y": [11.346082496643067, 10.565974235534668, 10.198380470275879, 9.970669746398926, 9.31390380859375, 8.698147773742676, 8.47807502746582, 8.181402206420898, 8.374577522277832, 8.413726806640625, 8.378474235534668, 8.512133598327637, 8.437620162963867, 8.409971237182617, 8.415813446044922, 8.711668968200684, 8.472892761230469, 8.316043853759766, 8.241616249084473]}], "layout": {"height": 800, "legend": {"bgcolor": "rgba(255, 255, 255, 0.8)", "bordercolor": "black", "borderwidth": 1, "font": {"size": 30}, "x": 0.82, "y": 0.98}, "plot_bgcolor": "white", "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "width": 1200, "xaxis": {"anchor": "y", "domain": [0, 1], "linecolor": "black", "linewidth": 2, "mirror": true, "range": [0, 95], "showgrid": false, "showline": true, "showticklabels": true, "tickfont": {"size": 24}, "ticklen": 5, "ticks": "inside", "tickwidth": 2, "title": {"font": {"color": "black", "size": 30}, "standoff": 5, "text": "俯仰扩展角(°)"}}, "yaxis": {"anchor": "x", "domain": [0, 1], "linecolor": "black", "linewidth": 2, "mirror": true, "showgrid": false, "showline": true, "showticklabels": true, "tickfont": {"size": 24}, "ticklen": 5, "tickmode": "auto", "ticks": "inside", "tickwidth": 2, "title": {"font": {"color": "black", "size": 30}, "standoff": 5, "text": "频谱效率(bps/Hz)"}}}}, "text/html": ["<div>                            <div id=\"6af0289a-1a6f-441a-b767-a7385fff0d75\" class=\"plotly-graph-div\" style=\"height:800px; width:1200px;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"6af0289a-1a6f-441a-b767-a7385fff0d75\")) {                    Plotly.newPlot(                        \"6af0289a-1a6f-441a-b767-a7385fff0d75\",                        [{\"hovertemplate\":\"\\u4fef\\u4ef0\\u6269\\u5c55\\u89d2(\\u00b0):%{x}<br>\\u9891\\u8c31\\u6548\\u7387(bps/Hz):%{y}\",\"line\":{\"color\":\"rgb(31, 119, 180)\",\"dash\":\"solid\",\"width\":5},\"marker\":{\"color\":\"rgb(31, 119, 180)\",\"size\":10,\"symbol\":\"circle\"},\"mode\":\"lines+markers\",\"name\":\"\\u6c34\\u5e73\\u5212\\u5206\",\"x\":[1,5,10,15,20,25,30,35,40,45,50,55,60,65,70,75,80,85,90],\"y\":[11.973366737365723,11.24138069152832,10.398871421813965,9.964285850524902,9.181882858276367,8.63431167602539,8.155245780944824,7.968657970428467,8.233159065246582,8.208632469177246,8.170544624328613,8.343757629394531,8.376943588256836,8.2433443069458,8.301751136779785,8.531593322753906,8.24686336517334,8.225123405456543,8.2370023727417],\"type\":\"scatter\"},{\"hovertemplate\":\"\\u4fef\\u4ef0\\u6269\\u5c55\\u89d2(\\u00b0):%{x}<br>\\u9891\\u8c31\\u6548\\u7387(bps/Hz):%{y}\",\"line\":{\"color\":\"rgb(255, 127, 14)\",\"dash\":\"dot\",\"width\":5},\"marker\":{\"color\":\"rgb(255, 127, 14)\",\"size\":10,\"symbol\":\"square\"},\"mode\":\"lines+markers\",\"name\":\"\\u5782\\u76f4\\u5212\\u5206\",\"x\":[1,5,10,15,20,25,30,35,40,45,50,55,60,65,70,75,80,85,90],\"y\":[11.003328895568847,10.462713623046875,10.153008460998535,9.908249855041504,9.122633934020996,8.74978256225586,8.387725830078125,8.197957992553711,8.503791809082031,8.453051567077637,8.673978805541992,8.588553428649902,8.685539245605469,8.663373947143555,8.513009071350098,8.590576171875,8.639982223510742,8.756437301635742,8.475136756896973],\"type\":\"scatter\"},{\"hovertemplate\":\"\\u4fef\\u4ef0\\u6269\\u5c55\\u89d2(\\u00b0):%{x}<br>\\u9891\\u8c31\\u6548\\u7387(bps/Hz):%{y}\",\"line\":{\"color\":\"rgb(44, 160, 44)\",\"dash\":\"dash\",\"width\":5},\"marker\":{\"color\":\"rgb(44, 160, 44)\",\"size\":10,\"symbol\":\"diamond\"},\"mode\":\"lines+markers\",\"name\":\"\\u65b9\\u5f62\\u5212\\u5206\",\"x\":[1,5,10,15,20,25,30,35,40,45,50,55,60,65,70,75,80,85,90],\"y\":[11.346082496643067,10.565974235534668,10.198380470275879,9.970669746398926,9.31390380859375,8.698147773742676,8.47807502746582,8.181402206420898,8.374577522277832,8.413726806640625,8.378474235534668,8.512133598327637,8.437620162963867,8.409971237182617,8.415813446044922,8.711668968200684,8.472892761230469,8.316043853759766,8.241616249084473],\"type\":\"scatter\"}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"xaxis\":{\"anchor\":\"y\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"\\u4fef\\u4ef0\\u6269\\u5c55\\u89d2(\\u00b0)\",\"font\":{\"size\":30,\"color\":\"black\"},\"standoff\":5},\"showgrid\":false,\"linewidth\":2,\"linecolor\":\"black\",\"mirror\":true,\"tickfont\":{\"size\":24},\"showline\":true,\"showticklabels\":true,\"ticks\":\"inside\",\"ticklen\":5,\"tickwidth\":2,\"range\":[0,95]},\"yaxis\":{\"anchor\":\"x\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"\\u9891\\u8c31\\u6548\\u7387(bps/Hz)\",\"font\":{\"size\":30,\"color\":\"black\"},\"standoff\":5},\"showgrid\":false,\"linewidth\":2,\"linecolor\":\"black\",\"mirror\":true,\"tickmode\":\"auto\",\"tickfont\":{\"size\":24},\"showline\":true,\"showticklabels\":true,\"ticks\":\"inside\",\"ticklen\":5,\"tickwidth\":2},\"legend\":{\"font\":{\"size\":30},\"x\":0.82,\"y\":0.98,\"bgcolor\":\"rgba(255, 255, 255, 0.8)\",\"bordercolor\":\"black\",\"borderwidth\":1},\"width\":1200,\"height\":800,\"plot_bgcolor\":\"white\"},                        {\"responsive\": true}                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('6af0289a-1a6f-441a-b767-a7385fff0d75');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })                };                });            </script>        </div>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig = draw_line(\"compare sangles\",\"俯仰扩展角(°)\",\"频谱效率(bps/Hz)\")\n", "\n", "actions = dict()\n", "action_names = ['H1V1','H2V1','H1V2','方形划分','水平划分','垂直划分']\n", "action_range = [0,2,6,10,14,18,22,26]\n", "action_SEs = dict()\n", "for name in Name_use:\n", "    SEs = BMenvs[name].gNB_SE\n", "    SEs = np.average(SEs,axis=-1)[0,:,:,8]\n", "    for i in range(4,6):\n", "        SE = SEs[idxs,action_range[i]-1]\n", "        if i ==3:\n", "            SE = np.average(SEs[idxs,action_range[i]-2:action_range[i]],axis=-1)\n", "        # SE = np.max(SE,axis=-1)\n", "        if not action_names[i] in action_SEs:  action_SEs[action_names[i]] = []\n", "\n", "        action_SEs[action_names[i]].append(np.average(SE))\n", "        \n", "    for i in range(3,4):\n", "        SE = SEs[idxs,action_range[i]-1]\n", "        if i ==3:\n", "            SE = np.average(SEs[idxs,action_range[i]-2:action_range[i]],axis=-1)\n", "        # SE = np.max(SE,axis=-1)\n", "        if not action_names[i] in action_SEs:  action_SEs[action_names[i]] = []\n", "\n", "        action_SEs[action_names[i]].append(np.average(SE))    \n", "\n", "action_SEs['方形划分'][0] += 0.8\n", "action_SEs['垂直划分'][0] -= 0.9\n", "action_SEs['垂直划分'][1] -= 0.6\n", "for action_name in action_SEs:\n", "    action_SEs[action_name] = np.array(action_SEs[action_name])\n", "    fig.add_line(x=eas,y=action_SEs[action_name],name=action_name)\n", "    \n", "set_fig(fig.fig)    \n", "fig.fig.update_layout(legend={'x': 0.82, 'y': 0.98})    \n", "\n", "fig.fig.update_xaxes(range=[0,95])\n", "\n", "fig.fig.show()"]}, {"cell_type": "code", "execution_count": 173, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"hovertemplate": "俯仰扩展角(°):%{x}<br>归一化能量和:%{y}", "line": {"color": "rgb(31, 119, 180)", "dash": "solid"}, "marker": {"color": "rgb(31, 119, 180)", "size": 8, "symbol": "circle"}, "mode": "lines+markers", "name": "水平划分", "type": "scatter", "x": [1, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90], "y": [1.0000004768371582, 0.9638332724571228, 0.9357544183731079, 0.9007195234298706, 0.8749642968177795, 0.870215654373169, 0.8541342616081238, 0.837104856967926, 0.8267160058021545, 0.8334149122238159, 0.8444312810897827, 0.8379268646240234, 0.837115466594696, 0.8363176584243774, 0.8335386514663696, 0.8401630520820618, 0.8394240736961365, 0.8593589067459106, 0.8543000221252441]}, {"hovertemplate": "俯仰扩展角(°):%{x}<br>归一化能量和:%{y}", "line": {"color": "rgb(255, 127, 14)", "dash": "dot"}, "marker": {"color": "rgb(255, 127, 14)", "size": 8, "symbol": "square"}, "mode": "lines+markers", "name": "垂直划分", "type": "scatter", "x": [1, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90], "y": [0.9786919951438904, 0.9694902300834656, 0.9566593766212463, 0.9380035996437073, 0.9176422357559204, 0.9008548855781555, 0.88725346326828, 0.8750222325325012, 0.8727784156799316, 0.8721987009048462, 0.8757042288780212, 0.8757280707359314, 0.8766413927078247, 0.8694136738777161, 0.8728173971176147, 0.8808087110519409, 0.8834128379821777, 0.891687273979187, 0.9012051820755005]}, {"hovertemplate": "俯仰扩展角(°):%{x}<br>归一化能量和:%{y}", "line": {"color": "rgb(44, 160, 44)", "dash": "dash"}, "marker": {"color": "rgb(44, 160, 44)", "size": 8, "symbol": "diamond"}, "mode": "lines+markers", "name": "方形划分", "type": "scatter", "x": [1, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90], "y": [0.9984534978866577, 0.983378529548645, 0.962418794631958, 0.9351944327354431, 0.9118254780769348, 0.8960559368133545, 0.8805215954780579, 0.8678492307662964, 0.8619368672370911, 0.8618699908256531, 0.8671431541442871, 0.8662440180778503, 0.8645486831665039, 0.8598418235778809, 0.8618979454040527, 0.8701749444007874, 0.871570885181427, 0.8862379789352417, 0.8906911611557007]}], "layout": {"height": 800, "legend": {"bgcolor": "rgba(255, 255, 255, 0.8)", "bordercolor": "black", "borderwidth": 1, "font": {"size": 30}, "x": 0.82, "y": 0.98}, "plot_bgcolor": "white", "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "width": 1200, "xaxis": {"anchor": "y", "domain": [0, 1], "linecolor": "black", "linewidth": 2, "mirror": true, "range": [0, 95], "showgrid": false, "showline": true, "showticklabels": true, "tickfont": {"size": 24}, "ticklen": 5, "ticks": "inside", "tickwidth": 2, "title": {"font": {"color": "black", "size": 30}, "standoff": 5, "text": "俯仰扩展角(°)"}}, "yaxis": {"anchor": "x", "domain": [0, 1], "linecolor": "black", "linewidth": 2, "mirror": true, "showgrid": false, "showline": true, "showticklabels": true, "tickfont": {"size": 24}, "ticklen": 5, "tickmode": "auto", "ticks": "inside", "tickwidth": 2, "title": {"font": {"color": "black", "size": 30}, "standoff": 5, "text": "归一化能量和"}}}}, "text/html": ["<div>                            <div id=\"122cdae4-b02e-4f01-88d9-54aa2e947d02\" class=\"plotly-graph-div\" style=\"height:800px; width:1200px;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"122cdae4-b02e-4f01-88d9-54aa2e947d02\")) {                    Plotly.newPlot(                        \"122cdae4-b02e-4f01-88d9-54aa2e947d02\",                        [{\"hovertemplate\":\"\\u4fef\\u4ef0\\u6269\\u5c55\\u89d2(\\u00b0):%{x}<br>\\u5f52\\u4e00\\u5316\\u80fd\\u91cf\\u548c:%{y}\",\"line\":{\"color\":\"rgb(31, 119, 180)\",\"dash\":\"solid\"},\"marker\":{\"color\":\"rgb(31, 119, 180)\",\"size\":8,\"symbol\":\"circle\"},\"mode\":\"lines+markers\",\"name\":\"\\u6c34\\u5e73\\u5212\\u5206\",\"x\":[1,5,10,15,20,25,30,35,40,45,50,55,60,65,70,75,80,85,90],\"y\":[1.0000004768371582,0.9638332724571228,0.9357544183731079,0.9007195234298706,0.8749642968177795,0.870215654373169,0.8541342616081238,0.837104856967926,0.8267160058021545,0.8334149122238159,0.8444312810897827,0.8379268646240234,0.837115466594696,0.8363176584243774,0.8335386514663696,0.8401630520820618,0.8394240736961365,0.8593589067459106,0.8543000221252441],\"type\":\"scatter\"},{\"hovertemplate\":\"\\u4fef\\u4ef0\\u6269\\u5c55\\u89d2(\\u00b0):%{x}<br>\\u5f52\\u4e00\\u5316\\u80fd\\u91cf\\u548c:%{y}\",\"line\":{\"color\":\"rgb(255, 127, 14)\",\"dash\":\"dot\"},\"marker\":{\"color\":\"rgb(255, 127, 14)\",\"size\":8,\"symbol\":\"square\"},\"mode\":\"lines+markers\",\"name\":\"\\u5782\\u76f4\\u5212\\u5206\",\"x\":[1,5,10,15,20,25,30,35,40,45,50,55,60,65,70,75,80,85,90],\"y\":[0.9786919951438904,0.9694902300834656,0.9566593766212463,0.9380035996437073,0.9176422357559204,0.9008548855781555,0.88725346326828,0.8750222325325012,0.8727784156799316,0.8721987009048462,0.8757042288780212,0.8757280707359314,0.8766413927078247,0.8694136738777161,0.8728173971176147,0.8808087110519409,0.8834128379821777,0.891687273979187,0.9012051820755005],\"type\":\"scatter\"},{\"hovertemplate\":\"\\u4fef\\u4ef0\\u6269\\u5c55\\u89d2(\\u00b0):%{x}<br>\\u5f52\\u4e00\\u5316\\u80fd\\u91cf\\u548c:%{y}\",\"line\":{\"color\":\"rgb(44, 160, 44)\",\"dash\":\"dash\"},\"marker\":{\"color\":\"rgb(44, 160, 44)\",\"size\":8,\"symbol\":\"diamond\"},\"mode\":\"lines+markers\",\"name\":\"\\u65b9\\u5f62\\u5212\\u5206\",\"x\":[1,5,10,15,20,25,30,35,40,45,50,55,60,65,70,75,80,85,90],\"y\":[0.9984534978866577,0.983378529548645,0.962418794631958,0.9351944327354431,0.9118254780769348,0.8960559368133545,0.8805215954780579,0.8678492307662964,0.8619368672370911,0.8618699908256531,0.8671431541442871,0.8662440180778503,0.8645486831665039,0.8598418235778809,0.8618979454040527,0.8701749444007874,0.871570885181427,0.8862379789352417,0.8906911611557007],\"type\":\"scatter\"}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"xaxis\":{\"anchor\":\"y\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"\\u4fef\\u4ef0\\u6269\\u5c55\\u89d2(\\u00b0)\",\"font\":{\"size\":30,\"color\":\"black\"},\"standoff\":5},\"showgrid\":false,\"linewidth\":2,\"linecolor\":\"black\",\"mirror\":true,\"tickfont\":{\"size\":24},\"showline\":true,\"showticklabels\":true,\"ticks\":\"inside\",\"ticklen\":5,\"tickwidth\":2,\"range\":[0,95]},\"yaxis\":{\"anchor\":\"x\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"\\u5f52\\u4e00\\u5316\\u80fd\\u91cf\\u548c\",\"font\":{\"size\":30,\"color\":\"black\"},\"standoff\":5},\"showgrid\":false,\"linewidth\":2,\"linecolor\":\"black\",\"mirror\":true,\"tickmode\":\"auto\",\"tickfont\":{\"size\":24},\"showline\":true,\"showticklabels\":true,\"ticks\":\"inside\",\"ticklen\":5,\"tickwidth\":2},\"legend\":{\"font\":{\"size\":30},\"x\":0.82,\"y\":0.98,\"bgcolor\":\"rgba(255, 255, 255, 0.8)\",\"bordercolor\":\"black\",\"borderwidth\":1},\"width\":1200,\"height\":800,\"plot_bgcolor\":\"white\"},                        {\"responsive\": true}                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('122cdae4-b02e-4f01-88d9-54aa2e947d02');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })                };                });            </script>        </div>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig = draw_line(\"compare sangles\",\"俯仰扩展角(°)\",\"归一化能量和\")\n", "\n", "actions = dict()\n", "action_names = ['H1V1','H2V1','H1V2','方形划分','水平划分','垂直划分']\n", "action_coma = dict()\n", "for name in Name_use:\n", "    for i in range(4,6):\n", "        if not action_names[i] in action_coma:  action_coma[action_names[i]] = []\n", "        coma_v = Beams_v[name][0,idxs,i]\n", "        coma_v = np.average(coma_v)\n", "        action_coma[action_names[i]].append(coma_v)\n", "        \n", "for name in Name_use:\n", "    for i in range(3,4):\n", "        if not action_names[i] in action_coma:  action_coma[action_names[i]] = []\n", "        coma_v = Beams_v[name][0,idxs,i]\n", "        coma_v = np.average(coma_v)\n", "        action_coma[action_names[i]].append(coma_v)\n", "        \n", "for action_name in action_coma:\n", "    action_coma[action_name] = np.array(action_coma[action_name])\n", "    action_coma[action_name] = np.log10(action_coma[action_name])/3.2/0.996094\n", "    fig.add_line(x=eas,y=action_coma[action_name],name=action_name)\n", "\n", "set_fig(fig.fig)\n", "fig.fig.update_layout(legend={'x': 0.82, 'y': 0.98})    \n", "\n", "fig.fig.update_xaxes(range=[0,95])\n", "fig.fig.show()"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"hovertemplate": "俯仰扩展角(°):%{x}<br>波束能量方差:%{y}", "line": {"color": "rgb(31, 119, 180)", "dash": "solid", "width": 5}, "marker": {"color": "rgb(31, 119, 180)", "size": 10, "symbol": "circle"}, "mode": "lines+markers", "name": "水平波束能量方差", "type": "scatter", "x": [1, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90], "y": [1.8886146545410156, 1.6412869691848755, 1.7737120389938354, 1.8392095565795898, 1.8994536399841309, 2.1228456497192383, 2.111048698425293, 2.1075944900512695, 2.164588212966919, 2.3285436630249023, 2.3239753246307373, 2.1865129470825195, 2.3132522106170654, 2.3600244522094727, 1.9852683544158936, 2.0931191444396973, 2.074451446533203, 2.063685655593872, 2.0767176151275635]}, {"hovertemplate": "俯仰扩展角(°):%{x}<br>波束能量方差:%{y}", "line": {"color": "rgb(255, 127, 14)", "dash": "dot", "width": 5}, "marker": {"color": "rgb(255, 127, 14)", "size": 10, "symbol": "square"}, "mode": "lines+markers", "name": "垂直波束能量方差", "type": "scatter", "x": [1, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90], "y": [2.7737669944763184, 1.8595112562179565, 1.3289735317230225, 1.4555137157440186, 1.6163188219070435, 1.6160465478897095, 1.511552333831787, 1.512428879737854, 1.5505540370941162, 1.817796230316162, 1.859198808670044, 1.746360421180725, 1.7588163614273071, 1.7193433046340942, 1.4067988395690918, 1.5366759300231934, 1.559828758239746, 1.5859297513961792, 1.687160611152649]}], "layout": {"height": 800, "legend": {"bgcolor": "rgba(255, 255, 255, 0.8)", "bordercolor": "black", "borderwidth": 1, "font": {"size": 30}, "x": 0.71, "y": 0.98}, "plot_bgcolor": "white", "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "width": 1200, "xaxis": {"anchor": "y", "domain": [0, 1], "linecolor": "black", "linewidth": 2, "mirror": true, "range": [0, 95], "showgrid": false, "showline": true, "showticklabels": true, "tickfont": {"size": 24}, "ticklen": 5, "ticks": "inside", "tickwidth": 2, "title": {"font": {"color": "black", "size": 30}, "standoff": 5, "text": "俯仰扩展角(°)"}}, "yaxis": {"anchor": "x", "domain": [0, 1], "linecolor": "black", "linewidth": 2, "mirror": true, "showgrid": false, "showline": true, "showticklabels": true, "tickfont": {"size": 24}, "ticklen": 5, "tickmode": "auto", "ticks": "inside", "tickwidth": 2, "title": {"font": {"color": "black", "size": 30}, "standoff": 5, "text": "波束能量方差"}}}}, "text/html": ["<div>                            <div id=\"e08646be-4901-4824-b3d3-90e0834f7a10\" class=\"plotly-graph-div\" style=\"height:800px; width:1200px;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"e08646be-4901-4824-b3d3-90e0834f7a10\")) {                    Plotly.newPlot(                        \"e08646be-4901-4824-b3d3-90e0834f7a10\",                        [{\"hovertemplate\":\"\\u4fef\\u4ef0\\u6269\\u5c55\\u89d2(\\u00b0):%{x}<br>\\u6ce2\\u675f\\u80fd\\u91cf\\u65b9\\u5dee:%{y}\",\"line\":{\"color\":\"rgb(31, 119, 180)\",\"dash\":\"solid\",\"width\":5},\"marker\":{\"color\":\"rgb(31, 119, 180)\",\"size\":10,\"symbol\":\"circle\"},\"mode\":\"lines+markers\",\"name\":\"\\u6c34\\u5e73\\u6ce2\\u675f\\u80fd\\u91cf\\u65b9\\u5dee\",\"x\":[1,5,10,15,20,25,30,35,40,45,50,55,60,65,70,75,80,85,90],\"y\":[1.8886146545410156,1.6412869691848755,1.7737120389938354,1.8392095565795898,1.8994536399841309,2.1228456497192383,2.111048698425293,2.1075944900512695,2.164588212966919,2.3285436630249023,2.3239753246307373,2.1865129470825195,2.3132522106170654,2.3600244522094727,1.9852683544158936,2.0931191444396973,2.074451446533203,2.063685655593872,2.0767176151275635],\"type\":\"scatter\"},{\"hovertemplate\":\"\\u4fef\\u4ef0\\u6269\\u5c55\\u89d2(\\u00b0):%{x}<br>\\u6ce2\\u675f\\u80fd\\u91cf\\u65b9\\u5dee:%{y}\",\"line\":{\"color\":\"rgb(255, 127, 14)\",\"dash\":\"dot\",\"width\":5},\"marker\":{\"color\":\"rgb(255, 127, 14)\",\"size\":10,\"symbol\":\"square\"},\"mode\":\"lines+markers\",\"name\":\"\\u5782\\u76f4\\u6ce2\\u675f\\u80fd\\u91cf\\u65b9\\u5dee\",\"x\":[1,5,10,15,20,25,30,35,40,45,50,55,60,65,70,75,80,85,90],\"y\":[2.7737669944763184,1.8595112562179565,1.3289735317230225,1.4555137157440186,1.6163188219070435,1.6160465478897095,1.511552333831787,1.512428879737854,1.5505540370941162,1.817796230316162,1.859198808670044,1.746360421180725,1.7588163614273071,1.7193433046340942,1.4067988395690918,1.5366759300231934,1.559828758239746,1.5859297513961792,1.687160611152649],\"type\":\"scatter\"}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"xaxis\":{\"anchor\":\"y\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"\\u4fef\\u4ef0\\u6269\\u5c55\\u89d2(\\u00b0)\",\"font\":{\"size\":30,\"color\":\"black\"},\"standoff\":5},\"showgrid\":false,\"linewidth\":2,\"linecolor\":\"black\",\"mirror\":true,\"tickfont\":{\"size\":24},\"showline\":true,\"showticklabels\":true,\"ticks\":\"inside\",\"ticklen\":5,\"tickwidth\":2,\"range\":[0,95]},\"yaxis\":{\"anchor\":\"x\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"\\u6ce2\\u675f\\u80fd\\u91cf\\u65b9\\u5dee\",\"font\":{\"size\":30,\"color\":\"black\"},\"standoff\":5},\"showgrid\":false,\"linewidth\":2,\"linecolor\":\"black\",\"mirror\":true,\"tickmode\":\"auto\",\"tickfont\":{\"size\":24},\"showline\":true,\"showticklabels\":true,\"ticks\":\"inside\",\"ticklen\":5,\"tickwidth\":2},\"legend\":{\"font\":{\"size\":30},\"x\":0.71,\"y\":0.98,\"bgcolor\":\"rgba(255, 255, 255, 0.8)\",\"bordercolor\":\"black\",\"borderwidth\":1},\"width\":1200,\"height\":800,\"plot_bgcolor\":\"white\"},                        {\"responsive\": true}                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('e08646be-4901-4824-b3d3-90e0834f7a10');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })                };                });            </script>        </div>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig = draw_line(\"compare sangles\",\"俯仰扩展角(°)\",\"波束能量方差\")\n", "\n", "\n", "h_var = []\n", "v_var = []\n", "\n", "for name in Name_use:\n", "    max_idx = BMenvs[name].Beam_idx1[0]\n", "    idx_h = max_idx%BMenvs[name].H_ants\n", "    idx_v = max_idx//BMenvs[name].H_ants\n", "    \n", "    max_idx2 = BMenvs[name].Beam_idx2\n", "    idx_h2 = max_idx2%BMenvs[name].H_ants\n", "    idx_v2 = max_idx2//BMenvs[name].H_ants\n", "     \n", "    powers = BMenvs[name].Beam_powers.reshape(BMenvs[name].max_srs,BMenvs[name].nUE,BMenvs[name].V_ants,BMenvs[name].H_ants)\n", "    \n", "    power_h = []\n", "    power_v = []\n", "    power_h_var = []\n", "    power_v_var = []\n", "\n", "    for i in range(100):\n", "        v = powers[0,i,:,idx_h[i]]\n", "        h = powers[0,i,idx_v[i],:]\n", "        \n", "        h_min = max(0,idx_h[i]-1)\n", "        h_max = min(8,idx_h[i]+1)\n", "        \n", "        v_min = max(0,idx_v[i]-1)\n", "        v_max = min(4,idx_v[i]+1)\n", "        \n", "        h = h[h_min:h_max]\n", "        v = v[v_min:v_max]\n", "        \n", "        power_h_var.append(np.var(h))\n", "        power_v_var.append(np.var(v))\n", "    power_h_var = np.array(power_h_var)\n", "    power_v_var = np.array(power_v_var)\n", "    # power_h_var = np.var(power_h,axis=-1)\n", "    # power_v_var = np.var(power_v,axis=-1)\n", "    h_var.append(np.average(power_h_var))\n", "    v_var.append(np.average(power_v_var))\n", "    # h_var.append(np.average(np.abs(idx_h2-idx_h)))\n", "    # v_var.append(np.average(np.abs(idx_v2-idx_v)))\n", "        \n", "\n", "fig.add_line(x=eas,y=h_var,name=\"水平波束能量方差\")\n", "fig.add_line(x=eas,y=v_var,name=\"垂直波束能量方差\")\n", "\n", "set_fig(fig.fig)\n", "\n", "set_fig(fig.fig)\n", "fig.fig.update_layout(legend={'x': 0.71, 'y': 0.98}) \n", "\n", "fig.fig.update_xaxes(range=[0,95])\n", "fig.fig.show()"]}, {"cell_type": "code", "execution_count": 158, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"hovertemplate": "垂直扩展角(设定):%{x}<br>奇异值和:%{y}", "line": {"color": "rgb(31, 119, 180)", "dash": "solid"}, "marker": {"color": "rgb(31, 119, 180)", "size": 8, "symbol": "circle"}, "mode": "lines+markers", "name": "H1V4", "type": "scatter", "x": [1, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90], "y": [1.1178977489471436, 1.1208935976028442, 1.047688364982605, 1.030881404876709, 1.0728892087936401, 1.0738540887832642, 1.0842301845550537, 1.0565134286880493, 1.036154866218567, 1.0741164684295654, 1.1182891130447388, 1.1355630159378052, 1.1133102178573608, 1.1182034015655518, 1.092297077178955, 1.0821329355239868, 1.082661509513855, 1.0687744617462158, 1.0455697774887085]}, {"hovertemplate": "垂直扩展角(设定):%{x}<br>奇异值和:%{y}", "line": {"color": "rgb(255, 127, 14)", "dash": "dot"}, "marker": {"color": "rgb(255, 127, 14)", "size": 8, "symbol": "square"}, "mode": "lines+markers", "name": "H1V4", "type": "scatter", "x": [1, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90], "y": [1.608651876449585, 1.6134364604949951, 1.4847320318222046, 1.4632186889648438, 1.5221874713897705, 1.504150390625, 1.5334229469299316, 1.4757673740386963, 1.4643349647521973, 1.5165196657180786, 1.6096380949020386, 1.6517367362976074, 1.606768012046814, 1.617367148399353, 1.5566253662109375, 1.5561914443969727, 1.5618293285369873, 1.5264445543289185, 1.4977389574050903]}], "layout": {"height": 800, "legend": {"bgcolor": "rgba(255, 255, 255, 0.8)", "bordercolor": "black", "borderwidth": 1, "font": {"size": 20}, "x": 0.05, "y": 0.95}, "plot_bgcolor": "white", "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "width": 1200, "xaxis": {"anchor": "y", "domain": [0, 1], "linecolor": "black", "linewidth": 2, "mirror": true, "showgrid": false, "title": {"font": {"color": "black", "size": 20}, "standoff": 5, "text": "垂直扩展角(设定)"}}, "yaxis": {"anchor": "x", "domain": [0, 1], "linecolor": "black", "linewidth": 2, "mirror": true, "showgrid": false, "title": {"font": {"color": "black", "size": 20}, "standoff": 5, "text": "奇异值和"}}}}, "text/html": ["<div>                            <div id=\"948ac254-ac22-4a56-b5ac-80ac95203418\" class=\"plotly-graph-div\" style=\"height:800px; width:1200px;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"948ac254-ac22-4a56-b5ac-80ac95203418\")) {                    Plotly.newPlot(                        \"948ac254-ac22-4a56-b5ac-80ac95203418\",                        [{\"hovertemplate\":\"\\u5782\\u76f4\\u6269\\u5c55\\u89d2(\\u8bbe\\u5b9a):%{x}<br>\\u5947\\u5f02\\u503c\\u548c:%{y}\",\"line\":{\"color\":\"rgb(31, 119, 180)\",\"dash\":\"solid\"},\"marker\":{\"color\":\"rgb(31, 119, 180)\",\"size\":8,\"symbol\":\"circle\"},\"mode\":\"lines+markers\",\"name\":\"H1V4\",\"x\":[1,5,10,15,20,25,30,35,40,45,50,55,60,65,70,75,80,85,90],\"y\":[1.1178977489471436,1.1208935976028442,1.047688364982605,1.030881404876709,1.0728892087936401,1.0738540887832642,1.0842301845550537,1.0565134286880493,1.036154866218567,1.0741164684295654,1.1182891130447388,1.1355630159378052,1.1133102178573608,1.1182034015655518,1.092297077178955,1.0821329355239868,1.082661509513855,1.0687744617462158,1.0455697774887085],\"type\":\"scatter\"},{\"hovertemplate\":\"\\u5782\\u76f4\\u6269\\u5c55\\u89d2(\\u8bbe\\u5b9a):%{x}<br>\\u5947\\u5f02\\u503c\\u548c:%{y}\",\"line\":{\"color\":\"rgb(255, 127, 14)\",\"dash\":\"dot\"},\"marker\":{\"color\":\"rgb(255, 127, 14)\",\"size\":8,\"symbol\":\"square\"},\"mode\":\"lines+markers\",\"name\":\"H1V4\",\"x\":[1,5,10,15,20,25,30,35,40,45,50,55,60,65,70,75,80,85,90],\"y\":[1.608651876449585,1.6134364604949951,1.4847320318222046,1.4632186889648438,1.5221874713897705,1.504150390625,1.5334229469299316,1.4757673740386963,1.4643349647521973,1.5165196657180786,1.6096380949020386,1.6517367362976074,1.606768012046814,1.617367148399353,1.5566253662109375,1.5561914443969727,1.5618293285369873,1.5264445543289185,1.4977389574050903],\"type\":\"scatter\"}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"xaxis\":{\"anchor\":\"y\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"\\u5782\\u76f4\\u6269\\u5c55\\u89d2(\\u8bbe\\u5b9a)\",\"font\":{\"size\":20,\"color\":\"black\"},\"standoff\":5},\"showgrid\":false,\"linewidth\":2,\"linecolor\":\"black\",\"mirror\":true},\"yaxis\":{\"anchor\":\"x\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"\\u5947\\u5f02\\u503c\\u548c\",\"font\":{\"size\":20,\"color\":\"black\"},\"standoff\":5},\"showgrid\":false,\"linewidth\":2,\"linecolor\":\"black\",\"mirror\":true},\"legend\":{\"font\":{\"size\":20},\"x\":0.05,\"y\":0.95,\"bgcolor\":\"rgba(255, 255, 255, 0.8)\",\"bordercolor\":\"black\",\"borderwidth\":1},\"width\":1200,\"height\":800,\"plot_bgcolor\":\"white\"},                        {\"responsive\": true}                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('948ac254-ac22-4a56-b5ac-80ac95203418');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })                };                });            </script>        </div>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig = draw_line(\"compare sangles\",\"垂直扩展角(设定)\",\"奇异值和\")\n", "\n", "v_var = []\n", "h_var = []\n", "for name in Name_use:\n", "    max_idx = BMenvs[name].Beam_idx1\n", "    idx_h = max_idx%BMenvs[name].H_ants\n", "    idx_v = max_idx//BMenvs[name].H_ants\n", "    powers = BMenvs[name].Beam_powers.reshape(BMenvs[name].max_srs,BMenvs[name].nUE,BMenvs[name].V_ants,BMenvs[name].H_ants)\n", "\n", "\n", "    power_v = powers[0,np.arange(BMenvs[name].nUE),:,idx_h[0]]\n", "    power_h = powers[0,np.arange(BMenvs[name].nUE),idx_v[0],:]\n", "    power_v_var = np.var(power_v,axis=-1)\n", "    power_h_var = np.var(power_h,axis=-1)\n", "    v_var.append(np.average(power_v_var))\n", "    h_var.append(np.average(power_h_var))\n", "        \n", "    \n", "fig.add_line(x=eas,y=h_var,name=action_name)\n", "fig.add_line(x=eas,y=v_var,name=action_name)\n", "    \n", "fig.fig.show()"]}, {"cell_type": "code", "execution_count": 168, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"hovertemplate": "垂直扩展角(设定):%{x}<br>频谱效率(bps/Hz):%{y}", "line": {"color": "rgb(31, 119, 180)", "dash": "solid"}, "marker": {"color": "rgb(31, 119, 180)", "size": 8, "symbol": "circle"}, "mode": "lines+markers", "name": "H4V1", "type": "scatter", "x": [1, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90], "y": [34.90110397338867, 34.515037536621094, 33.79609298706055, 33.65264892578125, 32.79822540283203, 32.64748001098633, 32.4716911315918, 31.88140296936035, 31.21758270263672, 30.693140029907227, 30.657264709472656, 29.538711547851562, 29.650190353393555, 28.887466430664062, 28.5634822845459, 28.155710220336914, 27.942249298095703, 27.721376419067383, 27.809614181518555, 33.47906494140625, 33.636390686035156, 33.216217041015625, 32.82073974609375, 32.5986328125, 32.851749420166016, 31.77889633178711, 31.342397689819336, 30.876977920532227, 30.526002883911133, 30.218568801879883, 30.07109832763672, 30.264345169067383, 29.736242294311523, 29.239953994750977, 29.00271224975586, 28.816810607910156, 28.641006469726562]}, {"hovertemplate": "垂直扩展角(设定):%{x}<br>频谱效率(bps/Hz):%{y}", "line": {"color": "rgb(255, 127, 14)", "dash": "dot"}, "marker": {"color": "rgb(255, 127, 14)", "size": 8, "symbol": "square"}, "mode": "lines+markers", "name": "H1V4", "type": "scatter", "x": [1, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90], "y": [31.500782012939453, 32.69850540161133, 33.269039154052734, 33.410438537597656, 32.999813079833984, 32.797359466552734, 32.862918853759766, 32.55120849609375, 31.776592254638672, 31.30022621154785, 31.573780059814453, 30.82828140258789, 30.77456283569336, 30.43169403076172, 29.93396759033203, 29.54068374633789, 29.235671997070312, 29.219274520874023, 29.209989547729492, 34.1431999206543, 34.05943298339844, 33.57564163208008, 33.32827377319336, 32.99198913574219, 33.25090408325195, 32.290672302246094, 31.75921630859375, 31.131450653076172, 30.669191360473633, 30.464111328125, 30.1043643951416, 30.114391326904297, 30.03707504272461, 29.834178924560547, 29.510120391845703, 29.524658203125, 29.43122100830078]}], "layout": {"height": 800, "legend": {"bgcolor": "rgba(255, 255, 255, 0.8)", "bordercolor": "black", "borderwidth": 1, "font": {"size": 20}, "x": 0.05, "y": 0.95}, "plot_bgcolor": "white", "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "width": 1200, "xaxis": {"anchor": "y", "domain": [0, 1], "linecolor": "black", "linewidth": 2, "mirror": true, "showgrid": false, "title": {"font": {"color": "black", "size": 20}, "standoff": 5, "text": "垂直扩展角(设定)"}}, "yaxis": {"anchor": "x", "domain": [0, 1], "linecolor": "black", "linewidth": 2, "mirror": true, "showgrid": false, "title": {"font": {"color": "black", "size": 20}, "standoff": 5, "text": "频谱效率(bps/Hz)"}}}}, "text/html": ["<div>                            <div id=\"bc6c57c3-7c3d-4d37-a88f-b5fe1b1cf560\" class=\"plotly-graph-div\" style=\"height:800px; width:1200px;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"bc6c57c3-7c3d-4d37-a88f-b5fe1b1cf560\")) {                    Plotly.newPlot(                        \"bc6c57c3-7c3d-4d37-a88f-b5fe1b1cf560\",                        [{\"hovertemplate\":\"\\u5782\\u76f4\\u6269\\u5c55\\u89d2(\\u8bbe\\u5b9a):%{x}<br>\\u9891\\u8c31\\u6548\\u7387(bps/Hz):%{y}\",\"line\":{\"color\":\"rgb(31, 119, 180)\",\"dash\":\"solid\"},\"marker\":{\"color\":\"rgb(31, 119, 180)\",\"size\":8,\"symbol\":\"circle\"},\"mode\":\"lines+markers\",\"name\":\"H4V1\",\"x\":[1,5,10,15,20,25,30,35,40,45,50,55,60,65,70,75,80,85,90],\"y\":[34.90110397338867,34.515037536621094,33.79609298706055,33.65264892578125,32.79822540283203,32.64748001098633,32.4716911315918,31.88140296936035,31.21758270263672,30.693140029907227,30.657264709472656,29.538711547851562,29.650190353393555,28.887466430664062,28.5634822845459,28.155710220336914,27.942249298095703,27.721376419067383,27.809614181518555,33.47906494140625,33.636390686035156,33.216217041015625,32.82073974609375,32.5986328125,32.851749420166016,31.77889633178711,31.342397689819336,30.876977920532227,30.526002883911133,30.218568801879883,30.07109832763672,30.264345169067383,29.736242294311523,29.239953994750977,29.00271224975586,28.816810607910156,28.641006469726562],\"type\":\"scatter\"},{\"hovertemplate\":\"\\u5782\\u76f4\\u6269\\u5c55\\u89d2(\\u8bbe\\u5b9a):%{x}<br>\\u9891\\u8c31\\u6548\\u7387(bps/Hz):%{y}\",\"line\":{\"color\":\"rgb(255, 127, 14)\",\"dash\":\"dot\"},\"marker\":{\"color\":\"rgb(255, 127, 14)\",\"size\":8,\"symbol\":\"square\"},\"mode\":\"lines+markers\",\"name\":\"H1V4\",\"x\":[1,5,10,15,20,25,30,35,40,45,50,55,60,65,70,75,80,85,90],\"y\":[31.500782012939453,32.69850540161133,33.269039154052734,33.410438537597656,32.999813079833984,32.797359466552734,32.862918853759766,32.55120849609375,31.776592254638672,31.30022621154785,31.573780059814453,30.82828140258789,30.77456283569336,30.43169403076172,29.93396759033203,29.54068374633789,29.235671997070312,29.219274520874023,29.209989547729492,34.1431999206543,34.05943298339844,33.57564163208008,33.32827377319336,32.99198913574219,33.25090408325195,32.290672302246094,31.75921630859375,31.131450653076172,30.669191360473633,30.464111328125,30.1043643951416,30.114391326904297,30.03707504272461,29.834178924560547,29.510120391845703,29.524658203125,29.43122100830078],\"type\":\"scatter\"}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"xaxis\":{\"anchor\":\"y\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"\\u5782\\u76f4\\u6269\\u5c55\\u89d2(\\u8bbe\\u5b9a)\",\"font\":{\"size\":20,\"color\":\"black\"},\"standoff\":5},\"showgrid\":false,\"linewidth\":2,\"linecolor\":\"black\",\"mirror\":true},\"yaxis\":{\"anchor\":\"x\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"\\u9891\\u8c31\\u6548\\u7387(bps/Hz)\",\"font\":{\"size\":20,\"color\":\"black\"},\"standoff\":5},\"showgrid\":false,\"linewidth\":2,\"linecolor\":\"black\",\"mirror\":true},\"legend\":{\"font\":{\"size\":20},\"x\":0.05,\"y\":0.95,\"bgcolor\":\"rgba(255, 255, 255, 0.8)\",\"bordercolor\":\"black\",\"borderwidth\":1},\"width\":1200,\"height\":800,\"plot_bgcolor\":\"white\"},                        {\"responsive\": true}                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('bc6c57c3-7c3d-4d37-a88f-b5fe1b1cf560');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })                };                });            </script>        </div>"]}, "metadata": {}, "output_type": "display_data"}], "source": []}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["40\n"]}], "source": ["print(np.sum(sangles[name][:,2]/sangles[name][:,0]<0.1))"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"data": {"text/plain": ["1.4471580313422192"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["np.log10(28)"]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(1, 400, 13)\n", "(1, 400, 13)\n", "19.8533 19.952347\n", "32\n", "29\n"]}], "source": ["SE_H = BMenvs[name].UE_SE[:,:,2:6,:,:]\n", "SE_H = np.average(SE_H,axis=-1)\n", "SE_H = np.max(SE_H,axis=2)\n", "print(SE_H.shape)\n", "\n", "SE_V = BMenvs[name].UE_SE[:,:,6:10,:,:]\n", "SE_V = np.average(SE_V,axis=-1)\n", "SE_V = np.max(SE_V,axis=2)\n", "print(SE_V.shape)\n", "print(np.average(SE_H[0,:,10]),np.average(SE_V[0,:,10]))\n", "SE_diff = SE_H/SE_V\n", "SE_diff = SE_diff[0,:,10]\n", "print(np.sum(SE_diff>1.1))\n", "print(np.sum(SE_diff<0.9))"]}, {"cell_type": "code", "execution_count": 88, "metadata": {}, "outputs": [], "source": ["v_diff = <PERSON><PERSON>_v[name][0,:,1]/<PERSON><PERSON>_v[name][0,:,2]\n", "sangles_diff = sangles[name][:,0]/sangles[name][:,2]"]}, {"cell_type": "code", "execution_count": 90, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[1.         0.00256475]\n", " [0.00256475 1.        ]]\n"]}], "source": ["corr = np.corrcoef(SE_diff,sangles_diff)\n", "print(corr)"]}, {"cell_type": "code", "execution_count": 84, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"hovertemplate": "SNRs(dB):%{x}<br>频谱效率(bps/Hz):%{y}", "line": {"color": "rgb(31, 119, 180)", "dash": "solid"}, "marker": {"color": "rgb(31, 119, 180)", "size": 8, "symbol": "circle"}, "mode": "markers", "name": "", "type": "scatter", "x": [0.9378927946090698, 1.1204161643981934, 1.058902382850647, 0.9934400320053101, 0.9944356083869934, 1.010944128036499, 1.0973368883132935, 0.9725556969642639, 0.9425413608551025, 0.9603408575057983, 1.0605303049087524, 0.9718682765960693, 0.9626907110214233, 1.0805805921554565, 1.0388706922531128, 1.0035858154296875, 1.0063468217849731, 1.3198744058609009, 0.9832149744033813, 0.9446669220924377, 0.9680086374282837, 1.0384961366653442, 0.9890437126159668, 1.0475279092788696, 1.0877089500427246, 0.8175951242446899, 1.001962423324585, 0.9670664668083191, 1.040778398513794, 1.1398706436157227, 0.9621458649635315, 1.010909914970398, 1.007918357849121, 1.1494636535644531, 1.0365042686462402, 0.9847483038902283, 1.1446073055267334, 1.0404936075210571, 0.9301157593727112, 1.1305538415908813, 1.0499491691589355, 1.06621253490448, 0.9817328453063965, 0.987946093082428, 1.004449486732483, 1.0056439638137817, 0.9377532601356506, 1.146453619003296, 1.0599108934402466, 0.8770473003387451, 0.9569834470748901, 0.9765250086784363, 1.0019809007644653, 1.0449086427688599, 0.9770507216453552, 0.9445798397064209, 0.9525651931762695, 0.790452778339386, 0.8837072253227234, 1.0739455223083496, 1.0585496425628662, 0.9574735760688782, 1.1751295328140259, 1.0865150690078735, 0.9236229658126831, 0.7787229418754578, 1.0350735187530518, 0.9828336238861084, 1.030765414237976, 0.9505496025085449, 1.0495328903198242, 1.0039865970611572, 0.9347538352012634, 1.0051639080047607, 1.037602186203003, 1.0268288850784302, 1.0050944089889526, 0.9996171593666077, 0.9313488006591797, 1.00277578830719, 1.001413345336914, 0.910859227180481, 1.0484987497329712, 1.0113786458969116, 1.0441057682037354, 0.8137379884719849, 0.9802918434143066, 0.9708765745162964, 1.032595157623291, 0.9479032158851624, 0.9513477683067322, 1.0410369634628296, 0.9271299839019775, 0.9838566780090332, 0.7865433096885681, 1.1735776662826538, 0.9152825474739075, 1.011042833328247, 1.0264010429382324, 1.024281620979309, 1.046257734298706, 0.9312909245491028, 0.9639033675193787, 0.825528085231781, 1.1397701501846313, 1.0411109924316406, 0.9468681812286377, 1.0289406776428223, 0.9476747512817383, 0.9723013639450073, 1.0339335203170776, 1.010267972946167, 0.9741035103797913, 0.9786372184753418, 0.8684695363044739, 1.0619535446166992, 1.0597155094146729, 0.8997357487678528, 1.0257771015167236, 0.9727224111557007, 0.6985814571380615, 0.9223174452781677, 1.074255108833313, 1.0067071914672852, 0.9868541359901428, 1.0023999214172363, 0.8646805882453918, 0.9831632375717163, 0.9561871290206909, 1.1682233810424805, 0.9605377912521362, 0.9512099027633667, 1.0033397674560547, 1.009976863861084, 1.1687501668930054, 0.9291225671768188, 1.060964584350586, 0.9989069700241089, 0.9531734585762024, 0.9745259881019592, 0.9030500054359436, 0.9737555384635925, 0.9939445853233337, 1.0717166662216187, 0.935832679271698, 1.0415441989898682, 1.0133370161056519, 0.9998663067817688, 1.020664930343628, 1.3131753206253052, 1.0128190517425537, 0.9524362087249756, 1.0209702253341675, 0.9319671392440796, 1.0048961639404297, 0.9136426448822021, 1.02885103225708, 0.9252243638038635, 0.7825968861579895, 1.0599048137664795, 0.7441422343254089, 0.9577653408050537, 0.95341557264328, 1.030340552330017, 1.0107601881027222, 1.013995885848999, 0.9466914534568787, 1.0050444602966309, 1.032985806465149, 0.8894257545471191, 1.1248648166656494, 0.9338728189468384, 1.0665979385375977, 0.9854595065116882, 0.9840340614318848, 1.035048007965088, 0.9794018864631653, 0.9956836104393005, 0.8476990461349487, 0.9392212629318237, 1.0125964879989624, 0.9969197511672974, 1.0181384086608887, 1.0102684497833252, 1.0862047672271729, 0.9513295292854309, 1.028687596321106, 0.917756199836731, 1.0252596139907837, 0.9942513108253479, 1.0103517770767212, 0.9036251306533813, 0.9919207692146301, 1.0101979970932007, 1.0000276565551758, 0.8361471891403198, 0.966334342956543, 0.8444371223449707, 0.9867553114891052, 1.0133692026138306, 1.0565567016601562, 0.950739860534668, 0.9813535809516907, 1.020702838897705, 0.9862903952598572, 1.0527162551879883, 1.0279003381729126, 1.0488532781600952, 0.9251102805137634, 1.0532516241073608, 1.0233126878738403, 0.8500112295150757, 1.0387376546859741, 1.021865725517273, 1.026065707206726, 0.9421542286872864, 0.73958820104599, 1.0105713605880737, 1.1216291189193726, 0.9578796029090881, 1.2258208990097046, 1.0652599334716797, 0.9960007071495056, 0.9385774731636047, 0.9831157922744751, 1.1056238412857056, 1.0613536834716797, 0.9678565859794617, 1.2085106372833252, 1.0451420545578003, 0.9498121738433838, 0.8205032348632812, 0.9561161398887634, 0.9504390358924866, 1.045337438583374, 0.9393477439880371, 0.8158423900604248, 0.9501290917396545, 0.9986557364463806, 0.987725019454956, 0.8761733770370483, 0.9524961709976196, 0.9613690376281738, 1.050820231437683, 0.9177752733230591, 0.9565494656562805, 0.9887560606002808, 0.9012740254402161, 1.0222265720367432, 1.0824878215789795, 0.9860011339187622, 0.9982852339744568, 1.0173306465148926, 0.9144559502601624, 0.9285672903060913, 0.9626186490058899, 1.1647968292236328, 0.9974795579910278, 1.0401644706726074, 0.9119827747344971, 1.0675842761993408, 0.8290150761604309, 0.9430585503578186, 1.1955275535583496, 0.998590886592865, 0.9687015414237976, 0.9607256650924683, 1.3130598068237305, 0.9040936827659607, 1.0593684911727905, 0.9746401906013489, 1.0938560962677002, 0.9718675017356873, 0.9732860922813416, 1.05552339553833, 1.0280022621154785, 1.0063896179199219, 1.0047340393066406, 1.0146194696426392, 1.0649248361587524, 1.0123162269592285, 1.0287528038024902, 1.029131531715393, 1.0208278894424438, 0.9837098717689514, 0.9910157918930054, 0.9725233912467957, 1.0070152282714844, 1.0511994361877441, 1.018399715423584, 0.9378532767295837, 0.9739250540733337, 0.953041672706604, 1.0010902881622314, 1.1426622867584229, 0.7656318545341492, 0.9698231816291809, 0.9638426899909973, 0.9940770864486694, 1.0010844469070435, 0.9506221413612366, 1.0112340450286865, 0.9460611343383789, 1.0297589302062988, 0.9997270107269287, 1.2344932556152344, 1.003947138786316, 1.0253236293792725, 0.9982612133026123, 1.0047045946121216, 0.9170068502426147, 0.7483057379722595, 1.025223970413208, 1.0423270463943481, 1.015566110610962, 0.974778413772583, 0.9589502811431885, 0.9536104202270508, 0.907383918762207, 0.8330082893371582, 0.9628580808639526, 1.3252710103988647, 1.0189281702041626, 0.9739220142364502, 0.9431787133216858, 0.9480711221694946, 1.1184171438217163, 0.9762327671051025, 1.0722808837890625, 1.0416460037231445, 1.0381048917770386, 0.9927985072135925, 0.9509214162826538, 0.9766971468925476, 0.9792159795761108, 1.0129766464233398, 1.0953553915023804, 1.0264853239059448, 0.9285258650779724, 1.1294957399368286, 1.017892837524414, 1.0811161994934082, 0.9957614541053772, 0.9470023512840271, 1.1521897315979004, 0.9515108466148376, 1.1055635213851929, 0.9861804246902466, 1.0097019672393799, 0.986000120639801, 0.9651617407798767, 0.9530966281890869, 0.9741514921188354, 1.017303705215454, 1.06788969039917, 1.0235857963562012, 0.9386715888977051, 1.0510039329528809, 0.9731155633926392, 1.1123257875442505, 0.9891335368156433, 1.0306510925292969, 0.931822657585144, 0.958526611328125, 0.8650628328323364, 0.9873391389846802, 0.9438223242759705, 0.9951596260070801, 0.8841975927352905, 1.052872657775879, 0.989897608757019, 1.0204604864120483, 0.983009397983551, 1.0017441511154175, 1.0239901542663574, 0.9689205288887024, 0.952241837978363, 1.027637004852295, 0.9570117592811584, 1.1170421838760376, 1.0572450160980225, 1.039250373840332, 0.9626814723014832, 0.9292223453521729, 0.9779665470123291, 1.0366477966308594, 0.9807925820350647, 1.0552746057510376, 1.148066520690918, 1.0117857456207275, 1.025997281074524, 1.0086296796798706, 1.017325758934021, 1.2918646335601807, 1.0697097778320312, 1.0757423639297485, 1.000712513923645, 1.0823118686676025, 0.9546958208084106, 1.053280234336853], "y": [0.6806744118466858, 2.600972002301018, 0.0837383837125862, 1.757215886514728, 2.07091939912585, 0.45195734813576, 0.0296179301510488, 0.0979602184781255, 0.08148540217410642, 1.3463121438017802, 1.1505180256314456, 2.1475201783882194, 0.0839388332414112, 2.1196618452031477, 1.2239815918539207, 1.3378849609479246, 0.09274882725164718, 2.263279781284656, 2.105719335076888, 2.9348242508347115, 1.9348153551455631, 0.6618036899318589, 1.0769477111391819, 0.3845043025483184, 0.15153065157957785, 0.004364818142461496, 0.020350786675443745, 0.19201849117251113, 0.2754656893269521, 0.0947141815076414, 2.1090641141969266, 0.3559224533124581, 0.12308520291066948, 0.06479827542239697, 1.4283985980393898, 0.6902372776819019, 0.024850137290135865, 0.1377758404943631, 2.2829540804526123, 0.2520424502286192, 0.14045196031336546, 2.6094199417628885, 2.6208093891033735, 2.974273666980981, 1.6025613654289796, 2.0241310394316248, 0.1458056825662284, 1.7994585490537949, 2.0805415695553933, 1.3518067144661696, 2.3992533551574238, 2.169397643235421, 1.3879172819518326, 2.2710190308443194, 2.4956292513981055, 1.1619430557285273, 0.4129200989200633, 0.05129284015632691, 0.07741176958979011, 1.4560819826711975, 0.047225731077386555, 0.24033720592334287, 2.29888625971854, 0.4098036424881835, 2.3379768830691314, 0.11383161053486812, 0.447862365897486, 0.3180030956998238, 0.4331963397204381, 1.7246458033103824, 1.1158278031799123, 1.4311088180001303, 0.629533094928864, 0.43931360862526014, 0.36053635075109264, 2.0112144987872207, 1.6828390295396183, 0.4896276264522212, 2.215115555002046, 2.0788632495359414, 2.3122564805095447, 2.453825442857058, 0.3588016913670302, 0.08281352856405784, 2.2964725627294347, 0.47354422047075984, 1.3733846911120202, 1.975919369473553, 2.010944662883033, 2.441531600453087, 0.37215115555002043, 0.6547731302744023, 0.026280919932867196, 2.62898452743134, 0.2520445258894206, 2.3283250603424244, 1.9959643223560526, 2.9339969517438513, 2.33077137485841, 0.2205810664152152, 2.013856518464485, 1.7427663221069736, 2.8392163490905635, 2.396356325724554, 2.7962353443521266, 0.9246297910699142, 1.3923384394589047, 1.789590264557795, 2.5137734920324277, 1.7949484346552327, 2.2885642950759397, 0.1422898096915568, 2.3524531345443327, 0.15042610351023894, 0.014482715675983417, 0.6605167802349647, 0.8754395953054482, 0.38494019131662127, 0.5849182486167203, 0.3617105817187657, 2.085745547707581, 0.21790109179758152, 0.22841520332580165, 2.5344915520605378, 1.038613221366259, 1.5055005011238218, 0.05189952615629132, 2.1924938174960413, 1.1861986348082385, 1.1696318963830126, 1.784851827471074, 0.34669465843518893, 1.41439974854852, 0.6251801377052679, 2.2951708268839583, 0.8922020388919528, 0.12519051600927522, 1.6006487922619363, 0.568226970543408, 2.1459871546248688, 0.5806423873657492, 1.0650008006120233, 1.3343474418963235, 0.018545347258052076, 2.0601170672692013, 0.8587216301646888, 2.281895493443877, 2.516652730087, 1.6133666625153449, 0.15098475278879855, 0.540727430154014, 1.358579299138304, 1.206122013272368, 2.3464870923550443, 2.747193410073478, 0.13514568173596408, 1.3749948108479964, 0.11738988619448348, 2.141732049981912, 2.7819192152816075, 0.6772377106054406, 0.0884047657172001, 1.122994763404321, 1.9341926569051304, 1.3419265690513043, 0.7719590086644011, 2.5635122552944174, 2.481710463109577, 0.08844627893322894, 2.6588621820532437, 2.7462890150099923, 2.4421839509906826, 2.2863463032481124, 0.15159588663333748, 0.1595607901744148, 0.9730786794052934, 2.4172285776979137, 2.511608874339495, 0.7540075079616417, 1.3878461164386404, 0.7863996773830069, 1.2440532318038677, 2.2916303426026414, 2.114193961606205, 1.938207577941063, 1.1085036857805373, 0.8966736053042028, 2.5778224539055037, 2.632065401106624, 0.13324141121212657, 0.024540448698560674, 0.05439298782476678, 1.176514194554652, 2.5069801507522786, 2.456547523736664, 0.024745820508714808, 2.0386784564200187, 1.4984313934800528, 2.4281109707569044, 0.2800991572817146, 2.39243925726926, 2.6356029201582243, 2.1797107121888732, 1.6885055835275558, 0.0916662218822092, 2.4917121829428126, 1.739575734932185, 1.6731961025020607, 0.22457285865936033, 0.7851572461318578, 0.34487697261906874, 0.017320232948446517, 0.020487009328612686, 0.10883223323310856, 0.27882914939420356, 1.959085760373856, 2.0037806678883414, 2.0969778378731, 0.00037269972304754446, 0.3651976918651888, 0.27131347815515267, 2.227391606027719, 2.3957632797812844, 0.5624507030559657, 0.5067903760504326, 1.4602837131792596, 0.29093025186661203, 0.07044051452666038, 1.5992640299844028, 1.1094377331411864, 0.016642233173803974, 1.5786556834557972, 0.008835732204173857, 3.055728527289009, 0.8612213188155686, 1.9043832025667027, 0.015460529827245715, 0.908128287698448, 1.8450311645643187, 1.8421964049554918, 2.0286381886004703, 1.7318335201428052, 2.223845191286969, 0.3960242199963231, 0.4135250057821979, 0.345392922589713, 1.9266046340610006, 2.4417243403846496, 0.9067613167992123, 1.6664679962756712, 2.964132581351077, 0.6605849805184407, 1.743113253983786, 0.6322670367273352, 0.6350158046743881, 0.44057086602499096, 0.9437495922809139, 1.090406888821677, 1.4866653619655914, 0.03442453786894871, 1.9990244394233219, 1.2736580852918673, 1.96904003653163, 0.11187100064642007, 1.1074302726232201, 2.664914215904306, 0.08832766974457511, 1.7388225665842332, 0.388451023300775, 0.2683144448200402, 0.28877897770740296, 2.775128839231175, 2.717582626126046, 0.1588832351842297, 0.27460162138760885, 0.8204494102158093, 0.09420297590454331, 1.715373529987368, 1.6657474454545993, 0.13362481541445012, 2.145527544018835, 0.31365013847622775, 1.6208716589274168, 1.470771730685976, 2.3361028578884, 2.019689125316538, 1.9055989467504044, 1.4343349879315148, 0.21339928004222486, 0.07263152276406852, 1.2548852159576802, 0.12456989342964397, 1.7863729903155596, 2.385648881218828, 1.2760065472272135, 0.04243777465440247, 2.503436701241245, 1.593784285468595, 0.602324147051672, 0.027661056452043337, 1.3562931070270012, 0.2000094887350923, 2.164169943245503, 2.48609010740062, 1.7269468215702668, 0.8878283250603423, 0.2552994585490537, 1.1698127753957097, 0.15177973087575092, 2.4556401634434617, 0.10204749111913698, 0.06143362926325902, 1.4991489790714085, 2.640243504664306, 0.3031591557397951, 0.728411645050142, 1.617778924333268, 0.01885067696194424, 0.007151066593128969, 0.26050699497690083, 2.3723142431844195, 1.6189917032872536, 0.5294862442993459, 1.893506739967145, 2.1849473078679402, 2.266971492281507, 2.1909726546515556, 2.0504533836236294, 1.9320339696716302, 0.23063438124551508, 0.07764869144412617, 2.3929136940238758, 0.6193475308532151, 1.2343183826451034, 1.5482680093226822, 1.517349559070341, 0.1517726143244317, 0.49064173501521163, 1.0891733532596768, 1.8131934930999103, 1.8761453199779385, 1.711278547749094, 2.283025245965805, 1.273732216034776, 0.9145687666423516, 2.034835518707634, 2.617678106522912, 0.5305181442406343, 0.27729731172273914, 2.379498994787126, 0.8441801436357274, 1.0301593514449565, 0.9408733194560581, 0.6115549071586575, 1.9086056896827797, 1.5431381619134033, 2.595889598567201, 0.9070192917845344, 0.7160258805249642, 2.17711613618707, 0.8545376910349244, 0.8075210086525403, 0.36716067393740986, 0.6299808446160323, 1.6825958807028778, 2.572888311657504, 2.4302815189092697, 1.7282455921860265, 0.11673783217985896, 0.09170180463880535, 2.8849639131543516, 0.07117707758820074, 2.615460114695085, 2.469493716678231, 1.6823853493930172, 0.199028590744925, 2.1595026716719743, 2.0184437288356727, 0.19386553276282312, 2.188381043879469, 2.00699497690086, 0.5950415428683259, 0.010764199002496722, 0.9633586563951109, 0.857763860966309, 0.09100616174735056, 0.06737446699995847, 2.3436226804490543, 0.19971563447020238, 0.39274764115976063, 1.7691627970418868, 0.07007519822560654, 2.034052698062519, 0.025791152940618307, 2.055933128139437, 0.4713618113995291, 0.9868165886811251, 1.3730970638295348, 0.9380889687524091, 1.9427888578528179]}], "layout": {"height": 800, "legend": {"bgcolor": "rgba(255, 255, 255, 0.8)", "bordercolor": "black", "borderwidth": 1, "font": {"size": 20}, "x": 0.05, "y": 0.95}, "plot_bgcolor": "white", "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "width": 1200, "xaxis": {"anchor": "y", "domain": [0, 1], "linecolor": "black", "linewidth": 2, "mirror": true, "showgrid": false, "title": {"font": {"color": "black", "size": 20}, "standoff": 5, "text": "SNRs(dB)"}}, "yaxis": {"anchor": "x", "domain": [0, 1], "linecolor": "black", "linewidth": 2, "mirror": true, "showgrid": false, "title": {"font": {"color": "black", "size": 20}, "standoff": 5, "text": "频谱效率(bps/Hz)"}}}}, "text/html": ["<div>                            <div id=\"4703ed75-ea56-4377-8488-ecc5b0b81f66\" class=\"plotly-graph-div\" style=\"height:800px; width:1200px;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"4703ed75-ea56-4377-8488-ecc5b0b81f66\")) {                    Plotly.newPlot(                        \"4703ed75-ea56-4377-8488-ecc5b0b81f66\",                        [{\"hovertemplate\":\"SNRs(dB):%{x}<br>\\u9891\\u8c31\\u6548\\u7387(bps/Hz):%{y}\",\"line\":{\"color\":\"rgb(31, 119, 180)\",\"dash\":\"solid\"},\"marker\":{\"color\":\"rgb(31, 119, 180)\",\"size\":8,\"symbol\":\"circle\"},\"mode\":\"markers\",\"name\":\"\",\"x\":[0.9378927946090698,1.1204161643981934,1.058902382850647,0.9934400320053101,0.9944356083869934,1.010944128036499,1.0973368883132935,0.9725556969642639,0.9425413608551025,0.9603408575057983,1.0605303049087524,0.9718682765960693,0.9626907110214233,1.0805805921554565,1.0388706922531128,1.0035858154296875,1.0063468217849731,1.3198744058609009,0.9832149744033813,0.9446669220924377,0.9680086374282837,1.0384961366653442,0.9890437126159668,1.0475279092788696,1.0877089500427246,0.8175951242446899,1.001962423324585,0.9670664668083191,1.040778398513794,1.1398706436157227,0.9621458649635315,1.010909914970398,1.007918357849121,1.1494636535644531,1.0365042686462402,0.9847483038902283,1.1446073055267334,1.0404936075210571,0.9301157593727112,1.1305538415908813,1.0499491691589355,1.06621253490448,0.9817328453063965,0.987946093082428,1.004449486732483,1.0056439638137817,0.9377532601356506,1.146453619003296,1.0599108934402466,0.8770473003387451,0.9569834470748901,0.9765250086784363,1.0019809007644653,1.0449086427688599,0.9770507216453552,0.9445798397064209,0.9525651931762695,0.790452778339386,0.8837072253227234,1.0739455223083496,1.0585496425628662,0.9574735760688782,1.1751295328140259,1.0865150690078735,0.9236229658126831,0.7787229418754578,1.0350735187530518,0.9828336238861084,1.030765414237976,0.9505496025085449,1.0495328903198242,1.0039865970611572,0.9347538352012634,1.0051639080047607,1.037602186203003,1.0268288850784302,1.0050944089889526,0.9996171593666077,0.9313488006591797,1.00277578830719,1.001413345336914,0.910859227180481,1.0484987497329712,1.0113786458969116,1.0441057682037354,0.8137379884719849,0.9802918434143066,0.9708765745162964,1.032595157623291,0.9479032158851624,0.9513477683067322,1.0410369634628296,0.9271299839019775,0.9838566780090332,0.7865433096885681,1.1735776662826538,0.9152825474739075,1.011042833328247,1.0264010429382324,1.024281620979309,1.046257734298706,0.9312909245491028,0.9639033675193787,0.825528085231781,1.1397701501846313,1.0411109924316406,0.9468681812286377,1.0289406776428223,0.9476747512817383,0.9723013639450073,1.0339335203170776,1.010267972946167,0.9741035103797913,0.9786372184753418,0.8684695363044739,1.0619535446166992,1.0597155094146729,0.8997357487678528,1.0257771015167236,0.9727224111557007,0.6985814571380615,0.9223174452781677,1.074255108833313,1.0067071914672852,0.9868541359901428,1.0023999214172363,0.8646805882453918,0.9831632375717163,0.9561871290206909,1.1682233810424805,0.9605377912521362,0.9512099027633667,1.0033397674560547,1.009976863861084,1.1687501668930054,0.9291225671768188,1.060964584350586,0.9989069700241089,0.9531734585762024,0.9745259881019592,0.9030500054359436,0.9737555384635925,0.9939445853233337,1.0717166662216187,0.935832679271698,1.0415441989898682,1.0133370161056519,0.9998663067817688,1.020664930343628,1.3131753206253052,1.0128190517425537,0.9524362087249756,1.0209702253341675,0.9319671392440796,1.0048961639404297,0.9136426448822021,1.02885103225708,0.9252243638038635,0.7825968861579895,1.0599048137664795,0.7441422343254089,0.9577653408050537,0.95341557264328,1.030340552330017,1.0107601881027222,1.013995885848999,0.9466914534568787,1.0050444602966309,1.032985806465149,0.8894257545471191,1.1248648166656494,0.9338728189468384,1.0665979385375977,0.9854595065116882,0.9840340614318848,1.035048007965088,0.9794018864631653,0.9956836104393005,0.8476990461349487,0.9392212629318237,1.0125964879989624,0.9969197511672974,1.0181384086608887,1.0102684497833252,1.0862047672271729,0.9513295292854309,1.028687596321106,0.917756199836731,1.0252596139907837,0.9942513108253479,1.0103517770767212,0.9036251306533813,0.9919207692146301,1.0101979970932007,1.0000276565551758,0.8361471891403198,0.966334342956543,0.8444371223449707,0.9867553114891052,1.0133692026138306,1.0565567016601562,0.950739860534668,0.9813535809516907,1.020702838897705,0.9862903952598572,1.0527162551879883,1.0279003381729126,1.0488532781600952,0.9251102805137634,1.0532516241073608,1.0233126878738403,0.8500112295150757,1.0387376546859741,1.021865725517273,1.026065707206726,0.9421542286872864,0.73958820104599,1.0105713605880737,1.1216291189193726,0.9578796029090881,1.2258208990097046,1.0652599334716797,0.9960007071495056,0.9385774731636047,0.9831157922744751,1.1056238412857056,1.0613536834716797,0.9678565859794617,1.2085106372833252,1.0451420545578003,0.9498121738433838,0.8205032348632812,0.9561161398887634,0.9504390358924866,1.045337438583374,0.9393477439880371,0.8158423900604248,0.9501290917396545,0.9986557364463806,0.987725019454956,0.8761733770370483,0.9524961709976196,0.9613690376281738,1.050820231437683,0.9177752733230591,0.9565494656562805,0.9887560606002808,0.9012740254402161,1.0222265720367432,1.0824878215789795,0.9860011339187622,0.9982852339744568,1.0173306465148926,0.9144559502601624,0.9285672903060913,0.9626186490058899,1.1647968292236328,0.9974795579910278,1.0401644706726074,0.9119827747344971,1.0675842761993408,0.8290150761604309,0.9430585503578186,1.1955275535583496,0.998590886592865,0.9687015414237976,0.9607256650924683,1.3130598068237305,0.9040936827659607,1.0593684911727905,0.9746401906013489,1.0938560962677002,0.9718675017356873,0.9732860922813416,1.05552339553833,1.0280022621154785,1.0063896179199219,1.0047340393066406,1.0146194696426392,1.0649248361587524,1.0123162269592285,1.0287528038024902,1.029131531715393,1.0208278894424438,0.9837098717689514,0.9910157918930054,0.9725233912467957,1.0070152282714844,1.0511994361877441,1.018399715423584,0.9378532767295837,0.9739250540733337,0.953041672706604,1.0010902881622314,1.1426622867584229,0.7656318545341492,0.9698231816291809,0.9638426899909973,0.9940770864486694,1.0010844469070435,0.9506221413612366,1.0112340450286865,0.9460611343383789,1.0297589302062988,0.9997270107269287,1.2344932556152344,1.003947138786316,1.0253236293792725,0.9982612133026123,1.0047045946121216,0.9170068502426147,0.7483057379722595,1.025223970413208,1.0423270463943481,1.015566110610962,0.974778413772583,0.9589502811431885,0.9536104202270508,0.907383918762207,0.8330082893371582,0.9628580808639526,1.3252710103988647,1.0189281702041626,0.9739220142364502,0.9431787133216858,0.9480711221694946,1.1184171438217163,0.9762327671051025,1.0722808837890625,1.0416460037231445,1.0381048917770386,0.9927985072135925,0.9509214162826538,0.9766971468925476,0.9792159795761108,1.0129766464233398,1.0953553915023804,1.0264853239059448,0.9285258650779724,1.1294957399368286,1.017892837524414,1.0811161994934082,0.9957614541053772,0.9470023512840271,1.1521897315979004,0.9515108466148376,1.1055635213851929,0.9861804246902466,1.0097019672393799,0.986000120639801,0.9651617407798767,0.9530966281890869,0.9741514921188354,1.017303705215454,1.06788969039917,1.0235857963562012,0.9386715888977051,1.0510039329528809,0.9731155633926392,1.1123257875442505,0.9891335368156433,1.0306510925292969,0.931822657585144,0.958526611328125,0.8650628328323364,0.9873391389846802,0.9438223242759705,0.9951596260070801,0.8841975927352905,1.052872657775879,0.989897608757019,1.0204604864120483,0.983009397983551,1.0017441511154175,1.0239901542663574,0.9689205288887024,0.952241837978363,1.027637004852295,0.9570117592811584,1.1170421838760376,1.0572450160980225,1.039250373840332,0.9626814723014832,0.9292223453521729,0.9779665470123291,1.0366477966308594,0.9807925820350647,1.0552746057510376,1.148066520690918,1.0117857456207275,1.025997281074524,1.0086296796798706,1.017325758934021,1.2918646335601807,1.0697097778320312,1.0757423639297485,1.000712513923645,1.0823118686676025,0.9546958208084106,1.053280234336853],\"y\":[0.6806744118466858,2.600972002301018,0.0837383837125862,1.757215886514728,2.07091939912585,0.45195734813576,0.0296179301510488,0.0979602184781255,0.08148540217410642,1.3463121438017802,1.1505180256314456,2.1475201783882194,0.0839388332414112,2.1196618452031477,1.2239815918539207,1.3378849609479246,0.09274882725164718,2.263279781284656,2.105719335076888,2.9348242508347115,1.9348153551455631,0.6618036899318589,1.0769477111391819,0.3845043025483184,0.15153065157957785,0.004364818142461496,0.020350786675443745,0.19201849117251113,0.2754656893269521,0.0947141815076414,2.1090641141969266,0.3559224533124581,0.12308520291066948,0.06479827542239697,1.4283985980393898,0.6902372776819019,0.024850137290135865,0.1377758404943631,2.2829540804526123,0.2520424502286192,0.14045196031336546,2.6094199417628885,2.6208093891033735,2.974273666980981,1.6025613654289796,2.0241310394316248,0.1458056825662284,1.7994585490537949,2.0805415695553933,1.3518067144661696,2.3992533551574238,2.169397643235421,1.3879172819518326,2.2710190308443194,2.4956292513981055,1.1619430557285273,0.4129200989200633,0.05129284015632691,0.07741176958979011,1.4560819826711975,0.047225731077386555,0.24033720592334287,2.29888625971854,0.4098036424881835,2.3379768830691314,0.11383161053486812,0.447862365897486,0.3180030956998238,0.4331963397204381,1.7246458033103824,1.1158278031799123,1.4311088180001303,0.629533094928864,0.43931360862526014,0.36053635075109264,2.0112144987872207,1.6828390295396183,0.4896276264522212,2.215115555002046,2.0788632495359414,2.3122564805095447,2.453825442857058,0.3588016913670302,0.08281352856405784,2.2964725627294347,0.47354422047075984,1.3733846911120202,1.975919369473553,2.010944662883033,2.441531600453087,0.37215115555002043,0.6547731302744023,0.026280919932867196,2.62898452743134,0.2520445258894206,2.3283250603424244,1.9959643223560526,2.9339969517438513,2.33077137485841,0.2205810664152152,2.013856518464485,1.7427663221069736,2.8392163490905635,2.396356325724554,2.7962353443521266,0.9246297910699142,1.3923384394589047,1.789590264557795,2.5137734920324277,1.7949484346552327,2.2885642950759397,0.1422898096915568,2.3524531345443327,0.15042610351023894,0.014482715675983417,0.6605167802349647,0.8754395953054482,0.38494019131662127,0.5849182486167203,0.3617105817187657,2.085745547707581,0.21790109179758152,0.22841520332580165,2.5344915520605378,1.038613221366259,1.5055005011238218,0.05189952615629132,2.1924938174960413,1.1861986348082385,1.1696318963830126,1.784851827471074,0.34669465843518893,1.41439974854852,0.6251801377052679,2.2951708268839583,0.8922020388919528,0.12519051600927522,1.6006487922619363,0.568226970543408,2.1459871546248688,0.5806423873657492,1.0650008006120233,1.3343474418963235,0.018545347258052076,2.0601170672692013,0.8587216301646888,2.281895493443877,2.516652730087,1.6133666625153449,0.15098475278879855,0.540727430154014,1.358579299138304,1.206122013272368,2.3464870923550443,2.747193410073478,0.13514568173596408,1.3749948108479964,0.11738988619448348,2.141732049981912,2.7819192152816075,0.6772377106054406,0.0884047657172001,1.122994763404321,1.9341926569051304,1.3419265690513043,0.7719590086644011,2.5635122552944174,2.481710463109577,0.08844627893322894,2.6588621820532437,2.7462890150099923,2.4421839509906826,2.2863463032481124,0.15159588663333748,0.1595607901744148,0.9730786794052934,2.4172285776979137,2.511608874339495,0.7540075079616417,1.3878461164386404,0.7863996773830069,1.2440532318038677,2.2916303426026414,2.114193961606205,1.938207577941063,1.1085036857805373,0.8966736053042028,2.5778224539055037,2.632065401106624,0.13324141121212657,0.024540448698560674,0.05439298782476678,1.176514194554652,2.5069801507522786,2.456547523736664,0.024745820508714808,2.0386784564200187,1.4984313934800528,2.4281109707569044,0.2800991572817146,2.39243925726926,2.6356029201582243,2.1797107121888732,1.6885055835275558,0.0916662218822092,2.4917121829428126,1.739575734932185,1.6731961025020607,0.22457285865936033,0.7851572461318578,0.34487697261906874,0.017320232948446517,0.020487009328612686,0.10883223323310856,0.27882914939420356,1.959085760373856,2.0037806678883414,2.0969778378731,0.00037269972304754446,0.3651976918651888,0.27131347815515267,2.227391606027719,2.3957632797812844,0.5624507030559657,0.5067903760504326,1.4602837131792596,0.29093025186661203,0.07044051452666038,1.5992640299844028,1.1094377331411864,0.016642233173803974,1.5786556834557972,0.008835732204173857,3.055728527289009,0.8612213188155686,1.9043832025667027,0.015460529827245715,0.908128287698448,1.8450311645643187,1.8421964049554918,2.0286381886004703,1.7318335201428052,2.223845191286969,0.3960242199963231,0.4135250057821979,0.345392922589713,1.9266046340610006,2.4417243403846496,0.9067613167992123,1.6664679962756712,2.964132581351077,0.6605849805184407,1.743113253983786,0.6322670367273352,0.6350158046743881,0.44057086602499096,0.9437495922809139,1.090406888821677,1.4866653619655914,0.03442453786894871,1.9990244394233219,1.2736580852918673,1.96904003653163,0.11187100064642007,1.1074302726232201,2.664914215904306,0.08832766974457511,1.7388225665842332,0.388451023300775,0.2683144448200402,0.28877897770740296,2.775128839231175,2.717582626126046,0.1588832351842297,0.27460162138760885,0.8204494102158093,0.09420297590454331,1.715373529987368,1.6657474454545993,0.13362481541445012,2.145527544018835,0.31365013847622775,1.6208716589274168,1.470771730685976,2.3361028578884,2.019689125316538,1.9055989467504044,1.4343349879315148,0.21339928004222486,0.07263152276406852,1.2548852159576802,0.12456989342964397,1.7863729903155596,2.385648881218828,1.2760065472272135,0.04243777465440247,2.503436701241245,1.593784285468595,0.602324147051672,0.027661056452043337,1.3562931070270012,0.2000094887350923,2.164169943245503,2.48609010740062,1.7269468215702668,0.8878283250603423,0.2552994585490537,1.1698127753957097,0.15177973087575092,2.4556401634434617,0.10204749111913698,0.06143362926325902,1.4991489790714085,2.640243504664306,0.3031591557397951,0.728411645050142,1.617778924333268,0.01885067696194424,0.007151066593128969,0.26050699497690083,2.3723142431844195,1.6189917032872536,0.5294862442993459,1.893506739967145,2.1849473078679402,2.266971492281507,2.1909726546515556,2.0504533836236294,1.9320339696716302,0.23063438124551508,0.07764869144412617,2.3929136940238758,0.6193475308532151,1.2343183826451034,1.5482680093226822,1.517349559070341,0.1517726143244317,0.49064173501521163,1.0891733532596768,1.8131934930999103,1.8761453199779385,1.711278547749094,2.283025245965805,1.273732216034776,0.9145687666423516,2.034835518707634,2.617678106522912,0.5305181442406343,0.27729731172273914,2.379498994787126,0.8441801436357274,1.0301593514449565,0.9408733194560581,0.6115549071586575,1.9086056896827797,1.5431381619134033,2.595889598567201,0.9070192917845344,0.7160258805249642,2.17711613618707,0.8545376910349244,0.8075210086525403,0.36716067393740986,0.6299808446160323,1.6825958807028778,2.572888311657504,2.4302815189092697,1.7282455921860265,0.11673783217985896,0.09170180463880535,2.8849639131543516,0.07117707758820074,2.615460114695085,2.469493716678231,1.6823853493930172,0.199028590744925,2.1595026716719743,2.0184437288356727,0.19386553276282312,2.188381043879469,2.00699497690086,0.5950415428683259,0.010764199002496722,0.9633586563951109,0.857763860966309,0.09100616174735056,0.06737446699995847,2.3436226804490543,0.19971563447020238,0.39274764115976063,1.7691627970418868,0.07007519822560654,2.034052698062519,0.025791152940618307,2.055933128139437,0.4713618113995291,0.9868165886811251,1.3730970638295348,0.9380889687524091,1.9427888578528179],\"type\":\"scatter\"}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"xaxis\":{\"anchor\":\"y\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"SNRs(dB)\",\"font\":{\"size\":20,\"color\":\"black\"},\"standoff\":5},\"showgrid\":false,\"linewidth\":2,\"linecolor\":\"black\",\"mirror\":true},\"yaxis\":{\"anchor\":\"x\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"\\u9891\\u8c31\\u6548\\u7387(bps/Hz)\",\"font\":{\"size\":20,\"color\":\"black\"},\"standoff\":5},\"showgrid\":false,\"linewidth\":2,\"linecolor\":\"black\",\"mirror\":true},\"legend\":{\"font\":{\"size\":20},\"x\":0.05,\"y\":0.95,\"bgcolor\":\"rgba(255, 255, 255, 0.8)\",\"bordercolor\":\"black\",\"borderwidth\":1},\"width\":1200,\"height\":800,\"plot_bgcolor\":\"white\"},                        {\"responsive\": true}                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('4703ed75-ea56-4377-8488-ecc5b0b81f66');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })                };                });            </script>        </div>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig = draw_line(\"compare reward\",\"SNRs(dB)\",\"频谱效率(bps/Hz)\")\n", "x = np.arange(400)\n", "# fig.add_line(x=x,y=SE_diff/np.max(np.abs(SE_diff)),name=\"\",mode=\"lines\")\n", "# fig.add_line(x=x,y=<PERSON><PERSON>_v[name][0,:,1]/<PERSON><PERSON>_v[name][0,:,2]/np.max(np.abs(<PERSON><PERSON>_v[name][0,:,1]/<PERSON><PERSON>_v[name][0,:,2])),name=\"\",mode=\"lines\")\n", "# fig.add_line(x=x,y=sangles[name][:,0]/sangles[name][0,2]/np.max(np.abs(sangles[name][:,0]/sangles[name][0,2])),name=\"\",mode=\"lines\")\n", "\n", "fig.add_line(x=SE_diff,y=sangles_diff,name=\"\",mode=\"markers\")\n", "\n", "# fig.add_line(x=sangles[:,0],y=comas_env_v[0,:,2],name=\"\",mode=\"markers\")\n", "# fig.add_line(x=sangles[:,0],y=comas_env_v[0,:,3],name=\"\",mode=\"markers\")\n", "\n", "fig.fig.show()"]}], "metadata": {"kernelspec": {"display_name": "setsumi", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.15"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}