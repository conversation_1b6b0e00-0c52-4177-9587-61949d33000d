{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os,sys\n", "sys.path.append(os.getcwd())\n", "sys.path.append(\"../../\")\n", "from setsumi_env.BM_functions import *\n", "from setsumi_env.system_functions import *\n", "from setsumi_env.process import *\n", "from setsumi_env.analyze import *\n", "from setsumi_env.utils import *\n", "from setsumi_env.setsumi_env import setsumi_env"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["setsumi load sim log OK\n", "Load begin\n", "Load Finish\n", "simulate srs\n", "cal coma\n", "cal beam\n", "OK and store Beam Data\n"]}], "source": ["BMENV = setsumi_env(path=\"/data/setsumi/env/Data/test/test_add/\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4\n", "8\n", "(32, 32)\n", "(32, 2)\n", "[[-59. -23.]\n", " [-40. -25.]\n", " [-23. -25.]\n", " [ -8. -26.]\n", " [  8. -26.]\n", " [ 23. -25.]\n", " [ 40. -25.]\n", " [ 59. -23.]\n", " [-55.  -8.]\n", " [-37.  -8.]\n", " [-21.  -8.]\n", " [ -7.  -8.]\n", " [  7.  -8.]\n", " [ 21.  -8.]\n", " [ 37.  -8.]\n", " [ 55.  -8.]\n", " [-55.   8.]\n", " [-37.   8.]\n", " [-21.   8.]\n", " [ -7.   8.]\n", " [  7.   8.]\n", " [ 21.   8.]\n", " [ 37.   8.]\n", " [ 55.   8.]\n", " [-59.  23.]\n", " [-40.  25.]\n", " [-23.  25.]\n", " [ -8.  26.]\n", " [  8.  26.]\n", " [ 23.  25.]\n", " [ 40.  25.]\n", " [ 59.  23.]]\n", "59.0\n", "26.0\n"]}], "source": ["print(BMENV.V_ants)\n", "print(BMENV.H_ants)\n", "print(BMENV.Beams.shape)\n", "\n", "print(BMENV.Beam_angle.shape)\n", "print(BMENV.Beam_angle)\n", "print(BMENV.angle_H_max)\n", "print(BMENV.angle_V_max)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(100, 4000, 17, 4, 64)\n", "(100, 2000, 17, 4)\n", "(100, 2000)\n", "(100, 2000)\n", "(100, 2000, 1, 32, 32)\n"]}], "source": ["print(BMENV.H_freq.shape)\n", "\n", "print(BMENV.srs_choose_all.shape)\n", "print(BMENV.sub_choose_all.shape)\n", "print(BMENV.port_choose_all.shape)\n", "\n", "print(BMENV.coma_all.shape)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(32,)\n"]}], "source": ["def cal_coma(srs_p1,srs_p2): # (nUE, nAnts)\n", "    srs_p1 = np.expand_dims(srs_p1,-2)\n", "    srs_p2 = np.expand_dims(srs_p2,-2)\n", "    #!coma srs.H@srs \n", "    # srs_p1.H@srs_p1\n", "    # Coma (nUE, nAnts ,nAnts)\n", "    Coma_p1 = np.conj(srs_p1.transpose(0,2,1))@srs_p1     #srs.H@srs\n", "    Coma_p2 = np.conj(srs_p2.transpose(0,2,1))@srs_p2     #srs.H@srs\n", "\n", "    Tra_p1 = np.trace(Coma_p1,axis1=-2,axis2=-1)\n", "    Tra_p2 = np.trace(Coma_p2,axis1=-2,axis2=-1)\n", "    a1 = np.divide(Tra_p1,Tra_p1+Tra_p2+1e-50)\n", "    a2 = np.divide(Tra_p2,Tra_p1+Tra_p2+1e-50)\n", "    Coma = Coma_p1*np.expand_dims(a1,axis=(-1,-2)) + Coma_p2*np.expand_dims(a2,axis=(-1,-2))\n", "    return Coma\n", "\n", "H = BMENV.H_freq[0]\n", "Ants_all = BMENV.H_ants * BMENV.V_ants\n", "Ants_all_2pol = Ants_all*2\n", "srs_pol1 = H[0,0,0,0:Ants_all]\n", "srs_pol2 = H[0,0,0,Ants_all:Ants_all_2pol]\n", "print(srs_pol1.shape)\n", "short_coma = cal_coma(np.expand_dims(srs_pol1,0),np.expand_dims(srs_pol2,0))\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(2000, 100)\n", "(2000, 100)\n", "(2000, 100, 32)\n", "(2000, 100)\n", "(2000, 100)\n", "(2000, 100, 32)\n"]}], "source": ["print(BMENV.Beam_idx1.shape)\n", "print(BMENV.Beam_idx2.shape)\n", "print(BMENV.Beam_idxs.shape)\n", "print(BMENV.Beam_power1.shape)\n", "print(BMENV.Beam_power2.shape)\n", "print(BMENV.Beam_powers.shape)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[ 0  0  0  0]\n", " [ 1  1  1  1]\n", " [ 2  2  2  2]\n", " [ 3  3  3  3]\n", " [ 4  4  4  4]\n", " [ 5  5  5  5]\n", " [ 6  6  6  6]\n", " [ 7  7  7  7]\n", " [ 8  8  8  8]\n", " [ 9  9  9  9]\n", " [10 10 10 10]\n", " [11 11 11 11]\n", " [12 12 12 12]\n", " [13 13 13 13]\n", " [14 14 14 14]\n", " [15 15 15 15]\n", " [16 16 16 16]]\n", "[[0 1 2 3]\n", " [0 1 2 3]\n", " [0 1 2 3]\n", " [0 1 2 3]\n", " [0 1 2 3]\n", " [0 1 2 3]\n", " [0 1 2 3]\n", " [0 1 2 3]\n", " [0 1 2 3]\n", " [0 1 2 3]\n", " [0 1 2 3]\n", " [0 1 2 3]\n", " [0 1 2 3]\n", " [0 1 2 3]\n", " [0 1 2 3]\n", " [0 1 2 3]\n", " [0 1 2 3]]\n"]}], "source": ["subs = [[i for i in range(17)]]*4\n", "subs = np.array(subs).transpose(1,0)\n", "ports = [[i for i in range(4)]]*17\n", "ports = np.array(ports)\n", "time_choose = BMENV.srs_choose_all[0]\n", "H_ = H[time_choose,subs,ports]\n", "print(subs)\n", "print(ports)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["V_ants = BMENV.V_ants\n", "H_ants = BMENV.H_ants\n", "\n", "draw_tool = draw(numHant=BMENV.H_ants,numVant=BMENV.V_ants,HSpacing=0.5,VSpacing=0.5)\n", "Beams = generate_dft_beams(numVant=BMENV.V_ants,numHant=BMENV.H_ants)\n", "\n", "Ants_all = BMENV.Ants_num\n", "PMI_map = []\n", "\n", "action = [2,1,1,4]\n", "H_split = action[0]\n", "V_split = action[1]\n", "RI = action[2]\n", "Port = action[3]\n", "N1 = max(V_split,H_split)*int(Port/H_split/V_split/2)\n", "N2 = min(V_split,H_split)\n", "O1 = (N1>1)*3+1\n", "O2 = (N2>1)*3+1\n", "\n", "codebook_origin = PMI_class.get_pmi_codebook(N1,N2,O1,O2,1,RI)\n", "codebook_origin = codebook_origin.transpose(0,1,2,3,5,4)\n", "cd_shape = codebook_origin.shape\n", "cd_shape = list(cd_shape)\n", "codebook = codebook_origin.reshape(-1,cd_shape[-1])\n", "codebook = np.expand_dims(codebook,axis=-1)\n", "cd_shape[-1] = 2\n", "        \n", "Beam_angle_all = []\n", "for beam_idx in range(len(Beams)):\n", "    Beam = Beams[beam_idx]\n", "    Beam = np.expand_dims(<PERSON>am,0)\n", "    nBeam = int(Port/H_split/V_split/2) \n", "    Beam = np.repeat(Beam,nBeam,axis=0)\n", "    Ports_vector = generate_beam_pattern(V_ants,H_ants,V_split,H_split)\n", "    if len(Beam.shape) == 1:\n", "        Beam = np.expand_dims(<PERSON>am,0)\n", "    Beam = Beam.transpose(1,0) #16*n\n", "    Beam_Port = Beam*Ports_vector\n", "    FRF = np.kron(np.eye(2,dtype=np.complex64),Beam_Port)\n", "    FRF = FRF/np.sqrt(V_ants*H_ants*2)/np.sqrt(nBeam)*np.sqrt(Port)\n", "\n", "    F_ALL = FRF@codebook\n", "    Beam_angle = []\n", "    for i in range(F_ALL.shape[0]):\n", "        angles = draw_tool.cal_gain(F_ALL[i,0:Ants_all],Find_angle_only=True)\n", "        Beam_angle.append(angles)\n", "    Beam_angle = np.array(Beam_angle)\n", "    Beam_angle = Beam_angle.reshape(cd_shape)\n", "    Beam_angle_all.append(Beam_angle)\n", "Beam_angle_all = np.array(Beam_angle_all)\n", "PMI_map.append(Beam_angle_all)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["pattern = generate_beam_pattern(V_ants,H_ants,V_split,H_split)   "]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["n=1\n", "s1,v1,d1 = np.linalg.svd(BMENV.coma_all[0,0,0])\n", "s1 = s1.T\n", "if n is not None:\n", "    svdbeam = np.transpose(s1[0:n+1],(1,0))\n", "else:\n", "    svdbeam = np.transpose(s1,(1,0))"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(100, 2000, 1, 32, 32)\n", "(100, 2000)\n"]}], "source": ["print(BMENV.coma_all.shape)\n", "print(BMENV.sub_choose_all.shape)\n", "coma_all = BMENV.coma_all\n", "idx,power = update_Beam(BMENV.coma_all[0][0],BMENV.Beams)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(32,)\n", "(32,)\n", "(32, 32)\n"]}], "source": ["# input H nSub*Port*Ants\n", "# cal_param\n", "H=BMENV.H_freq[0,0]\n", "Ants_all = V_ants*H_ants\n", "Ants_all_2pol = 2*Ants_all\n", "# get srs data\n", "# H nport*nsub*Ants_all_2pol\n", "srs_pol1 = H[:,:,0:Ants_all]\n", "srs_pol2 = H[:,:,Ants_all:Ants_all_2pol]\n", "srs_pol1 = srs_pol1.reshape(-1,Ants_all)\n", "srs_pol2 = srs_pol2.reshape(-1,Ants_all) \n", "# get short coma\n", "coma = cal_coma(srs_pol1,srs_pol2)\n", "coma = np.average(coma,axis=(0))\n", "# get long coma\n", "# direct average\n", "coma = np.expand_dims(coma,axis=0)\n", "# get Beam\n", "# dft beam from coma\n", "Beams = generate_dft_beams(V_ants,H_ants)\n", "beam_idx,power = update_Beam(coma,Beams)\n", "beam_idx = beam_idx[0]\n", "power = power[0]\n", "\n", "Beam = Beams[beam_idx]\n", "print(beam_idx.shape)\n", "print(power.shape)\n", "print(Beam.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["H=BMENV.H_freq[0,0]\n", "Beam,power,idx = cal_beam_method1(H,V_ants,H_ants)\n", "FRF = cal_analog_matrix(<PERSON><PERSON>,V_ants,H_ants,2,1,4)\n", "H=H@FRF\n", "N1=2\n", "N2=1\n", "layer=3\n", "O1 = (N1>1)*3+1\n", "O2 = (N2>1)*3+1\n", "assert(layer <= N1*N2*2)   \n", "codebook_origin = PMI_class.get_pmi_codebook(N1,N2,O1,O2,1,layer)\n", "H = np.expand_dims(H,(0,1,2,3)) #0,1,2,3,nSub,ue_port,max_gNB_port\n", "codebook = np.expand_dims(codebook_origin, axis=4)\n", "HP = H@codebook #0,1,2,3,ue_port,layer\n", "Layer_power = np.conj(HP.transpose(0,1,2,3,4,6,5))@HP\n", "HP_power = np.trace(np.abs(Layer_power),axis1=-1,axis2=-2)\n", "HP_power = np.sum(HP_power,axis = -1)\n", "\n", "idx = np.unravel_index(np.argmax(HP_power),HP_power.shape)\n", "P = codebook_origin[idx]\n", "max_power = HP_power[idx]\n", "lp = Layer_power[idx]"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os,sys\n", "sys.path.append(os.getcwd())\n", "sys.path.append(\"../../\")\n", "from setsumi_env.BM_functions import *"]}], "metadata": {"kernelspec": {"display_name": "setsumi", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.15"}}, "nbformat": 4, "nbformat_minor": 2}