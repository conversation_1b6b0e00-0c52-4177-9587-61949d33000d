{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os,sys\n", "sys.path.append(os.getcwd())\n", "sys.path.append(\"../../\")\n", "from setsumi_env.BM_functions import *\n", "from setsumi_env.system_functions import *\n", "from setsumi_env.process import *\n", "from setsumi_env.analyze import *\n", "from setsumi_env.utils import *\n", "from setsumi_env.setsumi_env import setsumi_env\n", "import numpy as np\n", "import pickle\n", "import re\n", "import pandas as pd\n", "from collections import Counter\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "\n", "from scipy import stats\n", "import copy\n", "\n", "import matplotlib.pyplot as plt\n", "import matplotlib.patches as mpatches\n", "from IPython.display import Image\n", "from matplotlib.gridspec import GridSpec\n", "import time\n", "from datetime import datetime"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["setsumi load sim log OK\n", "Load begin\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[0;32m/tmp/ipykernel_1470439/3462240179.py\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0mBMenv\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0msetsumi_env\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mpath\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m\"/data/BM_data/28GHZ/UMa_2000_o/\"\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mmax_time\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;36m200\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mmax_length\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;36m200\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mgNB_tti\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;36m2\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mseed\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;36m777\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      2\u001b[0m \u001b[0mdraw_tool\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mBMenv\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdraw\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      3\u001b[0m \u001b[0msim_par\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mBMenv\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0msim_par\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      4\u001b[0m \u001b[0mBMenv\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mload_data\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      5\u001b[0m \u001b[0mBMenv\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mload_H\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/data/setsumi/beamforming/setsumi_env/setsumi_env.py\u001b[0m in \u001b[0;36m__init__\u001b[0;34m(self, logger, path, max_time, max_length, seed, enable_load, gNB_tti, UE_tti, Beam_tti)\u001b[0m\n\u001b[1;32m    140\u001b[0m             \u001b[0;32mpass\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    141\u001b[0m         \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 142\u001b[0;31m             \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mload_H\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    143\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    144\u001b[0m         \u001b[0;32mif\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mos\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mpath\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mexists\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mpath\u001b[0m\u001b[0;34m+\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mbeam_name\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;32mand\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0menable_load\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/data/setsumi/beamforming/setsumi_env/setsumi_env.py\u001b[0m in \u001b[0;36mload_H\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    177\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mlogger\u001b[0m \u001b[0;32mis\u001b[0m \u001b[0;32mnot\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    178\u001b[0m             \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mlogger\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"Load begin\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 179\u001b[0;31m         \u001b[0mget_data\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0msim_par\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mclear_time\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;32mTrue\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    180\u001b[0m         \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mH_freq\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0marray\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0msim_par\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdata\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m'H_freq'\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mdtype\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcomplex64\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    181\u001b[0m         \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mH_pg\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0marray\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0msim_par\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdata\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m'H_pg'\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mdtype\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcomplex64\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/data/setsumi/beamforming/setsumi_env/process.py\u001b[0m in \u001b[0;36mget_data\u001b[0;34m(sim_par, clear_time)\u001b[0m\n\u001b[1;32m    135\u001b[0m     \u001b[0;32mif\u001b[0m \u001b[0;32mnot\u001b[0m \u001b[0mos\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mpath\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mexists\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0msim_par\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mData_dir\u001b[0m\u001b[0;34m+\u001b[0m\u001b[0;34m\"data_redo.npz\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    136\u001b[0m         \u001b[0mpre_process\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0msim_par\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mData_dir\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m~\u001b[0m\u001b[0mclear_time\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 137\u001b[0;31m     \u001b[0mdata_redo\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mload\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0msim_par\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mData_dir\u001b[0m\u001b[0;34m+\u001b[0m\u001b[0;34m\"data_redo.npz\"\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mallow_pickle\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;32mTrue\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m'arr_0'\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mitem\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    138\u001b[0m     \u001b[0msim_par\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdata\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mdata_redo\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    139\u001b[0m     \u001b[0;32mif\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mclear_time\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/conda/envs/setsumi/lib/python3.7/site-packages/numpy/lib/npyio.py\u001b[0m in \u001b[0;36m__getitem__\u001b[0;34m(self, key)\u001b[0m\n\u001b[1;32m    254\u001b[0m                 return format.read_array(bytes,\n\u001b[1;32m    255\u001b[0m                                          \u001b[0mallow_pickle\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mallow_pickle\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 256\u001b[0;31m                                          pickle_kwargs=self.pickle_kwargs)\n\u001b[0m\u001b[1;32m    257\u001b[0m             \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    258\u001b[0m                 \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mzip\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mread\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mkey\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/conda/envs/setsumi/lib/python3.7/site-packages/numpy/lib/format.py\u001b[0m in \u001b[0;36mread_array\u001b[0;34m(fp, allow_pickle, pickle_kwargs)\u001b[0m\n\u001b[1;32m    746\u001b[0m             \u001b[0mpickle_kwargs\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34m{\u001b[0m\u001b[0;34m}\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    747\u001b[0m         \u001b[0;32mtry\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 748\u001b[0;31m             \u001b[0marray\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mpickle\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mload\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mfp\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mpickle_kwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    749\u001b[0m         \u001b[0;32mexcept\u001b[0m \u001b[0mUnicodeError\u001b[0m \u001b[0;32mas\u001b[0m \u001b[0merr\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    750\u001b[0m             \u001b[0;31m# Friendlier error message\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/conda/envs/setsumi/lib/python3.7/zipfile.py\u001b[0m in \u001b[0;36mread\u001b[0;34m(self, n)\u001b[0m\n\u001b[1;32m    928\u001b[0m         \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_offset\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;36m0\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    929\u001b[0m         \u001b[0;32mwhile\u001b[0m \u001b[0mn\u001b[0m \u001b[0;34m>\u001b[0m \u001b[0;36m0\u001b[0m \u001b[0;32mand\u001b[0m \u001b[0;32mnot\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_eof\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 930\u001b[0;31m             \u001b[0mdata\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_read1\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mn\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    931\u001b[0m             \u001b[0;32mif\u001b[0m \u001b[0mn\u001b[0m \u001b[0;34m<\u001b[0m \u001b[0mlen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mdata\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    932\u001b[0m                 \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_readbuffer\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mdata\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/conda/envs/setsumi/lib/python3.7/zipfile.py\u001b[0m in \u001b[0;36m_read1\u001b[0;34m(self, n)\u001b[0m\n\u001b[1;32m   1018\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_left\u001b[0m \u001b[0;34m<=\u001b[0m \u001b[0;36m0\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1019\u001b[0m             \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_eof\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;32mTrue\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1020\u001b[0;31m         \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_update_crc\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mdata\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1021\u001b[0m         \u001b[0;32mreturn\u001b[0m \u001b[0mdata\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1022\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/conda/envs/setsumi/lib/python3.7/zipfile.py\u001b[0m in \u001b[0;36m_update_crc\u001b[0;34m(self, newdata)\u001b[0m\n\u001b[1;32m    943\u001b[0m             \u001b[0;31m# No need to compute the CRC if we don't have a reference value\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    944\u001b[0m             \u001b[0;32mreturn\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 945\u001b[0;31m         \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_running_crc\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mcrc32\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mnewdata\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_running_crc\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    946\u001b[0m         \u001b[0;31m# Check the CRC if we're at the end of the file\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    947\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_eof\u001b[0m \u001b[0;32mand\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_running_crc\u001b[0m \u001b[0;34m!=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_expected_crc\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["BMenv = setsumi_env(path=\"/data/BM_data/28GHZ/UMa_2000_o/\",max_time=200,max_length=200,gNB_tti=2,seed=777) \n", "draw_tool = BMenv.draw\n", "sim_par = BMenv.sim_par\n", "BMenv.load_data()\n", "BMenv.load_H()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ues1 = [2, 26, 33, 109, 268, 304, 328, 374, 466, 535, 592, 599, 676, 779, 844, 947, 1011, 1022, 1061, 1130, 1175, 1183, 1292, 1307, 1359, 1380, 1419, 1440, 1447, 1504, 1511, 1512, 1569, 1647, 1658, 1779, 1798, 1897]\n", "ues2 = [99, 102, 174, 181, 275, 303, 312, 319, 333, 336, 363, 430, 589, 605, 641, 731, 752, 792, 871, 892, 976, 982, 1080, 1092, 1198, 1267, 1397, 1423, 1517, 1534, 1543, 1580, 1586, 1682, 1695, 1769, 1807, 1865, 1889, 1916, 1952]"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [], "source": ["UE_SE = np.zeros((BMenv.max_length,BMenv.nUE,len(BMenv.action_space),BMenv.nSNR,BMenv.nSub),dtype=np.float32)\n"]}, {"cell_type": "code", "execution_count": 74, "metadata": {}, "outputs": [], "source": ["gNB_SE = np.zeros((BMenv.max_length,BMenv.nUE,len(BMenv.action_space),BMenv.nSNR,BMenv.nSub),dtype=np.float32)"]}, {"cell_type": "code", "execution_count": 76, "metadata": {}, "outputs": [], "source": ["with open(BMenv.path+\"SE_data\", 'wb') as file:\n", "    pickle.dump(gNB_SE, file,protocol = 4)\n", "    pickle.dump(UE_SE, file,protocol = 4)"]}, {"cell_type": "code", "execution_count": 183, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["38\n"]}], "source": ["print(len(ues1))"]}, {"cell_type": "code", "execution_count": 146, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(790, 17, 64, 4)\n", "(790, 17, 4, 64)\n"]}], "source": ["print(F.shape)\n", "print(H.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["HF = H@F\n", "Layer_power = np.conj(HF.transpose(0,1,3,2))@HP"]}, {"cell_type": "code", "execution_count": 235, "metadata": {}, "outputs": [], "source": ["action_space = [(1,1,1,2),(1,1,2,2),\\\n", "                        (2,1,1,4),(2,1,2,4),\\\n", "                        (2,1,3,4),(2,1,4,4),\\\n", "                        (1,2,1,4),(1,2,2,4),\\\n", "                        (1,2,3,4),(1,2,4,4),\\\n", "                        (2,2,1,8),(2,2,2,8),\\\n", "                        (2,2,3,8),(2,2,4,8),\\\n", "                        (4,1,1,8),(4,1,2,8),\\\n", "                        (4,1,3,8),(4,1,4,8),]\n", "                        # (1,1,1,4),(1,1,2,4),\\\n", "                        # (1,1,3,4),(1,1,4,4),]"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'np' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[0;32m/tmp/ipykernel_1439682/1185924748.py\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0mse\u001b[0m \u001b[0;34m=\u001b[0m  \u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mclip\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mse\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;36m8\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      2\u001b[0m \u001b[0mprint\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mmax\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mse\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mNameError\u001b[0m: name 'np' is not defined"]}], "source": ["se =  np.clip(se,0,8)\n", "print(np.max(se))"]}, {"cell_type": "code", "execution_count": 298, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(790,)\n"]}], "source": ["UEs = np.array([2,26])\n", "UEs = np.array(ues1+ues2)\n", "nUE = UEs.shape[0]\n", "times = np.arange(190,200)\n", "ntime = times.shape[0]\n", "actions = np.arange(len(action_space))\n", "# actions = np.array([5])\n", "# SNRs = np.arange(-30,35,5,dtype=np.float32)\n", "SNRs = np.array([30])\n", "nSNR = SNRs.shape[0]\n", "H_ants = 8\n", "V_ants = 4\n", "\n", "UEs_new, times_new = np.meshgrid(UEs, times) #用户连续\n", "UEs = UEs_new.reshape(-1)\n", "times = times_new.reshape(-1)\n", "mod_time = times%BMenv.max_time #sim_par.time代表数据总的个数\n", "srs_idx = times//BMenv.gNB_tti\n", "print(srs_idx.shape)\n", "\n", "nUE = UEs.shape[0]\n", "\n", "H = BMenv.H_freq[UEs,mod_time]\n", "\n", "Beam_idx = BMenv.Beam_idxs[srs_idx,UEs]\n", "SE_all_ac = []\n", "sinr_all_se = []\n", "for ac in actions:\n", "    action = action_space[ac]\n", "    H_split = action[0]\n", "    V_split = action[1]\n", "    RI = action[2]\n", "    Port = action[3]\n", "    N1 = max(V_split,H_split)*int(Port/H_split/V_split/2)\n", "    N2 = min(V_split,H_split)\n", "    nBeam = int(Port/H_split/V_split/2)\n", "    O1 = (N1>1)*3+1\n", "    O2 = (N2>1)*3+1\n", "    assert(RI <= N1*N2*2)   \n", "    pattern = generate_beam_pattern(V_ants,H_ants,V_split,H_split)\n", "    \n", "    Beam_idx = BMenv.Beam_idxs[srs_idx,UEs]\n", "    Beam_idx = Beam_idx[:,0:nBeam]\n", "    Beam = BMenv.Beams[Beam_idx]\n", "    Beam = Beam.transpose(0,2,1)\n", "    Ports_vector = pattern\n", "    Beam_Port = Beam*Ports_vector\n", "    FRF = np.kron(np.eye(2,dtype=np.complex64),Beam_Port)\n", "    FRF = FRF/np.sqrt(V_ants*H_ants*2)/np.sqrt(nBeam)*np.sqrt(Port)\n", "    FRF = np.expand_dims(FRF,axis=1)\n", "    codebook_origin = PMI_class.get_pmi_codebook(N1,N2,O1,O2,1,RI)\n", "    codebook_origin = codebook_origin.astype(np.complex64)\n", "    # PMI = BMenv.UE_PMI[times,UEs,ac]\n", "    # P = codebook_origin[PMI[:,:,0],PMI[:,:,1],PMI[:,:,2],PMI[:,:,3]]\n", "    # F = FRF@P\n", "    \n", "    UE_H = H@FRF\n", "    UE_H = np.expand_dims(UE_H,(0,1,2,3))\n", "    codebook_re = codebook_origin.reshape(-1,Port,RI)\n", "    codebook = np.expand_dims(codebook_origin, axis=(4,5))\n", "\n", "    HP = np.matmul(UE_H,codebook)\n", "    Layer_power = np.conj(HP.transpose(0,1,2,3,4,5,7,6))@HP\n", "    HP_power = np.trace(np.abs(Layer_power),axis1=-1,axis2=-2)\n", "    \n", "    bs_shape = HP_power.shape[0:4]\n", "    HP_power = HP_power.reshape(-1,nUE,BMenv.nSub)\n", "    idx = np.argmax(HP_power,axis=0)\n", "\n", "    PMI = np.unravel_index(idx,bs_shape)\n", "    PMI = np.array(PMI)\n", "    PMI = PMI.transpose(1,2,0)\n", "\n", "    P = codebook_re[idx]\n", "    \n", "    FBB = P\n", "\n", "    F = FRF@FBB\n", "    \n", "    # Layer_power = Layer_power.reshape(-1,nUE,BMenv.nSub,RI,RI)\n", "    # # lp = Layer_power[idx,np.arange(idx.shape[0])]\n", "    # lp = Layer_power[idx,ues[:,:,0],subs[:,:,0]]\n", "    \n", "    \n", "\n", "    # SE_all = [cal_se_auto(H[i:i+1],SNRs,F[i]) for i in range(times.shape[0])]\n", "    # SE_all = np.array(SE_all)\n", "    # SE_all = SE_all.transpose(0,1,3,2)\n", "    \n", "    \n", "    # SE_all = SE_all.reshape(ntime,nUE,BMenv.nSub,nSNR)\n", "    # SE_all = SE_all.transpose(0,1,3,2)\n", "    # print(SE_all.shape)\n", "    # SE_all_ac.append(SE_all)\n", "    # UE_SE[times,UEs,ac] = SE_all[:,0,:,:]\n", "    # print(f\"ac {ac} OK\")\n", "    # SE_all_ac.append(SE_all)\n", "    sinr_se = []\n", "    for snr_idx in range(BMenv.nSNR):\n", "        snr = BMenv.SNRs[snr_idx]\n", "        R = np.float32(np.power(10,(-snr/10)))\n", "        HF = H@F/np.sqrt(RI)\n", "        lp = np.conj(HF.transpose(0,1,3,2))@HF\n", "        sinr = np.linalg.inv(lp/R+np.eye(RI,dtype=np.complex64))\n", "        sinr = np.real(np.diagonal(sinr,axis1 = -1,axis2 = -2))\n", "        sinr = 1./sinr -1 \n", "        sinr = np.clip(sinr,0.00001,1000000000)\n", "\n", "        se =  np.log2(1 + sinr)\n", "        se =  np.clip(se,0,8)\n", "        sinr_se.append(np.sum(se,axis=-1))\n", "    sinr_all_se.append(sinr_se)"]}, {"cell_type": "code", "execution_count": 300, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(18, 13, 790, 17)\n"]}], "source": ["sinr_all_se = np.array(sinr_all_se)\n", "print(sinr_all_se.shape)"]}, {"cell_type": "code", "execution_count": 301, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(18, 13, 790)\n"]}], "source": ["sinr_all_se = np.average(sinr_all_se,axis=-1)\n", "print(sinr_all_se.shape)"]}, {"cell_type": "code", "execution_count": 283, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1.         1.         0.95192308 0.95192308 0.95192308 0.95192308\n", " 0.95192308 0.95192308 0.95192308 0.95192308 0.87903226 0.87903226\n", " 0.87903226 0.87903226 0.87903226 0.87903226 0.87903226 0.87903226]\n"]}], "source": ["fix = 64*0.01+64*8*0.005\n", "fix2 = 64*8*0.01\n", "a1 = fix+1+0.25*2\n", "a2 = fix+1+0.25*4\n", "a3 = fix+1+0.25*8\n", "a4 = fix2+1+0.25*4\n", "TP_EE = np.array([a1,a1,\\\n", "                        a2,a2,a2,a2,\\\n", "                        a2,a2,a2,a2,\\\n", "                        a3,a3,a3,a3,\\\n", "                        a3,a3,a3,a3])\n", "TP_EE = (1/TP_EE*np.min(TP_EE)+1)/2\n", "print(TP_EE)"]}, {"cell_type": "code", "execution_count": 297, "metadata": {}, "outputs": [], "source": ["sinr_all_se2 = copy.deepcopy(sinr_all_se)"]}, {"cell_type": "code", "execution_count": 319, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Counter({1: 77, 7: 61, 8: 58, 9: 43, 3: 40, 13: 35, 4: 19, 11: 18, 17: 15, 12: 13, 5: 10, 0: 1})\n"]}], "source": ["ss = sinr_all_se2[:,12,:]*TP_EE[:,np.newaxis]\n", "ss = ss.reshape(18,10,79)\n", "print(Counter(np.argmax(ss[:,:,0:39],axis=0).reshape(-1)))"]}, {"cell_type": "code", "execution_count": 330, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Counter({10: 125, 6: 100, 2: 52, 7: 34, 11: 22, 14: 21, 3: 15, 0: 14, 15: 6, 1: 1})\n"]}], "source": ["ss = sinr_all_se[:,5,:]*TP_EE[:,np.newaxis]\n", "ss = ss.reshape(18,10,79)\n", "print(Counter(np.argmax(ss[:,:,0:39],axis=0).reshape(-1)))"]}, {"cell_type": "code", "execution_count": 309, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(18, 13, 790)\n"]}], "source": ["print(sinr_all_se.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sinr_all_se = np.average(sinr_all_se,axis=-1)"]}, {"cell_type": "code", "execution_count": 244, "metadata": {}, "outputs": [], "source": ["sinr_gNB_se = np.zeros((BMenv.max_length,BMenv.nUE,len(BMenv.action_space),BMenv.nSNR,BMenv.nSub),dtype=np.float32)"]}, {"cell_type": "code", "execution_count": 242, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[   2   26   33 ... 1889 1916 1952]\n"]}], "source": ["print(UEs)"]}, {"cell_type": "code", "execution_count": 247, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["15800\n"]}], "source": ["print(UEs.shape[0])"]}, {"cell_type": "code", "execution_count": 248, "metadata": {}, "outputs": [], "source": ["UEs = np.array(ues1+ues2)\n", "for i in range(UEs.shape[0]):\n", "    sinr_gNB_se[:,UEs[i],:,:,:] = sinr_all_se[:,i,:,:,:]"]}, {"cell_type": "code", "execution_count": 239, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(18, 13, 15800, 17)\n", "(200, 79, 18, 13, 17)\n"]}], "source": ["sinr_all_se = np.array(sinr_all_se)\n", "print(sinr_all_se.shape)\n", "sinr_all_se = sinr_all_se.reshape(18,13,200,79,17)\n", "sinr_all_se = sinr_all_se.transpose(2,3,0,1,4)\n", "print(sinr_all_se.shape)"]}, {"cell_type": "code", "execution_count": 249, "metadata": {}, "outputs": [], "source": ["with open(BMenv.path+\"sinr_se_data\", 'wb') as file:\n", "    pickle.dump(sinr_gNB_se, file,protocol = 4)"]}, {"cell_type": "code", "execution_count": 214, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1.         1.         0.95192308 0.95192308 0.95192308 0.95192308\n", " 0.95192308 0.95192308 0.95192308 0.95192308 0.87903226 0.87903226\n", " 0.87903226 0.87903226 0.87903226 0.87903226 0.87903226 0.87903226\n", " 0.83005618 0.83005618 0.83005618 0.83005618]\n"]}], "source": ["fix = 64*0.01+64*8*0.005\n", "fix2 = 64*8*0.01\n", "a1 = fix+1+0.25*2\n", "a2 = fix+1+0.25*4\n", "a3 = fix+1+0.25*8\n", "a4 = fix2+1+0.25*4\n", "TP_EE = np.array([a1,a1,\\\n", "                        a2,a2,a2,a2,\\\n", "                        a2,a2,a2,a2,\\\n", "                        a3,a3,a3,a3,\\\n", "                        a3,a3,a3,a3,\\\n", "                        a4,a4,a4,a4])\n", "TP_EE = (1/TP_EE*np.min(TP_EE)+1)/2\n", "print(TP_EE)"]}, {"cell_type": "code", "execution_count": 215, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(22, 13, 790, 17)\n"]}], "source": ["# sinr_all_se = np.array(sinr_all_se)\n", "sinr_all_se = np.array(sinr_all_se)\n", "print(sinr_all_se.shape)"]}, {"cell_type": "code", "execution_count": 185, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(18, 13, 790)\n"]}], "source": ["RI = np.argmax()"]}, {"cell_type": "code", "execution_count": 221, "metadata": {}, "outputs": [], "source": ["sinr_all_se_avg = np.average(sinr_all_se,axis=-1)\n", "ch3 = np.argmax(sinr_all_se_avg[:,1:12,:],axis=0)\n", "sinr_all_se_avg2 = sinr_all_se_avg*TP_EE[:,np.newaxis,np.newaxis]\n", "ch4 = np.argmax(sinr_all_se_avg2[:,1:12,:],axis=0)"]}, {"cell_type": "code", "execution_count": 252, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(200, 2000, 18, 13, 17)\n"]}], "source": ["print(sinr_gNB_se.shape)"]}, {"cell_type": "code", "execution_count": 255, "metadata": {}, "outputs": [], "source": ["sinr_all_se_avg_ = np.average(sinr_all_se_avg2,axis=-1)"]}, {"cell_type": "code", "execution_count": 256, "metadata": {}, "outputs": [], "source": ["sinr_all_se_avg2 = sinr_gNB_se[:,ues1,:,:,:]\n", "sinr_all_se_avg2 = np.average(sinr_all_se_avg2,axis=-1)\n", "sinr_all_se_avg2 = np.average(sinr_all_se_avg2,axis=0)\n", "sinr_all_se_avg_ = np.average(sinr_all_se_avg2,axis=0)"]}, {"cell_type": "code", "execution_count": 263, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(18, 13)\n"]}], "source": ["print(sinr_all_se_avg_.shape)\n", "sinr_all_se_avg_ = sinr_all_se_avg_*TP_EE[0:18][:,np.newaxis]"]}, {"cell_type": "code", "execution_count": 264, "metadata": {}, "outputs": [], "source": ["action_map = {}\n", "for i in range(len(action_space)):\n", "    action = action_space[i]\n", "    name = f\"H{action[0]}V{action[1]}P{action[3]}\"\n", "    if not name in action_map:\n", "        action_map[name] = []\n", "    action_map[name].append(i)\n", "action_map = action_map"]}, {"cell_type": "code", "execution_count": 265, "metadata": {}, "outputs": [], "source": ["sinr_all_ac = reduce_action(sinr_all_se_avg_,action_map)"]}, {"cell_type": "code", "execution_count": 266, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"customdata": [null, null, null, null, null, null, null, null, null, null, null, null, null], "hovertemplate": "snr:%{x}<br>se:%{y}<br>%{customdata}", "mode": "lines", "name": "H1V1P2", "type": "scatter", "x": [-30, -25, -20, -15, -10, -5, 0, 5, 10, 15, 20, 25, 30], "y": [0.024164214730262756, 0.070083387196064, 0.18547523021697998, 0.43550345301628113, 0.9005983471870422, 1.6475863456726074, 2.7415549755096436, 4.584751129150391, 6.955152988433838, 9.732501983642578, 12.776565551757812, 15.971590042114258, 19.241262435913086]}, {"customdata": [null, null, null, null, null, null, null, null, null, null, null, null, null], "hovertemplate": "snr:%{x}<br>se:%{y}<br>%{customdata}", "mode": "lines", "name": "H2V1P4", "type": "scatter", "x": [-30, -25, -20, -15, -10, -5, 0, 5, 10, 15, 20, 25, 30], "y": [0.029207243709466778, 0.08416875115094276, 0.22020322328003553, 0.5081294517104442, 1.0269744109648924, 1.8309397823535478, 2.9618557966672454, 4.870075849386362, 7.269098556958712, 10.022830119499792, 12.991138733350313, 16.070606836905846, 19.200549987646248]}, {"customdata": [null, null, null, null, null, null, null, null, null, null, null, null, null], "hovertemplate": "snr:%{x}<br>se:%{y}<br>%{customdata}", "mode": "lines", "name": "H1V2P4", "type": "scatter", "x": [-30, -25, -20, -15, -10, -5, 0, 5, 10, 15, 20, 25, 30], "y": [0.033105844989992105, 0.09494644467933819, 0.24594210718686763, 0.5585590509267954, 1.1071454217800727, 1.937532800894517, 3.162257167009207, 5.117059515072749, 7.548795681733351, 10.32392920897557, 13.30509261901562, 16.391684422126183, 19.64681839942932]}, {"customdata": [null, null, null, null, null, null, null, null, null, null, null, null, null], "hovertemplate": "snr:%{x}<br>se:%{y}<br>%{customdata}", "mode": "lines", "name": "H2V2P8", "type": "scatter", "x": [-30, -25, -20, -15, -10, -5, 0, 5, 10, 15, 20, 25, 30], "y": [0.037130549640184446, 0.10551267243441075, 0.26919407229269704, 0.5994849464585704, 1.1631698435352695, 1.9938540112587713, 3.268683877683455, 5.192630129475748, 7.535956921115998, 10.16458675938268, 12.954656754770587, 15.822659446347144, 18.723611093336537]}, {"customdata": [null, null, null, null, null, null, null, null, null, null, null, null, null], "hovertemplate": "snr:%{x}<br>se:%{y}<br>%{customdata}", "mode": "lines", "name": "H4V1P8", "type": "scatter", "x": [-30, -25, -20, -15, -10, -5, 0, 5, 10, 15, 20, 25, 30], "y": [0.02862964576530841, 0.08234575252619482, 0.21460499614477158, 0.4922378947657923, 0.98771313505788, 1.7485164740393238, 2.8346201200639047, 4.630368997973781, 6.872733446859544, 9.43337546625445, 12.18418989642974, 15.032600433595718, 17.924961074706047]}], "layout": {"height": 700, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "se"}, "width": 1000, "xaxis": {"anchor": "y", "domain": [0, 1], "title": {"font": {"size": 30}, "text": "snr"}}, "yaxis": {"anchor": "x", "domain": [0, 1], "title": {"font": {"size": 30}, "text": "se"}}}}, "text/html": ["<div>                            <div id=\"0ead21a7-3182-498b-ba39-3306741ff861\" class=\"plotly-graph-div\" style=\"height:700px; width:1000px;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"0ead21a7-3182-498b-ba39-3306741ff861\")) {                    Plotly.newPlot(                        \"0ead21a7-3182-498b-ba39-3306741ff861\",                        [{\"customdata\":[null,null,null,null,null,null,null,null,null,null,null,null,null],\"hovertemplate\":\"snr:%{x}<br>se:%{y}<br>%{customdata}\",\"mode\":\"lines\",\"name\":\"H1V1P2\",\"x\":[-30.0,-25.0,-20.0,-15.0,-10.0,-5.0,0.0,5.0,10.0,15.0,20.0,25.0,30.0],\"y\":[0.024164214730262756,0.070083387196064,0.18547523021697998,0.43550345301628113,0.9005983471870422,1.6475863456726074,2.7415549755096436,4.584751129150391,6.955152988433838,9.732501983642578,12.776565551757812,15.971590042114258,19.241262435913086],\"type\":\"scatter\"},{\"customdata\":[null,null,null,null,null,null,null,null,null,null,null,null,null],\"hovertemplate\":\"snr:%{x}<br>se:%{y}<br>%{customdata}\",\"mode\":\"lines\",\"name\":\"H2V1P4\",\"x\":[-30.0,-25.0,-20.0,-15.0,-10.0,-5.0,0.0,5.0,10.0,15.0,20.0,25.0,30.0],\"y\":[0.029207243709466778,0.08416875115094276,0.22020322328003553,0.5081294517104442,1.0269744109648924,1.8309397823535478,2.9618557966672454,4.870075849386362,7.269098556958712,10.022830119499792,12.991138733350313,16.070606836905846,19.200549987646248],\"type\":\"scatter\"},{\"customdata\":[null,null,null,null,null,null,null,null,null,null,null,null,null],\"hovertemplate\":\"snr:%{x}<br>se:%{y}<br>%{customdata}\",\"mode\":\"lines\",\"name\":\"H1V2P4\",\"x\":[-30.0,-25.0,-20.0,-15.0,-10.0,-5.0,0.0,5.0,10.0,15.0,20.0,25.0,30.0],\"y\":[0.033105844989992105,0.09494644467933819,0.24594210718686763,0.5585590509267954,1.1071454217800727,1.937532800894517,3.162257167009207,5.117059515072749,7.548795681733351,10.32392920897557,13.30509261901562,16.391684422126183,19.64681839942932],\"type\":\"scatter\"},{\"customdata\":[null,null,null,null,null,null,null,null,null,null,null,null,null],\"hovertemplate\":\"snr:%{x}<br>se:%{y}<br>%{customdata}\",\"mode\":\"lines\",\"name\":\"H2V2P8\",\"x\":[-30.0,-25.0,-20.0,-15.0,-10.0,-5.0,0.0,5.0,10.0,15.0,20.0,25.0,30.0],\"y\":[0.037130549640184446,0.10551267243441075,0.26919407229269704,0.5994849464585704,1.1631698435352695,1.9938540112587713,3.268683877683455,5.192630129475748,7.535956921115998,10.16458675938268,12.954656754770587,15.822659446347144,18.723611093336537],\"type\":\"scatter\"},{\"customdata\":[null,null,null,null,null,null,null,null,null,null,null,null,null],\"hovertemplate\":\"snr:%{x}<br>se:%{y}<br>%{customdata}\",\"mode\":\"lines\",\"name\":\"H4V1P8\",\"x\":[-30.0,-25.0,-20.0,-15.0,-10.0,-5.0,0.0,5.0,10.0,15.0,20.0,25.0,30.0],\"y\":[0.02862964576530841,0.08234575252619482,0.21460499614477158,0.4922378947657923,0.98771313505788,1.7485164740393238,2.8346201200639047,4.630368997973781,6.872733446859544,9.43337546625445,12.18418989642974,15.032600433595718,17.924961074706047],\"type\":\"scatter\"}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"xaxis\":{\"anchor\":\"y\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"snr\",\"font\":{\"size\":30}}},\"yaxis\":{\"anchor\":\"x\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"se\",\"font\":{\"size\":30}}},\"title\":{\"text\":\"se\"},\"width\":1000,\"height\":700},                        {\"responsive\": true}                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('0ead21a7-3182-498b-ba39-3306741ff861');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })                };                });            </script>        </div>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "fig_se = draw_line(\"se\",\"snr\",\"se\")\n", "for i in range(0,len(action_map)):\n", "    name = list(action_map.keys())[i]\n", "    se = sinr_all_ac[i]\n", "    se = np.max(se,axis=0)\n", "    fig_se.add_line(SNRs,se,name)\n", "fig_show = fig_se.get_fig()\n", "fig_show.show()"]}, {"cell_type": "code", "execution_count": 222, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Counter({10: 2283, 11: 2073, 18: 1058, 19: 802, 14: 739, 15: 564, 12: 278, 20: 231, 21: 187, 17: 159, 13: 152, 16: 41, 6: 38, 4: 25, 7: 17, 5: 15, 8: 13, 2: 10, 9: 5})\n", "Counter({10: 1449, 3: 1330, 2: 901, 7: 854, 6: 658, 18: 648, 1: 522, 11: 481, 4: 452, 14: 407, 5: 224, 19: 195, 15: 147, 0: 115, 17: 65, 13: 65, 8: 60, 12: 41, 20: 24, 9: 24, 21: 23, 16: 5})\n"]}], "source": ["print(Counter(ch3.reshape(-1)))\n", "print(Counter(ch4.reshape(-1)))"]}, {"cell_type": "code", "execution_count": 195, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(4, 790)\n"]}], "source": ["print(ch4.shape)"]}, {"cell_type": "code", "execution_count": 220, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Counter({7: 403, 1: 250, 3: 219, 11: 133, 19: 78, 2: 64, 8: 58, 10: 48, 13: 41, 6: 32, 12: 30, 0: 29, 18: 28, 9: 24, 20: 23, 21: 20, 15: 13, 4: 12, 17: 11, 14: 3, 16: 1})\n", "Counter({4: 439, 3: 433, 5: 224, 1: 189, 7: 109, 17: 54, 2: 42, 6: 34, 11: 29, 13: 24, 15: 17, 12: 11, 10: 10, 14: 6, 18: 5, 16: 4, 21: 3, 0: 2, 19: 2, 8: 2, 20: 1})\n"]}], "source": ["ch44 = ch4.reshape(4,10,79)\n", "print(Counter(ch44[:,:,0:38].reshape(-1)))\n", "print(Counter(ch44[:,:,38:79].reshape(-1)))"]}, {"cell_type": "code", "execution_count": 132, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(790, 1, 13, 17)\n"]}], "source": ["SE_all_ac = np.array(SE_all_ac)\n", "print(SE_all.shape)\n", "SE_all_ac = np.average(SE_all_ac,axis=-1)\n", "SE_all_ac = SE_all_ac[:,:,0,:]"]}, {"cell_type": "code", "execution_count": 133, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1.         1.         0.93055556 0.93055556 0.93055556 0.93055556\n", " 0.93055556 0.93055556 0.93055556 0.93055556 0.83695652 0.83695652\n", " 0.83695652 0.83695652 0.83695652 0.83695652 0.83695652 0.83695652]\n"]}], "source": ["fix = 32*0.01+32*8*0.005\n", "a1 = fix+1+0.25*2\n", "a2 = fix+1+0.25*4\n", "a3 = fix+1+0.25*8\n", "TP_EE = np.array([a1,a1,\\\n", "                        a2,a2,a2,a2,\\\n", "                        a2,a2,a2,a2,\\\n", "                        a3,a3,a3,a3,\\\n", "                        a3,a3,a3,a3])\n", "TP_EE = (1/TP_EE*np.min(TP_EE)+1)/2\n", "print(TP_EE)\n", "ch1 = np.argmax(SE_all_ac,axis=0)\n", "SE_all_ac2 = SE_all_ac*TP_EE[:,np.newaxis,np.newaxis]\n", "ch2 = np.argmax(SE_all_ac2,axis=0)"]}, {"cell_type": "code", "execution_count": 137, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Counter({13: 6548, 17: 3489, 9: 210, 5: 23})\n"]}], "source": ["print(Counter(ch1.reshape(-1)))"]}, {"cell_type": "code", "execution_count": 143, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(18, 790, 13)\n", "(18, 790, 13)\n", "[ 5.42549019 10.25799076  6.05286481 11.05840948 11.77499042 12.30338722\n", "  5.96624024 10.84431604 11.24952392 11.53836522  6.37626027 11.67336994\n", " 12.50033407 13.11384487  6.18112138 11.33757239 12.11721969 12.74708956]\n", "[ 5.42549019 10.25799076  5.63252698 10.29046438 10.95728275 11.44898533\n", "  5.55191801 10.09123854 10.46830698 10.73708986  5.33665261  9.7701031\n", " 10.46223613 10.97571799  5.17332985  9.48905516 10.14158605 10.66875974]\n"]}], "source": ["print(SE_all_ac.shape)\n", "print(SE_all_ac2.shape)\n", "print(np.average(SE_all_ac,axis=(-1,-2)))\n", "print(np.average(SE_all_ac2,axis=(-1,-2)))"]}, {"cell_type": "code", "execution_count": 145, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Counter({13: 6548, 17: 3489, 9: 210, 5: 23})\n"]}], "source": ["print(Counter(ch1.reshape(-1)))"]}, {"cell_type": "code", "execution_count": 138, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Counter({5: 3485, 13: 2800, 9: 2256, 17: 1427, 1: 302})\n"]}], "source": ["print(Counter(ch2.reshape(-1)))"]}], "metadata": {"kernelspec": {"display_name": "setsumi", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.15"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}