{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os,sys\n", "sys.path.append(os.getcwd())\n", "sys.path.append(\"../../\")\n", "from setsumi_env.BM_functions import *\n", "from setsumi_env.system_functions import *\n", "from setsumi_env.process import *\n", "from setsumi_env.analyze import *\n", "from setsumi_env.utils import *\n", "from setsumi_env.setsumi_env import setsumi_env\n", "import numpy as np\n", "import pickle\n", "import re\n", "import pandas as pd\n", "from collections import Counter\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "\n", "from scipy import stats\n", "import copy\n", "\n", "import matplotlib.pyplot as plt\n", "import matplotlib.patches as mpatches\n", "from IPython.display import Image\n", "from matplotlib.gridspec import GridSpec\n", "import time\n", "from datetime import datetime"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["BMenv = setsumi_env(path=\"/data/BM_data/28GHZ/UMa_4000t_o/\",max_time=1000,max_length=1000,gNB_tti=2,seed=777) \n", "draw_tool = BMenv.draw\n", "sim_par = BMenv.sim_par"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["BMenv = setsumi_env(path=\"/data/BM_data/28GHZ/UMi_1000t_i/\",max_time=1000,max_length=1000,gNB_tti=2,seed=777) \n", "draw_tool = BMenv.draw\n", "sim_par = BMenv.sim_par"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["BMenv = setsumi_env(path=\"/data/BM_data/28GHZ/UMa_2000_i/\",max_time=200,max_length=200,gNB_tti=2,seed=777) \n", "draw_tool = BMenv.draw\n", "sim_par = BMenv.sim_par\n", "# BMenv.load_data()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(BMenv.sinr_min)\n", "print(BMenv.sinr_max_diff)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["BMenv.pos_store_data()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["save_rp_TP = copy.deepcopy(BMenv.gNB_TP)\n", "save_UE_CQI = copy.deepcopy(BMenv.UE_CQI)\n", "save_UE_TP = copy.deepcopy(BMenv.UE_TP)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["save2_rp_TP = copy.deepcopy(BMenv.gNB_TP)\n", "save2_UE_CQI = copy.deepcopy(BMenv.UE_CQI)\n", "save2_UE_TP = copy.deepcopy(BMenv.UE_TP)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["save_rp_TP = np.sum(save_rp_TP,axis=-1)\n", "save2_rp_TP = np.sum(save2_rp_TP,axis=-1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(save_rp_TP.shape)\n", "print(save2_rp_TP.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["av1 = np.average(save_rp_TP,axis=0)\n", "av2 = np.average(save2_rp_TP,axis=0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["av_worst = av1[:,:,0]\n", "av2_worst = av2[:,:,0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(np.argmax(av1[:,:,8],axis=-1))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(np.argmax(av2[:,:,8],axis=-1))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(np.average(save2_UE_CQI),np.average(save_UE_CQI))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(BMenv.report_TP.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["BMenv.sinr_min = 0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["time_diff = 200\n", "ntime_diff = int(1000/time_diff)\n", "\n", "for time_idx in range(0,ntime_diff):\n", "    UEs = np.arange(100)\n", "    times = np.arange(time_idx*time_diff,time_idx*time_diff+time_diff,1)\n", "    UEs_new, times_new = np.meshgrid(UEs, times)\n", "    UEs = UEs_new.reshape(-1)\n", "    times = times_new.reshape(-1)\n", "    action = BMenv.action_space[0]\n", "    V_ants = BMenv.V_ants\n", "    H_ants = BMenv.H_ants\n", "    actions = np.arange(len(BMenv.action_space))\n", "\n", "    tag_times = []\n", "    # nUE = 2000\n", "    # times = [180]*2000\n", "    # times = np.array(times)\n", "    mod_time = times%BMenv.max_time #sim_par.time代表数据总的个数\n", "    srs_idx = times//BMenv.gNB_tti\n", "\n", "\n", "    tag_times.append(time.time())\n", "\n", "    nUE = UEs.shape[0]\n", "\n", "    H = BMenv.H_freq[UEs,mod_time]\n", "    srs_choose = BMenv.srs_choose_all[UEs,srs_idx]\n", "\n", "    H_origin = BMenv.H_freq\n", "    subs = [[[i for i in range(BMenv.nSub)]]*BMenv.UE_port]*nUE\n", "    subs = np.array(subs).transpose(0,2,1)\n", "    ports = [[[i for i in range(BMenv.UE_port)]]*BMenv.nSub]*nUE\n", "    ports = np.array(ports)\n", "    ues = [[list(UEs)]*BMenv.nSub]*BMenv.UE_port\n", "    ues = np.array(ues).transpose(2,1,0)\n", "\n", "\n", "    tag_times.append(time.time())\n", "    H_srs = H_origin[ues,srs_choose,subs,ports]\n", "    H = H.astype(np.complex64)\n", "    H_srs = H_srs.astype(np.complex64)\n", "\n", "    for ac in actions:\n", "        action = BMenv.action_space[ac]\n", "        H_split = action[0]\n", "        V_split = action[1]\n", "        RI = action[2]\n", "        Port = action[3]\n", "        N1 = max(V_split,H_split)*int(Port/H_split/V_split/2)\n", "        N2 = min(V_split,H_split)\n", "        nBeam = int(Port/H_split/V_split/2)\n", "        O1 = (N1>1)*3+1\n", "        O2 = (N2>1)*3+1\n", "        assert(RI <= N1*N2*2)   \n", "        codebook_origin = PMI_class.get_pmi_codebook(N1,N2,O1,O2,1,RI)*np.sqrt(RI)\n", "        codebook_origin = codebook_origin.astype(np.complex64)\n", "        pattern = generate_beam_pattern(V_ants,H_ants,V_split,H_split)\n", "\n", "        tag_times.append(time.time())\n", "        Beam_idx = BMenv.Beam_idxs[srs_idx,UEs]\n", "        Beam_idx = Beam_idx[:,0:nBeam]\n", "        Beam = BMenv.Beams[Beam_idx]\n", "        Beam = Beam.transpose(0,2,1)\n", "\n", "        Ports_vector = pattern\n", "        Beam_Port = Beam*Ports_vector\n", "        FRF = np.kron(np.eye(2,dtype=np.complex64),Beam_Port)\n", "        FRF = FRF/np.sqrt(V_ants*H_ants*2)/np.sqrt(nBeam)*np.sqrt(Port)\n", "        FRF = np.expand_dims(FRF,axis=1)\n", "\n", "        #cal UE report\n", "        UE_H = H@FRF\n", "        UE_H = np.expand_dims(UE_H,(0,1,2,3))\n", "        codebook_re = codebook_origin.reshape(-1,Port,RI)\n", "        codebook = np.expand_dims(codebook_origin, axis=(4,5))\n", "\n", "        HP = np.matmul(UE_H,codebook)\n", "        Layer_power = np.conj(HP.transpose(0,1,2,3,4,5,7,6))@HP\n", "        HP_power = np.trace(np.abs(Layer_power),axis1=-1,axis2=-2)\n", "        # HP_power = np.sum(HP_power,axis = -1)\n", "\n", "        bs_shape = HP_power.shape[0:4]\n", "        HP_power = HP_power.reshape(-1,nUE,BMenv.nSub)\n", "        idx = np.argmax(HP_power,axis=0)\n", "        PMI = np.unravel_index(idx,bs_shape)\n", "        PMI = np.array(PMI)\n", "        PMI = PMI.transpose(1,2,0)\n", "\n", "        P = codebook_re[idx]\n", "        # max_power = HP_power[idx,np.arange(idx.shape[0])]\n", "        # lp = Layer_power[idx]\n", "        FBB = P\n", "        # FRF = dp.array(FRF)\n", "        # FBB = dp.array(FBB)\n", "        F = FRF@FBB\n", "\n", "        Layer_power = Layer_power.reshape(-1,nUE,BMenv.nSub,RI,RI)\n", "        # lp = Layer_power[idx,np.arange(idx.shape[0])]\n", "        lp = Layer_power[idx,ues[:,:,0],subs[:,:,0]]\n", "\n", "\n", "        BMenv.UE_PMI[times,UEs,ac,:,:] = PMI\n", "        for snr_idx in range(BMenv.nSNR):\n", "        # for snr_idx in [10]:\n", "            snr = BMenv.SNRs[snr_idx]\n", "            R = np.float32(np.power(10,(-snr/10)))\n", "            sinr = np.linalg.inv(lp/R+np.eye(RI,dtype=np.complex64))\n", "            sinr = np.real(np.diagonal(sinr,axis1 = -1,axis2 = -2))\n", "            sinr = 1./sinr -1 \n", "            sinr = np.clip(sinr,0.00001,1000000000)\n", "            sinr = 10*np.log10(sinr)\n", "\n", "            #redo sinr\n", "            sinr[sinr < BMenv.sinr_min] = -50\n", "            if sinr.shape[-1] > 1:\n", "                sinr[(sinr[:,:,0] - sinr[:,:,1] > BMenv.sinr_max_diff),1] = -50\n", "                sinr[(sinr[:,:,1] - sinr[:,:,0] > BMenv.sinr_max_diff),0] = -50\n", "            if sinr.shape[-1] > 3:\n", "                sinr[(sinr[:,:,2] - sinr[:,:,3] > BMenv.sinr_max_diff),3] = -50\n", "                sinr[(sinr[:,:,3] - sinr[:,:,2] > BMenv.sinr_max_diff),2] = -50\n", "            layer = np.sum(sinr > -40,axis = -1)\n", "            layer = np.clip(layer,1,4)\n", "            mi = sinr2mi(sinr)\n", "            mi[mi<0.0014] = 0 #min_mi = 0.0013\n", "            avg_mi = np.sum(mi,axis=-1)/layer\n", "            sinr_avg = mi2sinr(avg_mi)\n", "            mcs = np.searchsorted(sinr2mcs_table,sinr_avg)\n", "            CQI = sinr2cqi(sinr_avg)\n", "            TPs = BMenv.TP_buffer[mcs,layer-1]\n", "\n", "            BMenv.report_CQI[times,UEs,ac,snr_idx,:] = CQI\n", "            BMenv.UE_TP[times,UEs,ac,snr_idx,:] = TPs\n", "        print(f\"{datetime.now()} Time {time_idx} and action {ac} process UE OK\")\n", "\n", "\n", "        #cal gNB report\n", "        UE_H = H_srs@FRF\n", "        UE_H = np.expand_dims(UE_H,(0,1,2,3))\n", "        codebook_re = codebook_origin.reshape(-1,Port,RI)\n", "        codebook = np.expand_dims(codebook_origin, axis=(4,5))\n", "\n", "        HP = np.matmul(UE_H,codebook)\n", "        Layer_power = np.conj(HP.transpose(0,1,2,3,4,5,7,6))@HP\n", "        HP_power = np.trace(np.abs(Layer_power),axis1=-1,axis2=-2)\n", "        # HP_power = np.sum(HP_power,axis = -1)\n", "\n", "        bs_shape = HP_power.shape[0:4]\n", "        HP_power = HP_power.reshape(-1,nUE,BMenv.nSub)\n", "        idx = np.argmax(HP_power,axis=0)\n", "        PMI = np.unravel_index(idx,bs_shape)\n", "        PMI = np.array(PMI)\n", "        PMI = PMI.transpose(1,2,0)\n", "\n", "        P = codebook_re[idx]\n", "        FBB = P\n", "        F = FRF@FBB\n", "\n", "        Layer_power = Layer_power.reshape(-1,nUE,BMenv.nSub,RI,RI)\n", "        # lp = Layer_power[idx,np.arange(idx.shape[0])]\n", "        HF = H@F\n", "        HFH = np.conj(HF.transpose(0,1,3,2))@HF\n", "        lp = HFH\n", "        BMenv.gNB_PMI[times,UEs,ac,:,:] = PMI\n", "        for snr_idx in range(BMenv.nSNR):\n", "        # for snr_idx in [10]:\n", "            snr = BMenv.SNRs[snr_idx]\n", "            R = np.float32(np.power(10,(-snr/10)))\n", "            sinr = np.linalg.inv(lp/R+np.eye(RI,dtype=np.complex64))\n", "            sinr = np.real(np.diagonal(sinr,axis1 = -1,axis2 = -2))\n", "            sinr = 1./sinr -1 \n", "            sinr = np.clip(sinr,0.00001,1000000000)\n", "            sinr = 10*np.log10(sinr)\n", "\n", "            #redo sinr\n", "            sinr[sinr < BMenv.sinr_min] = -50\n", "            if sinr.shape[-1] > 1:\n", "                sinr[(sinr[:,:,0] - sinr[:,:,1] > BMenv.sinr_max_diff),1] = -50\n", "                sinr[(sinr[:,:,1] - sinr[:,:,0] > BMenv.sinr_max_diff),0] = -50\n", "            if sinr.shape[-1] > 3:\n", "                sinr[(sinr[:,:,2] - sinr[:,:,3] > BMenv.sinr_max_diff),3] = -50\n", "                sinr[(sinr[:,:,3] - sinr[:,:,2] > BMenv.sinr_max_diff),2] = -50\n", "            layer = np.sum(sinr > -40,axis = -1)\n", "            layer = np.clip(layer,1,4)\n", "            mi = sinr2mi(sinr)\n", "            mi[mi<0.0014] = 0 #min_mi = 0.0013\n", "            avg_mi = np.sum(mi,axis=-1)/layer\n", "            sinr_avg = mi2sinr(avg_mi)\n", "            mcs = np.searchsorted(sinr2mcs_table,sinr_avg)\n", "            CQI = sinr2cqi(sinr_avg)\n", "            TPs = BMenv.TP_buffer[mcs,layer-1]\n", "            \n", "            BMenv.gNB_TP[times,UEs,ac,snr_idx,:] = TPs\n", "\n", "        print(f\"{datetime.now()} Time {time_idx} and action {ac} process gNB OK\")\n", "    print(f\"{datetime.now()} Time {time_idx} process all actions OK\")\n", "    BMenv.store_data()\n", "    print(f\"{datetime.now()} Time {time_idx} store OK\")"]}], "metadata": {"kernelspec": {"display_name": "setsumi", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.15"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "f8a110c2d076917b81a0ccbfc6885a5d1bb94a5882c2e7e7ff56bc5bea07b6a0"}}}, "nbformat": 4, "nbformat_minor": 2}