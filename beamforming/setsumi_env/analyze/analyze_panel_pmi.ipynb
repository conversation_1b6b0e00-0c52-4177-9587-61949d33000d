{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import os,sys\n", "sys.path.append(os.getcwd())\n", "sys.path.append(\"../../\")\n", "from setsumi_env.BM_functions import *\n", "from setsumi_env.system_functions import *\n", "from setsumi_env.process import *\n", "from setsumi_env.analyze import *\n", "from setsumi_env.utils import *\n", "from setsumi_env.setsumi_env import setsumi_env\n", "import numpy as np\n", "import pickle\n", "import re\n", "import pandas as pd\n", "from collections import Counter\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "\n", "from scipy import stats\n", "import copy\n", "\n", "import matplotlib.pyplot as plt\n", "import matplotlib.patches as mpatches\n", "from IPython.display import Image\n", "from matplotlib.gridspec import GridSpec\n", "import time\n", "from datetime import datetime"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["setsumi load sim log OK\n", "load Beam data OK\n", "load report data OK\n"]}], "source": ["BMenv = setsumi_env(path=\"/data/BM_data/28GHZ/UMa_4000t_o/\",max_time=1000,max_length=1000,gNB_tti=2,seed=777) \n", "draw_tool = BMenv.draw\n", "sim_par = BMenv.sim_par\n", "BMenv.load_data()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "setsumi", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.15"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}