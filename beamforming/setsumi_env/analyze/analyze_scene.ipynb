{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os,sys\n", "sys.path.append(os.getcwd())\n", "sys.path.append(\"../../\")\n", "from setsumi_env.BM_functions import *\n", "from setsumi_env.system_functions import *\n", "from setsumi_env.process import *\n", "from setsumi_env.analyze import *\n", "from setsumi_env.utils import *\n", "from setsumi_env.setsumi_env import setsumi_env\n", "import numpy as np\n", "import pickle\n", "import re\n", "import pandas as pd\n", "from collections import Counter\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "\n", "from scipy import stats\n", "import copy\n", "\n", "import matplotlib.pyplot as plt\n", "import matplotlib.patches as mpatches\n", "from IPython.display import Image\n", "from matplotlib.gridspec import GridSpec\n", "import time\n", "from datetime import datetime"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["setsumi load sim log OK\n", "load Beam data OK\n", "load report data OK\n"]}], "source": ["BMenv = setsumi_env(path=\"/data/BM_data/28GHZ/sakura/UMa/\",max_time=400,max_length=400,gNB_tti=2,seed=1215) \n", "draw_tool = BMenv.draw\n", "sim_par = BMenv.sim_par\n", "BMenv.load_data()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["setsumi load sim log OK\n", "load Beam data OK\n", "load report data OK\n"]}], "source": ["BMenv1 = setsumi_env(path=\"/data/BM_data/28GHZ/sakura/UMi/\",max_time=400,max_length=400,gNB_tti=2,seed=1215) \n", "# draw_tool = BMenv.draw\n", "sim_par1 = BMenv1.sim_par\n", "BMenv1.load_data()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(400, 400, 30, 13)\n"]}], "source": ["se1 = np.average(BMenv.gNB_SE,axis=-1)\n", "print(se1.shape)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["diff1 = se1[:,:,5,:]/se1[:,:,9,:]"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["se2 = np.average(BMenv1.gNB_SE,axis=-1)\n", "diff2 = se2[:,:,5,:]/se2[:,:,9,:]"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.1804688 1.2053065\n"]}], "source": ["print(np.average(diff1[:,:,12]),np.average(diff2[:,:,12]))"]}], "metadata": {"kernelspec": {"display_name": "setsumi", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.15"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}