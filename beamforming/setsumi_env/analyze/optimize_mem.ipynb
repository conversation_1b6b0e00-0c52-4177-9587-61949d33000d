{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os,sys\n", "sys.path.append(os.getcwd())\n", "sys.path.append(\"../../\")\n", "from setsumi_env.BM_functions import *\n", "from setsumi_env.system_functions import *\n", "from setsumi_env.process import *\n", "from setsumi_env.analyze import *\n", "from setsumi_env.utils import *\n", "from setsumi_env.setsumi_env import setsumi_env\n", "import numpy as np\n", "import pickle\n", "import re\n", "import pandas as pd\n", "from collections import Counter\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "\n", "from scipy import stats\n", "import copy\n", "\n", "import matplotlib.pyplot as plt\n", "import matplotlib.patches as mpatches\n", "from IPython.display import Image\n", "from matplotlib.gridspec import GridSpec\n", "import time"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["with open(\"/data/BM_data/28GHZ/UMa_4000t_o/report.pkl\", 'rb') as fo: \n", "        gNB_PMI = pickle.load(fo, encoding='bytes')\n", "        report_TP = pickle.load(fo, encoding='bytes')\n", "        UE_PMI = pickle.load(fo, encoding='bytes')\n", "        UE_TP = pickle.load(fo, encoding='bytes')\n", "        UE_CQI = pickle.load(fo, encoding='bytes')"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["setsumi\n"]}], "source": ["if os.path.exists(\"/data/BM_data/28GHZ/UMa_4000t_o/report.pkl\"):\n", "    print(\"setsumi\")"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [], "source": ["TP_ac = np.sum(UE_TP,axis=-1)\n", "TP_ac = TP_ac.transpose(2,0,1,3)\n", "TP_ac = reduce_action(TP_ac,BMenv.action_map)\n", "best_RI = [np.argmax(tps,axis=0) for tps in TP_ac]\n", "\n", "best_idx = copy.deepcopy(best_RI)\n", "add_idx = 0\n", "for i in range(len(BMenv.action_map)):\n", "    name = list(BMenv.action_map.keys())[i]\n", "    idx = BMenv.action_map[name]\n", "    best_idx[i] = best_idx[i] + add_idx\n", "    add_idx += len(idx)\n", "        \n", "best_RI = np.array(expand_action(best_RI,BMenv.action_map)) \n", "best_RI = best_RI.transpose(1,2,0,3)\n", "\n", "best_idx = np.array(expand_action(best_idx,BMenv.action_map)) \n", "best_idx = best_idx.transpose(1,2,0,3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "def reduce_action(data,action_map):\n", "    panel_data = []\n", "    for i in range(len(action_map)):\n", "        name = list(action_map.keys())[i]\n", "        idx = action_map[name]\n", "        panel_data.append(data[idx])\n", "    return panel_data"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["action_map = BMenv.action_map"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [], "source": ["def expand_action(data,action_map):\n", "    result = []\n", "    for i in range(len(action_map)):\n", "        name = list(action_map.keys())[i]\n", "        idx = action_map[name]\n", "        for id in idx:\n", "            result.append(data[i])\n", "    return result"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [], "source": ["result = []\n", "for i in range(len(action_map)):\n", "    name = list(action_map.keys())[i]\n", "    idx = action_map[name]\n", "    for id in idx:\n", "        result.append(best_ac[i])"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1 1 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3]\n"]}], "source": ["print(result[:,2000,98,12])"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(18, 4000, 100, 13)\n"]}], "source": ["result = np.array(result)\n", "print(result.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(result)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def  expand"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(4000, 100, 5, 13, 17)\n"]}], "source": ["print(best_ac.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(best_ac.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["report = np.array(best_ac)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["int32\n"]}], "source": ["print(TP_ac[0].dtype)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(5, 4000, 100, 13, 17)\n"]}], "source": ["best_ac = np.array(best_ac)\n", "print(best_ac.shape)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["int64\n"]}], "source": ["print(best_ac.dtype)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(4000, 100, 18, 17, 4)\n"]}], "source": ["print(best_data.shape)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Load begin\n", "setsumi load sim log OK\n", "Load Finish\n", "simulate srs\n", "cal coma\n", "cal beam\n"]}, {"ename": "UnsupportedOperation", "evalue": "read", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mUnsupportedOperation\u001b[0m                      Traceback (most recent call last)", "\u001b[0;32m/tmp/ipykernel_2476929/2676780431.py\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0mBMenv\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0msetsumi_env\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mpath\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m\"/data/BM_data/28GHZ/UMa_4000t_o/\"\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mmax_length\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;36m4000\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mgNB_tti\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;36m2\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      2\u001b[0m \u001b[0mdraw_tool\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mBMenv\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdraw\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      3\u001b[0m \u001b[0msim_par\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mBMenv\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0msim_par\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/data/setsumi/beamforming/setsumi_env/setsumi_env.py\u001b[0m in \u001b[0;36m__init__\u001b[0;34m(self, logger, path, max_time, max_length, seed, enable_load, gNB_tti, UE_tti, Beam_tti)\u001b[0m\n\u001b[1;32m    143\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    144\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0mos\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mpath\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mexists\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mpath\u001b[0m\u001b[0;34m+\u001b[0m\u001b[0;34m\"report.pkl\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 145\u001b[0;31m             \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mload_data\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    146\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    147\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mnorm_H\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/data/setsumi/beamforming/setsumi_env/setsumi_env.py\u001b[0m in \u001b[0;36mload_data\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    311\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mload_data\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    312\u001b[0m         \u001b[0;32mwith\u001b[0m \u001b[0mopen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mpath\u001b[0m\u001b[0;34m+\u001b[0m\u001b[0;34m'report.pkl'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m'wb'\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;32mas\u001b[0m \u001b[0mfile\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 313\u001b[0;31m             \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mgNB_PMI\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mpickle\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mload\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mfile\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mencoding\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m'bytes'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    314\u001b[0m             \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mreport_TP\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mpickle\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mload\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mfile\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mencoding\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m'bytes'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    315\u001b[0m             \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mUE_PMI\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mpickle\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mload\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mfile\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mencoding\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m'bytes'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mUnsupportedOperation\u001b[0m: read"]}], "source": ["BMenv = setsumi_env(path=\"/data/BM_data/28GHZ/UMa_4000t_o/\",max_length=4000,gNB_tti=2) \n", "draw_tool = BMenv.draw\n", "sim_par = BMenv.sim_par"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["预先计算好beam pattern可以降低计算复杂度"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["UEs = np.arange(100)\n", "times = np.arange(0,10,1)\n", "UEs_new, times_new = np.meshgrid(UEs, times)\n", "UEs = UEs_new.reshape(-1)\n", "times = times_new.reshape(-1)\n", "action = BMenv.action_space[0]\n", "V_ants = BMenv.V_ants\n", "H_ants = BMenv.H_ants\n", "actions = np.arange(len(BMenv.action_space))"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(2, 100)\n"]}], "source": ["print(UEs_new.shape)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["tag_times = []\n", "# nUE = 2000\n", "# times = [180]*2000\n", "# times = np.array(times)\n", "mod_time = times%BMenv.max_time #sim_par.time代表数据总的个数\n", "srs_idx = times//BMenv.gNB_tti\n", "\n", "\n", "tag_times.append(time.time())\n", "\n", "nUE = UEs.shape[0]\n", "\n", "H = BMenv.H_freq[UEs,mod_time]\n", "srs_choose = BMenv.srs_choose_all[UEs,srs_idx]\n", "\n", "H_origin = BMenv.H_freq\n", "subs = [[[i for i in range(BMenv.nSub)]]*BMenv.UE_port]*nUE\n", "subs = np.array(subs).transpose(0,2,1)\n", "ports = [[[i for i in range(BMenv.UE_port)]]*BMenv.nSub]*nUE\n", "ports = np.array(ports)\n", "ues = [[list(UEs)]*BMenv.nSub]*BMenv.UE_port\n", "ues = np.array(ues).transpose(2,1,0)\n", "\n", "\n", "tag_times.append(time.time())\n", "H_srs = H_origin[ues,srs_choose,subs,ports]\n", "H = H.astype(np.complex64)\n", "H_srs = H_srs.astype(np.complex64)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(4000, 100, 18, 17, 4)\n"]}], "source": ["print(BMenv.gNB_PMI.shape)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["for ac in actions:\n", "    action = BMenv.action_space[ac]\n", "    H_split = action[0]\n", "    V_split = action[1]\n", "    RI = action[2]\n", "    Port = action[3]\n", "    N1 = max(V_split,H_split)*int(Port/H_split/V_split/2)\n", "    N2 = min(V_split,H_split)\n", "    nBeam = int(Port/H_split/V_split/2)\n", "    O1 = (N1>1)*3+1\n", "    O2 = (N2>1)*3+1\n", "    assert(RI <= N1*N2*2)   \n", "    codebook_origin = PMI_class.get_pmi_codebook(N1,N2,O1,O2,1,RI)*np.sqrt(RI)\n", "    codebook_origin = codebook_origin.astype(np.complex64)\n", "    pattern = generate_beam_pattern(V_ants,H_ants,V_split,H_split)\n", "\n", "    tag_times.append(time.time())\n", "    Beam_idx = BMenv.Beam_idxs[srs_idx,UEs]\n", "    Beam_idx = Beam_idx[:,0:nBeam]\n", "    Beam = BMenv.Beams[Beam_idx]\n", "    Beam = Beam.transpose(0,2,1)\n", "\n", "    Ports_vector = pattern\n", "    Beam_Port = Beam*Ports_vector\n", "    FRF = np.kron(np.eye(2,dtype=np.complex64),Beam_Port)\n", "    FRF = FRF/np.sqrt(V_ants*H_ants*2)/np.sqrt(nBeam)*np.sqrt(Port)\n", "    FRF = np.expand_dims(FRF,axis=1)\n", "\n", "    #cal UE report\n", "    UE_H = H@FRF\n", "    UE_H = np.expand_dims(UE_H,(0,1,2,3))\n", "    codebook_re = codebook_origin.reshape(-1,Port,RI)\n", "    codebook = np.expand_dims(codebook_origin, axis=(4,5))\n", "\n", "    HP = np.matmul(UE_H,codebook)\n", "    Layer_power = np.conj(HP.transpose(0,1,2,3,4,5,7,6))@HP\n", "    HP_power = np.trace(np.abs(Layer_power),axis1=-1,axis2=-2)\n", "    # HP_power = np.sum(HP_power,axis = -1)\n", "\n", "    bs_shape = HP_power.shape[0:4]\n", "    HP_power = HP_power.reshape(-1,nUE,BMenv.nSub)\n", "    idx = np.argmax(HP_power,axis=0)\n", "    PMI = np.unravel_index(idx,bs_shape)\n", "    PMI = np.array(PMI)\n", "    PMI = PMI.transpose(1,2,0)\n", "\n", "    P = codebook_re[idx]\n", "    # max_power = HP_power[idx,np.arange(idx.shape[0])]\n", "    # lp = Layer_power[idx]\n", "    FBB = P\n", "    # FRF = dp.array(FRF)\n", "    # FBB = dp.array(FBB)\n", "    F = FRF@FBB\n", "\n", "    Layer_power = Layer_power.reshape(-1,nUE,BMenv.nSub,RI,RI)\n", "    # lp = Layer_power[idx,np.arange(idx.shape[0])]\n", "    lp = Layer_power[idx,ues[:,:,0],subs[:,:,0]]\n", "\n", "\n", "    BMenv.UE_PMI[times,UEs,ac,:,:] = PMI\n", "    for snr_idx in range(BMenv.nSNR):\n", "        snr = BMenv.SNRs[snr_idx]\n", "        R = np.float32(np.power(10,(-snr/10)))\n", "        sinr = np.linalg.inv(lp/R+np.eye(RI,dtype=np.complex64))\n", "        sinr = np.real(np.diagonal(sinr,axis1 = -1,axis2 = -2))\n", "        sinr = 1./sinr -1 \n", "        sinr = np.clip(sinr,0.00001,1000000000)\n", "        sinr = 10*np.log10(sinr)\n", "\n", "        #redo sinr\n", "        sinr[sinr < BMenv.sinr_min] = -50\n", "        if sinr.shape[-1] > 1:\n", "            sinr[(sinr[:,:,0] - sinr[:,:,1] > BMenv.sinr_max_diff),1] = -50\n", "            sinr[(sinr[:,:,1] - sinr[:,:,0] > BMenv.sinr_max_diff),0] = -50\n", "        if sinr.shape[-1] > 3:\n", "            sinr[(sinr[:,:,2] - sinr[:,:,3] > BMenv.sinr_max_diff),3] = -50\n", "            sinr[(sinr[:,:,3] - sinr[:,:,2] > BMenv.sinr_max_diff),2] = -50\n", "        layer = np.sum(sinr > -40,axis = -1)\n", "        layer = np.clip(layer,1,4)\n", "        mi = sinr2mi(sinr)\n", "        mi[mi<0.0014] = 0 #min_mi = 0.0013\n", "        avg_mi = np.sum(mi,axis=-1)/layer\n", "        sinr_avg = mi2sinr(avg_mi)\n", "        mcs = np.searchsorted(sinr2mcs_table,sinr_avg)\n", "        CQI = sinr2cqi(sinr_avg)\n", "        TPs = BMenv.TP_buffer[mcs,layer-1]\n", "\n", "        BMenv.report_CQI[times,UEs,ac,snr_idx,:] = CQI\n", "        BMenv.UE_TP[times,UEs,ac,snr_idx,:] = TPs\n", "\n", "\n", "    #cal gNB report\n", "    UE_H = H_srs@FRF\n", "    UE_H = np.expand_dims(UE_H,(0,1,2,3))\n", "    codebook_re = codebook_origin.reshape(-1,Port,RI)\n", "    codebook = np.expand_dims(codebook_origin, axis=(4,5))\n", "\n", "    HP = np.matmul(UE_H,codebook)\n", "    Layer_power = np.conj(HP.transpose(0,1,2,3,4,5,7,6))@HP\n", "    HP_power = np.trace(np.abs(Layer_power),axis1=-1,axis2=-2)\n", "    # HP_power = np.sum(HP_power,axis = -1)\n", "\n", "    bs_shape = HP_power.shape[0:4]\n", "    HP_power = HP_power.reshape(-1,nUE,BMenv.nSub)\n", "    idx = np.argmax(HP_power,axis=0)\n", "    PMI = np.unravel_index(idx,bs_shape)\n", "    PMI = np.array(PMI)\n", "    PMI = PMI.transpose(1,2,0)\n", "\n", "    P = codebook_re[idx]\n", "    FBB = P\n", "    F = FRF@FBB\n", "\n", "    Layer_power = Layer_power.reshape(-1,nUE,BMenv.nSub,RI,RI)\n", "    # lp = Layer_power[idx,np.arange(idx.shape[0])]\n", "    HF = H@F\n", "    HFH = np.conj(HF.transpose(0,1,3,2))@HF\n", "    lp = HFH\n", "    BMenv.gNB_PMI[times,UEs,ac,:,:] = PMI\n", "    for snr_idx in range(BMenv.nSNR):\n", "        snr = BMenv.SNRs[snr_idx]\n", "        R = np.float32(np.power(10,(-snr/10)))\n", "        sinr = np.linalg.inv(lp/R+np.eye(RI,dtype=np.complex64))\n", "        sinr = np.real(np.diagonal(sinr,axis1 = -1,axis2 = -2))\n", "        sinr = 1./sinr -1 \n", "        sinr = np.clip(sinr,0.00001,1000000000)\n", "        sinr = 10*np.log10(sinr)\n", "\n", "        #redo sinr\n", "        sinr[sinr < BMenv.sinr_min] = -50\n", "        if sinr.shape[-1] > 1:\n", "            sinr[(sinr[:,:,0] - sinr[:,:,1] > BMenv.sinr_max_diff),1] = -50\n", "            sinr[(sinr[:,:,1] - sinr[:,:,0] > BMenv.sinr_max_diff),0] = -50\n", "        if sinr.shape[-1] > 3:\n", "            sinr[(sinr[:,:,2] - sinr[:,:,3] > BMenv.sinr_max_diff),3] = -50\n", "            sinr[(sinr[:,:,3] - sinr[:,:,2] > BMenv.sinr_max_diff),2] = -50\n", "        layer = np.sum(sinr > -40,axis = -1)\n", "        layer = np.clip(layer,1,4)\n", "        mi = sinr2mi(sinr)\n", "        mi[mi<0.0014] = 0 #min_mi = 0.0013\n", "        avg_mi = np.sum(mi,axis=-1)/layer\n", "        sinr_avg = mi2sinr(avg_mi)\n", "        mcs = np.searchsorted(sinr2mcs_table,sinr_avg)\n", "        CQI = sinr2cqi(sinr_avg)\n", "        TPs = BMenv.TP_buffer[mcs,layer-1]\n", "        \n", "        BMenv.gNB_TP[times,UEs,ac,snr_idx,:] = TPs"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-03-18 18:00:51.842540\n", "2023-03-18 18:00:51.843014 Time  and action  process UE OK\n"]}], "source": ["print(datetime.now())\n", "print(f\"{datetime.now()} Time  and action  process UE OK\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["51.2 \n", "优化一次后：\n", "44.9"]}], "metadata": {"kernelspec": {"display_name": "setsumi", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.15"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "f8a110c2d076917b81a0ccbfc6885a5d1bb94a5882c2e7e7ff56bc5bea07b6a0"}}}, "nbformat": 4, "nbformat_minor": 2}