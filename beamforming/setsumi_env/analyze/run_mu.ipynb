{"cells": [{"cell_type": "code", "execution_count": 1, "id": "38ee23ea", "metadata": {}, "outputs": [], "source": ["import os,sys\n", "sys.path.append(os.getcwd())\n", "sys.path.append(\"../../\")\n", "from setsumi_env.BM_functions import *\n", "from setsumi_env.system_functions import *\n", "from setsumi_env.process import *\n", "from setsumi_env.analyze import *\n", "from setsumi_env.utils import *\n", "from setsumi_env.setsumi_env import setsumi_env\n", "import numpy as np\n", "import pickle\n", "import re\n", "import pandas as pd\n", "from collections import Counter\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "\n", "from scipy import stats\n", "import copy\n", "\n", "import matplotlib.pyplot as plt\n", "import matplotlib.patches as mpatches\n", "from IPython.display import Image\n", "from matplotlib.gridspec import GridSpec\n", "import time\n", "from datetime import datetime"]}, {"cell_type": "code", "execution_count": 3, "id": "216f472a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["setsumi load sim log OK\n", "1000\n"]}], "source": ["Data_dir = \"/home/<USER>/quadriga/inf_v0/\"\n", "sim_par = Sim_Log(Data_dir) \n", "# get_result(sim_par)\n", "get_data(sim_par)\n", "nUE=sim_par.nUE\n", "print(nUE)"]}, {"cell_type": "code", "execution_count": 4, "id": "e4755b2a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(1000, 1, 17, 4, 64)\n", "(1000, 1)\n", "(1000, 1, 3)\n", "begin sim mu\n", "UE 0 simulate OK\n", "UE 1 simulate OK\n", "UE 2 simulate OK\n", "UE 3 simulate OK\n", "UE 4 simulate OK\n", "UE 5 simulate OK\n", "UE 6 simulate OK\n", "UE 7 simulate OK\n", "UE 8 simulate OK\n", "UE 9 simulate OK\n", "UE 10 simulate OK\n", "UE 11 simulate OK\n", "UE 12 simulate OK\n", "UE 13 simulate OK\n", "UE 14 simulate OK\n", "UE 15 simulate OK\n", "UE 16 simulate OK\n", "UE 17 simulate OK\n", "UE 18 simulate OK\n", "UE 19 simulate OK\n", "UE 20 simulate OK\n", "UE 21 simulate OK\n", "UE 22 simulate OK\n", "UE 23 simulate OK\n", "UE 24 simulate OK\n", "UE 25 simulate OK\n", "UE 26 simulate OK\n", "UE 27 simulate OK\n", "UE 28 simulate OK\n", "UE 29 simulate OK\n", "UE 30 simulate OK\n", "UE 31 simulate OK\n", "UE 32 simulate OK\n", "UE 33 simulate OK\n", "UE 34 simulate OK\n", "UE 35 simulate OK\n", "UE 36 simulate OK\n", "UE 37 simulate OK\n", "UE 38 simulate OK\n", "UE 39 simulate OK\n", "UE 40 simulate OK\n", "UE 41 simulate OK\n", "UE 42 simulate OK\n", "UE 43 simulate OK\n", "UE 44 simulate OK\n", "UE 45 simulate OK\n", "UE 46 simulate OK\n", "UE 47 simulate OK\n", "UE 48 simulate OK\n", "UE 49 simulate OK\n", "UE 50 simulate OK\n", "UE 51 simulate OK\n", "UE 52 simulate OK\n", "UE 53 simulate OK\n", "UE 54 simulate OK\n", "UE 55 simulate OK\n", "UE 56 simulate OK\n", "UE 57 simulate OK\n", "UE 58 simulate OK\n", "UE 59 simulate OK\n", "UE 60 simulate OK\n", "UE 61 simulate OK\n", "UE 62 simulate OK\n", "UE 63 simulate OK\n", "UE 64 simulate OK\n", "UE 65 simulate OK\n", "UE 66 simulate OK\n", "UE 67 simulate OK\n", "UE 68 simulate OK\n", "UE 69 simulate OK\n", "UE 70 simulate OK\n", "UE 71 simulate OK\n", "UE 72 simulate OK\n", "UE 73 simulate OK\n", "UE 74 simulate OK\n", "UE 75 simulate OK\n", "UE 76 simulate OK\n", "UE 77 simulate OK\n", "UE 78 simulate OK\n", "UE 79 simulate OK\n", "UE 80 simulate OK\n", "UE 81 simulate OK\n", "UE 82 simulate OK\n", "UE 83 simulate OK\n", "UE 84 simulate OK\n", "UE 85 simulate OK\n", "UE 86 simulate OK\n", "UE 87 simulate OK\n", "UE 88 simulate OK\n", "UE 89 simulate OK\n", "UE 90 simulate OK\n", "UE 91 simulate OK\n", "UE 92 simulate OK\n", "UE 93 simulate OK\n", "UE 94 simulate OK\n", "UE 95 simulate OK\n", "UE 96 simulate OK\n", "UE 97 simulate OK\n", "UE 98 simulate OK\n", "UE 99 simulate OK\n", "UE 100 simulate OK\n", "UE 101 simulate OK\n", "UE 102 simulate OK\n", "UE 103 simulate OK\n", "UE 104 simulate OK\n", "UE 105 simulate OK\n", "UE 106 simulate OK\n", "UE 107 simulate OK\n", "UE 108 simulate OK\n", "UE 109 simulate OK\n", "UE 110 simulate OK\n", "UE 111 simulate OK\n", "UE 112 simulate OK\n", "UE 113 simulate OK\n", "UE 114 simulate OK\n", "UE 115 simulate OK\n", "UE 116 simulate OK\n", "UE 117 simulate OK\n", "UE 118 simulate OK\n", "UE 119 simulate OK\n", "UE 120 simulate OK\n", "UE 121 simulate OK\n", "UE 122 simulate OK\n", "UE 123 simulate OK\n", "UE 124 simulate OK\n", "UE 125 simulate OK\n", "UE 126 simulate OK\n", "UE 127 simulate OK\n", "UE 128 simulate OK\n", "UE 129 simulate OK\n", "UE 130 simulate OK\n", "UE 131 simulate OK\n", "UE 132 simulate OK\n", "UE 133 simulate OK\n", "UE 134 simulate OK\n", "UE 135 simulate OK\n", "UE 136 simulate OK\n", "UE 137 simulate OK\n", "UE 138 simulate OK\n", "UE 139 simulate OK\n", "UE 140 simulate OK\n", "UE 141 simulate OK\n", "UE 142 simulate OK\n", "UE 143 simulate OK\n", "UE 144 simulate OK\n", "UE 145 simulate OK\n", "UE 146 simulate OK\n", "UE 147 simulate OK\n", "UE 148 simulate OK\n", "UE 149 simulate OK\n", "UE 150 simulate OK\n", "UE 151 simulate OK\n", "UE 152 simulate OK\n", "UE 153 simulate OK\n", "UE 154 simulate OK\n", "UE 155 simulate OK\n", "UE 156 simulate OK\n", "UE 157 simulate OK\n", "UE 158 simulate OK\n", "UE 159 simulate OK\n", "UE 160 simulate OK\n", "UE 161 simulate OK\n", "UE 162 simulate OK\n", "UE 163 simulate OK\n", "UE 164 simulate OK\n", "UE 165 simulate OK\n", "UE 166 simulate OK\n", "UE 167 simulate OK\n", "UE 168 simulate OK\n", "UE 169 simulate OK\n", "UE 170 simulate OK\n", "UE 171 simulate OK\n", "UE 172 simulate OK\n", "UE 173 simulate OK\n", "UE 174 simulate OK\n", "UE 175 simulate OK\n", "UE 176 simulate OK\n", "UE 177 simulate OK\n", "UE 178 simulate OK\n", "UE 179 simulate OK\n", "UE 180 simulate OK\n", "UE 181 simulate OK\n", "UE 182 simulate OK\n", "UE 183 simulate OK\n", "UE 184 simulate OK\n", "UE 185 simulate OK\n", "UE 186 simulate OK\n", "UE 187 simulate OK\n", "UE 188 simulate OK\n", "UE 189 simulate OK\n", "UE 190 simulate OK\n", "UE 191 simulate OK\n", "UE 192 simulate OK\n", "UE 193 simulate OK\n", "UE 194 simulate OK\n", "UE 195 simulate OK\n", "UE 196 simulate OK\n", "UE 197 simulate OK\n", "UE 198 simulate OK\n", "UE 199 simulate OK\n", "UE 200 simulate OK\n", "UE 201 simulate OK\n", "UE 202 simulate OK\n", "UE 203 simulate OK\n", "UE 204 simulate OK\n", "UE 205 simulate OK\n", "UE 206 simulate OK\n", "UE 207 simulate OK\n", "UE 208 simulate OK\n", "UE 209 simulate OK\n", "UE 210 simulate OK\n", "UE 211 simulate OK\n", "UE 212 simulate OK\n", "UE 213 simulate OK\n", "UE 214 simulate OK\n", "UE 215 simulate OK\n", "UE 216 simulate OK\n", "UE 217 simulate OK\n", "UE 218 simulate OK\n", "UE 219 simulate OK\n", "UE 220 simulate OK\n", "UE 221 simulate OK\n", "UE 222 simulate OK\n", "UE 223 simulate OK\n", "UE 224 simulate OK\n", "UE 225 simulate OK\n", "UE 226 simulate OK\n", "UE 227 simulate OK\n", "UE 228 simulate OK\n", "UE 229 simulate OK\n", "UE 230 simulate OK\n", "UE 231 simulate OK\n", "UE 232 simulate OK\n", "UE 233 simulate OK\n", "UE 234 simulate OK\n", "UE 235 simulate OK\n", "UE 236 simulate OK\n", "UE 237 simulate OK\n", "UE 238 simulate OK\n", "UE 239 simulate OK\n", "UE 240 simulate OK\n", "UE 241 simulate OK\n", "UE 242 simulate OK\n", "UE 243 simulate OK\n", "UE 244 simulate OK\n", "UE 245 simulate OK\n", "UE 246 simulate OK\n", "UE 247 simulate OK\n", "UE 248 simulate OK\n", "UE 249 simulate OK\n", "UE 250 simulate OK\n", "UE 251 simulate OK\n", "UE 252 simulate OK\n", "UE 253 simulate OK\n", "UE 254 simulate OK\n", "UE 255 simulate OK\n", "UE 256 simulate OK\n", "UE 257 simulate OK\n", "UE 258 simulate OK\n", "UE 259 simulate OK\n", "UE 260 simulate OK\n", "UE 261 simulate OK\n", "UE 262 simulate OK\n", "UE 263 simulate OK\n", "UE 264 simulate OK\n", "UE 265 simulate OK\n", "UE 266 simulate OK\n", "UE 267 simulate OK\n", "UE 268 simulate OK\n", "UE 269 simulate OK\n", "UE 270 simulate OK\n", "UE 271 simulate OK\n", "UE 272 simulate OK\n", "UE 273 simulate OK\n", "UE 274 simulate OK\n", "UE 275 simulate OK\n", "UE 276 simulate OK\n", "UE 277 simulate OK\n", "UE 278 simulate OK\n", "UE 279 simulate OK\n", "UE 280 simulate OK\n", "UE 281 simulate OK\n", "UE 282 simulate OK\n", "UE 283 simulate OK\n", "UE 284 simulate OK\n", "UE 285 simulate OK\n", "UE 286 simulate OK\n", "UE 287 simulate OK\n", "UE 288 simulate OK\n", "UE 289 simulate OK\n", "UE 290 simulate OK\n", "UE 291 simulate OK\n", "UE 292 simulate OK\n", "UE 293 simulate OK\n", "UE 294 simulate OK\n", "UE 295 simulate OK\n", "UE 296 simulate OK\n", "UE 297 simulate OK\n", "UE 298 simulate OK\n", "UE 299 simulate OK\n", "UE 300 simulate OK\n", "UE 301 simulate OK\n", "UE 302 simulate OK\n", "UE 303 simulate OK\n", "UE 304 simulate OK\n", "UE 305 simulate OK\n", "UE 306 simulate OK\n", "UE 307 simulate OK\n", "UE 308 simulate OK\n", "UE 309 simulate OK\n", "UE 310 simulate OK\n", "UE 311 simulate OK\n", "UE 312 simulate OK\n", "UE 313 simulate OK\n", "UE 314 simulate OK\n", "UE 315 simulate OK\n", "UE 316 simulate OK\n", "UE 317 simulate OK\n", "UE 318 simulate OK\n", "UE 319 simulate OK\n", "UE 320 simulate OK\n", "UE 321 simulate OK\n", "UE 322 simulate OK\n", "UE 323 simulate OK\n", "UE 324 simulate OK\n", "UE 325 simulate OK\n", "UE 326 simulate OK\n", "UE 327 simulate OK\n", "UE 328 simulate OK\n", "UE 329 simulate OK\n", "UE 330 simulate OK\n", "UE 331 simulate OK\n", "UE 332 simulate OK\n", "UE 333 simulate OK\n", "UE 334 simulate OK\n", "UE 335 simulate OK\n", "UE 336 simulate OK\n", "UE 337 simulate OK\n", "UE 338 simulate OK\n", "UE 339 simulate OK\n", "UE 340 simulate OK\n", "UE 341 simulate OK\n", "UE 342 simulate OK\n", "UE 343 simulate OK\n", "UE 344 simulate OK\n", "UE 345 simulate OK\n", "UE 346 simulate OK\n", "UE 347 simulate OK\n", "UE 348 simulate OK\n", "UE 349 simulate OK\n", "UE 350 simulate OK\n", "UE 351 simulate OK\n", "UE 352 simulate OK\n", "UE 353 simulate OK\n", "UE 354 simulate OK\n", "UE 355 simulate OK\n", "UE 356 simulate OK\n", "UE 357 simulate OK\n", "UE 358 simulate OK\n", "UE 359 simulate OK\n", "UE 360 simulate OK\n", "UE 361 simulate OK\n", "UE 362 simulate OK\n", "UE 363 simulate OK\n", "UE 364 simulate OK\n", "UE 365 simulate OK\n", "UE 366 simulate OK\n", "UE 367 simulate OK\n", "UE 368 simulate OK\n", "UE 369 simulate OK\n", "UE 370 simulate OK\n", "UE 371 simulate OK\n", "UE 372 simulate OK\n", "UE 373 simulate OK\n", "UE 374 simulate OK\n", "UE 375 simulate OK\n", "UE 376 simulate OK\n", "UE 377 simulate OK\n", "UE 378 simulate OK\n", "UE 379 simulate OK\n", "UE 380 simulate OK\n", "UE 381 simulate OK\n", "UE 382 simulate OK\n", "UE 383 simulate OK\n", "UE 384 simulate OK\n", "UE 385 simulate OK\n", "UE 386 simulate OK\n", "UE 387 simulate OK\n", "UE 388 simulate OK\n", "UE 389 simulate OK\n", "UE 390 simulate OK\n", "UE 391 simulate OK\n", "UE 392 simulate OK\n", "UE 393 simulate OK\n", "UE 394 simulate OK\n", "UE 395 simulate OK\n", "UE 396 simulate OK\n", "UE 397 simulate OK\n", "UE 398 simulate OK\n", "UE 399 simulate OK\n", "UE 400 simulate OK\n", "UE 401 simulate OK\n", "UE 402 simulate OK\n", "UE 403 simulate OK\n", "UE 404 simulate OK\n", "UE 405 simulate OK\n", "UE 406 simulate OK\n", "UE 407 simulate OK\n", "UE 408 simulate OK\n", "UE 409 simulate OK\n", "UE 410 simulate OK\n", "UE 411 simulate OK\n", "UE 412 simulate OK\n", "UE 413 simulate OK\n", "UE 414 simulate OK\n", "UE 415 simulate OK\n", "UE 416 simulate OK\n", "UE 417 simulate OK\n", "UE 418 simulate OK\n", "UE 419 simulate OK\n", "UE 420 simulate OK\n", "UE 421 simulate OK\n", "UE 422 simulate OK\n", "UE 423 simulate OK\n", "UE 424 simulate OK\n", "UE 425 simulate OK\n", "UE 426 simulate OK\n", "UE 427 simulate OK\n", "UE 428 simulate OK\n", "UE 429 simulate OK\n", "UE 430 simulate OK\n", "UE 431 simulate OK\n", "UE 432 simulate OK\n", "UE 433 simulate OK\n", "UE 434 simulate OK\n", "UE 435 simulate OK\n", "UE 436 simulate OK\n", "UE 437 simulate OK\n", "UE 438 simulate OK\n", "UE 439 simulate OK\n", "UE 440 simulate OK\n", "UE 441 simulate OK\n", "UE 442 simulate OK\n", "UE 443 simulate OK\n", "UE 444 simulate OK\n", "UE 445 simulate OK\n", "UE 446 simulate OK\n", "UE 447 simulate OK\n", "UE 448 simulate OK\n", "UE 449 simulate OK\n", "UE 450 simulate OK\n", "UE 451 simulate OK\n", "UE 452 simulate OK\n", "UE 453 simulate OK\n", "UE 454 simulate OK\n", "UE 455 simulate OK\n", "UE 456 simulate OK\n", "UE 457 simulate OK\n", "UE 458 simulate OK\n", "UE 459 simulate OK\n", "UE 460 simulate OK\n", "UE 461 simulate OK\n", "UE 462 simulate OK\n", "UE 463 simulate OK\n", "UE 464 simulate OK\n", "UE 465 simulate OK\n", "UE 466 simulate OK\n", "UE 467 simulate OK\n", "UE 468 simulate OK\n", "UE 469 simulate OK\n", "UE 470 simulate OK\n", "UE 471 simulate OK\n", "UE 472 simulate OK\n", "UE 473 simulate OK\n", "UE 474 simulate OK\n", "UE 475 simulate OK\n", "UE 476 simulate OK\n", "UE 477 simulate OK\n", "UE 478 simulate OK\n", "UE 479 simulate OK\n", "UE 480 simulate OK\n", "UE 481 simulate OK\n", "UE 482 simulate OK\n", "UE 483 simulate OK\n", "UE 484 simulate OK\n", "UE 485 simulate OK\n", "UE 486 simulate OK\n", "UE 487 simulate OK\n", "UE 488 simulate OK\n", "UE 489 simulate OK\n", "UE 490 simulate OK\n", "UE 491 simulate OK\n", "UE 492 simulate OK\n", "UE 493 simulate OK\n", "UE 494 simulate OK\n", "UE 495 simulate OK\n", "UE 496 simulate OK\n", "UE 497 simulate OK\n", "UE 498 simulate OK\n", "UE 499 simulate OK\n", "UE 500 simulate OK\n", "UE 501 simulate OK\n", "UE 502 simulate OK\n", "UE 503 simulate OK\n", "UE 504 simulate OK\n", "UE 505 simulate OK\n", "UE 506 simulate OK\n", "UE 507 simulate OK\n", "UE 508 simulate OK\n", "UE 509 simulate OK\n", "UE 510 simulate OK\n", "UE 511 simulate OK\n", "UE 512 simulate OK\n", "UE 513 simulate OK\n", "UE 514 simulate OK\n", "UE 515 simulate OK\n", "UE 516 simulate OK\n", "UE 517 simulate OK\n", "UE 518 simulate OK\n", "UE 519 simulate OK\n", "UE 520 simulate OK\n", "UE 521 simulate OK\n", "UE 522 simulate OK\n", "UE 523 simulate OK\n", "UE 524 simulate OK\n", "UE 525 simulate OK\n", "UE 526 simulate OK\n", "UE 527 simulate OK\n", "UE 528 simulate OK\n", "UE 529 simulate OK\n", "UE 530 simulate OK\n", "UE 531 simulate OK\n", "UE 532 simulate OK\n", "UE 533 simulate OK\n", "UE 534 simulate OK\n", "UE 535 simulate OK\n", "UE 536 simulate OK\n", "UE 537 simulate OK\n", "UE 538 simulate OK\n", "UE 539 simulate OK\n", "UE 540 simulate OK\n", "UE 541 simulate OK\n", "UE 542 simulate OK\n", "UE 543 simulate OK\n", "UE 544 simulate OK\n", "UE 545 simulate OK\n", "UE 546 simulate OK\n", "UE 547 simulate OK\n", "UE 548 simulate OK\n", "UE 549 simulate OK\n", "UE 550 simulate OK\n", "UE 551 simulate OK\n", "UE 552 simulate OK\n", "UE 553 simulate OK\n", "UE 554 simulate OK\n", "UE 555 simulate OK\n", "UE 556 simulate OK\n", "UE 557 simulate OK\n", "UE 558 simulate OK\n", "UE 559 simulate OK\n", "UE 560 simulate OK\n", "UE 561 simulate OK\n", "UE 562 simulate OK\n", "UE 563 simulate OK\n", "UE 564 simulate OK\n", "UE 565 simulate OK\n", "UE 566 simulate OK\n", "UE 567 simulate OK\n", "UE 568 simulate OK\n", "UE 569 simulate OK\n", "UE 570 simulate OK\n", "UE 571 simulate OK\n", "UE 572 simulate OK\n", "UE 573 simulate OK\n", "UE 574 simulate OK\n", "UE 575 simulate OK\n", "UE 576 simulate OK\n", "UE 577 simulate OK\n", "UE 578 simulate OK\n", "UE 579 simulate OK\n", "UE 580 simulate OK\n", "UE 581 simulate OK\n", "UE 582 simulate OK\n", "UE 583 simulate OK\n", "UE 584 simulate OK\n", "UE 585 simulate OK\n", "UE 586 simulate OK\n", "UE 587 simulate OK\n", "UE 588 simulate OK\n", "UE 589 simulate OK\n", "UE 590 simulate OK\n", "UE 591 simulate OK\n", "UE 592 simulate OK\n", "UE 593 simulate OK\n", "UE 594 simulate OK\n", "UE 595 simulate OK\n", "UE 596 simulate OK\n", "UE 597 simulate OK\n", "UE 598 simulate OK\n", "UE 599 simulate OK\n", "UE 600 simulate OK\n", "UE 601 simulate OK\n", "UE 602 simulate OK\n", "UE 603 simulate OK\n", "UE 604 simulate OK\n", "UE 605 simulate OK\n", "UE 606 simulate OK\n", "UE 607 simulate OK\n", "UE 608 simulate OK\n", "UE 609 simulate OK\n", "UE 610 simulate OK\n", "UE 611 simulate OK\n", "UE 612 simulate OK\n", "UE 613 simulate OK\n", "UE 614 simulate OK\n", "UE 615 simulate OK\n", "UE 616 simulate OK\n", "UE 617 simulate OK\n", "UE 618 simulate OK\n", "UE 619 simulate OK\n", "UE 620 simulate OK\n", "UE 621 simulate OK\n", "UE 622 simulate OK\n", "UE 623 simulate OK\n", "UE 624 simulate OK\n", "UE 625 simulate OK\n", "UE 626 simulate OK\n", "UE 627 simulate OK\n", "UE 628 simulate OK\n", "UE 629 simulate OK\n", "UE 630 simulate OK\n", "UE 631 simulate OK\n", "UE 632 simulate OK\n", "UE 633 simulate OK\n", "UE 634 simulate OK\n", "UE 635 simulate OK\n", "UE 636 simulate OK\n", "UE 637 simulate OK\n", "UE 638 simulate OK\n", "UE 639 simulate OK\n", "UE 640 simulate OK\n", "UE 641 simulate OK\n", "UE 642 simulate OK\n", "UE 643 simulate OK\n", "UE 644 simulate OK\n", "UE 645 simulate OK\n", "UE 646 simulate OK\n", "UE 647 simulate OK\n", "UE 648 simulate OK\n", "UE 649 simulate OK\n", "UE 650 simulate OK\n", "UE 651 simulate OK\n", "UE 652 simulate OK\n", "UE 653 simulate OK\n", "UE 654 simulate OK\n", "UE 655 simulate OK\n", "UE 656 simulate OK\n", "UE 657 simulate OK\n", "UE 658 simulate OK\n", "UE 659 simulate OK\n", "UE 660 simulate OK\n", "UE 661 simulate OK\n", "UE 662 simulate OK\n", "UE 663 simulate OK\n", "UE 664 simulate OK\n", "UE 665 simulate OK\n", "UE 666 simulate OK\n", "UE 667 simulate OK\n", "UE 668 simulate OK\n", "UE 669 simulate OK\n", "UE 670 simulate OK\n", "UE 671 simulate OK\n", "UE 672 simulate OK\n", "UE 673 simulate OK\n", "UE 674 simulate OK\n", "UE 675 simulate OK\n", "UE 676 simulate OK\n", "UE 677 simulate OK\n", "UE 678 simulate OK\n", "UE 679 simulate OK\n", "UE 680 simulate OK\n", "UE 681 simulate OK\n", "UE 682 simulate OK\n", "UE 683 simulate OK\n", "UE 684 simulate OK\n", "UE 685 simulate OK\n", "UE 686 simulate OK\n", "UE 687 simulate OK\n", "UE 688 simulate OK\n", "UE 689 simulate OK\n", "UE 690 simulate OK\n", "UE 691 simulate OK\n", "UE 692 simulate OK\n", "UE 693 simulate OK\n", "UE 694 simulate OK\n", "UE 695 simulate OK\n", "UE 696 simulate OK\n", "UE 697 simulate OK\n", "UE 698 simulate OK\n", "UE 699 simulate OK\n", "UE 700 simulate OK\n", "UE 701 simulate OK\n", "UE 702 simulate OK\n", "UE 703 simulate OK\n", "UE 704 simulate OK\n", "UE 705 simulate OK\n", "UE 706 simulate OK\n", "UE 707 simulate OK\n", "UE 708 simulate OK\n", "UE 709 simulate OK\n", "UE 710 simulate OK\n", "UE 711 simulate OK\n", "UE 712 simulate OK\n", "UE 713 simulate OK\n", "UE 714 simulate OK\n", "UE 715 simulate OK\n", "UE 716 simulate OK\n", "UE 717 simulate OK\n", "UE 718 simulate OK\n", "UE 719 simulate OK\n", "UE 720 simulate OK\n", "UE 721 simulate OK\n", "UE 722 simulate OK\n", "UE 723 simulate OK\n", "UE 724 simulate OK\n", "UE 725 simulate OK\n", "UE 726 simulate OK\n", "UE 727 simulate OK\n", "UE 728 simulate OK\n", "UE 729 simulate OK\n", "UE 730 simulate OK\n", "UE 731 simulate OK\n", "UE 732 simulate OK\n", "UE 733 simulate OK\n", "UE 734 simulate OK\n", "UE 735 simulate OK\n", "UE 736 simulate OK\n", "UE 737 simulate OK\n", "UE 738 simulate OK\n", "UE 739 simulate OK\n", "UE 740 simulate OK\n", "UE 741 simulate OK\n", "UE 742 simulate OK\n", "UE 743 simulate OK\n", "UE 744 simulate OK\n", "UE 745 simulate OK\n", "UE 746 simulate OK\n", "UE 747 simulate OK\n", "UE 748 simulate OK\n", "UE 749 simulate OK\n", "UE 750 simulate OK\n", "UE 751 simulate OK\n", "UE 752 simulate OK\n", "UE 753 simulate OK\n", "UE 754 simulate OK\n", "UE 755 simulate OK\n", "UE 756 simulate OK\n", "UE 757 simulate OK\n", "UE 758 simulate OK\n", "UE 759 simulate OK\n", "UE 760 simulate OK\n", "UE 761 simulate OK\n", "UE 762 simulate OK\n", "UE 763 simulate OK\n", "UE 764 simulate OK\n", "UE 765 simulate OK\n", "UE 766 simulate OK\n", "UE 767 simulate OK\n", "UE 768 simulate OK\n", "UE 769 simulate OK\n", "UE 770 simulate OK\n", "UE 771 simulate OK\n", "UE 772 simulate OK\n", "UE 773 simulate OK\n", "UE 774 simulate OK\n", "UE 775 simulate OK\n", "UE 776 simulate OK\n", "UE 777 simulate OK\n", "UE 778 simulate OK\n", "UE 779 simulate OK\n", "UE 780 simulate OK\n", "UE 781 simulate OK\n", "UE 782 simulate OK\n", "UE 783 simulate OK\n", "UE 784 simulate OK\n", "UE 785 simulate OK\n", "UE 786 simulate OK\n", "UE 787 simulate OK\n", "UE 788 simulate OK\n", "UE 789 simulate OK\n", "UE 790 simulate OK\n", "UE 791 simulate OK\n", "UE 792 simulate OK\n", "UE 793 simulate OK\n", "UE 794 simulate OK\n", "UE 795 simulate OK\n", "UE 796 simulate OK\n", "UE 797 simulate OK\n", "UE 798 simulate OK\n", "UE 799 simulate OK\n", "UE 800 simulate OK\n", "UE 801 simulate OK\n", "UE 802 simulate OK\n", "UE 803 simulate OK\n", "UE 804 simulate OK\n", "UE 805 simulate OK\n", "UE 806 simulate OK\n", "UE 807 simulate OK\n", "UE 808 simulate OK\n", "UE 809 simulate OK\n", "UE 810 simulate OK\n", "UE 811 simulate OK\n", "UE 812 simulate OK\n", "UE 813 simulate OK\n", "UE 814 simulate OK\n", "UE 815 simulate OK\n", "UE 816 simulate OK\n", "UE 817 simulate OK\n", "UE 818 simulate OK\n", "UE 819 simulate OK\n", "UE 820 simulate OK\n", "UE 821 simulate OK\n", "UE 822 simulate OK\n", "UE 823 simulate OK\n", "UE 824 simulate OK\n", "UE 825 simulate OK\n", "UE 826 simulate OK\n", "UE 827 simulate OK\n", "UE 828 simulate OK\n", "UE 829 simulate OK\n", "UE 830 simulate OK\n", "UE 831 simulate OK\n", "UE 832 simulate OK\n", "UE 833 simulate OK\n", "UE 834 simulate OK\n", "UE 835 simulate OK\n", "UE 836 simulate OK\n", "UE 837 simulate OK\n", "UE 838 simulate OK\n", "UE 839 simulate OK\n", "UE 840 simulate OK\n", "UE 841 simulate OK\n", "UE 842 simulate OK\n", "UE 843 simulate OK\n", "UE 844 simulate OK\n", "UE 845 simulate OK\n", "UE 846 simulate OK\n", "UE 847 simulate OK\n", "UE 848 simulate OK\n", "UE 849 simulate OK\n", "UE 850 simulate OK\n", "UE 851 simulate OK\n", "UE 852 simulate OK\n", "UE 853 simulate OK\n", "UE 854 simulate OK\n", "UE 855 simulate OK\n", "UE 856 simulate OK\n", "UE 857 simulate OK\n", "UE 858 simulate OK\n", "UE 859 simulate OK\n", "UE 860 simulate OK\n", "UE 861 simulate OK\n", "UE 862 simulate OK\n", "UE 863 simulate OK\n", "UE 864 simulate OK\n", "UE 865 simulate OK\n", "UE 866 simulate OK\n", "UE 867 simulate OK\n", "UE 868 simulate OK\n", "UE 869 simulate OK\n", "UE 870 simulate OK\n", "UE 871 simulate OK\n", "UE 872 simulate OK\n", "UE 873 simulate OK\n", "UE 874 simulate OK\n", "UE 875 simulate OK\n", "UE 876 simulate OK\n", "UE 877 simulate OK\n", "UE 878 simulate OK\n", "UE 879 simulate OK\n", "UE 880 simulate OK\n", "UE 881 simulate OK\n", "UE 882 simulate OK\n", "UE 883 simulate OK\n", "UE 884 simulate OK\n", "UE 885 simulate OK\n", "UE 886 simulate OK\n", "UE 887 simulate OK\n", "UE 888 simulate OK\n", "UE 889 simulate OK\n", "UE 890 simulate OK\n", "UE 891 simulate OK\n", "UE 892 simulate OK\n", "UE 893 simulate OK\n", "UE 894 simulate OK\n", "UE 895 simulate OK\n", "UE 896 simulate OK\n", "UE 897 simulate OK\n", "UE 898 simulate OK\n", "UE 899 simulate OK\n", "UE 900 simulate OK\n", "UE 901 simulate OK\n", "UE 902 simulate OK\n", "UE 903 simulate OK\n", "UE 904 simulate OK\n", "UE 905 simulate OK\n", "UE 906 simulate OK\n", "UE 907 simulate OK\n", "UE 908 simulate OK\n", "UE 909 simulate OK\n", "UE 910 simulate OK\n", "UE 911 simulate OK\n", "UE 912 simulate OK\n", "UE 913 simulate OK\n", "UE 914 simulate OK\n", "UE 915 simulate OK\n", "UE 916 simulate OK\n", "UE 917 simulate OK\n", "UE 918 simulate OK\n", "UE 919 simulate OK\n", "UE 920 simulate OK\n", "UE 921 simulate OK\n", "UE 922 simulate OK\n", "UE 923 simulate OK\n", "UE 924 simulate OK\n", "UE 925 simulate OK\n", "UE 926 simulate OK\n", "UE 927 simulate OK\n", "UE 928 simulate OK\n", "UE 929 simulate OK\n", "UE 930 simulate OK\n", "UE 931 simulate OK\n", "UE 932 simulate OK\n", "UE 933 simulate OK\n", "UE 934 simulate OK\n", "UE 935 simulate OK\n", "UE 936 simulate OK\n", "UE 937 simulate OK\n", "UE 938 simulate OK\n", "UE 939 simulate OK\n", "UE 940 simulate OK\n", "UE 941 simulate OK\n", "UE 942 simulate OK\n", "UE 943 simulate OK\n", "UE 944 simulate OK\n", "UE 945 simulate OK\n", "UE 946 simulate OK\n", "UE 947 simulate OK\n", "UE 948 simulate OK\n", "UE 949 simulate OK\n", "UE 950 simulate OK\n", "UE 951 simulate OK\n", "UE 952 simulate OK\n", "UE 953 simulate OK\n", "UE 954 simulate OK\n", "UE 955 simulate OK\n", "UE 956 simulate OK\n", "UE 957 simulate OK\n", "UE 958 simulate OK\n", "UE 959 simulate OK\n", "UE 960 simulate OK\n", "UE 961 simulate OK\n", "UE 962 simulate OK\n", "UE 963 simulate OK\n", "UE 964 simulate OK\n", "UE 965 simulate OK\n", "UE 966 simulate OK\n", "UE 967 simulate OK\n", "UE 968 simulate OK\n", "UE 969 simulate OK\n", "UE 970 simulate OK\n", "UE 971 simulate OK\n", "UE 972 simulate OK\n", "UE 973 simulate OK\n", "UE 974 simulate OK\n", "UE 975 simulate OK\n", "UE 976 simulate OK\n", "UE 977 simulate OK\n", "UE 978 simulate OK\n", "UE 979 simulate OK\n", "UE 980 simulate OK\n", "UE 981 simulate OK\n", "UE 982 simulate OK\n", "UE 983 simulate OK\n", "UE 984 simulate OK\n", "UE 985 simulate OK\n", "UE 986 simulate OK\n", "UE 987 simulate OK\n", "UE 988 simulate OK\n", "UE 989 simulate OK\n", "UE 990 simulate OK\n", "UE 991 simulate OK\n", "UE 992 simulate OK\n", "UE 993 simulate OK\n", "UE 994 simulate OK\n", "UE 995 simulate OK\n", "UE 996 simulate OK\n", "UE 997 simulate OK\n", "UE 998 simulate OK\n", "UE 999 simulate OK\n"]}], "source": ["\n", "# TPs = sim_par.result['TPs']\n", "loc = sim_par.data['loc']\n", "\n", "H_freq = np.array(sim_par.data['H_freq'])\n", "print(H_freq.shape)\n", "H_pg = np.array(sim_par.data['H_pg'])\n", "print(H_pg.shape)\n", "loc = np.array(sim_par.data['loc'])\n", "print(loc.shape)\n", "\n", "Nt = sim_par.Ants_all\n", "\n", "Nr = 2\n", "NRF = 4\n", "# result = sim_par.result\n", "# Beam = result['Beam']\n", "# Beam = Beam[0] #Beam的选择与PMI的选择与snr无关\n", "# PMIs = result['PMIs']\n", "# PMIs = PMIs[0] \n", "V_ants = sim_par.V_ants\n", "H_ants = sim_par.H_ants\n", "Ants_all = sim_par.Ants_all\n", "\n", "draw_tool = draw()\n", "Beams = generate_dft_beams(numVant=sim_par.V_ants,numHant=sim_par.H_ants)\n", "\n", "for i in range(0,sim_par.nUE):\n", "    pg = H_pg[i]\n", "    pg_data = np.sqrt(np.power(10,(pg/10)))\n", "    pg_data = np.expand_dims(pg_data,(1,2,3))\n", "    H_freq[i] = H_freq[i]/pg_data/np.sqrt(2)\n", "    \n", "use_seed = 723\n", "np.random.seed(use_seed)\n", "max_length = 4000\n", "srs_choose_all = []\n", "sub_choose_all = []\n", "port_choose_all = []\n", "for i in range(0,sim_par.nUE):\n", "    srs_choose,sub_choose,port_choose = simulate_srs(sim_par,max_length)\n", "    srs_choose_all.append(srs_choose)\n", "    sub_choose_all.append(sub_choose)\n", "    port_choose_all.append(port_choose)\n", "    \n", "SE_mu = []\n", "mus = [0,0.2,0.4,0.6,0.8,0.85,0.9,0.92,0.94,0.96,0.98,0.99]\n", "gNB_tti = 2\n", "\n", "print(\"begin sim mu\")\n", "\n", "for i in range(0,sim_par.nUE):\n", "    se_longs = []\n", "    H = H_freq[i]\n", "    for mu_idx in range(len(mus)):\n", "        mu = mus[mu_idx]\n", "        coma_ue = simulate_coma(H,sub_choose_all[i],port_choose_all[i],gNB_tti,mu)\n", "        se_long = cal_long_se(H,coma_ue,beam_tti=gNB_tti,time=sim_par.time,gNB_tti=gNB_tti)\n", "        se_longs.append(se_long)\n", "    print(\"UE \"+str(i)+\" simulate OK\")\n", "    SE_mu.append(se_longs)\n", "    np.savez(\"se_mu\",SE_mu)"]}], "metadata": {"kernelspec": {"display_name": "meiziy", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 5}