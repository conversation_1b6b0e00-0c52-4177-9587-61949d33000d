{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os,sys\n", "sys.path.append(os.getcwd())\n", "sys.path.append(\"../../\")\n", "from setsumi_env.BM_functions import *\n", "from setsumi_env.system_functions import *\n", "from setsumi_env.process import *\n", "from setsumi_env.analyze import *\n", "from setsumi_env.utils import *\n", "from setsumi_env.setsumi_env import setsumi_env\n", "import numpy as np\n", "import pickle\n", "import re\n", "import pandas as pd\n", "from collections import Counter\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "\n", "from scipy import stats\n", "import copy\n", "\n", "import matplotlib.pyplot as plt\n", "import matplotlib.patches as mpatches\n", "from IPython.display import Image\n", "from matplotlib.gridspec import GridSpec\n", "import time\n", "from datetime import datetime"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["ues1 = [2, 26, 33, 109, 268, 304, 328, 374, 466, 535, 592, 599, 676, 779, 844, 947, 1011, 1022, 1061, 1130, 1175, 1183, 1292, 1307, 1359, 1380, 1419, 1440, 1447, 1504, 1511, 1512, 1569, 1647, 1658, 1779, 1798, 1897]\n", "ues2 = [99, 102, 174, 181, 275, 303, 312, 319, 333, 336, 363, 430, 589, 605, 641, 731, 752, 792, 871, 892, 976, 982, 1080, 1092, 1198, 1267, 1397, 1423, 1517, 1534, 1543, 1580, 1586, 1682, 1695, 1769, 1807, 1865, 1889, 1916, 1952]\n", "\n", "ues_98 = [2, 26, 33, 81, 94, 103, 104, 109, 145, 148, 234, 243, 247, 248, 265, 268, 288, 304, 322, 328, 374, 410, 416, 442, 466, 477, 501, 504, 520, 535, 592, 599, 645, 668, 676, 701, 730, 744, 753, 774, 779, 811, 819, 838, 844, 847, 849, 858, 874, 933, 947, 958, 967, 981, 1003, 1011, 1012, 1013, 1022, 1044, 1061, 1085, 1097, 1100, 1130, 1136, 1175, 1183, 1184, 1208, 1213, 1217, 1252, 1292, 1307, 1353, 1359, 1380, 1399, 1417, 1419, 1432, 1440, 1447, 1461, 1483, 1504, 1511, 1512, 1538, 1569, 1574, 1601, 1623, 1647, 1658, 1733, 1743, 1779, 1798, 1833, 1893, 1897, 1909, 1925, 1931, 1967, 1972, 1986, 1997]\n", "ues_120 = [0, 1, 7, 9, 31, 48, 54, 66, 67, 82, 85, 86, 96, 99, 102, 143, 156, 162, 164, 174, 181, 183, 185, 186, 197, 223, 236, 237, 242, 250, 260, 275, 280, 286, 303, 306, 312, 315, 319, 323, 333, 336, 348, 363, 391, 395, 402, 413, 419, 430, 437, 460, 487, 488, 494, 509, 515, 525, 532, 536, 539, 564, 576, 589, 593, 595, 605, 625, 640, 641, 663, 681, 683, 684, 696, 714, 731, 749, 752, 769, 790, 791, 792, 799, 826, 829, 832, 842, 850, 869, 871, 872, 887, 892, 900, 902, 907, 919, 926, 949, 951, 976, 982, 1018, 1026, 1051, 1080, 1090, 1092, 1095, 1099, 1106, 1108, 1119, 1169, 1170, 1171, 1177, 1178, 1182, 1194, 1198, 1204, 1212, 1224, 1236, 1250, 1267, 1284, 1287, 1293, 1304, 1315, 1316, 1328, 1329, 1364, 1378, 1386, 1397, 1413, 1415, 1423, 1424, 1428, 1477, 1507, 1517, 1518, 1519, 1534, 1543, 1550, 1558, 1572, 1578, 1580, 1582, 1586, 1591, 1629, 1631, 1645, 1662, 1673, 1679, 1682, 1687, 1694, 1695, 1732, 1739, 1740, 1741, 1744, 1747, 1754, 1766, 1769, 1770, 1773, 1778, 1785, 1792, 1795, 1800, 1806, 1807, 1813, 1826, 1832, 1845, 1848, 1849, 1862, 1865, 1871, 1885, 1889, 1898, 1900, 1907, 1911, 1916, 1920, 1922, 1928, 1952, 1953, 1957, 1977, 1979, 1990]\n", "ue_list1 = list(np.sort(ues1+ues2))\n", "ue_list2 = list(np.sort(ues_98+ues_120))\n", "ue_list =  [item for item in ue_list2 if item not in ue_list1]"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["ue_list =  [item for item in range(2000) if item not in ue_list2]"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[3, 4, 5, 6, 8, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 27, 28, 29, 30, 32, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52, 53, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 83, 84, 87, 88, 89, 90, 91, 92, 93, 95, 97, 98, 100, 101, 105, 106, 107, 108, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 144, 146, 147, 149, 150, 151, 152, 153, 154, 155, 157, 158, 159, 160, 161, 163, 165, 166, 167, 168, 169, 170, 171, 172, 173, 175, 176, 177, 178, 179, 180, 182, 184, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 235, 238, 239, 240, 241, 244, 245, 246, 249, 251, 252, 253, 254, 255, 256, 257, 258, 259, 261, 262, 263, 264, 266, 267, 269, 270, 271, 272, 273, 274, 276, 277, 278, 279, 281, 282, 283, 284, 285, 287, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 305, 307, 308, 309, 310, 311, 313, 314, 316, 317, 318, 320, 321, 324, 325, 326, 327, 329, 330, 331, 332, 334, 335, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 392, 393, 394, 396, 397, 398, 399, 400, 401, 403, 404, 405, 406, 407, 408, 409, 411, 412, 414, 415, 417, 418, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 431, 432, 433, 434, 435, 436, 438, 439, 440, 441, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 461, 462, 463, 464, 465, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 478, 479, 480, 481, 482, 483, 484, 485, 486, 489, 490, 491, 492, 493, 495, 496, 497, 498, 499, 500, 502, 503, 505, 506, 507, 508, 510, 511, 512, 513, 514, 516, 517, 518, 519, 521, 522, 523, 524, 526, 527, 528, 529, 530, 531, 533, 534, 537, 538, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 551, 552, 553, 554, 555, 556, 557, 558, 559, 560, 561, 562, 563, 565, 566, 567, 568, 569, 570, 571, 572, 573, 574, 575, 577, 578, 579, 580, 581, 582, 583, 584, 585, 586, 587, 588, 590, 591, 594, 596, 597, 598, 600, 601, 602, 603, 604, 606, 607, 608, 609, 610, 611, 612, 613, 614, 615, 616, 617, 618, 619, 620, 621, 622, 623, 624, 626, 627, 628, 629, 630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 642, 643, 644, 646, 647, 648, 649, 650, 651, 652, 653, 654, 655, 656, 657, 658, 659, 660, 661, 662, 664, 665, 666, 667, 669, 670, 671, 672, 673, 674, 675, 677, 678, 679, 680, 682, 685, 686, 687, 688, 689, 690, 691, 692, 693, 694, 695, 697, 698, 699, 700, 702, 703, 704, 705, 706, 707, 708, 709, 710, 711, 712, 713, 715, 716, 717, 718, 719, 720, 721, 722, 723, 724, 725, 726, 727, 728, 729, 732, 733, 734, 735, 736, 737, 738, 739, 740, 741, 742, 743, 745, 746, 747, 748, 750, 751, 754, 755, 756, 757, 758, 759, 760, 761, 762, 763, 764, 765, 766, 767, 768, 770, 771, 772, 773, 775, 776, 777, 778, 780, 781, 782, 783, 784, 785, 786, 787, 788, 789, 793, 794, 795, 796, 797, 798, 800, 801, 802, 803, 804, 805, 806, 807, 808, 809, 810, 812, 813, 814, 815, 816, 817, 818, 820, 821, 822, 823, 824, 825, 827, 828, 830, 831, 833, 834, 835, 836, 837, 839, 840, 841, 843, 845, 846, 848, 851, 852, 853, 854, 855, 856, 857, 859, 860, 861, 862, 863, 864, 865, 866, 867, 868, 870, 873, 875, 876, 877, 878, 879, 880, 881, 882, 883, 884, 885, 886, 888, 889, 890, 891, 893, 894, 895, 896, 897, 898, 899, 901, 903, 904, 905, 906, 908, 909, 910, 911, 912, 913, 914, 915, 916, 917, 918, 920, 921, 922, 923, 924, 925, 927, 928, 929, 930, 931, 932, 934, 935, 936, 937, 938, 939, 940, 941, 942, 943, 944, 945, 946, 948, 950, 952, 953, 954, 955, 956, 957, 959, 960, 961, 962, 963, 964, 965, 966, 968, 969, 970, 971, 972, 973, 974, 975, 977, 978, 979, 980, 983, 984, 985, 986, 987, 988, 989, 990, 991, 992, 993, 994, 995, 996, 997, 998, 999, 1000, 1001, 1002, 1004, 1005, 1006, 1007, 1008, 1009, 1010, 1014, 1015, 1016, 1017, 1019, 1020, 1021, 1023, 1024, 1025, 1027, 1028, 1029, 1030, 1031, 1032, 1033, 1034, 1035, 1036, 1037, 1038, 1039, 1040, 1041, 1042, 1043, 1045, 1046, 1047, 1048, 1049, 1050, 1052, 1053, 1054, 1055, 1056, 1057, 1058, 1059, 1060, 1062, 1063, 1064, 1065, 1066, 1067, 1068, 1069, 1070, 1071, 1072, 1073, 1074, 1075, 1076, 1077, 1078, 1079, 1081, 1082, 1083, 1084, 1086, 1087, 1088, 1089, 1091, 1093, 1094, 1096, 1098, 1101, 1102, 1103, 1104, 1105, 1107, 1109, 1110, 1111, 1112, 1113, 1114, 1115, 1116, 1117, 1118, 1120, 1121, 1122, 1123, 1124, 1125, 1126, 1127, 1128, 1129, 1131, 1132, 1133, 1134, 1135, 1137, 1138, 1139, 1140, 1141, 1142, 1143, 1144, 1145, 1146, 1147, 1148, 1149, 1150, 1151, 1152, 1153, 1154, 1155, 1156, 1157, 1158, 1159, 1160, 1161, 1162, 1163, 1164, 1165, 1166, 1167, 1168, 1172, 1173, 1174, 1176, 1179, 1180, 1181, 1185, 1186, 1187, 1188, 1189, 1190, 1191, 1192, 1193, 1195, 1196, 1197, 1199, 1200, 1201, 1202, 1203, 1205, 1206, 1207, 1209, 1210, 1211, 1214, 1215, 1216, 1218, 1219, 1220, 1221, 1222, 1223, 1225, 1226, 1227, 1228, 1229, 1230, 1231, 1232, 1233, 1234, 1235, 1237, 1238, 1239, 1240, 1241, 1242, 1243, 1244, 1245, 1246, 1247, 1248, 1249, 1251, 1253, 1254, 1255, 1256, 1257, 1258, 1259, 1260, 1261, 1262, 1263, 1264, 1265, 1266, 1268, 1269, 1270, 1271, 1272, 1273, 1274, 1275, 1276, 1277, 1278, 1279, 1280, 1281, 1282, 1283, 1285, 1286, 1288, 1289, 1290, 1291, 1294, 1295, 1296, 1297, 1298, 1299, 1300, 1301, 1302, 1303, 1305, 1306, 1308, 1309, 1310, 1311, 1312, 1313, 1314, 1317, 1318, 1319, 1320, 1321, 1322, 1323, 1324, 1325, 1326, 1327, 1330, 1331, 1332, 1333, 1334, 1335, 1336, 1337, 1338, 1339, 1340, 1341, 1342, 1343, 1344, 1345, 1346, 1347, 1348, 1349, 1350, 1351, 1352, 1354, 1355, 1356, 1357, 1358, 1360, 1361, 1362, 1363, 1365, 1366, 1367, 1368, 1369, 1370, 1371, 1372, 1373, 1374, 1375, 1376, 1377, 1379, 1381, 1382, 1383, 1384, 1385, 1387, 1388, 1389, 1390, 1391, 1392, 1393, 1394, 1395, 1396, 1398, 1400, 1401, 1402, 1403, 1404, 1405, 1406, 1407, 1408, 1409, 1410, 1411, 1412, 1414, 1416, 1418, 1420, 1421, 1422, 1425, 1426, 1427, 1429, 1430, 1431, 1433, 1434, 1435, 1436, 1437, 1438, 1439, 1441, 1442, 1443, 1444, 1445, 1446, 1448, 1449, 1450, 1451, 1452, 1453, 1454, 1455, 1456, 1457, 1458, 1459, 1460, 1462, 1463, 1464, 1465, 1466, 1467, 1468, 1469, 1470, 1471, 1472, 1473, 1474, 1475, 1476, 1478, 1479, 1480, 1481, 1482, 1484, 1485, 1486, 1487, 1488, 1489, 1490, 1491, 1492, 1493, 1494, 1495, 1496, 1497, 1498, 1499, 1500, 1501, 1502, 1503, 1505, 1506, 1508, 1509, 1510, 1513, 1514, 1515, 1516, 1520, 1521, 1522, 1523, 1524, 1525, 1526, 1527, 1528, 1529, 1530, 1531, 1532, 1533, 1535, 1536, 1537, 1539, 1540, 1541, 1542, 1544, 1545, 1546, 1547, 1548, 1549, 1551, 1552, 1553, 1554, 1555, 1556, 1557, 1559, 1560, 1561, 1562, 1563, 1564, 1565, 1566, 1567, 1568, 1570, 1571, 1573, 1575, 1576, 1577, 1579, 1581, 1583, 1584, 1585, 1587, 1588, 1589, 1590, 1592, 1593, 1594, 1595, 1596, 1597, 1598, 1599, 1600, 1602, 1603, 1604, 1605, 1606, 1607, 1608, 1609, 1610, 1611, 1612, 1613, 1614, 1615, 1616, 1617, 1618, 1619, 1620, 1621, 1622, 1624, 1625, 1626, 1627, 1628, 1630, 1632, 1633, 1634, 1635, 1636, 1637, 1638, 1639, 1640, 1641, 1642, 1643, 1644, 1646, 1648, 1649, 1650, 1651, 1652, 1653, 1654, 1655, 1656, 1657, 1659, 1660, 1661, 1663, 1664, 1665, 1666, 1667, 1668, 1669, 1670, 1671, 1672, 1674, 1675, 1676, 1677, 1678, 1680, 1681, 1683, 1684, 1685, 1686, 1688, 1689, 1690, 1691, 1692, 1693, 1696, 1697, 1698, 1699, 1700, 1701, 1702, 1703, 1704, 1705, 1706, 1707, 1708, 1709, 1710, 1711, 1712, 1713, 1714, 1715, 1716, 1717, 1718, 1719, 1720, 1721, 1722, 1723, 1724, 1725, 1726, 1727, 1728, 1729, 1730, 1731, 1734, 1735, 1736, 1737, 1738, 1742, 1745, 1746, 1748, 1749, 1750, 1751, 1752, 1753, 1755, 1756, 1757, 1758, 1759, 1760, 1761, 1762, 1763, 1764, 1765, 1767, 1768, 1771, 1772, 1774, 1775, 1776, 1777, 1780, 1781, 1782, 1783, 1784, 1786, 1787, 1788, 1789, 1790, 1791, 1793, 1794, 1796, 1797, 1799, 1801, 1802, 1803, 1804, 1805, 1808, 1809, 1810, 1811, 1812, 1814, 1815, 1816, 1817, 1818, 1819, 1820, 1821, 1822, 1823, 1824, 1825, 1827, 1828, 1829, 1830, 1831, 1834, 1835, 1836, 1837, 1838, 1839, 1840, 1841, 1842, 1843, 1844, 1846, 1847, 1850, 1851, 1852, 1853, 1854, 1855, 1856, 1857, 1858, 1859, 1860, 1861, 1863, 1864, 1866, 1867, 1868, 1869, 1870, 1872, 1873, 1874, 1875, 1876, 1877, 1878, 1879, 1880, 1881, 1882, 1883, 1884, 1886, 1887, 1888, 1890, 1891, 1892, 1894, 1895, 1896, 1899, 1901, 1902, 1903, 1904, 1905, 1906, 1908, 1910, 1912, 1913, 1914, 1915, 1917, 1918, 1919, 1921, 1923, 1924, 1926, 1927, 1929, 1930, 1932, 1933, 1934, 1935, 1936, 1937, 1938, 1939, 1940, 1941, 1942, 1943, 1944, 1945, 1946, 1947, 1948, 1949, 1950, 1951, 1954, 1955, 1956, 1958, 1959, 1960, 1961, 1962, 1963, 1964, 1965, 1966, 1968, 1969, 1970, 1971, 1973, 1974, 1975, 1976, 1978, 1980, 1981, 1982, 1983, 1984, 1985, 1987, 1988, 1989, 1991, 1992, 1993, 1994, 1995, 1996, 1998, 1999]\n"]}], "source": ["print(ue_list)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["ue_list = np.sort(ues_98+ues_120)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["setsumi load sim log OK\n", "load Beam data OK\n", "Load begin\n", "Load Finish\n", "simulate srs\n"]}], "source": ["BMenv = setsumi_env(path=\"/data/BM_data/28GHZ/UMa_2000_o/\",max_time=200,max_length=200,gNB_tti=2,seed=777,enable_load=True) \n", "draw_tool = BMenv.draw\n", "sim_par = BMenv.sim_par\n", "BMenv.load_H()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-04-02 17:16:35.641796 Time 0 process UE OK\n", "2023-04-02 17:18:42.963072 Time 1 process UE OK\n", "2023-04-02 17:20:34.235392 Time 2 process UE OK\n", "2023-04-02 17:22:26.091938 Time 3 process UE OK\n", "2023-04-02 17:24:17.423679 Time 4 process UE OK\n", "2023-04-02 17:26:09.718331 Time 5 process UE OK\n", "2023-04-02 17:28:02.051945 Time 6 process UE OK\n", "2023-04-02 17:29:55.016528 Time 7 process UE OK\n", "2023-04-02 17:31:49.232899 Time 8 process UE OK\n", "2023-04-02 17:33:41.730535 Time 9 process UE OK\n"]}], "source": ["ues1 = [2, 26, 33, 109, 268, 304, 328, 374, 466, 535, 592, 599, 676, 779, 844, 947, 1011, 1022, 1061, 1130, 1175, 1183, 1292, 1307, 1359, 1380, 1419, 1440, 1447, 1504, 1511, 1512, 1569, 1647, 1658, 1779, 1798, 1897]\n", "ues2 = [99, 102, 174, 181, 275, 303, 312, 319, 333, 336, 363, 430, 589, 605, 641, 731, 752, 792, 871, 892, 976, 982, 1080, 1092, 1198, 1267, 1397, 1423, 1517, 1534, 1543, 1580, 1586, 1682, 1695, 1769, 1807, 1865, 1889, 1916, 1952]\n", "\n", "UEs = np.sort(ues1+ues2)\n", "time_diff = 20\n", "ntime_diff = int(200/time_diff)\n", "actions = np.arange(len(BMenv.action_space))\n", "\n", "for time_idx in range(0,ntime_diff):\n", "    times = np.arange(time_idx*time_diff,time_idx*time_diff+time_diff,1)\n", "    BMenv.run_batch_result(UEs,times,actions)\n", "    print(f\"{datetime.now()} Time {time_idx} process UE OK\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["BMenv.run_batch_result(np.array([0]),np.array([0]),np.array([0]))"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["UE_SE_ac = np.sum(BMenv.UE_SE,axis=-1)\n", "# TP_ac_redo = TP_ac*BMenv.TP_EE[np.newaxis,np.newaxis,:,np.newaxis]\n", "BMenv.best_SE = np.max(UE_SE_ac,axis=2)\n", "UE_SE_ac = UE_SE_ac.transpose(2,0,1,3)\n", "UE_SE_ac = reduce_action(UE_SE_ac,BMenv.action_map)\n", "best_RI = [np.argmax(ses,axis=0) for ses in UE_SE_ac]\n", "\n", "best_idx = copy.deepcopy(best_RI)\n", "add_idx = 0\n", "for i in range(len(BMenv.action_map)):\n", "    name = list(BMenv.action_map.keys())[i]\n", "    idx = BMenv.action_map[name]\n", "    best_idx[i] = best_idx[i] + add_idx\n", "    add_idx += len(idx)\n", "        \n", "best_RI = np.array(expand_action(best_RI,BMenv.action_map)) \n", "best_RI = best_RI.transpose(1,2,0,3)\n", "\n", "best_idx = np.array(expand_action(best_idx,BMenv.action_map)) \n", "best_idx = best_idx.transpose(1,2,0,3)\n", "\n", "BMenv.report_RI = np.array(best_RI,dtype=np.int8) + 1\n", "BMenv.report_idx = np.array(best_idx,dtype=np.int8)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(200, 2000, 30, 13)\n"]}], "source": ["print(BMenv.report_RI.shape)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1.         1.         0.90384615 0.90384615 0.90384615 0.90384615\n", " 0.90384615 0.90384615 0.90384615 0.90384615 0.75806452 0.75806452\n", " 0.75806452 0.75806452 0.75806452 0.75806452 0.75806452 0.75806452\n", " 0.75806452 0.75806452 0.75806452 0.75806452 0.66011236 0.66011236\n", " 0.66011236 0.66011236 0.57881773 0.57881773 0.57881773 0.57881773]\n"]}], "source": ["fix = 64*0.01+64*8*0.005\n", "fix2 = 64*8*0.01\n", "a1 = fix+1+0.25*2\n", "a2 = fix+1+0.25*4\n", "a3 = fix+1+0.25*8\n", "a4 = fix2+1+0.25*4\n", "a5 = fix2+1+0.25*8\n", "EE = np.array([a1,a1,\\\n", "                a2,a2,a2,a2,a2,a2,a2,a2,\\\n", "                a3,a3,a3,a3,a3,a3,a3,a3,a3,a3,a3,a3,\\\n", "                a4,a4,a4,a4,\\\n", "                a5,a5,a5,a5,\n", "                ])\n", "EE = (1/EE*np.min(EE))\n", "print(EE)"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(100, 2000)\n", "(200, 2000, 30, 17, 4)\n"]}], "source": ["print(BMenv.Beam_idx1.shape)\n", "print(BMenv.UE_PMI.shape)"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["64.10407\n"]}], "source": ["print(np.max(BMenv.UE_SE))"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(200, 38, 30, 13)\n", "(30, 200, 38, 13)\n", "Counter({10: 1462, 6: 1437, 2: 1407, 18: 836, 7: 528, 22: 340, 26: 321, 0: 297, 11: 216, 3: 188, 19: 168, 14: 150, 23: 120, 1: 72, 27: 29, 15: 29})\n"]}], "source": ["ses = BMenv.UE_SE[:,ues1]\n", "ses = np.average(ses,axis=-1)\n", "print(ses.shape)\n", "# ses = np.average(ses,axis=0)\n", "ses = ses.transpose(2,0,1,3)\n", "ses = ses*EE[:,np.newaxis,np.newaxis,np.newaxis]\n", "print(ses.shape)\n", "print(Counter(np.argmax(ses[:,:,0:39,2],axis=0).reshape(-1)))"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[1.20177215 1.30044304 1.42227848 1.56373418 1.70107595 1.79765823\n", "  1.85582278 1.90316456 1.94474684 1.97981013 1.99392405 1.99924051\n", "  1.99993671]\n", " [1.04436709 1.16677215 1.34120253 1.51968354 1.6564557  1.77037975\n", "  1.8828481  2.07626582 2.36873418 2.69563291 2.99417722 3.2264557\n", "  3.42164557]\n", " [1.0371519  1.16689873 1.325      1.49525316 1.65968354 1.76867089\n", "  1.84683544 1.93721519 2.07170886 2.25588608 2.5035443  2.7893038\n", "  3.06278481]\n", " [1.05575949 1.20196203 1.38126582 1.54962025 1.68797468 1.79424051\n", "  1.91626582 2.13031646 2.46189873 2.82310127 3.14531646 3.39234177\n", "  3.5743038 ]\n", " [1.04594937 1.17879747 1.36797468 1.53221519 1.66227848 1.77949367\n", "  1.9        2.12183544 2.44879747 2.80816456 3.10626582 3.3471519\n", "  3.53272152]\n", " [1.04139241 1.17696203 1.33367089 1.50506329 1.67443038 1.78063291\n", "  1.86550633 1.97563291 2.12974684 2.33132911 2.61613924 2.97443038\n", "  3.31955696]\n", " [1.04588608 1.17816456 1.36449367 1.52772152 1.66835443 1.79056962\n", "  1.96575949 2.28310127 2.67772152 3.05227848 3.33987342 3.55379747\n", "  3.70962025]\n", " [1.0293038  1.15987342 1.36050633 1.51544304 1.65550633 1.77303797\n", "  1.87867089 2.03632911 2.30398734 2.65259494 3.01734177 3.33474684\n", "  3.56481013]]\n"]}], "source": ["rp1 = np.average(BMenv.report_RI[:,UEs],axis=0)\n", "rp1 = np.average(rp1,axis=0)\n", "idxs = [0,2,6,10,14,18,22,26]\n", "print(rp1[idxs])"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["OK and store report Data\n"]}], "source": ["BMenv.store_data()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["load report data OK\n"]}], "source": ["BMenv.load_data()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2218729.0 2145204.0\n"]}], "source": ["print(np.sum(BMenv.UE_SE),np.sum(BMenv.gNB_SE))"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ 0  3  5  7  9 10 12 14 15 15]\n"]}], "source": ["d = np.arange(10)\n", "e = np.searchsorted(table,d)\n", "print(e)"]}], "metadata": {"kernelspec": {"display_name": "setsumi", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.15"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "f8a110c2d076917b81a0ccbfc6885a5d1bb94a5882c2e7e7ff56bc5bea07b6a0"}}}, "nbformat": 4, "nbformat_minor": 2}