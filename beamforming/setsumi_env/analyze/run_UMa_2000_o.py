import os,sys
sys.path.append(os.getcwd())
sys.path.append("../../")
from setsumi_env.BM_functions import *
from setsumi_env.system_functions import *
from setsumi_env.process import *
from setsumi_env.analyze import *
from setsumi_env.utils import *
from setsumi_env.setsumi_env import setsumi_env
import numpy as np
import pickle
import re
import pandas as pd
from collections import Counter
import plotly.graph_objects as go
from plotly.subplots import make_subplots

from scipy import stats
import copy

import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from IPython.display import Image
from matplotlib.gridspec import GridSpec
import time
from datetime import datetime

BMenv = setsumi_env(path="/data/BM_data/28GHZ/UMa_2000_i/",max_time=200,max_length=200,gNB_tti=2,seed=777) 
draw_tool = BMenv.draw
sim_par = BMenv.sim_par

time_diff = 10
ntime_diff = int(200/time_diff)

for time_idx in range(0,ntime_diff):
    UEs = np.arange(BMenv.nUE)
    times = np.arange(time_idx*time_diff,time_idx*time_diff+time_diff,1)
    UEs_new, times_new = np.meshgrid(UEs, times)
    UEs = UEs_new.reshape(-1)
    times = times_new.reshape(-1)
    action = BMenv.action_space[0]
    V_ants = BMenv.V_ants
    H_ants = BMenv.H_ants
    actions = np.arange(len(BMenv.action_space))

    tag_times = []
    # nUE = 2000
    # times = [180]*2000
    # times = np.array(times)
    mod_time = times%BMenv.max_time #sim_par.time代表数据总的个数
    srs_idx = times//BMenv.gNB_tti


    tag_times.append(time.time())

    nUE = UEs.shape[0]

    H = BMenv.H_freq[UEs,mod_time]
    srs_choose = BMenv.srs_choose_all[UEs,srs_idx]

    H_origin = BMenv.H_freq
    subs = [[[i for i in range(BMenv.nSub)]]*BMenv.UE_port]*nUE
    subs = np.array(subs).transpose(0,2,1)
    ports = [[[i for i in range(BMenv.UE_port)]]*BMenv.nSub]*nUE
    ports = np.array(ports)
    ues = [[list(UEs)]*BMenv.nSub]*BMenv.UE_port
    ues = np.array(ues).transpose(2,1,0)


    tag_times.append(time.time())
    H_srs = H_origin[ues,srs_choose,subs,ports]
    H = H.astype(np.complex64)
    H_srs = H_srs.astype(np.complex64)

    for ac in actions:
        action = BMenv.action_space[ac]
        H_split = action[0]
        V_split = action[1]
        RI = action[2]
        Port = action[3]
        N1 = max(V_split,H_split)*int(Port/H_split/V_split/2)
        N2 = min(V_split,H_split)
        nBeam = int(Port/H_split/V_split/2)
        O1 = (N1>1)*3+1
        O2 = (N2>1)*3+1
        assert(RI <= N1*N2*2)   
        codebook_origin = PMI_class.get_pmi_codebook(N1,N2,O1,O2,1,RI)*np.sqrt(RI)
        codebook_origin = codebook_origin.astype(np.complex64)
        pattern = generate_beam_pattern(V_ants,H_ants,V_split,H_split)

        tag_times.append(time.time())
        Beam_idx = BMenv.Beam_idxs[srs_idx,UEs]
        Beam_idx = Beam_idx[:,0:nBeam]
        Beam = BMenv.Beams[Beam_idx]
        Beam = Beam.transpose(0,2,1)

        Ports_vector = pattern
        Beam_Port = Beam*Ports_vector
        FRF = np.kron(np.eye(2,dtype=np.complex64),Beam_Port)
        FRF = FRF/np.sqrt(V_ants*H_ants*2)/np.sqrt(nBeam)*np.sqrt(Port)
        FRF = np.expand_dims(FRF,axis=1)

        #cal UE report
        UE_H = H@FRF
        UE_H = np.expand_dims(UE_H,(0,1,2,3))
        codebook_re = codebook_origin.reshape(-1,Port,RI)
        codebook = np.expand_dims(codebook_origin, axis=(4,5))

        HP = np.matmul(UE_H,codebook)
        Layer_power = np.conj(HP.transpose(0,1,2,3,4,5,7,6))@HP
        HP_power = np.trace(np.abs(Layer_power),axis1=-1,axis2=-2)
        # HP_power = np.sum(HP_power,axis = -1)

        bs_shape = HP_power.shape[0:4]
        HP_power = HP_power.reshape(-1,nUE,BMenv.nSub)
        idx = np.argmax(HP_power,axis=0)
        PMI = np.unravel_index(idx,bs_shape)
        PMI = np.array(PMI)
        PMI = PMI.transpose(1,2,0)

        P = codebook_re[idx]
        # max_power = HP_power[idx,np.arange(idx.shape[0])]
        # lp = Layer_power[idx]
        FBB = P
        # FRF = dp.array(FRF)
        # FBB = dp.array(FBB)
        F = FRF@FBB

        Layer_power = Layer_power.reshape(-1,nUE,BMenv.nSub,RI,RI)
        # lp = Layer_power[idx,np.arange(idx.shape[0])]
        lp = Layer_power[idx,ues[:,:,0],subs[:,:,0]]


        BMenv.UE_PMI[times,UEs,ac,:,:] = PMI
        for snr_idx in range(BMenv.nSNR):
        # for snr_idx in [10]:
            snr = BMenv.SNRs[snr_idx]
            R = np.float32(np.power(10,(-snr/10)))
            sinr = np.linalg.inv(lp/R+np.eye(RI,dtype=np.complex64))
            sinr = np.real(np.diagonal(sinr,axis1 = -1,axis2 = -2))
            sinr = 1./sinr -1 
            sinr = np.clip(sinr,0.00001,1000000000)
            sinr = 10*np.log10(sinr)

            #redo sinr
            sinr[sinr < BMenv.sinr_min] = -50
            if sinr.shape[-1] > 1:
                sinr[(sinr[:,:,0] - sinr[:,:,1] > BMenv.sinr_max_diff),1] = -50
                sinr[(sinr[:,:,1] - sinr[:,:,0] > BMenv.sinr_max_diff),0] = -50
            if sinr.shape[-1] > 3:
                sinr[(sinr[:,:,2] - sinr[:,:,3] > BMenv.sinr_max_diff),3] = -50
                sinr[(sinr[:,:,3] - sinr[:,:,2] > BMenv.sinr_max_diff),2] = -50
            layer = np.sum(sinr > -40,axis = -1)
            layer = np.clip(layer,1,4)
            mi = sinr2mi(sinr)
            mi[mi<0.0014] = 0 #min_mi = 0.0013
            avg_mi = np.sum(mi,axis=-1)/layer
            sinr_avg = mi2sinr(avg_mi)
            mcs = np.searchsorted(sinr2mcs_table,sinr_avg)
            CQI = sinr2cqi(sinr_avg)
            TPs = BMenv.TP_buffer[mcs,layer-1]

            BMenv.report_CQI[times,UEs,ac,snr_idx,:] = CQI
            BMenv.UE_TP[times,UEs,ac,snr_idx,:] = TPs
        print(f"{datetime.now()} Time {time_idx} and action {ac} process UE OK")


        #cal gNB report
        UE_H = H_srs@FRF
        UE_H = np.expand_dims(UE_H,(0,1,2,3))
        codebook_re = codebook_origin.reshape(-1,Port,RI)
        codebook = np.expand_dims(codebook_origin, axis=(4,5))

        HP = np.matmul(UE_H,codebook)
        Layer_power = np.conj(HP.transpose(0,1,2,3,4,5,7,6))@HP
        HP_power = np.trace(np.abs(Layer_power),axis1=-1,axis2=-2)
        # HP_power = np.sum(HP_power,axis = -1)

        bs_shape = HP_power.shape[0:4]
        HP_power = HP_power.reshape(-1,nUE,BMenv.nSub)
        idx = np.argmax(HP_power,axis=0)
        PMI = np.unravel_index(idx,bs_shape)
        PMI = np.array(PMI)
        PMI = PMI.transpose(1,2,0)

        P = codebook_re[idx]
        FBB = P
        F = FRF@FBB

        Layer_power = Layer_power.reshape(-1,nUE,BMenv.nSub,RI,RI)
        # lp = Layer_power[idx,np.arange(idx.shape[0])]
        HF = H@F
        HFH = np.conj(HF.transpose(0,1,3,2))@HF
        lp = HFH
        BMenv.gNB_PMI[times,UEs,ac,:,:] = PMI
        for snr_idx in range(BMenv.nSNR):
        # for snr_idx in [10]:
            snr = BMenv.SNRs[snr_idx]
            R = np.float32(np.power(10,(-snr/10)))
            sinr = np.linalg.inv(lp/R+np.eye(RI,dtype=np.complex64))
            sinr = np.real(np.diagonal(sinr,axis1 = -1,axis2 = -2))
            sinr = 1./sinr -1 
            sinr = np.clip(sinr,0.00001,1000000000)
            sinr = 10*np.log10(sinr)

            #redo sinr
            sinr[sinr < BMenv.sinr_min] = -50
            if sinr.shape[-1] > 1:
                sinr[(sinr[:,:,0] - sinr[:,:,1] > BMenv.sinr_max_diff),1] = -50
                sinr[(sinr[:,:,1] - sinr[:,:,0] > BMenv.sinr_max_diff),0] = -50
            if sinr.shape[-1] > 3:
                sinr[(sinr[:,:,2] - sinr[:,:,3] > BMenv.sinr_max_diff),3] = -50
                sinr[(sinr[:,:,3] - sinr[:,:,2] > BMenv.sinr_max_diff),2] = -50
            layer = np.sum(sinr > -40,axis = -1)
            layer = np.clip(layer,1,4)
            mi = sinr2mi(sinr)
            mi[mi<0.0014] = 0 #min_mi = 0.0013
            avg_mi = np.sum(mi,axis=-1)/layer
            sinr_avg = mi2sinr(avg_mi)
            mcs = np.searchsorted(sinr2mcs_table,sinr_avg)
            CQI = sinr2cqi(sinr_avg)
            TPs = BMenv.TP_buffer[mcs,layer-1]
            
            BMenv.gNB_TP[times,UEs,ac,snr_idx,:] = TPs

        print(f"{datetime.now()} Time {time_idx} and action {ac} process gNB OK")
    print(f"{datetime.now()} Time {time_idx} process all actions OK")
    BMenv.store_data()
    print(f"{datetime.now()} Time {time_idx} store OK")