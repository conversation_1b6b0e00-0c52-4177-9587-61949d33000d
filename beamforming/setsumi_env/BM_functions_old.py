import os,sys
sys.path.append(os.getcwd())

import numpy as np
import pandas as pd
# from utils.generate_beam_pattern import generate_beam_pattern
# from utils.sinr2mib import Mib2Sinr, Sinr2Mib
# from utils.get_sinr import get_sinr
# from utils.generate_beam import generate_dft_beams
# from utils.PMI import PMI
# from utils.mcs2qam import Mcs2Qam
# from utils.mib2bler import Mib2Bler
# from utils.cqi2sinr import Sinr2Cqi
# from utils.throughput import TbCalc

from utils import *
import re

#global data
PMI_class = PMI()
sinr2mi = Sinr2Mib()
mi2sinr = Mib2Sinr()
mcs2qam = Mcs2Qam()
mi2bler = Mib2Bler()
sinr2cqi = Sinr2Cqi()
sinr2mcs_table = np.array([-5.23,-4.7,-3.7,-3.18,-2.45,-1.6,-0.88,-0.12,
                                        0.79,1.38,1.88,2.7,3.65,4.51,5.24,5.88,
                                        6.75,7.29,8.23,9.14,9.92,11.09,11.86,12.93,
                                        14.14,14.98,15.91,17.15])
sinr_min = 0
sinr_max_diff = 15

'''
action space
notation: H_spilt,V_spilt,RI,Port
if Port > H_spilt*V_spilt*2 -> nBeam nBeam = Port/H_spilt/V_spilt/2
'''
# H_spilt,V_spilt,RI
action_space = [(2,1,1,4),(2,1,2,4),\
                (2,1,3,4),(2,1,4,4),\
                (1,2,1,4),(1,2,2,4),\
                (1,2,3,4),(1,2,4,4),\
                (1,1,1,2),(1,1,2,2),\
                (1,1,1,4),(1,1,2,4),\
                (1,1,3,4),(1,1,4,4)] 

class Sim_Log():
    def __init__(self,Data_dir=None):
        self.seed = 0
        self.nCell = 21
        self.nBS = int(self.nCell/3)
        self.nUE = 1
        self.H_ants = 8
        self.V_ants = 2
        self.Ants_all = self.H_ants*self.V_ants
        self.Ants_all_2pol = self.Ants_all*2
        self.HSpacing = 0.5
        self.VSpacing = 0.8
        self.UE_Port = 4
        self.time_unit = 20 #单位ms
        self.time = 1
        self.nSub = 17
        self.path = ""

        self.cm_name = np.zeros((self.nUE,self.nCell))
        self.cm_npath = np.zeros((self.nUE,self.nCell))
        self.cm_no_snap = np.zeros((self.nUE,self.nCell))

        self.cs_name = list()
        self.cs_npath = list()
        self.cs_no_snap = list()
        self.cs_name = self.cs_name.append("default")
        self.cs_npath = self.cs_npath.append(24)
        self.cs_no_snap = self.cs_no_snap.append(1)
        self.SNRs = [-10,-5,0,5,10,15,20,25,30,35,40]
        self.Data_dir = ""

        if Data_dir is not None:
            self.analyze_log(Data_dir)
    
    def analyze_log(self,Data_dir):
        self.Data_dir = Data_dir
        log_path = Data_dir + "/log.txt"
        with open(log_path, "r") as f:
            log = f.read()
        print("setsumi load sim log OK")
        seed = re.compile(r'random seed is (\d+)\n').findall(log)
        if(len(seed) > 0 and seed[0].isdigit()): self.seed = int(seed[0])

        nCell = re.compile(r'BS Cell number is (\d+)\n').findall(log)
        if(len(nCell) > 0 and nCell[0].isdigit()): 
            self.nCell = int(nCell[0])
            self.nBS = int(self.nCell/3)

        nUE = re.compile(r'UE number is (\d+)\n').findall(log)
        if(len(nUE) > 0 and nUE[0].isdigit()): self.nUE = int(nUE[0])

        H_ants = re.compile(r'H_ants is (\d+)\n').findall(log)
        if(len(H_ants) > 0 and H_ants[0].isdigit()): self.H_ants = int(H_ants[0])
        
        V_ants = re.compile(r'V_ants is (\d+)\n').findall(log)
        if(len(V_ants) > 0 and V_ants[0].isdigit()): self.V_ants = int(V_ants[0])

        UE_Port = re.compile(r'UE Port is (\d+)\n').findall(log)
        if(len(UE_Port) > 0 and UE_Port[0].isdigit()): self.UE_Port = int(UE_Port[0])

        nSub = re.compile(r'nSub is (\d+)\n').findall(log)
        if(len(nSub) > 0 and nSub[0].isdigit()): self.nSub = int(nSub[0])

        time_unit = re.compile(r'sim time unit is (\d+)\n').findall(log)
        if(len(time_unit) > 0 and time_unit[0].isdigit()): self.time_unit = int(time_unit[0])

        time = re.compile(r'sim time number is (\d+)\n').findall(log)
        if(len(time) > 0 and time[0].isdigit()): self.time = int(time[0])

        bs_loc = re.compile(r'bs location: x: (\S+) y: (\S+) z: (\S+)').findall(log)
        self.bs_loc = np.array(bs_loc).astype(float)

        cms_info = re.compile(r'sim cm (\d+) name is (\S+),npath is (\d+),no_snap is (\d+)\n').findall(log)
        if(len(cms_info)) > 0:
            cm_name = np.zeros((self.nUE,self.nBS),dtype=object)
            cm_npath = np.zeros((self.nUE,self.nBS),dtype=int)
            cm_no_snap = np.zeros((self.nUE,self.nBS),dtype=int)
            for i in range(0,len(cms_info)):
                cm_info = cms_info[i]
                name_data = cm_info[1]
                ue_info = re.compile(r'[\S+]Tx(\d+)_Rx(\d+)').findall(name_data)
                if not len(ue_info):
                    ue_info = re.compile(r'[\S+](\d+)_ue(\d+)').findall(name_data)
                cell = int(ue_info[0][0])-1
                ue = int(ue_info[0][1])-1
                cm_name[ue,cell] = name_data
                cm_npath[ue,cell] = int(cm_info[2])
                cm_no_snap[ue,cell] = int(cm_info[3])
            self.cm_name = cm_name
            self.cm_npath = cm_npath
            self.cm_no_snap = cm_no_snap

        css_info = re.compile(r'sim cs (\d+) name is (\S+),npath is (\d+),no_snap is (\d+)\n').findall(log)
        cs_name = list()
        cs_npath = list()
        cs_no_snap = list()
        if(len(css_info)) > 0:
            for i in range(0,len(css_info)):
                cs_info = css_info[i]
                cs_name.append(cs_info[1])
                cs_npath.append(int(cs_info[2]))
                cs_no_snap.append(int(cs_info[3]))
            self.cs_name = cs_name
            self.cs_npath = cs_npath
            self.cs_no_snap = cs_no_snap

        self.Ants_all = self.H_ants*self.V_ants
        self.Ants_all_2pol = self.Ants_all*2
        self.Cell_UE = self.nCell*self.nUE

def load_data(path,sim_par,freq=True,multile_cell=True):
    nUE = sim_par.nUE
    if multile_cell:
        nCell = sim_par.nCell
    else:
        nCell = 1
    Cell_UE = nUE*nCell

    if not freq:
        npath = sim_par.cs_npath
    else:
        npath = [sim_par.nSub]*Cell_UE

    with open(path, 'r') as file:
        Datas_all = list()
        for i in range(0,Cell_UE):
            nSub = npath[i]
            file_data = file.readline()
            target_data = re.split('\s+',file_data.strip())
            for index,item in enumerate(target_data):
                target_data[index] = float(item)
            time = sim_par.time
            H_ants = sim_par.H_ants
            V_ants = sim_par.V_ants
            Port = sim_par.UE_Port
            Ants_all_2pol = sim_par.Ants_all_2pol
            data = np.array(target_data)
            data = data.reshape(time,nSub,Ants_all_2pol,Port,2)
            data = data[:,:,:,:,0] + 1j*data[:,:,:,:,1]
            data = data.reshape(time,nSub,H_ants,V_ants,2,Port)
            data = data.transpose(0,1,5,4,3,2)
            data = data.reshape(time,nSub,Port,Ants_all_2pol)
            Datas_all.append(data)
            # data = data.transpose(0,2,1,3,4,5)
    file.close()

    Datas_cell_ue = list()
    for i in range(0,sim_par.nUE):
        Datas_ue = list()
        for j in range(0,sim_par.nCell):
            idx = j*sim_par.nUE + i
            Datas_ue.append(Datas_all[idx])
        Datas_cell_ue.append(Datas_ue)

    return Datas_cell_ue

def load_gain(path,sim_par):
    with open(path, 'r') as file:
        Datas_all = list()
        for i in range(0,sim_par.Cell_UE):
            file_data = file.readline()
            target_data = re.split('\s+',file_data.strip())
            for index,item in enumerate(target_data):
                target_data[index] = float(item)
            Datas_all.append(np.array(target_data))
    file.close()
    
    Datas_cell_ue = list()
    for i in range(0,sim_par.nUE):
        Datas_ue = list()
        for j in range(0,sim_par.nCell):
            idx = j*sim_par.nUE + i
            Datas_ue.append(Datas_all[idx])
        Datas_cell_ue.append(Datas_ue)

    return Datas_cell_ue

def choose_best_cell(data,sim_par):
    cell_ue = list()
    sinr_all = list()
    rsrps_all = list()
    for i in range(0,sim_par.nUE):
        rsrps = list()
        for j in range(0,sim_par.nCell):
            tmp = data[i][j] #choose ue
            tmp = tmp[0,:,:,0] #time*npath*port*ants
            rsrp = np.sum(np.abs(tmp)*np.abs(tmp)) / 4 # Divide by 4 UE ant
            rsrps.append(rsrp)
        rsrps = np.array(rsrps)
        sinr_ue =10*np.log10(rsrps/(np.sum(rsrps)-rsrps))
        # print("ue is %d,sinr is %.2f at cell %d" %(i,np.max(sinr_ue),np.argmax(sinr_ue)))
        cell_ue.append(np.argmax((rsrps)))
        sinr_all.append(sinr_ue)
        rsrps_all.append(rsrps)
    cell_ue = np.array(cell_ue)
    sinr_all = np.array(sinr_all)
    return cell_ue,sinr_all,rsrps_all

def cal_coma(srs_p1,srs_p2): # (nUE, nAnts)
    srs_p1 = np.expand_dims(srs_p1,-2)
    srs_p2 = np.expand_dims(srs_p2,-2)
    #!coma srs.H@srs 
    # srs_p1.H@srs_p1
    # Coma (nUE, nAnts ,nAnts)
    Coma_p1 = np.conj(srs_p1.transpose(0,2,1))@srs_p1     #srs.H@srs
    Coma_p2 = np.conj(srs_p2.transpose(0,2,1))@srs_p2     #srs.H@srs

    Tra_p1 = np.trace(Coma_p1,axis1=-2,axis2=-1)
    Tra_p2 = np.trace(Coma_p2,axis1=-2,axis2=-1)
    a1 = np.divide(Tra_p1,Tra_p1+Tra_p2+1e-50)
    a2 = np.divide(Tra_p2,Tra_p1+Tra_p2+1e-50)
    Coma = Coma_p1*np.expand_dims(a1,axis=(-1,-2)) + Coma_p2*np.expand_dims(a2,axis=(-1,-2))
    return Coma

def update_svd_Beam(Coma,n=None):
    s1,v1,d1 = np.linalg.svd(Coma)
    s1 = s1.T
    if n:
        beam = np.transpose(s1[0:n+1],(1,0))
    else:
        beam = np.transpose(s1,(1,0))
    return beam
    

def update_Beam(Coma,Beams):
    """
    Coma_Long和全部可选的Beams计算全部可选的Beams的能量，从这些Beams选择能量最大和第二大的Beam返回(idx1, power1, idx2, power2)
    """
    Coma = Coma
    Beams = np.expand_dims(Beams,2) #nBeam*n_ant*1\

    Beam_ = np.expand_dims(Beams,0)
    Coma_ = np.expand_dims(Coma,1)
    Beam_power = np.conj(Beam_.transpose(0,1,3,2))@Coma_@Beam_ #Beam.H@Coma@Beam
    Beam_power = Beam_power.squeeze((-1,-2)) # 为每个UE从所有Beam中选择最大能量的一个
    
    Coma_norm = np.linalg.norm(Coma,axis=(-1,-2)) #nUE
    Beams_norm = np.linalg.norm(Beams,axis=(-1,-2)) #nBeam
    Beam_power = np.abs(Beam_power/np.expand_dims(Coma_norm,1)/np.expand_dims(Beams_norm,0)) #

    idx = np.argsort(Beam_power,kind='mergesort',axis=1)
    power = np.sort(Beam_power,kind='mergesort',axis=1)
    idx = np.flip(idx,axis=1) #反转
    power = np.flip(power,axis=1)
    return idx,power

def get_Beam_Port(Beam,V_ant_num,H_ant_num,V_split,H_split):
    Ports_vector = generate_beam_pattern(V_ant_num,H_ant_num,V_split,H_split)
    if len(Beam.shape) == 1:
        Beam = np.expand_dims(Beam,0)
    Beam = Beam.transpose(1,0) #16*n
    Beam_Port = Beam*Ports_vector
    Beam_Port = np.kron(np.eye(2),Beam_Port)
    return Beam_Port

def get_PMI(H,N1,N2,layer): #ue_port*gnb_port
    O1 = (N1>1)*3+1
    O2 = (N2>1)*3+1
    assert(layer <= N1*N2*2)   
    codebook = PMI_class.get_pmi_codebook(N1,N2,O1,O2,1,layer)
    H = np.expand_dims(H,(0,1,2,3)) #0,1,2,3,nSub,ue_port,max_gNB_port
    codebook = np.expand_dims(codebook, axis=4)
    HP = H@codebook #0,1,2,3,ue_port,layer
    Layer_power = np.conj(HP.transpose(0,1,2,3,4,6,5))@HP
    HP_power = np.trace(np.abs(Layer_power),axis1=-1,axis2=-2)
    HP_power = np.sum(HP_power,axis = -1)

    idx = np.unravel_index(np.argmax(HP_power),HP_power.shape)
    P = codebook[idx]
    max_power = HP_power[idx]
    return idx,P,max_power

def cal_sinr(HP,R):
    sinr = get_sinr(HP.transpose(0,2,1),R)
    sinr_origin = np.copy(sinr)
    sinr[sinr < sinr_min] = -50
    if sinr.shape[1] > 1:
        sinr[(sinr[:,0] - sinr[:,1] > sinr_max_diff),1] = -50
        sinr[(sinr[:,1] - sinr[:,0] > sinr_max_diff),0] = -50
    if sinr.shape[1] > 3:
        sinr[(sinr[:,2] - sinr[:,3] > sinr_max_diff),3] = -50
        sinr[(sinr[:,3] - sinr[:,2] > sinr_max_diff),2] = -50
    layer = np.sum(sinr > -40,axis = 1)
    layer = np.clip(layer,1,4)
    mi = sinr2mi(sinr)
    mi[mi<0.0014] = 0 #min_mi = 0.0013
    avg_mi = np.sum(mi,axis=1)/layer
    sinr_avg = mi2sinr(avg_mi)
    return sinr_avg,layer,sinr_origin

def get_tp_par():
    # Init PRB config
    nPRB = 272
    nRBG = 17 
    nBS_RF_CHAIN = 16
    PRBpreSB = [nBS_RF_CHAIN for i in range(nRBG-1)] + [nPRB-nBS_RF_CHAIN*(nRBG-1)]  # PRB of each subband
    nPRB_PDSCH = 156
    nREAvg = min(156,  nPRB_PDSCH)
    return PRBpreSB,nREAvg

def get_tp(mcs,layer,nSub,sub=False):
    '''
        get a list of UE throughput
    '''
    PRBpreSB,nREAvg = get_tp_par()
    assert len(PRBpreSB) == nSub
    if sub: TP = []
    else:   TP = 0.
    for band in range(nSub):
        if(len(mcs) == nSub):
            mcs_sub = mcs[band]
            layer_sub = layer[band]
        else:
            mcs_sub = mcs[0]
            layer_sub = layer[0]
        if sub: TP.append(TbCalc(mcs_sub, layer_sub, PRBpreSB[band], nREAvg))
        else:   TP += TbCalc(mcs_sub, layer_sub, PRBpreSB[band], nREAvg)
    return TP

def cal_action_report(H,action,R,H_ants=8,V_ants=2,nSub=17):
    # input H nSub*Port*Ants
    Beam_Port,P,beam_idx,power = cal_action_BP(H,action,H_ants=8,V_ants=2,nSub=17)
    BP = Beam_Port@P
    BP_max = np.max(np.sum(np.abs(BP),axis=-1))
    HP = H@BP/BP_max
    sinr,layer,sinr_origin = cal_sinr(HP,R)
    mcs = np.searchsorted(sinr2mcs_table,sinr)
    TP = get_tp(mcs,layer,nSub,False)
    CQI = sinr2cqi(sinr)
    
    return mcs,TP,sinr,CQI,beam_idx,power,PMI,sinr_origin

def cal_action_BP(H,action,H_ants=8,V_ants=2,nSub=17):
    # input H nSub*Port*Ants
    # cal_param
    Ants_all = H_ants*V_ants
    Ants_all_2pol = 2*Ants_all
    action = action_space[action]
    H_split = action[0]
    V_split = action[1]
    RI = action[2]
    Port = action[3]

    N1 = max(V_split,H_split)*int(Port/H_split/V_split/2)
    N2 = min(V_split,H_split)
    nBeam = int(N1*N2/H_split/V_split)

    # get srs data
    # H nport*nsub*Ants_all_2pol
    srs_pol1 = H[:,:,0:Ants_all]
    srs_pol2 = H[:,:,Ants_all:Ants_all_2pol]
    srs_pol1 = srs_pol1.reshape(-1,Ants_all)
    srs_pol2 = srs_pol2.reshape(-1,Ants_all)
    
    # get short coma
    coma = cal_coma(srs_pol1,srs_pol2)
    coma = np.average(coma,axis=(0))
    
    # get long coma
    # 2 method
    # direct average
    coma = np.expand_dims(coma,axis=0)

    # get Beam
    # 4 method
    # dft beam from coma
    # svd beam from coma
    # dft beam from H
    # svd beam from H
    Beams = generate_dft_beams(V_ants,H_ants)
    beam_idx,power = update_Beam(coma,Beams)
    beam_idx = beam_idx[0]
    power = power[0]
    beam = Beams[beam_idx[0:nBeam]]

    # get complete analog matrix
    Beam_Port = get_Beam_Port(beam,V_ant_num = V_ants,H_ant_num = H_ants,V_split = V_split,H_split = H_split)
    
    # get equality channel of port
    UE_H = H@Beam_Port
    
    # get digital matrix
    PMI,P,max_power = get_PMI(UE_H,N1,N2,RI)
    
    # # get full matrix
    # BP = Beam_Port@P
    
    # # norm matrix
    # BP_max = np.max(np.sum(np.abs(BP),axis=-1))
    # BP = BP/BP_max
    return Beam_Port,P,beam_idx,power

def get_best_action(H,R,H_ants=8,V_ants=2,nSub=17):
    mcs_max = None
    TP_max = 0
    sinr_max = None
    CQI_max = None
    beam_best = None
    beam_power = None
    action_max = 0
    TPs = list()
    sinrs = list()
    PMIs = list()
    for i in range(0,len(action_space)):
        mcs,TP,sinr,CQI,beam_idx,power,PMI,sinr_origin = cal_action_report(H,i,R,H_ants=H_ants,V_ants=V_ants,nSub=nSub)
        TPs.append(TP)
        sinrs.append(sinr)
        PMIs.append(PMI)
        if(TP > TP_max):
            TP_max = TP
            mcs_max = mcs
            sinr_max = sinr
            CQI_max = CQI
            action_max = i
            beam_best = beam_idx
            beam_power = power
    return action_max,TP_max,mcs_max,sinr_max,CQI_max,TPs,beam_best,beam_power,sinrs,PMIs

def restore_H(H,path): #restore H as Beam,use for draw
    Ants = H.shape[2]
    data1 = np.real(H)
    data2 = np.imag(H)
    data1 = np.expand_dims(data1,axis=3)
    data2 = np.expand_dims(data2,axis=3)
    data = np.concatenate((data1,data2),axis=3)
    data = data.transpose(2,0,1,3)
    data = data.reshape(Ants,-1)
    data = pd.DataFrame(data)
    data.to_csv(path,index=False,header=True)


if __name__ == '__main__':
    path = "./Data/H_re.txt"
    data = np.loadtxt(path)
    nUE = 1
    H_ants = 8
    V_ants = 2
    Port = 4
    Ants_all = H_ants*V_ants
    Ants_all_2pol = Ants_all*2
    print(data.shape)
    data = data.reshape(nUE,-1,17,Ants_all_2pol,4,2)
    data = data[:,:,:,:,:,0] + 1j*data[:,:,:,:,:,1]
    print(data.shape)
    time = data.shape[1]
    nSub = 17

    data = data.reshape(nUE,time,nSub,H_ants,V_ants,2,Port)
    data = data.transpose(1,0,2,6,5,4,3)
    print(data.shape)
    data = data.reshape(time,nUE,nSub,Port,Ants_all_2pol)
    print(data.shape)

    for i in range(0,5):
        action,TP,_,_,_ = get_best_action(data[i,0],R = 6.8e-14)
        print(action)
        print(TP)