close all;
clear all;
warning('off','all');

base_path = './Data/scenario_test_H8V4_05/';
ue_num = 2000;
% UMa Indoor
scenario_set = 'UMa_LOS_O2I';
scenario_na = strcat('3GPP_38.901_',scenario_set);
scenario_path = strcat(base_path,scenario_set,"/");
mkdir(scenario_path);
result = run(scenario_path,scenario_na,ue_num,1,true);

scenario_set = 'UMa_NLOS_O2I';
scenario_na = strcat('3GPP_38.901_',scenario_set);
scenario_path = strcat(base_path,scenario_set,"/");
mkdir(scenario_path);
result = run(scenario_path,scenario_na,ue_num,1,true);

% UMa Outdoor
scenario_set = 'UMa_LOS';
scenario_na = strcat('3GPP_38.901_',scenario_set);
scenario_path = strcat(base_path,scenario_set,"/");
mkdir(scenario_path);
result = run(scenario_path,scenario_na,ue_num,0,true);

scenario_set = 'UMa_NLOS';
scenario_na = strcat('3GPP_38.901_',scenario_set);
scenario_path = strcat(base_path,scenario_set,"/");
mkdir(scenario_path);
result = run(scenario_path,scenario_na,ue_num,0,true);

% UMi Indoor
scenario_set = 'UMi_LOS_O2I';
scenario_na = strcat('3GPP_38.901_',scenario_set);
scenario_path = strcat(base_path,scenario_set,"/");
mkdir(scenario_path);
result = run(scenario_path,scenario_na,ue_num,1,true);

scenario_set = 'UMi_NLOS_O2I';
scenario_na = strcat('3GPP_38.901_',scenario_set);
scenario_path = strcat(base_path,scenario_set,"/");
mkdir(scenario_path);
result = run(scenario_path,scenario_na,ue_num,1,true);

% UMi Outdoor
scenario_set = 'UMi_LOS';
scenario_na = strcat('3GPP_38.901_',scenario_set);
scenario_path = strcat(base_path,scenario_set,"/");
mkdir(scenario_path);
result = run(scenario_path,scenario_na,ue_num,0,true);

scenario_set = 'UMi_NLOS';
scenario_na = strcat('3GPP_38.901_',scenario_set);
scenario_path = strcat(base_path,scenario_set,"/");
mkdir(scenario_path);
result = run(scenario_path,scenario_na,ue_num,0,true);