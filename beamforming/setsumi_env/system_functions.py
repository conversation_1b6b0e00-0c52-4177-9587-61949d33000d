from setsumi_env.BM_functions import *
from setsumi_env.utils import *
import copy

def simulate_srs(sim_par,time=4000,gNB_tti=2):
    nSub = sim_par.nSub
    UE_Port = sim_par.UE_Port
    total_nsub_ueport = nSub*UE_Port # nSub*UE_Port  17*4
    choose_sub_port = list(np.arange(0,total_nsub_ueport))
    srs_choose = []
    rand_sub = []
    rand_port = []
    srs_inst = np.zeros((nSub,UE_Port),dtype=int)
    for i in range(time):
        if i%gNB_tti == 0:
            sub_port = np.random.choice(choose_sub_port)
            choose_sub_port.remove(sub_port)
            if not choose_sub_port:
                choose_sub_port = list(np.arange(0,total_nsub_ueport))
            sub = sub_port//UE_Port
            port = sub_port%UE_Port
            rand_sub.append(sub)
            rand_port.append(port)
            redo_time = i%sim_par.time
            srs_inst[sub,port] = redo_time
            srs_choose.append(copy.deepcopy(srs_inst))
    return srs_choose,rand_sub,rand_port

def simulate_coma(H,rand_sub,rand_port,gNB_tti,mu,time=None):
    short_coma = None
    long_coma = []
    # comas = []
    Ants_all_2pol = H.shape[-1]
    Ants_all = Ants_all_2pol//2
    if time is None:
        max_time = H.shape[0]
    else:
        max_time = time
    for i in range(len(rand_sub)):
        time = i*gNB_tti
        sub = rand_port[i]
        port = rand_port[i]
        srs_pol1 = H[time%max_time,sub,port,0:Ants_all]
        srs_pol2 = H[time%max_time,sub,port,Ants_all:Ants_all_2pol]
        short_coma = cal_coma(np.expand_dims(srs_pol1,0),np.expand_dims(srs_pol2,0))
        
        if max_time == 1:
            srs_pol1 = H[time%max_time,:,:,0:Ants_all]
            srs_pol2 = H[time%max_time,:,:,Ants_all:Ants_all_2pol]
            
            short_coma = cal_coma(srs_pol1.reshape(-1,Ants_all),srs_pol2.reshape(-1,Ants_all))
            short_coma = np.average(short_coma,axis=(0))
            short_coma = short_coma[np.newaxis,:,:]
        # comas.append(short_coma)
        if long_coma:
            long_coma.append(mu*long_coma[-1]+(1-mu)*short_coma)
        else:
            long_coma.append(short_coma)
    return long_coma

def cal_singular(comas):
    action_panel = [(1,1,1,2),(2,1,1,4),(1,2,1,4),(2,2,1,8),(4,1,1,8)]

    patterns = []
    coma_actions = []
    for action in action_panel:
        H_split = action[0]
        V_split = action[1]
        pattern = generate_beam_pattern(numVant=4,numHant=8,V_split=V_split,H_split=H_split)
        pattern_idx = []
        coma_action = []
        for npattern in range(0,pattern.shape[1]):
            idx = np.squeeze(np.argwhere(pattern[:,npattern] == 1)).astype(int)
            pattern_idx.append(idx)
            coma_action.append(comas[:,:,:,idx,:][:,:,:,:,idx])
        coma_actions.append(coma_action)
        patterns.append(pattern_idx)
    coma_HV = coma_actions[1]
    coma_VH = coma_actions[2]
    coma_HV = np.array(coma_HV)
    coma_VH = np.array(coma_VH)
    s,v1,d = np.linalg.svd(coma_HV,full_matrices=True)
    s,v2,d = np.linalg.svd(coma_VH,full_matrices=True)
    v1_ = v1[:,:,:,0,0]
    v2_ = v2[:,:,:,0,0]
    v1_add = np.sum(v1_,axis=0)
    v2_add = np.sum(v2_,axis=0)
    v1_1 = v1_.transpose(2,1,0)
    v2_1 = v2_.transpose(2,1,0)
    vv = np.concatenate((v1_1,v2_1),axis=-1)
    return vv

def choose_H(H,time_choose):
    # srs拼接H
    subs = [[i for i in range(17)]]*4
    subs = np.array(subs).transpose(1,0)
    ports = [[i for i in range(4)]]*17
    ports = np.array(ports)
    H_ = H[time_choose,subs,ports]
    return H_

def cal_PMI_map(action_space,H_ants=8,V_ants=4,HSpacing=0.5,VSpacing=0.8):
    draw_tool = draw(numHant=H_ants,numVant=V_ants,HSpacing=HSpacing,VSpacing=VSpacing)
    Beams = generate_dft_beams(numVant=V_ants,numHant=H_ants)

    Ants_all = H_ants*V_ants
    PMI_map = []
    for action_idx in range(len(action_space)):
        action = action_space[action_idx]
        H_split = action[0]
        V_split = action[1]
        RI = action[2]
        Port = action[3]
        N1 = max(V_split,H_split)*int(Port/H_split/V_split/2)
        N2 = min(V_split,H_split)
        O1 = (N1>1)*3+1
        O2 = (N2>1)*3+1

        codebook_origin = PMI_class.get_pmi_codebook(N1,N2,O1,O2,1,RI)
        codebook_origin = codebook_origin.transpose(0,1,2,3,5,4)
        cd_shape = codebook_origin.shape
        cd_shape = list(cd_shape)
        codebook = codebook_origin.reshape(-1,cd_shape[-1])
        codebook = np.expand_dims(codebook,axis=-1)
        cd_shape[-1] = 2
        Beam_angle_all = []
        for beam_idx in range(len(Beams)):
            Beam = Beams[beam_idx]
            Beam = np.expand_dims(Beam,0)
            nBeam = int(Port/H_split/V_split/2) 
            Beam = np.repeat(Beam,nBeam,axis=0)
            Ports_vector = generate_beam_pattern(V_ants,H_ants,V_split,H_split)
            if len(Beam.shape) == 1:
                Beam = np.expand_dims(Beam,0)
            Beam = Beam.transpose(1,0) #16*n
            Beam_Port = Beam*Ports_vector
            FRF = np.kron(np.eye(2,dtype=np.complex64),Beam_Port)
            FRF = FRF/np.sqrt(V_ants*H_ants*2)/np.sqrt(nBeam)*np.sqrt(Port)

            F_ALL = FRF@codebook
            Beam_angle = []
            for i in range(F_ALL.shape[0]):
                angles = draw_tool.cal_gain(F_ALL[i,0:Ants_all],Find_angle_only=True)
                Beam_angle.append(angles)
            Beam_angle = np.array(Beam_angle)
            Beam_angle = Beam_angle.reshape(cd_shape)
            Beam_angle_all.append(Beam_angle)
        Beam_angle_all = np.array(Beam_angle_all)
        PMI_map.append(Beam_angle_all)
    return PMI_map