import os
import numpy
import matlab.engine
from BM_functions import *
from process import *
from analyze import *
from utils import *
import numpy as np
import re
import pandas as pd
from collections import Counter
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import time
import os
import datetime

eng = matlab.engine.start_matlab()

set_los = False
ue_num = 2
seed = 723
no_snap = 20
#run UMi test

base_path = './Data/Move/'

#run UMa test
scenario_name = '3GPP_38.901_UMa_LOS'



# nfloor = [5,10,15,20,25,30,25,40]
nfloor = 5
# isds = [100,200,500,1000,1500,2000,2500,3000]
isds = [500]
# isds = [1500,2000]
indoor = 0
for isd in isds:
    name = 'Uma_isd_'+str(isd)
    print(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    print("run matlab begin at "+name)
    scenario_path = base_path+name+"/"
    os.makedirs(scenario_path,exist_ok=True)
    result = eng.run(scenario_path,scenario_name,ue_num,indoor,set_los,int(nfloor),float(isd),seed,True,no_snap)
    print("run matlab finish at "+name)
    print(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    print("process begin at "+name)
    all_process(scenario_path)
    print("process finish at "+name)
    print(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))