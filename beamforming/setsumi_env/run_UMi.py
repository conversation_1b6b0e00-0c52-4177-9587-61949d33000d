import os
import numpy
import matlab.engine
from BM_functions import *
from pre_process import *
from analyze import *
from utils import *
import numpy as np
import re
import pandas as pd
from collections import Counter
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import time
import os
import datetime

eng = matlab.engine.start_matlab()



base_path = './Data/test_build2/UMi/'
set_los = False
ue_num = 2000
seed = 723
#run UMi test
scenario_name = '3GPP_38.901_UMi_NLOS_O2I'

# floors = [1,2,3,4,5,8,10,15,20]
floors = [10,12,14,16,18,20,50]
# floors = [25,40]
isd = 200
indoor = 1
for nfloor in floors:
    name = 'UMi_nfloor_'+str(nfloor)
    print(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    print("run matlab begin at "+name)
    scenario_path = base_path+name+"/"
    os.makedirs(scenario_path,exist_ok=True)
    result = eng.run(scenario_path,scenario_name,ue_num,indoor,set_los,int(nfloor),float(200),seed)
    print("run matlab finish at "+name)
    print(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    print("process begin at "+name)
    all_process(scenario_path)
    print("process finish at "+name)
    print(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
