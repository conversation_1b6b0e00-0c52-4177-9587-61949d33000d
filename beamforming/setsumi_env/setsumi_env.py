# from BM_functions import *
# from system_functions import *
# from process import *
# from analyze import *
# from utils import *
import os,sys
sys.path.append(os.getcwd())
from setsumi_env.BM_functions import *
from setsumi_env.system_functions import *
from setsumi_env.process import *
from setsumi_env.analyze import *
from setsumi_env.utils import *
import numpy as np
import re
import pandas as pd
from collections import Counter
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import os
from scipy import stats
import copy
from typing import Tuple, List 
import pickle

import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from IPython.display import Image
from matplotlib.gridspec import GridSpec

class setsumi_env:
    def __init__(self,logger=print,path="/data/setsumi/Data/test/test_add/",max_time = 4000,max_length = 4000,seed=777,enable_load=True,gNB_tti=2,UE_tti=1,Beam_tti=2,w1=0,clear_time=True) -> None:
        self.logger = logger
        self.UE_tti = UE_tti # change to 20 tti collect once
        self.gNB_tti = gNB_tti # change to 20 tti collect once
        self.Beam_tti = Beam_tti
        self.seed = seed
        self.nCELL = 21
        sim_par = Sim_Log(path)
        self.path = path
        self.enable_load = enable_load
        self.sim_par = sim_par
        self.max_time = max_time
        self.clear_time = clear_time
        self.max_time = min(self.max_time,sim_par.time)
        self.max_length = max_length
        self.max_srs = (self.max_length+self.gNB_tti-1)//self.gNB_tti
        self.nSub = sim_par.nSub
        self.H_ants = sim_par.H_ants
        self.V_ants = sim_par.V_ants
        self.Ants_num = sim_par.Ants_all
        self.UE_port = sim_par.UE_Port
        self.nUE = sim_par.nUE
        self.all_coma = False

        self.data_name = f"sakura_{self.max_time}_{self.max_length}_{self.seed}_{self.gNB_tti}_{self.UE_tti}_{self.Beam_tti}.pkl"
        
        self.mu = 0.98
        self.Beams = generate_dft_beams(numVant=self.V_ants,numHant=self.H_ants)
        self.test = False
        self.draw = draw(numHant=sim_par.H_ants,numVant=sim_par.V_ants,HSpacing=sim_par.HSpacing,VSpacing=sim_par.VSpacing)
        with open("/home/<USER>/beamforming/data/BM/setsumi_H8V4_PMI_action22.pkl", 'rb') as f:
            PMI_map = pickle.load(f)[0]
        self.PMI_map=PMI_map
        print(self.PMI_map.shape)  # 输出 PMI_map 的维度

        Beam_angle = []
        for i in range(len(self.Beams)):
            angles = self.draw.cal_gain(self.Beams[:,i],Find_angle_only=True)
            Beam_angle.append(angles)
        self.Beam_angle = np.array(Beam_angle)
        self.angle_H_max = np.max(np.abs(self.Beam_angle[:,0]))
        self.angle_V_max = np.max(np.abs(self.Beam_angle[:,1]))
        self.load_H()
        # self.action_space = [(2,1,1,4),(2,1,2,4),\
        #         (2,1,3,4),(2,1,4,4),\
        #         (1,2,1,4),(1,2,2,4),\
        #         (1,2,3,4),(1,2,4,4),\
        #         (1,1,1,2),(1,1,2,2),\
        #         (1,1,1,4),(1,1,2,4),\
        #         (1,1,3,4),(1,1,4,4)] 
        
        self.action_space = [(1,1,1,2),(1,1,2,2),\
                        (2,1,1,4),(2,1,2,4),\
                        (2,1,3,4),(2,1,4,4),\
                        (1,2,1,4),(1,2,2,4),\
                        (1,2,3,4),(1,2,4,4),\
                        (2,2,1,8),(2,2,2,8),\
                        (2,2,3,8),(2,2,4,8),\
                        (4,1,1,8),(4,1,2,8),\
                        (4,1,3,8),(4,1,4,8),\
                        (1,4,1,8),(1,4,2,8),\
                        (1,4,3,8),(1,4,4,8),\
                        (1,1,1,4),(1,1,2,4),\
                        (1,1,3,4),(1,1,4,4),\
                        (1,1,1,8),(1,1,2,8),\
                        (1,1,3,8),(1,1,4,8), 
                        ]
        
        self.train_space = self.action_space[0:22]
        
        self.cqi_table = [0.1523,0.3770,0.8770,1.4766,1.9141,2.4063,2.7305,3.3223,3.9023,4.5234,5.1152,5.5547,6.2266,6.9141,7.4063]
        
        self.w1 = w1
        
        fix = 64*0.01+64*8*0.005
        fix2 = 64*8*0.01
        a1 = fix+1+0.25*2
        a2 = fix+1+0.25*4
        a3 = fix+1+0.25*8
        a4 = fix2+1+0.25*4
        a5 = fix2+1+0.25*8
        self.EE_coeff = np.array([a1,a1,\
                        a2,a2,a2,a2,a2,a2,a2,a2,\
                        a3,a3,a3,a3,a3,a3,a3,a3,a3,a3,a3,a3,\
                        a4,a4,a4,a4,\
                        a5,a5,a5,a5,
                        ])
        self.EE_coeff = (1/self.EE_coeff*np.min(self.EE_coeff))

        action_map = {}
        for i in range(len(self.action_space)):
            action = self.action_space[i]
            name = f"H{action[0]}V{action[1]}P{action[3]}"
            if not name in action_map:
                action_map[name] = []
            action_map[name].append(i)
        self.action_map = action_map

        self.SNRs = np.arange(-30,35,5,dtype=np.float32)
        self.nSNR = self.SNRs.shape[0]
        self.SNR_idx = 10
        self.SNR = self.SNRs[self.SNR_idx]

        self.gNB_PMI = np.zeros((self.max_length,self.nUE,len(self.action_space),self.nSub,4),dtype=np.int8)
        self.UE_PMI = np.zeros((self.max_length,self.nUE,len(self.action_space),self.nSub,4),dtype=np.int8)

        self.gNB_SE = np.zeros((self.max_length,self.nUE,len(self.action_space),self.nSNR,self.nSub),dtype=np.float32)
        self.UE_SE = np.zeros((self.max_length,self.nUE,len(self.action_space),self.nSNR,self.nSub),dtype=np.float32)
        
        self.Beam_idx1 = np.zeros((self.max_srs,self.nUE),dtype = int)
        self.Beam_idx2 = np.zeros((self.max_srs,self.nUE),dtype = int)
        self.Beam_idxs = np.zeros((self.max_srs,self.nUE,self.Ants_num),dtype = int)
        self.Beam_powers = np.zeros((self.max_srs,self.nUE,self.Ants_num),dtype = np.float32) 
        self.Beam_power1 = np.zeros((self.max_srs,self.nUE),dtype = np.float32) 
        self.Beam_power2 = np.zeros((self.max_srs,self.nUE),dtype = np.float32)
        
        self.beam_name = f"simulate_{self.max_time}_{self.max_length}_{self.seed}_{self.gNB_tti}_{self.UE_tti}_{self.Beam_tti}.pkl"
        
        if(os.path.exists(self.path+self.beam_name) and self.enable_load):
            with open(self.path+self.beam_name, 'rb') as file:
                self.coma_all = pickle.load(file, encoding='bytes')
                self.Beam_idx1 = pickle.load(file, encoding='bytes')
                self.Beam_idx2 = pickle.load(file, encoding='bytes')
                self.Beam_idxs = pickle.load(file, encoding='bytes')
                self.Beam_power1 = pickle.load(file, encoding='bytes')
                self.Beam_power2 = pickle.load(file, encoding='bytes')
                self.Beam_powers = pickle.load(file, encoding='bytes')
            print("load Beam data OK")
        else:
            self.load_H()
            if self.logger is not None:
                self.logger("cal coma")
            self.sequential_coma()
            if self.logger is not None:
                self.logger("cal beam")
            self.sequential_beam()
            with open(self.path+self.beam_name, 'wb') as file:
                pickle.dump(self.coma_all, file,protocol = 4)
                pickle.dump(self.Beam_idx1, file,protocol = 4)
                pickle.dump(self.Beam_idx2, file,protocol = 4)
                pickle.dump(self.Beam_idxs, file,protocol = 4)
                pickle.dump(self.Beam_power1, file,protocol = 4)
                pickle.dump(self.Beam_power2, file,protocol = 4)
                pickle.dump(self.Beam_powers, file,protocol = 4)
            print("OK and store Beam Data")
    
 
            
    def load_H(self):
        if self.logger is not None:
            self.logger("Load begin")
        get_data(self.sim_par,clear_time=True)
        self.H_freq = np.array(self.sim_par.data['H_freq'],dtype=np.complex64)
        self.H_pg = np.array(self.sim_par.data['H_pg'],dtype=np.complex64)
        self.loc = np.array(self.sim_par.data['loc'],dtype=np.complex64)
        if self.logger is not None:
            self.logger("Load Finish")
        self.norm_H()
        if self.logger is not None:
                self.logger("simulate srs")
        np.random.seed(self.seed)
        self.sequential_simulate()
    
    def norm_H(self):
        for i in range(0,self.nUE):
            pg = self.H_pg[i]
            pg_data = np.sqrt(np.power(10,(pg/10)))
            pg_data = np.expand_dims(pg_data,(1,2,3))
            self.H_freq[i] = self.H_freq[i]/pg_data/np.sqrt(2)
        
    def sequential_simulate(self):
        srs_choose_all = []
        sub_choose_all = []
        port_choose_all = []
        for i in range(0,self.nUE):
            srs_choose,sub_choose,port_choose = simulate_srs(self.sim_par,self.max_length,self.gNB_tti)
            srs_choose_all.append(srs_choose)
            sub_choose_all.append(sub_choose)
            port_choose_all.append(port_choose)
        self.srs_choose_all = np.array(srs_choose_all)
        self.sub_choose_all = np.array(sub_choose_all)
        self.port_choose_all = np.array(port_choose_all)
    
    def sequential_coma(self):
        coma_all = []
        for i in range(0,self.nUE):
            coma_ue = simulate_coma(self.H_freq[i],self.sub_choose_all[i],self.port_choose_all[i],self.gNB_tti,self.mu,self.max_time)
            coma_all.append(coma_ue)
        coma_all = np.array(coma_all)
        self.coma_all = coma_all      
        
    def sequential_beam(self):
        nUE = self.coma_all.shape[0]
        nsrs = self.coma_all.shape[1]
        for i in range(nUE):
            for j in range(nsrs):
                idx,power = update_Beam(self.coma_all[i][j],self.Beams)
                self.Beam_idx1[j,i] = idx[0][0]
                self.Beam_idx2[j,i] = idx[0][1]
                self.Beam_idxs[j,i] = idx[0]
                self.Beam_power1[j,i] = power[0][idx[0][0]]
                self.Beam_power2[j,i] = power[0][idx[0][1]]
                self.Beam_powers[j,i] = power[0]
        
    def get_state_batch(self,batch,times,ues,actions,SNRs=None) -> List[np.float32]:
        if(SNRs is None):
            SNRs = np.ones_like(times,dtype=int)*self.SNR_idx
        PMI = np.zeros((batch,self.nSub,2),dtype=np.float32)
        CQI = np.zeros((batch,self.nSub),dtype=np.float32)
        RI = np.zeros((batch),dtype=np.float32) #RI 不能用one-hot编码，不同的RI确实代表了不同的信道强度
        # RI_train = np.zeros((batch, 4),dtype=np.float32) # RL_train use one-hot
        SE = np.zeros((batch,self.nSub),dtype=np.float32)
        EE = np.zeros((batch),dtype=np.float32)
        SEE = np.zeros((batch),dtype=np.float32)
        # EE_r = np.zeros((batch),dtype=np.float32)
        # SE_r = np.zeros((batch),dtype=np.float32)
        SEE_r = np.zeros((batch),dtype=np.float32)
        Port_r = np.zeros((batch),dtype=np.float32)
        Ns_r = np.zeros((batch),dtype=np.float32)
        best_ac = np.zeros((batch),dtype=int)
        bset_port = np.zeros((batch),dtype=np.float32)
        best_ns = np.zeros((batch),dtype=np.float32)
        # TP_div = np.zeros((batch),dtype=np.float32)
        
        for i in range(0,batch):
            action = actions[i]
            time = times[i]
            ue = ues[i]
            snr_idx = SNRs[i]
            srs_idx = time//self.gNB_tti
            action_use = self.action_space[action]
            if(self.gNB_SE[time,ue,action,snr_idx,0] == 0): 
                self.run_batch_result(np.array(time),np.array(ue),np.array(action))

            RI_rp = self.report_RI[time,ue,action,self.SNR_idx]
            re_idx = self.report_idx[time,ue,action,self.SNR_idx]
            PMI_rp = self.UE_PMI[time,ue,re_idx]
            
            SE_rp = self.gNB_SE[time,ue,action,self.SNR_idx]
            CQI_rp = np.searchsorted(self.cqi_table,SE_rp)
                
            H_split = action_use[0]
            V_split = action_use[1]
            layer = action_use[2]
            UE_Port = action_use[3]
            action_re = self.action_space.index((H_split, V_split, RI_rp, UE_Port))
            PMI[i] = np.var(self.PMI_map[action_re][self.Beam_idx1[srs_idx,ue], PMI_rp[:,0], PMI_rp[:,1], PMI_rp[:,2], PMI_rp[:,3]], axis=1)
            
            PMI[i,:] = PMI[i,:]/(np.max(PMI[i,:]) + 1e-7)
            CQI[i,:] = CQI_rp
            RI[i] = RI_rp
            
            SE[i] = np.array(SE_rp)
            EE[i] = self.gNB_EE_ac[time,ue,action,self.SNR_idx]
            # EE_r[i] = (EE[i] - self.min_gNB_EE[time,ue,self.SNR_idx])/(self.max_gNB_EE[time,ue,self.SNR_idx] - self.min_gNB_EE[time,ue,self.SNR_idx] + 1e-7)
            # SE_r[i] = np.average(SE_rp)
            # SE_r[i] = (SE_r[i] - self.min_gNB_SE[time,ue,self.SNR_idx])/(self.max_gNB_SE[time,ue,self.SNR_idx] - self.min_gNB_SE[time,ue,self.SNR_idx] + 1e-7)
            
            SEE[i] = np.average(SE_rp)*self.w1+EE[i]*(1-self.w1)
            SEE_r[i] = (SEE[i] - self.min_gNB_SEE[time,ue,self.SNR_idx])/(self.max_gNB_SEE[time,ue,self.SNR_idx] - self.min_gNB_SEE[time,ue,self.SNR_idx] + 1e-7)
            
            Port_r[i] = UE_Port
            Ns_r[i] = layer
            best_ac[i] = self.best_action[time,ue,self.SNR_idx]
            best_ns[i] = self.action_space[best_ac[i]][2]
            bset_port[i] = self.action_space[best_ac[i]][3]
            
        
        # Channel Quality Features
        self.SE = np.average(SE,axis=1)
        self.EE = EE
        # self.reward = SE_r*self.w1+EE_r*(1-self.w1)
        # self.score = SE_r*self.w1+EE_r*(1-self.w1)
        self.reward = SEE_r
        self.score = SEE_r
        # self.reward = (best_ac == action)*1 + (bset_port == Port_r)*0.1 + (best_ns == Ns_r)*0.1
        self.RI = RI
        self.best_ac = best_ac
        Port_r = np.log2(Port_r)
        self.Port_r = Port_r
        self.Ns_r = Ns_r
        
        W1 = np.ones_like(RI)*self.w1
        
        PMI = np.average(PMI,axis=1)
        
        if(self.test):
            return None,PMI,CQI,RI,EE
        
        CQI = np.expand_dims(CQI,axis=[-2,-1]) /15.0 
        EE = np.expand_dims(EE,axis=[-3,-2,-1]) /100.0 
        RI = np.expand_dims(RI,axis=[-3,-2,-1]) / 4.0
        Port = np.expand_dims(Port_r,axis=[-3,-2,-1]) / 3.0
        Ns = np.expand_dims(Ns_r,axis=[-3,-2,-1]) / 4.0
        
        # UE Position Features
        PMI = np.expand_dims(PMI,axis=[-2,-1])
        srs_of_time = list(np.array(times)//self.gNB_tti)
        # Beam1_angle = self.Beam_angle[self.Beam_idx1[srs_of_time,list(ues)],:] / np.array([self.angle_H_max,self.angle_V_max]) 
        # Beam2_angle = self.Beam_angle[self.Beam_idx2[srs_of_time,list(ues)],:] / np.array([self.angle_H_max,self.angle_V_max]) 
        # Beam1_pwr = np.expand_dims(self.Beam_power1[srs_of_time,list(ues)],axis=-1) / 4.0
        # Beam2_pwr = np.expand_dims(self.Beam_power2[srs_of_time,list(ues)],axis=-1) / 4.0
        # Beam1_fe = np.expand_dims(np.concatenate([Beam1_angle,Beam1_pwr], axis=-1), -1)
        # Beam2_fe = np.expand_dims(np.concatenate([Beam2_angle,Beam2_pwr], axis=-1), -1)
        # Beam_feature = np.expand_dims(np.concatenate([Beam1_fe, Beam2_fe], axis=-1), -1)
        Beam_powers = self.Beam_powers[srs_of_time,list(ues),:]/np.max(self.Beam_powers[srs_of_time,list(ues),:],axis=-1)[:,np.newaxis]
        # Beam_powers = self.singular[srs_of_time,list(ue),:]/np.max(self.singular[srs_of_time,list(ue),:],axis=-1)[:,np.newaxis]
        
        return [Beam_powers,PMI,CQI,RI,EE,Port,Ns]
    
    def get_action(self,Port_old,Ns_old,HV,Port_diff,Ns_diff):
        Port_diff = np.clip(Port_diff,-1,1)
        Ns_diff = np.clip(Ns_diff,-1,1)
        Port = Port_old+Port_diff
        Port = np.clip(Port,1,3)
        Ns = Ns_old+Ns_diff
        Ns = np.around(Ns)
        Ns = np.clip(Ns,1,4)
        actions = []
        for i in range(Port.shape[0]):
            if(Port[i] == 1):
                actions.append(Ns[i]==1)
            elif(Port[i] == 2):
                actions.append(1+(HV[i]<0)*4+Ns[i])
            else:
                actions.append(9+Ns[i])
        return np.array(actions,dtype=int)
    
    def get_action_hybrid(self,Ns_old,Ns_diff,HV,Port):
        Ns_diff = np.clip(Ns_diff,-1,1)
        Ns = Ns_old+Ns_diff
        Ns = np.around(Ns)
        Ns = np.clip(Ns,1,4)
        actions = []
        for i in range(Port.shape[0]):
            if(Port[i] == 1):
                actions.append(Ns[i]==1)
            elif(Port[i] == 2):
                actions.append(1+(HV[i]<0)*4+Ns[i])
            else:
                if(HV[i] < -0.5):
                    actions.append(13+Ns[i]) #H4V1
                elif(HV[i] > 0.5):
                    actions.append(17+Ns[i]) #H4V1
                else:
                    actions.append(9+Ns[i]) #H4V1
        return np.array(actions,dtype=int)
        

    def run_batch_result(self,UEs,times,actions):
        UEs, times, actions = np.broadcast_arrays(UEs[:, None], times[None, :], actions)
        UEs = UEs.reshape(-1)
        times = times.reshape(-1)
        actions = actions.reshape(-1)
        mod_time = times%self.max_time #sim_par.time代表数据总的个数
        srs_idx = times//self.gNB_tti
        
        nUE = UEs.shape[0]
        H = self.H_freq[UEs,mod_time]
        srs_choose = self.srs_choose_all[UEs,srs_idx]
        
        H_origin = self.H_freq
        srs_subs = [[[i for i in range(self.nSub)]]*self.UE_port]*nUE
        srs_subs = np.array(srs_subs).transpose(0,2,1)
        srs_ports = [[[i for i in range(self.UE_port)]]*self.nSub]*nUE
        srs_ports = np.array(srs_ports)
        srs_ues = [[list(UEs)]*self.nSub]*self.UE_port
        srs_ues = np.array(srs_ues).transpose(2,1,0)
        
        H_srs = H_origin[srs_ues,srs_choose,srs_subs,srs_ports]
        H = H.astype(np.complex64)
        H_srs = H_srs.astype(np.complex64)
        for ac in actions:
            action = self.action_space[ac]
            RI = action[2]
            Port = action[3]
            codebook_origin,pattern,nBeam = self.get_cd_pattern(action)
            
            Beam_idx = self.Beam_idxs[srs_idx,UEs]
            Beam_idx = Beam_idx[:,0:nBeam]
            Beam = self.Beams[Beam_idx]
            Beam = Beam.transpose(0,2,1)

            Ports_vector = pattern
            Beam_Port = Beam*Ports_vector
            
            FRF = np.kron(np.eye(2,dtype=np.complex64),Beam_Port)
            FRF = FRF/np.sqrt(self.V_ants*self.H_ants*2)/np.sqrt(nBeam)*np.sqrt(Port)
            FRF = np.expand_dims(FRF,axis=1)

            gNB_H = H_srs@FRF
            
            PMI,FBB,lp = self.get_FBB(gNB_H,codebook_origin,action,nUE)
            F = FRF@FBB
            HF = H@F
            lp = np.conj(HF.transpose(0,1,3,2))@HF
            self.gNB_PMI[times,UEs,ac,:,:] = PMI
            for snr_idx in range(self.nSNR):
                snr = self.SNRs[snr_idx]
                R = np.float32(np.power(10,(-snr/10)))
                sinr = np.linalg.inv(lp/R+np.eye(RI,dtype=np.complex64))
                sinr = np.real(np.diagonal(sinr,axis1 = -1,axis2 = -2))
                sinr = 1./sinr -1 
                sinr = np.clip(sinr,0.00001,1000000000)
                se =  np.log2(1 + sinr)
                # se =  np.clip(se,0,8)
                se = np.sum(se,axis=-1)
                self.gNB_SE[times,UEs,ac,snr_idx,:] = se
            
            UE_H = H@FRF
            PMI,FBB,lp = self.get_FBB(UE_H,codebook_origin,action,nUE)
            F = FRF@FBB
            HF = H@F
            lp = np.conj(HF.transpose(0,1,3,2))@HF
            self.UE_PMI[times,UEs,ac,:,:] = PMI
            for snr_idx in range(self.nSNR):
                snr = self.SNRs[snr_idx]
                R = np.float32(np.power(10,(-snr/10)))
                sinr = np.linalg.inv(lp/R+np.eye(RI,dtype=np.complex64))
                sinr = np.real(np.diagonal(sinr,axis1 = -1,axis2 = -2))
                sinr = 1./sinr -1 
                sinr = np.clip(sinr,0.00001,1000000000)
                se =  np.log2(1 + sinr)
                # se =  np.clip(se,0,8)
                se = np.sum(se,axis=-1)
                self.UE_SE[times,UEs,ac,snr_idx,:] = se      

    def get_cd_pattern(self,action):
        H_split = action[0]
        V_split = action[1]
        RI = action[2]
        Port = action[3]
        N1 = max(V_split,H_split)*int(Port/H_split/V_split/2)
        N2 = min(V_split,H_split)
        nBeam = int(Port/H_split/V_split/2)
        O1 = (N1>1)*3+1
        O2 = (N2>1)*3+1
        assert(RI <= N1*N2*2)   
        codebook_origin = PMI_class.get_pmi_codebook(N1,N2,O1,O2,1,RI)
        codebook_origin = codebook_origin.astype(np.complex64)
        pattern = generate_beam_pattern(self.V_ants,self.H_ants,V_split,H_split)       
        return codebook_origin,pattern,nBeam
    
    def get_FBB(self,H_eq,codebook,action,nUE):
        RI = action[2]
        Port = action[3]
        H_eq = np.expand_dims(H_eq,(0,1,2,3))
        codebook_re = codebook.reshape(-1,Port,RI)
        codebook = np.expand_dims(codebook, axis=(4,5))
        
        HP = np.matmul(H_eq,codebook)
        Layer_power = np.conj(HP.transpose(0,1,2,3,4,5,7,6))@HP
        HP_power = np.trace(np.abs(Layer_power),axis1=-1,axis2=-2)

        bs_shape = HP_power.shape[0:4]
        HP_power = HP_power.reshape(-1,nUE,self.nSub)
        idx = np.argmax(HP_power,axis=0)
        PMI = np.unravel_index(idx,bs_shape)
        PMI = np.array(PMI)
        PMI = PMI.transpose(1,2,0)
        FBB = codebook_re[idx]
        Layer_power = Layer_power.reshape(-1,nUE,self.nSub,RI,RI)
        ues = [[i for i in range(nUE)]]*self.nSub
        ues = np.array(ues).transpose(1,0)
        subs = [[i for i in range(self.nSub)]]*nUE
        subs = np.array(subs)
        lp = Layer_power[idx,ues,subs]
        return PMI,FBB,lp
                    
    def store_data(self):
        
        # 确保目录存在
        os.makedirs(os.path.dirname(self.path), exist_ok=True)
        
        # 检查数据是否已初始化
        if not hasattr(self, 'gNB_PMI') or self.gNB_PMI is None:
            print("警告：数据未初始化，无法保存")
            return False
        
        try:
            with open(self.path+self.data_name, 'wb') as file:
                pickle.dump(self.gNB_PMI, file, protocol=4)
                pickle.dump(self.gNB_SE, file, protocol=4)
                pickle.dump(self.UE_PMI, file, protocol=4)
                pickle.dump(self.UE_SE, file, protocol=4)
            print(f"成功保存数据到 {self.path+self.data_name}")
            return True
        except Exception as e:
            print(f"保存数据时出错: {e}")
            return False
        
    def load_data(self,ue_list=None):
        with open(self.path+self.data_name, 'rb') as file:
            self.gNB_PMI = pickle.load(file, encoding='bytes')
            self.gNB_SE = pickle.load(file, encoding='bytes')
            self.UE_PMI = pickle.load(file, encoding='bytes')
            self.UE_SE = pickle.load(file, encoding='bytes')
        print("load report data OK")
        self.pos_process_data(ue_list)

    def pos_process_data(self,ue_list=None):        
        # self.UE_SE = self.UE_SE*self.TP_EE[np.newaxis,np.newaxis,:,np.newaxis,np.newaxis]
        # self.gNB_TP = self.gNB_TP*self.TP_EE[np.newaxis,np.newaxis,:,np.newaxis,np.newaxis]
        if(ue_list is not None):
            self.UE_SE = self.UE_SE[:,ue_list,:,:,:]
            self.gNB_SE = self.gNB_SE[:,ue_list,:,:,:]
            self.UE_PMI = self.UE_PMI[:,ue_list,:,:,:]
            self.gNB_PMI = self.gNB_PMI[:,ue_list,:,:,:]
            self.Beam_idx1 = self.Beam_idx1[:,ue_list]
            self.Beam_idx2 = self.Beam_idx2[:,ue_list]
            self.Beam_idxs = self.Beam_idxs[:,ue_list,:]
            self.Beam_powers = self.Beam_powers[:,ue_list,:]
            self.Beam_power1 = self.Beam_power1[:,ue_list]
            self.Beam_power2 = self.Beam_power2[:,ue_list]
            
        UE_SE_ac = np.average(self.UE_SE,axis=-1)
        gNB_SE_ac = np.average(self.gNB_SE,axis=-1)
        # TP_ac_redo = TP_ac*self.TP_EE[np.newaxis,np.newaxis,:,np.newaxis]
        UE_EE_ac = UE_SE_ac*self.EE_coeff[np.newaxis,np.newaxis,:,np.newaxis]
        gNB_EE_ac = gNB_SE_ac*self.EE_coeff[np.newaxis,np.newaxis,:,np.newaxis]
        
        self.UE_EE_ac = UE_EE_ac
        self.gNB_EE_ac = gNB_EE_ac
        # self.max_UE_EE = np.max(UE_EE_ac,axis=2)
        # self.max_gNB_EE = np.max(gNB_EE_ac,axis=2)
        # self.min_UE_EE = np.min(UE_EE_ac,axis=2)
        # self.min_gNB_EE = np.min(gNB_EE_ac,axis=2)
        
        # self.max_UE_SE = np.max(UE_SE_ac,axis=2)
        # self.max_gNB_SE = np.max(gNB_SE_ac,axis=2)
        # self.min_UE_SE = np.min(UE_SE_ac,axis=2)
        # self.min_gNB_SE = np.min(gNB_SE_ac,axis=2)
        
        gNB_SEE = gNB_SE_ac*self.w1+gNB_EE_ac*(1-self.w1)
        self.max_gNB_SEE = np.max(gNB_SEE,axis=2)
        self.min_gNB_SEE = np.min(gNB_SEE,axis=2)
        
        self.best_action = np.argmax(gNB_SEE[:,:,0:22,:],axis=2)
        
        # self.best_SE = np.max(UE_SE_ac,axis=2)
        # self.best_EE = np.max(UE_EE_ac,axis=2)
        UE_SE_ac = UE_SE_ac.transpose(2,0,1,3)
        UE_SE_ac = reduce_action(UE_SE_ac,self.action_map)
        best_RI = [np.argmax(ses,axis=0) for ses in UE_SE_ac]

        best_idx = copy.deepcopy(best_RI)
        add_idx = 0
        for i in range(len(self.action_map)):
            name = list(self.action_map.keys())[i]
            idx = self.action_map[name]
            best_idx[i] = best_idx[i] + add_idx
            add_idx += len(idx)
                
        best_RI = np.array(expand_action(best_RI,self.action_map)) 
        best_RI = best_RI.transpose(1,2,0,3)

        best_idx = np.array(expand_action(best_idx,self.action_map)) 
        best_idx = best_idx.transpose(1,2,0,3)
        
        self.report_RI = np.array(best_RI,dtype=np.int8) + 1
        self.report_idx = np.array(best_idx,dtype=np.int8)
        # self.singular = cal_singular(self.coma_all)
        
    def train_clear_buffer(self):
        self.gNB_PMI = None
        self.UE_SE = None
        self.Beam_idx1 = None
        self.Beam_idx2 = None
        self.Beam_idxs = None
        self.Beam_powers = np.zeros((self.max_srs,self.nUE,self.Ants_num),dtype = np.float32) 
        self.Beam_power1 = None
        self.Beam_power2 = None
        
if __name__ == '__main__':
    BMenv = setsumi_env(path="/data/setsumi/env/Data/test/test_add/",max_length=4000)

    BMenv.run_all_result(4000)
