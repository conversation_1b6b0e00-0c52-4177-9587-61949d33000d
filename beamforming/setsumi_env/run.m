function result = run(scenario_path,scenario_name,ue_num,spilt_ue,indoor,set_los,nfloor,bs_isd,myseed,schedule,no_snapshots)

warning('off','all');

% scenario_path = '/data/BM_data/28GHZ/sakura/UMa_v/';
% scenario_name = '3GPP_38.901_UMa_NLOS';
% ue_num = int64(400);
% spilt_ue = int64(20);
% indoor = 0.8;
% nfloor = 5;
% bs_isd = 500;
% no_snapshots = 400;
% myseed = 1215;
% set_los = false;
% schedule = true; %设置是否选择最佳cell

disp("setsumi2")
addpath(genpath('/data/setsumi/beamforming/setsumi_env/quadriga_src_sangles'));
addpath(genpath('/data/setsumi/beamforming/setsumi_env/quadriga_src_sangles/config'));

my_rng = rng;
if myseed > 0
    seed = int64(myseed);
else
    seed = my_rng.State(1,1);
end
% change seed for test
% seed = 936021407;
rng(seed);
check_my_rng = rng;
%仿真参数配置
s = qd_simulation_parameters;
s.center_frequency = 28e9;
% s.center_frequency = 28e9;
s.use_3GPP_baseline = 0; %增强3GPP，能够支持移动
s.show_progress_bars = 1; %显示进度条
scenario = scenario_name;
scenario_base = scenario(1:15);
% indoor = 1;

floder = scenario_path;

no_bs = 7; %bs个数 
no_rx = ue_num; %用户数量
if no_snapshots > 1
    move_enable = true; %设置用户是否移动
else
    move_enable = false;
end

if scenario_base(1:15) == '3GPP_38.901_UMi' 
    indoor_percent = indoor; %80% for Umi，0% for Uma
    %蜂窝小区设置
    isd = bs_isd; %500 for Uma，200 for Umi
    %设置距离天线的最小距离，不能低于这个距离
    no_go_dist = 10; %bs高度 10m for Umi 25m for Uma
    bs_height = 10; %bs高度 在典型的Uma场景是25m，Umi场景是10m
    downtilt = 12;
else
    indoor_percent = indoor; %80% for Umi，0% for Uma
    %蜂窝小区设置
    isd = bs_isd; %500 for Uma，200 for Umi
    %设置距离天线的最小距离，不能低于这个距离
    no_go_dist = 35; %bs高度 10m for Umi 25m for Uma
    bs_height = 25; %bs高度 在典型的Uma场景是25m，Umi场景是10m
    downtilt = 12;
end
%天线面板配置
H_ants = 8;
V_ants = 4;
V_d = 0.8;
UE_H_ants = 1;
UE_V_ants = 2;
UE_port = UE_H_ants*UE_V_ants*2;
BS_array  = qd_arrayant( '3gpp-mmw', 1, 1, s.center_frequency, 3, downtilt, 0.5, V_ants, H_ants, V_d, 0.5 ); 
%模型选择，垂直天线数，水平天线数，中心频率，极化方式，下倾，天线间的间距，垂直面板个数，水平面板个数，垂直面板间距，水平面板间距
UE_array = qd_arrayant('3gpp-mmw', 1, 1, s.center_frequency, 3, 0, 0.5, 2, 1, 0.5, 0.5);

cell_type = 'regular'; %选择基站产生的小区类型，regular对应一个基站按照120度生成三个小区,30/150/270 hexagonal一个基站一个小区
if cell_type == 'regular'
    cell_number = no_bs*3;
else
    cell_number = no_bs;
end
BW = 48960e3; %10M频带 BW=15KHZ*12sc*16prb*17sub
nSub = 17; %子频带个数
%储存数据 
% npath = b(1, 1).NumClusters;

% 设置移动参数
speed = 3/3.6; %3km/h->m/s
% time = 0.04; %s 0.04s
time_unit = 0.02; %s
time = time_unit*(no_snapshots - 1);
% no_snapshots = time/time_unit
move_len = time*speed;

%spilt ue
num_spilt = ue_num/spilt_ue;

file_H_time = fopen(strcat(floder,'H_time.txt'),'w');
file_H_freq = fopen(strcat(floder,'H_freq.txt'),'w');
file_power = fopen(strcat(floder,'power.txt'),'w');
file_gain = fopen(strcat(floder,'gain.txt'),'w');
file_log = fopen(strcat(floder,'log.txt'),'w');
file_loc = fopen(strcat(floder,'loc.txt'),'w');
file_sangles = fopen(strcat(floder,'sangles.txt'),'w');
file_angles = fopen(strcat(floder,'angles.txt'),'w');
file_pg = fopen(strcat(floder,'pg.txt'),'w');

fprintf(file_log,scenario_base);
fprintf(file_log,"\n");
fprintf(file_log,"random seed is %d\n",seed);
fprintf(file_log,"BS cell number is %d\n",cell_number);
fprintf(file_log,"UE number is %d\n",no_rx);

if schedule == true
    fprintf(file_log,"BS schedule is true\n");
else
    fprintf(file_log,"BS schedule is false\n");
end
fprintf(file_log,"H_ants is %d\n",H_ants);
fprintf(file_log,"V_ants is %d\n",V_ants);
fprintf(file_log,"UE Port is %d\n",UE_port);
fprintf(file_log,"nSub is %d\n",nSub);
fprintf(file_log,"sim time unit is %d\n",time_unit*1000);
fprintf(file_log,"sim time number is %d\n",no_snapshots);
fprintf(file_log,"sim par of cm\n");
fprintf(file_log,"UE are divided into %d parts\n",int64(num_spilt));
fprintf(file_log,"UE are divided by %d\n",int64(spilt_ue));

for spilt_idx = 1:num_spilt
    no_rx = spilt_ue;
    scene = qd_layout.generate('regular', no_bs, isd, BS_array);
    scene.simpar = s;
    scene.tx_position(3,:) = bs_height;
    scene.name = 'setsumi';


    scene.no_rx = no_rx;
    ind = true(1, no_rx);
    % 随机撒UE的位置
    Umi_build_r = 1;
    build_dist = 50;
    % if scenario_base == '3GPP_38.901_UMi'
    %     scene.randomize_rx_positions( Umi_build_r, 1.5, 1.5, 0, ind );
    %     scene.rx_position(1,:) = scene.rx_position(1,:)+build_dist*sqrt(3)/2;
    %     scene.rx_position(2,:) = scene.rx_position(1,:)+build_dist*1/2;
    % else
    scene.randomize_rx_positions( 0.93*isd, 1.5, 1.5, 0, ind );
    ind = false(1, no_rx);
    for n = 1 : no_bs
        dist_r = sqrt((scene.rx_position(1,:) - scene.tx_position(1,n)).^2 + (scene.rx_position(2,:) - scene.tx_position(2,n)).^2);
        change_location = dist_r < no_go_dist;
        x_scale = scene.rx_position(1,:) - scene.tx_position(1,n);
        y_scale = scene.rx_position(2,:) - scene.tx_position(2,n);
        scene.rx_position(1,:) = scene.rx_position(1,:)+x_scale./dist_r.*(no_go_dist-dist_r).*change_location;
        scene.rx_position(2,:) = scene.rx_position(2,:)+y_scale./dist_r.*(no_go_dist-dist_r).*change_location;
    end
    % end

    if spilt_idx == 1
        for bs_idx = 1:no_bs
            fprintf(file_log,"bs location: x: %.4f y: %.4f z: %.4f\n",scene.tx_position(1,bs_idx),scene.tx_position(2,bs_idx),scene.tx_position(3,bs_idx));
        end
    end
    % 手动指定部分UE的位置 rx_position(3,:) 第一个维度的三个值分别表示x，y，z，第二个维度指示UE
    % scene.rx_position(1,1) = 173;
    % scene.rx_position(2,1) = 100;

    % 设置移动轨迹
    if move_enable
        for n = 1 : scene.no_rx
            t = qd_track('linear',move_len,-pi/3);
            t.initial_position = scene.rx_position(:,n);
            t.movement_profile = [ 0,time;0,move_len]; 
            t.interpolate('time',0.02);
            t.name = scene.rx_name{1,n};
            scene.rx_track(1,n) = t;
        end
    end

    % 配置用户高度
    floor = randi(5,1,scene.no_rx) + 3;        % Number of floors in the building
    % 3GPP 随机楼层在5~8之间，然后再在楼栋中随机
    % floor = randi(5,1,scene.no_rx)*0 + double(nfloor);  
    for n = 1 : scene.no_rx
        floor( n ) =  randi(  floor( n ) );                 % Floor level of the UE
    end
    scene.rx_position(3,:) = 3*(floor-1) + 1.5; 

    % 配置楼内楼外
    indoor_rx = scene.set_scenario(scenario_base,[],[],indoor_percent);

    scene.rx_position(3,~indoor_rx) = 1.5; 

    if set_los
        scene.set_scenario(scenario,[],[],indoor_percent);
    end

    % scene.visualize ( [] , [], 2 , 1);
    % view(-33,66)

    scene.rx_array = UE_array;

    b = scene.init_builder;
    sic = size(b);
    for ib = 1 : numel(b)
        [ i1,i2 ] = qf.qind2sub( sic, ib );
        scenpar = b(i1,i2).scenpar;                 % Read scenario parameters
        scenpar.SC_lambda = 0;                      % Disable spatial consistency of SSF
        b(i1,i2).scenpar_nocheck = scenpar;         % Save parameters without check (faster)
    end

    b_ue = b;
    b_ue = split_multi_freq( b_ue );                      % Split t he builders for multiple frequencies
    gen_parameters( b_ue );                            % Generate LSF and SSF parameters (uncorrelated)
    cm = get_channels( b_ue );                         % Generate channels

    % reorder cm
    pattern = '\S+Tx(\d+)_Rx(\d+)';
    idx = zeros(numel(cm),1);
    for cm_idx = 1:numel(cm)
        cm_name = cm(1,cm_idx).name;    
        cm_result = regexp(cm_name, pattern, 'tokens');
        tx_idx = str2num(cm_result{1,1}{1,1});
        rx_idx = str2num(cm_result{1,1}{1,2});
        txrx_idx = (tx_idx-1)*no_rx + rx_idx;
        idx(cm_idx) = txrx_idx;
    end
    cm(1,idx) = cm(1,:);

    Num_ants_2pol = H_ants*V_ants*2;
    if cell_type == 'regular'
        cs = split_tx( cm, {1:Num_ants_2pol,Num_ants_2pol+1:Num_ants_2pol*2,Num_ants_2pol*2+1:Num_ants_2pol*3} );
        pattern = 'Tx(\d+)s(\d+)_Rx(\d+)';
        idx = zeros(numel(cs),1);
        for cs_idx = 1:numel(cs)
            cs_name = cs(1,cs_idx).name;    
            cs_result = regexp(cs_name, pattern, 'tokens');
            bx_idx = str2num(cs_result{1,1}{1,1});
            sector_idx = str2num(cs_result{1,1}{1,2});
            rx_idx = str2num(cs_result{1,1}{1,3});
            tx_idx = (bx_idx-1)*3 + sector_idx;
            txrx_idx = (tx_idx-1)*no_rx + rx_idx;
            idx(cs_idx) = txrx_idx;
        end
        cs(1,idx) = cs(1,:);
    else
        cs = cm;
    end



    rsrp_all = zeros(no_bs*3, no_rx);
    for ue_idx = 1:no_rx
        for bs_idx = 1:no_bs*3
            cs_idx = (bs_idx - 1)*no_rx + ue_idx;
            cs_data = cs(1,cs_idx).coeff;
            if(move_enable)
                tmp = cs_data(:,1,:,1);
            else
                tmp = cs_data(:,1,:);
            end
            rsrp = sum(abs(tmp(:).^2)/2);
            rsrp_all(bs_idx,ue_idx) = rsrp;
        end
    end

    [max_data,max_cell] = max(rsrp_all);
    max_bs = ceil(max_cell/3);
    best_bs = int64(max_bs-1)*no_rx + (1:no_rx);
    best_cell = int64(max_cell-1)*no_rx + (1:no_rx);
    cm_redo = cm(1,best_bs);
    cs_redo = cs(1,best_cell); 

    if schedule == true
        cm = cm_redo;
        cs = cs_redo;
    end

    for cm_idx = 1:numel(cm)
        fprintf(file_log,"sim cm %d name is %s,npath is %d,no_snap is %d\n",cm_idx,cm(1,cm_idx).name,cm(1,cm_idx).no_path,cm(1,cm_idx).no_snap);
        % fprintf(file_log,"sim ue of cm spilt idx %d\n",int64(spilt_idx))
    end

    for cs_idx = 1:numel(cs)
        fprintf(file_log,"sim cs %d name is %s,npath is %d,no_snap is %d\n",cs_idx,cs(1,cs_idx).name,cs(1,cs_idx).no_path,cs(1,cs_idx).no_snap);
        % fprintf(file_log,"sim ue of cs spilt idx %d\n",int64(spilt_idx))
    end

    %写入UE位置数据
    for ue_idx = 1 : no_rx
        no_snap = scene.rx_track(1,ue_idx).no_snapshots;
        for snap_idx = 1 : no_snap
            fprintf(file_loc,"%.4f ",scene.rx_track(1,ue_idx).positions(:,snap_idx)+scene.rx_track(1,ue_idx).initial_position);
            fprintf(file_loc,"\n");
        end
    end


    %写入能量数据
    %写入时域数据
    n_tr = numel(cs);
    for tr_idx = 1 : n_tr
        tr_builder = cs(1,tr_idx);
        H_time = tr_builder.coeff; 
        temp_H = [real(reshape(H_time,1,[]));imag(reshape(H_time,1,[]))];
        data = reshape(temp_H,1,[]);
        fprintf(file_H_time,'%.6g ',data);
        fprintf(file_H_time,"\n");
        fprintf(file_power,'%.6g ',cs(1,tr_idx).par.pow_cb);
        fprintf(file_power,"\n");
        fprintf(file_gain,'%.6g ',cs(1,tr_idx).par.gain_cb);
        fprintf(file_gain,"\n");
        fprintf(file_pg,'%.6f ',cs(1,tr_idx).par.pg);
        fprintf(file_pg,"\n");
        fprintf(file_sangles,'%.6g ',cs(1,tr_idx).par.asD_cb);
        fprintf(file_sangles,'%.6g ',cs(1,tr_idx).par.asA_cb);
        fprintf(file_sangles,'%.6g ',cs(1,tr_idx).par.esD_cb);
        fprintf(file_sangles,'%.6g ',cs(1,tr_idx).par.esA_cb);
        fprintf(file_sangles,"\n");

        fprintf(file_angles,'%.6g ',cs(1,tr_idx).par.AoD_cb);
        fprintf(file_angles,'%.6g ',cs(1,tr_idx).par.AoA_cb);
        fprintf(file_angles,'%.6g ',cs(1,tr_idx).par.EoD_cb);
        fprintf(file_angles,'%.6g ',cs(1,tr_idx).par.EoA_cb);
        fprintf(file_angles,"\n");
    end

    %写入频域数据
    
    for tr_idx = 1 : n_tr
        tr_builder = cs(1,tr_idx);
        H_freq = tr_builder.fr(BW,nSub);
        temp_H = [real(reshape(H_freq,1,[]));imag(reshape(H_freq,1,[]))];
        data = reshape(temp_H,1,[]);
        fprintf(file_H_freq,'%.6g ',data);
        fprintf(file_H_freq,"\n");
    end

end

fclose(file_loc);
fclose(file_H_time);
fclose(file_power);
fclose(file_gain);
fclose(file_pg);
fclose(file_H_freq);
fclose(file_log);
fclose(file_angles);
fclose(file_sangles);


result = "setsumi";
% cs = split_tx( cm, {1:32,65:96,129:160} );          % Split sectors for Antenna configuration 1
% c{1,1} = qf.reshapeo( cs, [ no_rx, scene.no_tx*3, no_freq ] );
% cs = split_tx( cm, {33:64,97:128,161:192} );         % Split sectors for Antenna configuration 2
% c{2,1} = qf.reshapeo( cs, [ no_rx, scene.no_tx*3, no_freq ] );
% 
% c_pol1 = c{1,1};
% c_pol2 = c{2,1};
% H_data1 = c_pol1(1,1).fr(10e6,17);
% pg = c_pol1(1,1).par.pg_parset;
% H_data1 = H_data1 / sqrt(10.^(0.1*pg));
% H_data1 = c_pol1(1,1).coeff;
% temp_H1 = [real(reshape(H_data1,1,[]));imag(reshape(H_data1,1,[]))];
% H_data2 = c_pol2(1,1).fr(10e6,17);
% pg = c_pol2(1,1).par.pg_parset;
% H_data2 = H_data2 / sqrt(10.^(0.1*pg));
% H_data2 = c_pol2(1,1).coeff;
% temp_H2 = [real(reshape(H_data2,1,[]));imag(reshape(H_data2,1,[]))];
% fileH = fopen('H5.txt','w');
% data1 = reshape(temp_H1,1,[]);
% data2 = reshape(temp_H2,1,[]);
% fprintf(fileH,'%f ',data1);
% fprintf(fileH,'%f ',data2);
% fprintf('\n');
% fclose(fileH);







