import os,sys
sys.path.append(os.getcwd())
from setsumi_env.BM_functions import *
from setsumi_env.process import *
from setsumi_env.utils import *
import numpy as np
import re
import pandas as pd
from collections import Counter
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import os

def get_HV_diff(TPs):
    TPs_diff = np.zeros(11)
    for i in range(0,TPs.shape[0]):
        TP_HV = TPs[i,:,0:4]
        TP_VH = TPs[i,:,4:8]
        TP_diff = np.max(TP_HV,axis=-1)/np.max(TP_VH,axis=-1)
        TP_diff_average = np.average(TP_diff)
        TPs_diff[i] = TP_diff_average
    return TPs_diff

def get_panel2nbeam_diff(TPs):
    TPs_diff = np.zeros(11)
    for i in range(0,TPs.shape[0]):
        TP_panel = TPs[i,:,0:8]
        TP_nbeam = TPs[i,:,10:14]
        TP_diff = np.max(TP_panel,axis=-1)/np.max(TP_nbeam,axis=-1)
        TP_diff_average = np.average(TP_diff)
        TPs_diff[i] = TP_diff_average
    return TPs_diff

def draw_tp(sim_par):
    SNRs = sim_par.SNRs
    result = sim_par.result
    Data_dir = sim_par.Data_dir
    action_ue_all = result['action']
    TPs_ue_all = result['TPs']

    test_TP = np.array(TPs_ue_all)
    TPs1 = test_TP[:,:,0:4]
    TPs2 = test_TP[:,:,4:8]
    TPs3 = test_TP[:,:,8:10]
    TPs4 = test_TP[:,:,10:14]
    TPs5 = test_TP[:,:,0:10]
    TPs6 = test_TP[:,:,0:8]

    TPs1_max = np.max(TPs1,axis=-1)
    TPs1_average = np.average(TPs1_max,axis=-1)
    TPs2_max = np.max(TPs2,axis=-1)
    TPs2_average = np.average(TPs2_max,axis=-1)
    TPs3_max = np.max(TPs3,axis=-1)
    TPs3_average = np.average(TPs3_max,axis=-1)
    TPs4_max = np.max(TPs4,axis=-1)
    TPs4_average = np.average(TPs4_max,axis=-1)
    TPsmy_max = np.zeros_like(TPs1_max)
    for i in range(0,len(SNRs)):
        TP_my = test_TP[i]
        action_list = action_ue_all[i]
        for j in range(0,len(action_list)):
            TPsmy_max[i,j] = test_TP[i,j,action_list[j]]
    TPsmy_average = np.average(TPsmy_max,axis=-1)

    TPs5_max = np.max(TPs5,axis=-1)
    TPs5_average = np.average(TPs5_max,axis=-1)

    TPs6_max = np.max(TPs6,axis=-1)
    TPs6_average = np.average(TPs6_max,axis=-1)

    draw = draw_line('average TP in '+Data_dir,'UE','TP')
    draw.add_line(SNRs,TPs1_average,name="HV")
    draw.add_line(SNRs,TPs2_average,name="VH")
    draw.add_line(SNRs,TPs3_average,name="single Beam")
    draw.add_line(SNRs,TPs4_average,name="multile Beam")
    draw.add_line(SNRs,TPsmy_average,name="best action")
    draw.add_line(SNRs,TPs5_average,name="HV and VH and single Beam")
    draw.add_line(SNRs,TPs6_average,name="HV and VH")

    fig = draw.get_fig()
    return fig

def draw_action(sim_par):
    result = sim_par.result
    nUE = sim_par.nUE
    SNRs = sim_par.SNRs
    Data_dir = sim_par.Data_dir
    TPs_ue_all = result['TPs']
    TPs_ue_action = np.zeros((TPs_ue_all.shape[0],TPs_ue_all.shape[1],4))
    TPs_ue_action[:,:,0] = np.max(TPs_ue_all[:,:,0:4],axis=-1)
    TPs_ue_action[:,:,1] = np.max(TPs_ue_all[:,:,4:8],axis=-1)
    TPs_ue_action[:,:,2] = np.max(TPs_ue_all[:,:,8:10],axis=-1)
    TPs_ue_action[:,:,3] = np.max(TPs_ue_all[:,:,10:14],axis=-1)

    TP_ue1 = list()
    TP_ue2 = list()
    TP_ue3 = list()
    TP_ue4 = list()
    TP_ue5 = list()
    TP_ue6 = list()
    for i in range(0,TPs_ue_all.shape[0]):
        TP_ue1.append(0)
        TP_ue2.append(0)
        TP_ue3.append(0)
        TP_ue4.append(0)
        TP_ue5.append(0)
        TP_ue6.append(0)
        for j in range(0,TPs_ue_all.shape[1]):
            TP_data = TPs_ue_action[i,j]
            idx = np.where(TP_data == np.max(TP_data))[0]
            idx_in = [i in idx for i in range(0,4)]
            TP_ue1[i] = TP_ue1[i] + idx_in[0]/nUE
            TP_ue2[i] = TP_ue2[i] + idx_in[1]/nUE
            TP_ue3[i] = TP_ue3[i] + idx_in[2]/nUE
            TP_ue4[i] = TP_ue4[i] + idx_in[3]/nUE
            TP_ue5[i] = TP_ue5[i] + np.sum(idx_in[0:3],dtype=bool)/nUE
            TP_ue6[i] = TP_ue6[i] + np.sum(idx_in[0:2],dtype=bool)/nUE

    draw = draw_line('ue num of action in '+Data_dir,'SNR','num')
    draw.add_line(SNRs,TP_ue1,name="HV")
    draw.add_line(SNRs,TP_ue2,name="VH")
    draw.add_line(SNRs,TP_ue3,name="single Beam")
    draw.add_line(SNRs,TP_ue4,name="multile Beam")
    draw.add_line(SNRs,np.ones_like(sim_par.SNRs),name="best action")
    draw.add_line(SNRs,TP_ue5,name="HV and VH and single Beam")
    draw.add_line(SNRs,TP_ue6,name="HV and VH")

    fig = draw.get_fig()
    return fig

if __name__ == '__main__':
    dir_path = "./Data/scenario_test2/"
    file_TP= open(dir_path+'average_TP.html', 'w')
    file_action = open(dir_path+'action.html', 'w')
    for root,dirs,files in os.walk(dir_path):
            if len(files) > 0 and root != dir_path:
                Data_dir = root + '/'
                sim_par = Sim_Log(Data_dir)
                sim_par.get_result()
                fig_TP = draw_tp(sim_par)
                file_TP.write(fig_TP.to_html(full_html=False, include_plotlyjs='cdn'))
                fig_action = draw_action(sim_par)
                file_action.write(fig_action.to_html(full_html=False, include_plotlyjs='cdn'))