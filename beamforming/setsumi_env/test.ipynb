{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'matlab'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 3\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>rt\u001b[39;00m \u001b[38;5;21;01mos\u001b[39;00m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mnumpy\u001b[39;00m\n\u001b[0;32m----> 3\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mmatlab\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mengine\u001b[39;00m\n\u001b[1;32m      4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mBM_functions\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;241m*\u001b[39m\n\u001b[1;32m      5\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mpre_process\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;241m*\u001b[39m\n", "\u001b[0;31mModuleNotFoundError\u001b[0m: No module named 'matlab'"]}], "source": ["import os\n", "import numpy\n", "import matlab.engine\n", "from BM_functions import *\n", "from pre_process import *\n", "from analyze import *\n", "from utils import *\n", "import numpy as np\n", "import re\n", "import pandas as pd\n", "from collections import Counter\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "import time\n", "import os\n", "import datetime"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["eng = matlab.engine.start_matlab()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["base_path = './Data/test4/'\n", "set_los = False\n", "ue_num = 2"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-01-04 17:24:52\n", "run matlab begin at UMi_nfloor_25\n", "\n", "bs_isd =\n", "\n", "   200\n", "\n", "Setting RX LOS state correlation distance to 50 m\n", "Parameters   [oooooooooooooooooooooooooooooooooooooooooooooooooo]     1 seconds\n", "Channels     [oooooooooooooooooooooooooooooooooooooooooooooooooo]     1 seconds\n", "run matlab finish at UMi_nfloor_25\n", "2023-01-04 17:25:23\n", "process begin at UMi_nfloor_25\n", "./Data/test4/UMi_nfloor_25/\n", "pre process begin\n", "setsumi load sim log OK\n", "5489\n", "setsumi load sim log OK\n", "5489\n", "2\n", "pos process begin\n", "SNR -10 process OK\n", "SNR -5 process OK\n", "SNR 0 process OK\n", "SNR 5 process OK\n", "SNR 10 process OK\n", "SNR 15 process OK\n", "SNR 20 process OK\n", "SNR 25 process OK\n", "SNR 30 process OK\n", "SNR 35 process OK\n", "SNR 40 process OK\n", "all process finish\n", "\n", "process finish at UMi_nfloor_25\n", "2023-01-04 17:25:24\n", "2023-01-04 17:25:24\n", "run matlab begin at UMi_nfloor_40\n", "\n", "bs_isd =\n", "\n", "   200\n", "\n", "Setting RX LOS state correlation distance to 50 m\n", "Parameters   [oooooooooooooooooooooooooooooooooooooooooooooooooo]     0 seconds\n", "Channels     [oooooooooooooooooooooooooooooooooooooooooooooooooo]     1 seconds\n", "run matlab finish at UMi_nfloor_40\n", "2023-01-04 17:25:51\n", "process begin at UMi_nfloor_40\n", "./Data/test4/UMi_nfloor_40/\n", "pre process begin\n", "setsumi load sim log OK\n", "2951115605\n", "setsumi load sim log OK\n", "2951115605\n", "2\n", "pos process begin\n", "SNR -10 process OK\n", "SNR -5 process OK\n", "SNR 0 process OK\n", "SNR 5 process OK\n", "SNR 10 process OK\n", "SNR 15 process OK\n", "SNR 20 process OK\n", "SNR 25 process OK\n", "SNR 30 process OK\n", "SNR 35 process OK\n", "SNR 40 process OK\n", "all process finish\n", "\n", "process finish at UMi_nfloor_40\n", "2023-01-04 17:25:52\n"]}], "source": ["base_path = './Data/test4/'\n", "set_los = False\n", "ue_num = 2\n", "#run UMi test\n", "scenario_name = '3GPP_38.901_UMi'\n", "\n", "# floors = [5,10,15,20,25,30,35,40]\n", "floors = [25,40]\n", "isd = 200\n", "indoor = 1\n", "for nfloor in floors:\n", "    name = 'UMi_nfloor_'+str(nfloor)\n", "    print(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))\n", "    print(\"run matlab begin at \"+name)\n", "    scenario_path = base_path+name+\"/\"\n", "    os.makedirs(scenario_path,exist_ok=True)\n", "    result = eng.run(scenario_path,scenario_name,ue_num,indoor,set_los,int(nfloor),float(200))\n", "    print(\"run matlab finish at \"+name)\n", "    print(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))\n", "    print(\"process begin at \"+name)\n", "    all_process(scenario_path)\n", "    print(\"process finish at \"+name)\n", "    print(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-01-04 17:26:11\n", "run matlab begin at UMi_nfloor_5\n", "\n", "bs_isd =\n", "\n", "    25\n", "\n", "Setting RX LOS state correlation distance to 50 m\n", "Parameters   [oooooooooooooooooooooooooooooooooooooooooooooooooo]     0 seconds\n", "Channels     [oooooooooooooooooooooooooooooooooooooooooooooooooo]     1 seconds\n", "run matlab finish at UMi_nfloor_5\n", "2023-01-04 17:26:38\n", "process begin at UMi_nfloor_5\n", "./Data/test4/UMi_nfloor_5/\n", "pre process begin\n", "setsumi load sim log OK\n", "1509194975\n", "pos process begin\n", "SNR -10 process OK\n", "SNR -5 process OK\n", "SNR 0 process OK\n", "SNR 5 process OK\n", "SNR 10 process OK\n", "SNR 15 process OK\n", "SNR 20 process OK\n", "SNR 25 process OK\n", "SNR 30 process OK\n", "SNR 35 process OK\n", "SNR 40 process OK\n", "all process finish\n", "\n", "process finish at UMi_nfloor_5\n", "2023-01-04 17:26:39\n", "2023-01-04 17:26:39\n", "run matlab begin at UMi_nfloor_5\n", "\n", "bs_isd =\n", "\n", "    40\n", "\n", "Setting RX LOS state correlation distance to 50 m\n", "Parameters   [oooooooooooooooooooooooooooooooooooooooooooooooooo]     0 seconds\n", "Channels     [oooooooooooooooooooooooooooooooooooooooooooooooooo]     1 seconds\n", "run matlab finish at UMi_nfloor_5\n", "2023-01-04 17:27:05\n", "process begin at UMi_nfloor_5\n", "./Data/test4/UMi_nfloor_5/\n", "pre process begin\n", "setsumi load sim log OK\n", "1950763015\n", "pos process begin\n", "SNR -10 process OK\n", "SNR -5 process OK\n", "SNR 0 process OK\n", "SNR 5 process OK\n", "SNR 10 process OK\n", "SNR 15 process OK\n", "SNR 20 process OK\n", "SNR 25 process OK\n", "SNR 30 process OK\n", "SNR 35 process OK\n", "SNR 40 process OK\n", "all process finish\n", "\n", "process finish at UMi_nfloor_5\n", "2023-01-04 17:27:06\n"]}], "source": ["#run UMa test\n", "scenario_name = '3GPP_38.901_UMa'\n", "\n", "# nfloor = [5,10,15,20,25,30,25,40]\n", "nfloor = 5\n", "# isds = [200,500,1000,1500,2000]\n", "isds = [1500,2000]\n", "indoor = 0\n", "for isd in floors:\n", "    name = 'UMi_nfloor_'+str(nfloor)\n", "    print(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))\n", "    print(\"run matlab begin at \"+name)\n", "    scenario_path = base_path+name+\"/\"\n", "    os.makedirs(scenario_path,exist_ok=True)\n", "    result = eng.run(scenario_path,scenario_name,ue_num,indoor,set_los,int(nfloor),float(isd))\n", "    print(\"run matlab finish at \"+name)\n", "    print(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))\n", "    print(\"process begin at \"+name)\n", "    all_process(scenario_path)\n", "    print(\"process finish at \"+name)\n", "    print(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))"]}], "metadata": {"kernelspec": {"display_name": "meiziy", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}