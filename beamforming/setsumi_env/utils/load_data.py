import os,sys
sys.path.append(os.getcwd())

import numpy as np
import pandas as pd

from setsumi_env.utils import *
import re

class Sim_Log():
    def __init__(self,Data_dir=None):
        self.seed = 0
        self.nCell = 21
        self.nBS = int(self.nCell/3)
        self.nUE = 1
        self.H_ants = 8
        self.V_ants = 2
        self.Ants_all = self.H_ants*self.V_ants
        self.Ants_all_2pol = self.Ants_all*2
        self.HSpacing = 0.5
        self.VSpacing = 0.5
        self.UE_Port = 4
        self.time_unit = 20 #单位ms
        self.time = 1
        self.nSub = 17
        self.path = ""

        self.cm_name = np.zeros((self.nUE,self.nCell))
        self.cm_npath = np.zeros((self.nUE,self.nCell))
        self.cm_no_snap = np.zeros((self.nUE,self.nCell))

        self.cs_name = list()
        self.cs_npath = list()
        self.cs_no_snap = list()
        self.cs_name = self.cs_name.append("default")
        self.cs_npath = self.cs_npath.append(24)
        self.cs_no_snap = self.cs_no_snap.append(1)
        self.Data_dir = ""
        self.multiple_cell = True
        self.schedule = False
        self.schedule_cell = self.nCell = 21

        if Data_dir is not None:
            self.analyze_log(Data_dir)
    
    def analyze_log(self,Data_dir):
        self.Data_dir = Data_dir
        log_path = Data_dir + "/log.txt"
        with open(log_path, "r") as f:
            log = f.read()
        print("setsumi load sim log OK")
        seed = re.compile(r'random seed is (\d+)\n').findall(log)
        if(len(seed) > 0 and seed[0].isdigit()): self.seed = int(seed[0])

        nCell = re.compile(r'BS Cell number is (\d+)\n').findall(log)
        if(len(nCell) > 0 and nCell[0].isdigit()): 
            self.nCell = int(nCell[0])
            self.nBS = int(self.nCell/3)
            self.schedule_cell = self.nCell

        nUE = re.compile(r'UE number is (\d+)\n').findall(log)
        if(len(nUE) > 0 and nUE[0].isdigit()): self.nUE = int(nUE[0])
        
        schedule = re.compile(r'BS schedule is (\S+)\n').findall(log)
        if(len(schedule) > 0): 
            self.schedule = True
            self.schedule_cell = 1
            
        H_ants = re.compile(r'H_ants is (\d+)\n').findall(log)
        if(len(H_ants) > 0 and H_ants[0].isdigit()): self.H_ants = int(H_ants[0])
        
        V_ants = re.compile(r'V_ants is (\d+)\n').findall(log)
        if(len(V_ants) > 0 and V_ants[0].isdigit()): self.V_ants = int(V_ants[0])

        UE_Port = re.compile(r'UE Port is (\d+)\n').findall(log)
        if(len(UE_Port) > 0 and UE_Port[0].isdigit()): self.UE_Port = int(UE_Port[0])

        nSub = re.compile(r'nSub is (\d+)\n').findall(log)
        if(len(nSub) > 0 and nSub[0].isdigit()): self.nSub = int(nSub[0])

        time_unit = re.compile(r'sim time unit is (\d+)\n').findall(log)
        if(len(time_unit) > 0 and time_unit[0].isdigit()): self.time_unit = int(time_unit[0])

        time = re.compile(r'sim time number is (\d+)\n').findall(log)
        if(len(time) > 0 and time[0].isdigit()): self.time = int(time[0])
        
        ue_div_part = re.compile(r'UE are divided into (\d+) parts\n').findall(log)
        if(len(ue_div_part) > 0 and ue_div_part[0].isdigit()): self.ue_div_part = int(ue_div_part[0])
        else:   self.ue_div_part = 1
        
        ue_part_num = re.compile(r'UE are divided by (\d+)\n').findall(log)
        if(len(ue_part_num) > 0 and ue_part_num[0].isdigit()): self.ue_part_num = int(ue_part_num[0])
        else:   self.ue_part_num = self.nUE

        bs_loc = re.compile(r'bs location: x: (\S+) y: (\S+) z: (\S+)').findall(log)
        self.bs_loc = np.array(bs_loc).astype(float)

        cms_info = re.compile(r'sim cm (\d+) name is (\S+),npath is (\d+),no_snap is (\d+)\n').findall(log)
        part_cms = np.ceil((len(cms_info))/self.ue_div_part)
        # cm_spilt_info = re.compile(r'sim ue of cm spilt idx (\d+)\n').findall(log)
        if(len(cms_info)) > 0:
            cm_name = np.zeros((self.nUE,self.nBS),dtype=object)
            cm_npath = np.zeros((self.nUE,self.nBS),dtype=int)
            cm_no_snap = np.zeros((self.nUE,self.nBS),dtype=int)
            for i in range(0,len(cms_info)):
                cm_info = cms_info[i]
                name_data = cm_info[1]
                ue_info = re.compile(r'[\S+]Tx(\d+)_Rx(\d+)').findall(name_data)
                if not len(ue_info):
                    ue_info = re.compile(r'[\S+](\d+)_ue(\d+)').findall(name_data)
                cell = int(ue_info[0][0])-1
                ue = int(ue_info[0][1])-1 + int(np.floor(i/part_cms)*self.ue_part_num)
                cm_name[ue,cell] = name_data
                cm_npath[ue,cell] = int(cm_info[2])
                cm_no_snap[ue,cell] = int(cm_info[3])
            self.cm_name = cm_name
            self.cm_npath = cm_npath
            self.cm_no_snap = cm_no_snap

        css_info = re.compile(r'sim cs (\d+) name is (\S+),npath is (\d+),no_snap is (\d+)\n').findall(log)
        # cs_spilt_info = re.compile(r'sim ue of cs spilt idx (\d+)\n').findall(log)
        cs_name = list()
        cs_npath = list()
        cs_no_snap = list()
        if(len(css_info)) > 0:
            for i in range(0,len(css_info)):
                cs_info = css_info[i]
                cs_name.append(cs_info[1])
                cs_npath.append(int(cs_info[2]))
                cs_no_snap.append(int(cs_info[3]))
            self.cs_name = cs_name
            self.cs_npath = cs_npath
            self.cs_no_snap = cs_no_snap

        self.Ants_all = self.H_ants*self.V_ants
        self.Ants_all_2pol = self.Ants_all*2
        self.Cell_UE = self.nCell*self.nUE

def load_data(path,sim_par,freq=True):
    nUE = sim_par.nUE
    if sim_par.multiple_cell:
        nCell = sim_par.schedule_cell
    else:
        nCell = 1
    Cell_UE = nUE*nCell

    if not freq:
        npath = sim_par.cs_npath
    else:
        npath = [sim_par.nSub]*Cell_UE

    with open(path, 'r') as file:
        print("open file OK")
        Datas_all = list()
        for i in range(0,Cell_UE):
            nSub = npath[i]
            file_data = file.readline()
            target_data = re.split('\s+',file_data.strip())
            for index,item in enumerate(target_data):
                target_data[index] = float(item)
            time = sim_par.time
            H_ants = sim_par.H_ants
            V_ants = sim_par.V_ants
            Port = sim_par.UE_Port
            Ants_all_2pol = sim_par.Ants_all_2pol
            # print("load UE "+str(i))
            data = np.array(target_data,dtype=np.float32)
            data = data.reshape(time,nSub,Ants_all_2pol,Port,2)
            data = data[:,:,:,:,0] + 1j*data[:,:,:,:,1]
            data = data.reshape(time,nSub,H_ants,V_ants,2,Port)
            data = data.transpose(0,1,5,4,3,2)
            data = data.reshape(time,nSub,Port,Ants_all_2pol)
            Datas_all.append(data)
            # data = data.transpose(0,2,1,3,4,5)
            # print("add UE "+str(i))
    file.close()

    print("add file ok")
    Datas_cell_ue = list()
    for i in range(0,sim_par.nUE):
        Datas_ue = list()
        for j in range(0,sim_par.schedule_cell):
            idx = j*sim_par.nUE + i
            Datas_ue.append(Datas_all[idx])
        Datas_cell_ue.append(Datas_ue)

    return Datas_cell_ue

def load_gain(path,sim_par):
    with open(path, 'r') as file:
        Datas_all = list()
        for i in range(0,sim_par.schedule_cell):
            file_data = file.readline()
            target_data = re.split('\s+',file_data.strip())
            for index,item in enumerate(target_data):
                target_data[index] = float(item)
            Datas_all.append(np.array(target_data))
    file.close()
    
    Datas_cell_ue = list()
    for i in range(0,sim_par.nUE):
        Datas_ue = list()
        for j in range(0,sim_par.schedule_cell):
            idx = j*sim_par.nUE + i
            Datas_ue.append(Datas_all[idx])
        Datas_cell_ue.append(Datas_ue)

    return Datas_cell_ue

def choose_best_cell(data,sim_par):
    cell_ue = list()
    sinr_all = list()
    rsrps_all = list()
    for i in range(0,sim_par.nUE):
        rsrps = list()
        for j in range(0,sim_par.schedule_cell):
            tmp = data[i][j] #choose ue
            tmp = tmp[0,:,:,0] #time*npath*port*ants
            rsrp = np.sum(np.abs(tmp)*np.abs(tmp)) / 4 # Divide by 4 UE ant
            rsrps.append(rsrp)
        rsrps = np.array(rsrps)
        sinr_ue =10*np.log10(rsrps/(np.sum(rsrps)-rsrps+1e-50))
        # print("ue is %d,sinr is %.2f at cell %d" %(i,np.max(sinr_ue),np.argmax(sinr_ue)))
        cell_ue.append(np.argmax((rsrps)))
        sinr_all.append(sinr_ue)
        rsrps_all.append(rsrps)
    cell_ue = np.array(cell_ue)
    sinr_all = np.array(sinr_all)
    return cell_ue,sinr_all,rsrps_all

def pre_process(Data_dir,save_time=True,):
    sim_par = Sim_Log(Data_dir)
    print(sim_par.seed)
    print(sim_par.nUE)
    data_time = load_data(Data_dir+"H_time.txt",sim_par,freq=False)
    print("load time OK")
    data_freq = load_data(Data_dir+"H_freq.txt",sim_par)
    print("load freq OK")
    cell_ue,sinr_all,rsrps_all = choose_best_cell(data_time,sim_par)
    bs_ue = (cell_ue/3).astype(int)
    # print(Sim_par.cm_name[(np.arange(0,len(bs_ue)),bs_ue)])

    # power = load_gain(Data_dir+"power.txt",par)
    # gain = load_gain(Data_dir+"gain.txt",par)

    loc = np.loadtxt(Data_dir+"loc.txt")
    loc = loc.reshape(sim_par.nUE,sim_par.time,3)

    pg = np.loadtxt(Data_dir+"pg.txt")
    pg = pg.reshape(sim_par.schedule_cell,sim_par.nUE,sim_par.time)
    pg = pg.transpose(1,0,2)

    H_time = list()
    H_freq = list()
    H_pg = list()
    for i in range(0,sim_par.nUE):
        if(save_time):
            H_time.append(data_time[i][cell_ue[i]])
        H_freq.append(data_freq[i][cell_ue[i]])
        H_pg.append(pg[i][cell_ue[i]])
    data_redo = dict()
    data_redo['H_time'] = H_time 
    data_redo['H_freq'] = H_freq
    data_redo['H_pg'] = H_pg
    data_redo['cell_ue'] = cell_ue
    data_redo['bs_ue'] = bs_ue
    data_redo['loc'] = loc
    data_redo['sinr_all'] = sinr_all
    data_redo['rsrps_all'] = rsrps_all

    np.savez(Data_dir+"data_redo",data_redo)
    
def pre_process_batch(dir_path):
    for root,dirs,files in os.walk(dir_path):
        if len(files) > 0 and root != dir_path:
            Data_dir = root + '/'
            if not os.path.exists(Data_dir+"data_redo.npz"):
                print("process begin at dir "+Data_dir)
                pre_process(Data_dir)
                print("process finish")
            else:
                print("already exit")
                
if __name__ == '__main__':
    Data_dir = '/data/BM_data/28GHZ/UMa_4000t_o/'
    print(Data_dir)
    print("pre process begin")
    sim_par = Sim_Log(Data_dir)
    print(sim_par.seed)
    # if not os.path.exists(Data_dir+"data_redo.npz"):
    pre_process(Data_dir,save_time=False)