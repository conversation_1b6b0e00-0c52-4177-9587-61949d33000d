# -*- encoding:utf-8 -*-
import numpy as np

cqi_table = {}
cqi_table[1] = ((1, 'QPSK', 78, 0.1523),
                (2, 'QPSK', 120, 0.2344),
                (3, 'QPSK', 193, 0.3770),
                (4, 'QPSK', 308, 0.6016),
                (5, 'QPSK', 449, 0.8770),
                (6, 'QPSK', 602, 1.1758),
                (7, '16QAM', 378, 1.4766),
                (8, '16QAM', 490, 1.9141),
                (9, '16QAM', 616, 2.4063),
                (10, '64QAM', 466, 2.7305),
                (11, '64QAM', 567, 3.3223),
                (12, '64QAM', 666, 3.9023),
                (13, '64QAM', 772, 4.5234),
                (14, '64QAM', 873, 5.1152),
                (15, '64QAM', 948, 5.5547),)
cqi_table[2] = ((1, 'QPSK', 78, 0.1523),
                (2, 'QPSK', 193, 0.3770),
                (3, 'QPSK', 449, 0.8770),
                (4, '16QAM', 378, 1.4766),
                (5, '16QAM', 490, 1.9141),
                (6, '16QAM', 616, 2.4063),
                (7, '64QAM', 466, 2.7305),
                (8, '64QAM', 567, 3.3223),
                (9, '64QAM', 666, 3.9023),
                (10, '64QAM', 772, 4.5234),
                (11, '64QAM', 873, 5.1152),
                (12, '256QAM', 711, 5.5547),
                (13, '256QAM', 797, 6.2266),
                (14, '256QAM', 885, 6.9141),
                (15, '256QAM', 948, 7.4063),)
cqi_table[3] = ((1, 'QPSK', 30, 0.0586),
                (2, 'QPSK', 50, 0.0977),
                (3, 'QPSK', 78, 0.1523),
                (4, 'QPSK', 120, 0.2344),
                (5, 'QPSK', 193, 0.3770),
                (6, 'QPSK', 308, 0.6016),
                (7, 'QPSK', 449, 0.8770),
                (8, 'QPSK', 602, 1.1758),
                (9, '16QAM', 378, 1.4766),
                (10, '16QAM', 490, 1.9141),
                (11, '16QAM', 616, 2.4063),
                (12, '64QAM', 466, 2.7305),
                (13, '64QAM', 567, 3.3223),
                (14, '64QAM', 666, 3.9023),
                (15, '64QAM', 772, 4.5234),)


class Cqi2Sinr:
    def __init__(self, table_index=1):
        self.cqi2se = np.array([e[-1] for e in cqi_table[table_index]])

    def __call__(self, CQI):
        # 2^se-1 or (2**se-1)*(-np.log2(5*bler)/1.5)
        CQI = np.clip(CQI,1,15)
        se = 2**self.cqi2se[CQI-1]-1
        return 10*np.log10(se)
    
class Sinr2Cqi:
    def __init__(self, table_index=1):
        self.cqi2se = np.array([e[-1] for e in cqi_table[table_index]])
        
    def __call__(self, sinr):
        sinr = 10**(sinr/10)
        se = np.log2(sinr+1)
        CQI = np.searchsorted(self.cqi2se,se)
        return CQI


if __name__ == '__main__':
    cqi2sinr = Cqi2Sinr()
    sinr2cqi = Sinr2Cqi()
    cqi = np.arange(1, 15)
    # cqi =  [ 0 ,2 , 3 , 5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15]

