import numpy as np

mcs_index_table = {}
# Table 5.1.3.1-1: MCS index table 1 for PDSCH
mcs_index_table[1] = np.array([0.2344, 0.3066, 0.3770, 0.4902, 0.6016, 0.7402, 0.8770, 1.0273, 1.1758, 1.3262, 1.3281, 1.4766, 1.6953, 1.9141, 2.1602,
                               2.4063, 2.5703, 2.5664, 2.7305, 3.0293, 3.3223, 3.6094, 3.9023, 4.2129, 4.5234, 4.8164, 5.1152, 5.3320, 5.5547])

# Table 5.1.3.1-2: MCS index table 2 for PDSCH
mcs_index_table[2] = np.array([0.2344, 0.3770, 0.6016, 0.8770, 1.1758, 1.4766, 1.6953, 1.9141, 2.1602, 2.4063, 2.5703, 2.7305, 3.0293, 3.3223,
                               3.6094, 3.9023, 4.2129, 4.5234, 4.8164, 5.1152, 5.3320, 5.5547, 5.8906, 6.2266, 6.5703, 6.9141, 7.1602, 7.4063])

# Table 5.1.3.1-3: MCS index table 3 for PDSCH
mcs_index_table[3] = np.array([0.0586, 0.0781, 0.0977, 0.1250, 0.1523, 0.1934, 0.2344, 0.3066, 0.3770, 0.4902, 0.6016, 0.7402, 0.8770, 1.0273, 1.1758,
                               1.3281, 1.4766, 1.6953, 1.9141, 2.1602, 2.4063, 2.5664, 2.7305, 3.0293, 3.3223, 3.6094, 3.9023, 4.2129, 4.5234])


class Sinr2Se:
    def __init__(self, bler=0.1) -> None:
        self.bler = bler
        self.val = -np.log(5*self.bler)/1.5

    def __call__(self, sinr):
        return np.log2(1+sinr/self.val)


class Se2Mcs:
    def __init__(self, table_index=1) -> None:
        self.table = mcs_index_table[table_index].copy()
        self.table[-1] = 1e9

    def __call__(self, se):
        return np.searchsorted(self.table, se)


class Sinr2Mcs:
    def __init__(self, table_index=1, bler=0.1) -> None:
        self.table = (2**mcs_index_table[table_index]-1)*-np.log(5*bler)/1.5
        self.table[-1] = 1e9

    def __call__(self, sinr):
        return np.searchsorted(self.table, sinr)


if __name__ == '__main__':
    print(Sinr2Se()(3000))
    print(Se2Mcs()([0, 0.2345, 0.3067, 0.5, 10]))
    print(Sinr2Mcs()([1, 10, 100, 1000]))
