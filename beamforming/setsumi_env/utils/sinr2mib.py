# -*- encoding:utf-8 -*-
import numpy as np


class SinrMibMapBase:
    def __init__(self, fn=__file__.replace('.py', '.npz')) -> None:
        self.map = {}
        for k, e in np.load(fn, allow_pickle=True).items():
            self.map[int(k)] = e.transpose()


class Sinr2Mib(SinrMibMapBase):
    def __init__(self, *args) -> None:
        super().__init__(*args)

    def __call__(self, sinr, qam=64):
        return self.map[qam][0, np.searchsorted(self.map[qam][1], sinr)]


class Mib2Sinr(SinrMibMapBase):
    def __init__(self, *args) -> None:
        super().__init__(*args)

    def __call__(self, sinr, qam=64):
        return self.map[qam][1, np.searchsorted(self.map[qam][0], sinr)]


if __name__ == '__main__':
    print(Sinr2Mib()( -60))
    # print(Sinr2Mib()(10*np.log10(9.32738075e-06)))
    print(Mib2Sinr()(0.99,qam=8))