from cmath import cos, sin
import numpy as np
# from torch import double
from math import pi
# from load_mat import check_consistency_with_mat

def generate_dft_beams(numVant=4, numHant=8, OS_V=1, OS_H=1):
    V_step = np.arange(-(numVant-1)/2, (numVant-1)/2 + 1, 1, dtype=np.float32).reshape((-1,1))
    V_beams = np.arange(-(numVant-1)/2, (numVant-1)/2 + 1, 1/OS_V, dtype=np.float32).reshape((-1,1))
    dft_beams_V = np.exp(np.dot(-1j*2*pi * V_step , V_beams.T / numVant)) 

    H_step = np.arange(-(numHant-1)/2, (numHant-1)/2 + 1, 1, dtype=np.float32).reshape((-1,1))
    H_beams = np.arange(-(numHant-1)/2, (numHant-1)/2 + 1, 1/OS_H, dtype=np.float32).reshape((-1,1))
    dft_beams_H = np.exp(np.dot(-1j*2*pi*H_step , H_beams.T / numHant))

    # kronecker        
    beams = np.kron(dft_beams_V,dft_beams_H)
 
    # import pdb;pdb.set_trace()
    return beams

if __name__ == '__main__':
    aa = generate_dft_beams(2,8,1,1)
    beammat =np.matrix([[-0.555570233019603 + 1j*0.831469612302545,
                        0.195090322016129 - 1j*0.980785280403230,
                        0.195090322016128 + 1j*0.980785280403230,
                        -0.555570233019602 - 1j*0.831469612302545,
                        0.831469612302545 + 1j*0.555570233019602,
                        -0.980785280403230 - 1j*0.195090322016128,
                        0.980785280403230 - 1j*0.195090322016129,
                        -0.831469612302545 + 1j*0.555570233019603,
                        -0.831469612302545 - 1j*0.555570233019603,
                        0.980785280403230 + 1j*0.195090322016129,
                        -0.980785280403230 + 1j*0.195090322016128,
                        0.831469612302545 - 1j*0.555570233019602,
                        -0.555570233019602 + 1j*0.831469612302545,
                        0.195090322016128 - 1j*0.980785280403230,
                        0.195090322016129 + 1j*0.980785280403230,
                        -0.555570233019603 - 1j*0.831469612302545]],dtype=np.complex128)
    Beams = aa
    len = 16
    RHO = np.zeros((len,len),dtype=np.float32)
    for m in range(0,len):
        for n in range(0,len):
            beam1 = Beams[:,m:m+1]
            beam2 = Beams[:,n:n+1].T
            # print(beam1.shape)
            rr = np.matmul(beammat,beammat.T)
            # print(rr.shape)
            RHO[m,n] = np.sum(beammat@beammat.H)/np.linalg.norm(beam1)/np.linalg.norm(beam2)
    print(np.matmul(beammat,beammat.H)/np.linalg.norm(beammat)/np.linalg.norm(beammat))
    # print(RHO)
    lll = beammat.squeeze(0)
    li = [(el*el) for el in lll.tolist()]
    print(sum(li))
    print(li)
    # import pdb;pdb.set_trace()

    # check_consistency_with_mat("./testcase/generate_dft_beam_4_8_1_1.mat", aa)
    
