import numpy as np
from math import pi

def hr2sinr(H ,R):
    IR = np.linalg.inv(R.transpose(2, 0, 1)) #nSc*nRx*nRx 201 求R的逆
    IR = IR.transpose(0, 2, 1) #nSc*nRx*nRx 210
    HR = np.inner(np.conj(H.transpose(2, 1, 0)), IR) #nSc nStrm nRx nRx 求H的共轭，并与IR计算得到内积
    HR = HR[:,:,0,:] #nSc nStrm nRx
    nStrm = H.shape[1]
    HRH =np.matmul(HR,H.transpose(2, 0, 1)) #nSc nStrm nStrm
    eye_data = np. expand_dims(np.eye(nStrm), 0).repeat(H.shape[2],0)
    IHRH = np.linalg.inv(HRH + eye_data)
    IHRH_diag = np.real(np.diagonal(IHRH,axis1 = 1,axis2 = 2)) #返回后面两维的对角线元素并取实部
    sinr = 1./IHRH_diag -1
    Wmmse = 1
    return sinr,Wmmse

def get_sinr(H,r=5.9858e-13):
    H = H.transpose(2,1,0)
    # r = 5.9858e-13
    # r = 5.9858e-13
    # r = 6.06e-13
    # r = 1
    # R = [[r,0,0,0],[0,r,0,0],[0,0,r,0],[0,0,0,r]]  
    R = np.eye(H.shape[0])*r 
    R = np.expand_dims(R,2)
    sinr,Wmmse = hr2sinr(H,R)
    sinr = np.clip(sinr,0.00001,1000000000)
    sinr = 10*np.log10(sinr)
    return sinr
