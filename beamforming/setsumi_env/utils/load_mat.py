import numpy as np
from scipy import io

def load_mat(fd):
    mat = io.loadmat(fd)
    got_array = mat[list(mat.keys())[-1]]
    return got_array

def check_consistency_with_mat(fd, arr):
    target = load_mat(fd).astype(arr.dtype)
    if np.array_equal(target, arr):
        print(f"PASS:{fd}")
    else:
        print(f"FAIL:{fd}")
    return np.array_equal(target, arr)

if __name__ == '__main__':
    # print(generate_dft_beams())
    load_mat("./testcase/generate_dft_beam_4_8_1_1.mat")
    # pass

