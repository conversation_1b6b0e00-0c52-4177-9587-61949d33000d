import os,sys
sys.path.append(os.getcwd())

import numpy as np
import pandas as pd
# from utils.generate_beam_pattern import generate_beam_pattern
# from utils.sinr2mib import Mib2Sinr, Sinr2Mib
# from utils.get_sinr import get_sinr
# from utils.generate_beam import generate_dft_beams
# from utils.PMI import PMI
# from utils.mcs2qam import Mcs2Qam
# from utils.mib2bler import Mib2B<PERSON>
# from utils.cqi2sinr import Sinr2Cqi
# from utils.throughput import TbCalc
import os,sys
sys.path.append(os.getcwd())
from setsumi_env.utils import *
import re

#global data
PMI_class = PMI()
sinr2mi = Sinr2Mib()
mi2sinr = Mib2Sinr()
mcs2qam = Mcs2Qam()
mi2bler = Mib2Bler()
sinr2cqi = Sinr2Cqi()
sinr2mcs_table = np.array([-5.23,-4.7,-3.7,-3.18,-2.45,-1.6,-0.88,-0.12,
                                        0.79,1.38,1.88,2.7,3.65,4.51,5.24,5.88,
                                        6.75,7.29,8.23,9.14,9.92,11.09,11.86,12.93,
                                        14.14,14.98,15.91,17.15])

'''
action space
notation: H_spilt,V_spilt,RI,Port
if Port > H_spilt*V_spilt*2 -> nBeam nBeam = Port/H_spilt/V_spilt/2
'''
# H_spilt,V_spilt,RI
# action_space = [(2,1,1,4),(2,1,2,4),\
#                 (2,1,3,4),(2,1,4,4),\
#                 (1,2,1,4),(1,2,2,4),\
#                 (1,2,3,4),(1,2,4,4),\
#                 (1,1,1,2),(1,1,2,2),\
#                 (1,1,1,4),(1,1,2,4),\
#                 (1,1,3,4),(1,1,4,4)] 

action_space = [(1,1,1,2),(1,1,2,2),\
                (2,1,1,4),(2,1,2,4),\
                (2,1,3,4),(2,1,4,4),\
                (1,2,1,4),(1,2,2,4),\
                (1,2,3,4),(1,2,4,4),\
                (2,2,1,8),(2,2,2,8),\
                (2,2,3,8),(2,2,4,8),\
                (4,1,1,8),(4,1,2,8),\
                (4,1,3,8),(4,1,4,8)] 

def cal_coma(srs_p1,srs_p2): # (nUE, nAnts)
    srs_p1 = np.expand_dims(srs_p1,-2)
    srs_p2 = np.expand_dims(srs_p2,-2)
    #!coma srs.H@srs 
    # srs_p1.H@srs_p1
    # Coma (nUE, nAnts ,nAnts)
    Coma_p1 = np.conj(srs_p1.transpose(0,2,1))@srs_p1     #srs.H@srs
    Coma_p2 = np.conj(srs_p2.transpose(0,2,1))@srs_p2     #srs.H@srs

    Tra_p1 = np.trace(Coma_p1,axis1=-2,axis2=-1)
    Tra_p2 = np.trace(Coma_p2,axis1=-2,axis2=-1)
    a1 = np.divide(Tra_p1,Tra_p1+Tra_p2+1e-50)
    a2 = np.divide(Tra_p2,Tra_p1+Tra_p2+1e-50)
    Coma = Coma_p1*np.expand_dims(a1,axis=(-1,-2)) + Coma_p2*np.expand_dims(a2,axis=(-1,-2))
    return Coma

# def sequential_simulate(init_coma,H,tti,mu):
    

def update_svd_Beam(Coma,n=None):
    s1,v1,d1 = np.linalg.svd(Coma)
    s1 = s1.T
    if n is not None:
        beam = np.transpose(s1[0:n+1],(1,0))
    else:
        beam = np.transpose(s1,(1,0))
    return beam
    

def update_Beam(Coma,Beams):
    """
    Coma nSub*Ants*Ants
    Coma_Long和全部可选的Beams计算全部可选的Beams的能量，从这些Beams选择能量最大和第二大的Beam返回(idx1, power1, idx2, power2)
    """
    Coma = Coma
    Beams = np.expand_dims(Beams,2) #nBeam*n_ant*1\

    Beam_ = np.expand_dims(Beams,0)
    Coma_ = np.expand_dims(Coma,1)
    Beam_power = np.conj(Beam_.transpose(0,1,3,2))@Coma_@Beam_ #Beam.H@Coma@Beam
    Beam_power = Beam_power.squeeze((-1,-2)) # 为每个UE从所有Beam中选择最大能量的一个
    
    Coma_norm = np.linalg.norm(Coma,axis=(-1,-2)) #nUE
    Beams_norm = np.linalg.norm(Beams,axis=(-1,-2)) #nBeam
    Beam_power = np.abs(Beam_power/np.expand_dims(Coma_norm,1)/np.expand_dims(Beams_norm,0)) #

    idx = np.argsort(Beam_power,kind='mergesort',axis=1)
    # power = np.sort(Beam_power,kind='mergesort',axis=1)
    idx = np.flip(idx,axis=1) #反转
    # power = np.flip(power,axis=1)
    power = Beam_power
    return idx,power

def get_PMI(H,N1,N2,layer): #ue_port*gnb_port
    O1 = (N1>1)*3+1
    O2 = (N2>1)*3+1
    assert(layer <= N1*N2*2)   
    codebook_origin = PMI_class.get_pmi_codebook(N1,N2,O1,O2,1,layer)
    H = np.expand_dims(H,(0,1,2,3)) #0,1,2,3,nSub,ue_port,max_gNB_port
    codebook = np.expand_dims(codebook_origin, axis=4)
    HP = H@codebook #0,1,2,3,ue_port,layer
    Layer_power = np.conj(HP.transpose(0,1,2,3,4,6,5))@HP
    HP_power = np.trace(np.abs(Layer_power),axis1=-1,axis2=-2)
    HP_power = np.sum(HP_power,axis = -1)

    idx = np.unravel_index(np.argmax(HP_power),HP_power.shape)
    P = codebook_origin[idx]
    max_power = HP_power[idx]
    lp = Layer_power[idx]
    return idx,P,max_power,lp

def cal_beam_method1(H,V_ants,H_ants):
    """_summary_

    Parameters
    ----------
    H : _type_
        _description_
    V_ants : _type_
        _description_
    H_ants : _type_
        _description_

    Returns
    -------
    Beam : _type_
        _description_
    power : _type_
        _description_
    beam_idx : _type_
        _description_
    """
    # input H nSub*Port*Ants
    # cal_param
    Ants_all = V_ants*H_ants
    Ants_all_2pol = 2*Ants_all
    # get srs data
    # H nport*nsub*Ants_all_2pol
    srs_pol1 = H[:,:,0:Ants_all]
    srs_pol2 = H[:,:,Ants_all:Ants_all_2pol]
    srs_pol1 = srs_pol1.reshape(-1,Ants_all)
    srs_pol2 = srs_pol2.reshape(-1,Ants_all) 
    # get short coma
    coma = cal_coma(srs_pol1,srs_pol2)
    coma = np.average(coma,axis=(0))
    # get long coma
    # direct average
    coma = np.expand_dims(coma,axis=0)
    # get Beam
    # dft beam from coma
    Beams = generate_dft_beams(V_ants,H_ants)
    beam_idx,power = update_Beam(coma,Beams)
    beam_idx = beam_idx[0]
    power = power[0]
    
    Beam = Beams[beam_idx]
    return Beam,power,beam_idx    

def cal_analog_matrix(Beam,V_ants,H_ants,V_split,H_split,Port):
    nBeam = int(Port/H_split/V_split/2) 
    Beam = Beam[0:nBeam]
    Ports_vector = generate_beam_pattern(V_ants,H_ants,V_split,H_split)
    if len(Beam.shape) == 1:
        Beam = np.expand_dims(Beam,0)
    Beam = Beam.transpose(1,0) #16*n
    Beam_Port = Beam*Ports_vector
    FRF = np.kron(np.eye(2),Beam_Port)
    FRF = FRF/np.sqrt(V_ants*H_ants*2)/np.sqrt(nBeam)*np.sqrt(Port)
    return FRF
    
def cal_digital_matrix_PMI(H,FRF,N1,N2,RI):
    """cal digital matrix based on codebook 
    http://www.sharetechnote.com/html/5G/5G_CSI_RS_Codebook.html

    Parameters
    ----------
    H : 3-D array of shape (nSub* UE_ants* Ants_all_2pol )
        Channel
    FRF : 2-D array of shape (Ants_all_2pol* nport )
        Analog precode matrix of BS
    N1 : int
        The number of antenna in horizontal direction
    N2 : int
        The number of antenna in vertical direction
    RI : int
        The number of layers

    Returns
    -------
    P : 2-D array of shape (nport* nstream )
        Digital precode matrix based on codebook of BS
    PMI : 1-D array of shape (4)
        The idx of P in codebook (i11, i12, i13, i2) 
    
    """
    # get equality channel of port
    UE_H = H@FRF
    # get digital matrix
    PMI,P,max_power,layer_power = get_PMI(UE_H,N1,N2,RI)
    P = P*np.sqrt(RI)
    return P,PMI,max_power,layer_power

def cal_action_matrix(H,Beam,V_ants,H_ants,action):
    # input H nSub*Port*Ants
    H_split = action[0]
    V_split = action[1]
    RI = action[2]
    Port = action[3]
    N1 = max(V_split,H_split)*int(Port/H_split/V_split/2)
    N2 = min(V_split,H_split)
    
    FRF = cal_analog_matrix(Beam,V_ants=V_ants,H_ants=H_ants,V_split=V_split,H_split=H_split,Port=Port)
    FBB,PMI,power,layer_power = cal_digital_matrix_PMI(H,FRF,N1,N2,RI)
    F = FRF@FBB
    
    assert np.abs(np.linalg.norm(F,'fro')**2 - RI) < 0.01,print("Norm error")
    
    return F,PMI,power

def cal_ue_matrix(H,F,N1,N2,RI):                                                              
    H_equal = H@F
    H_equal = H_equal.transpose(0,2,1)
    NRF = H_equal.shape[2]
    P,PMI,power,layer_power = cal_digital_matrix_PMI(H_equal,np.eye(NRF),N1,N2,RI)
    return P,PMI

def cal_opt_matrix(H,Nt,Nr,Ns):
    U, s, V = np.linalg.svd(H, full_matrices=True)
    V = V.conj().T
    Fopt = V[0:2*Nt,0:Ns]
    Wopt = U[0:2*Nr,0:Ns]
    return Fopt,Wopt

def OMP_dual(_Fopt,_NRF,_At):
    _FRF = np.empty((2*_At.shape[0],0))
    _Fres = _Fopt
    #   Nt = int(Fopt.shape[0]/2)
    Nt = int(_At[:,:].shape[0])
    for k in range(_NRF):
        PU_h = _At.conj().T@_Fres[0:Nt,:]
        ind_h = np.argmax(np.sum((np.absolute(PU_h))**2,axis=1))
        PU_v = _At.conj().T@_Fres[Nt:2*Nt,:]
        ind_v = np.argmax(np.sum((np.absolute(PU_v))**2,axis=1))
        _FRF = np.append(_FRF,np.vstack((_At[:,[ind_h]],_At[:,[ind_v]])) ,axis=1)
        _FBB = (np.linalg.pinv(_FRF))@_Fopt
        _Fres = (_Fopt-_FRF@_FBB)/np.linalg.norm((_Fopt-_FRF@_FBB),'fro')
        print(ind_h,ind_v)
    #   _Fres = (_Fopt-_FRF@_FBB)
    return _FRF,_FBB

def cal_report(H,F,SNR,nSub = 17,sinr_min = 0,sinr_max_diff = 15,tp_sub = False):
    #TODO compare different sinr_min
    """cal report form Channel and Precode matrix

    Parameters
    ----------
    H : 3-D array of shape (nSub* UE_ants* Ants_all_2pol)
        Channel
    F : 2-D array of shape (Ants_all_2pol* nStreams)
        Precode matrix at BS
    SNR : float
        SNR (dB)
    nSub : int, optional
        nSub, by default 17
    sinr_min : float, optional
        Set sinr to minimum value(such as -50) if sinr is small than sinr_min, by default 0
    sinr_max_diff : float, optional
        Cal the sinr diff between different stream, if the diff is large than sinr_max_diff, than set the smaller to minimum value, by default 15

    Returns
    -------
    TP : float
        Throughput
    MCS : 1-D array of shape (nSub)
        Modulation and Coding Schemes
    CQI : 1-D array of shape (nSub)
        Channel Quality Indicator,quantification of sinr
        0 for worst, 15 for bests
    sinr : 1-D array of shape (nSub)
        Sinr with optimization, optimization to 
    sinr_origin : 2-D array of shape (nSub* nStream)
        Sinr without optimization, just use for analyze 
        
    Detials:
    -------
    1. cal HF from H and F\n
    2. cal sinr by mmse\n
    3. optimize sinr to avoid unfairness with param sinr_min and sinr_max_diff, cal useful layer num\n
    4. find mcs from sinr2mcs_table with sinr\n
    5. cal TP and CQI\n
    
    """
    
    HF = H@F
    R = np.power(10,(-SNR/10))
    sinr,layer,sinr_origin = cal_sinr(HF,R,sinr_min,sinr_max_diff)
    mcs = np.searchsorted(sinr2mcs_table,sinr)
    TP = get_tp(mcs,layer,nSub,tp_sub)
    CQI = sinr2cqi(sinr)
    
    return TP,mcs,CQI,sinr,sinr_origin

def run_slot_result(H_srs,H,Beam,SNRs,action_space,H_ants=8,V_ants=4,sinr_min=-20,sinr_max_diff=15,nSub=17):
    gNB_PMIs = []
    gNB_TPs = []
    UE_PMIs = []
    UE_TPs = []
    UE_CQIs = []
    for i in range(0,len(action_space)):
        action = action_space[i]
        gNB_F,gNB_PMI,gNB_power = cal_action_matrix(H_srs,Beam,V_ants,H_ants,action)
        UE_F,UE_PMI,UE_power = cal_action_matrix(H,Beam,V_ants,H_ants,action)
        gNB_TP_ac = []
        UE_TP_ac = []
        UE_CQI_ac = []
        for SNR in SNRs:
            gNB_TP,gNB_mcs,gNB_CQI,gNB_sinr,gNB_sinr_origin = cal_report(H,gNB_F,SNR,nSub = nSub,sinr_min = sinr_min ,sinr_max_diff = sinr_max_diff,tp_sub=True)
            UE_TP,UE_mcs,UE_CQI,UE_sinr,UE_sinr_origin = cal_report(H,gNB_F,SNR,nSub = nSub,sinr_min = sinr_min ,sinr_max_diff = sinr_max_diff,tp_sub=True)
            gNB_TP_ac.append(gNB_TP)
            UE_TP_ac.append(UE_TP)
            UE_CQI_ac.append(UE_CQI)
        gNB_PMIs.append(gNB_PMI)
        UE_PMIs.append(UE_PMI)
        gNB_TPs.append(gNB_TP_ac)
        UE_TPs.append(UE_TP_ac)
        UE_CQIs.append(UE_CQI_ac)
    gNB_PMIs = np.array(gNB_PMIs)
    gNB_TPs = np.array(gNB_TPs)
    UE_PMIs = np.array(UE_PMIs)
    UE_TPs = np.array(UE_TPs)
    UE_CQIs = np.array(UE_CQIs)
    return gNB_PMIs,gNB_TPs,UE_PMIs,UE_TPs,UE_CQIs

def cal_se(H,SNR,F,W = None):
    SNR = 10**(SNR/10)
    if(W is None):
        Ns = H.shape[0]
        SE = np.log2(np.linalg.det(np.eye(Ns)+SNR/Ns*np.real(H@F@(F.conj().T)@(H.conj().T))))
    else:
        Heff1 = W.T@H@F
        Ns = W.shape[-1]
        # SE = np.log2(np.linalg.det(np.eye(Ns)+SNR/Ns*np.real(np.linalg.pinv(W)@H@<EMAIL>().<EMAIL>().T@W)))
        SE = np.log2(np.linalg.det(np.eye(Ns)+SNR/Ns*np.linalg.pinv(np.real(W.conj().T@W))*np.real(<EMAIL>().T)))
    return SE

def cal_se_sub(H,SNR,F,W):
    nSub = H.shape[0]
    SE_all = np.zeros(nSub)
    for sub in range(0,nSub):
        H_sub = H[sub]
        SE_all[sub] = cal_se(H_sub,SNR,F,W)
    return SE_all

def cal_se_auto(H,SNRs,F,W = None): 
    SNRs = 10**(SNRs/10)
    ntime = H.shape[0]
    nSub = H.shape[1]
    nSNR = SNRs.shape[0]
    SE_all = np.zeros((ntime,nSub,nSNR))
    for i in range(0,ntime):
        for j in range(0,nSub):
            H_sub = H[i,j]
            if(len(F.shape) == 3): #has suv
                F_sub = F[j]
            else:
                F_sub = F
            if(W is None):
                Ns = H_sub.shape[0]
                cal_matrix = np.real(H_sub@F_sub@(F_sub.conj().T)@(H_sub.conj().T))
            else:
                if(len(W.shape) == 3):
                    W_sub = W[j]
                else:
                    W_sub = W
                Heff1 = W_sub.T@H_sub@F_sub
                Ns = W.shape[-1]
                cal_matrix = np.linalg.pinv(np.real(W.conj().T@W))*np.real(<EMAIL>().T)
            for snr_idx in range(0,nSNR):
                SNR = SNRs[snr_idx]
                SE = np.log2(np.linalg.det(np.eye(Ns)+SNR/Ns*cal_matrix))
                SE_all[i,j,snr_idx] = SE
    return SE_all

def cal_long_se(H,coma,beam_tti=2,time=4000,gNB_tti=2):
    max_beam = int(time/beam_tti)
    beam_idxs = [i for i in range(0,max_beam)]
    se_long = []
    for beam_idx in beam_idxs:
        time_tti = beam_tti*beam_idx//gNB_tti
        F = update_svd_Beam(coma[time_tti][0],n=1) #update_from_coma
        F_2pol = np.kron(np.eye(2),F)

        actual_time = beam_tti*beam_idx
        se = cal_se_auto(H[actual_time:actual_time+beam_tti],np.arange(-30,35,5),F_2pol)
        se_long.append(se)
        # se_long1 = np.array(se_long1)
        # se
    se_long = np.array(se_long)
    return se_long

def get_action_choose(H,action_space = action_space,H_ants=8,V_ants=2,nSub=17):
    Beam,power,idx = cal_beam_method1(H,V_ants,H_ants)
    PMIs = dict()
    for action_idx in range(0,len(action_space)):
        action = action_space[action_idx]
        F,PMI,power = cal_action_matrix(H,Beam,V_ants,H_ants,action)
        PMIs[action_space] = PMI
    return Beam,power,idx,PMIs

def reduce_action(data,action_map):
    panel_data = []
    for i in range(len(action_map)):
        name = list(action_map.keys())[i]
        idx = action_map[name]
        panel_data.append(data[idx])
    return panel_data

def expand_action(data,action_map):
    result = []
    for i in range(len(action_map)):
        name = list(action_map.keys())[i]
        idx = action_map[name]
        for id in idx:
            result.append(data[i])
    return result

def recover_F_from_save(Beam):
    pass

def get_best_action(H,SNR,V_ants=4,H_ants=8,action_space = action_space,nSub=17):
    mcs_max = None
    TP_max = 0
    sinr_max = None
    CQI_max = None
    beam_best = None
    beam_power = None
    action_max = 0
    TPs = list()
    sinrs = list()
    PMIs = list()
    Beam,power,idx = cal_beam_method1(H,V_ants,H_ants)
    for i in range(0,len(action_space)):
        action = action_space[i]
        F,PMI,power = cal_action_matrix(H,Beam,V_ants,H_ants,action)
        TP,mcs,CQI,sinr,sinr_origin = cal_report(H,F,SNR,nSub = 17,sinr_min = -20,sinr_max_diff = 15)
        TPs.append(TP)
        sinrs.append(sinr)
        PMIs.append(PMI)
        if(TP > TP_max):
            TP_max = TP
            mcs_max = mcs
            sinr_max = sinr
            CQI_max = CQI
            action_max = i
            beam_best = idx
            beam_power = power
    return action_max,TP_max,mcs_max,sinr_max,CQI_max,TPs,beam_best,beam_power,sinrs,PMIs

def cal_sinr(HP,R,sinr_min = 0,sinr_max_diff = 15):
    sinr = get_sinr(HP.transpose(0,2,1),R)
    sinr_origin = np.copy(sinr)
    sinr[sinr < sinr_min] = -50
    if sinr.shape[1] > 1:
        sinr[(sinr[:,0] - sinr[:,1] > sinr_max_diff),1] = -50
        sinr[(sinr[:,1] - sinr[:,0] > sinr_max_diff),0] = -50
    if sinr.shape[1] > 3:
        sinr[(sinr[:,2] - sinr[:,3] > sinr_max_diff),3] = -50
        sinr[(sinr[:,3] - sinr[:,2] > sinr_max_diff),2] = -50
    layer = np.sum(sinr > -40,axis = 1)
    layer = np.clip(layer,1,4)
    mi = sinr2mi(sinr)
    mi[mi<0.0014] = 0 #min_mi = 0.0013
    avg_mi = np.sum(mi,axis=1)/layer
    sinr_avg = mi2sinr(avg_mi)
    return sinr_avg,layer,sinr_origin

def get_tp_par():
    # Init PRB config
    nPRB = 272
    nRBG = 17 
    nBS_RF_CHAIN = 16
    PRBpreSB = [nBS_RF_CHAIN for i in range(nRBG-1)] + [nPRB-nBS_RF_CHAIN*(nRBG-1)]  # PRB of each subband
    nPRB_PDSCH = 156
    nREAvg = min(156,  nPRB_PDSCH)
    return PRBpreSB,nREAvg

def get_tp(mcs,layer,nSub,sub=False):
    '''
        get a list of UE throughput
    '''
    PRBpreSB,nREAvg = get_tp_par()
    assert len(PRBpreSB) == nSub
    if sub: TP = []
    else:   TP = 0.
    for band in range(nSub):
        if(len(mcs) == nSub):
            mcs_sub = mcs[band]
            layer_sub = layer[band]
        else:
            mcs_sub = mcs[0]
            layer_sub = layer[0]
        if sub: TP.append(TbCalc(mcs_sub, layer_sub, PRBpreSB[band], nREAvg))
        else:   TP += TbCalc(mcs_sub, layer_sub, PRBpreSB[band], nREAvg)
    return TP

def restore_H(H,path): #restore H as Beam,use for draw
    Ants = H.shape[2]
    data1 = np.real(H)
    data2 = np.imag(H)
    data1 = np.expand_dims(data1,axis=3)
    data2 = np.expand_dims(data2,axis=3)
    data = np.concatenate((data1,data2),axis=3)
    data = data.transpose(2,0,1,3)
    data = data.reshape(Ants,-1)
    data = pd.DataFrame(data)
    data.to_csv(path,index=False,header=True)


if __name__ == '__main__':
    path = "./Data/H_re.txt"
    data = np.loadtxt(path)
    nUE = 1
    H_ants = 8
    V_ants = 2
    Port = 4
    Ants_all = H_ants*V_ants
    Ants_all_2pol = Ants_all*2
    print(data.shape)
    data = data.reshape(nUE,-1,17,Ants_all_2pol,4,2)
    data = data[:,:,:,:,:,0] + 1j*data[:,:,:,:,:,1]
    print(data.shape)
    time = data.shape[1]
    nSub = 17

    data = data.reshape(nUE,time,nSub,H_ants,V_ants,2,Port)
    data = data.transpose(1,0,2,6,5,4,3)
    print(data.shape)
    data = data.reshape(time,nUE,nSub,Port,Ants_all_2pol)
    print(data.shape)

    for i in range(0,5):
        action,TP,_,_,_ = get_best_action(data[i,0],R = 6.8e-14)
        print(action)
        print(TP)