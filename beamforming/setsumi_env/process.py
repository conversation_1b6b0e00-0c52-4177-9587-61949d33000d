import os,sys
sys.path.append(os.getcwd())
from setsumi_env.BM_functions import *
from setsumi_env.utils import *
from setsumi_env.utils import *
import numpy as np
import re
import pandas as pd
from collections import Counter
import plotly.graph_objects as go
from plotly.subplots import make_subplots

def pos_process(sim_par,cell_ue,H_freq,H_pg):
    Data_dir = sim_par.Data_dir
    action_ue_all = list()
    TP_ue_all = list()
    sinr_ue_all = list()
    mcs_ue_all = list()
    Beam_ue_all = list()
    Beam_power_ue_all = list()
    TPs_ue_all = list()
    sinrs_ue_all = list()
    PMIs_ue_all = list()
    R_actual_all = list()
    SNRs = np.arange(-30,35,5)

    for SNR in SNRs:
        action_ue = list()
        TP_ue = list()
        sinr_ue = list()
        mcs_ue = list()
        Beam_ue = list()
        Beam_power_ue = list()
        TPs_ue = list()
        sinrs_ue = list()
        PMIs_ue = list()
        R_actual = list()
        for ue_idx in range(0,sim_par.nUE):
        # for ue_idx in range(0,20):
            # for i in range(1,21):
                cell_idx = cell_ue[ue_idx] 
                bs_idx = int(cell_idx/3)
                choose_time = 0 #choose_time_0
                H = H_freq[ue_idx][choose_time]
                pg_dB = H_pg[ue_idx][choose_time]
                pg_data = np.sqrt(np.power(10,(pg_dB/10)))
                H = H/pg_data/np.sqrt(2)
                # mcs,TP,sinr,CQI,beam_idx,sinr_origin = cal_action_report(H,8,R,H_ants=Sim_par.H_ants,V_ants=Sim_par.V_ants,nSub=Sim_par.nSub)
                # print(np.max(sinr_origin),np.min(sinr_origin),Sim_par.cm_name[ue_idx][bs_idx])
                action,TP,mcs,sinr,CQI,TPs,Beam,Beam_power,sinrs,PMIs = get_best_action(H,SNR = SNR,V_ants=sim_par.V_ants,H_ants=sim_par.H_ants,nSub=sim_par.nSub)
                action_ue.append(action)
                TP_ue.append(TP)
                sinr_ue.append(sinr)
                mcs_ue.append(mcs)
                Beam_ue.append(Beam)
                Beam_power_ue.append(Beam_power)
                TPs_ue.append(TPs)
                sinrs_ue.append(sinrs)
                PMIs_ue.append(PMIs)
                # print(sinr[0])
                # print("action is %.2d,TP is %.1f,mcs is %.1f,Beam choose %d and %d,scene is %s" % (action,TP,np.average(mcs),Beam[0],Beam[1],Sim_par.cm_name[ue_idx][bs_idx][16:]))
                # print("HV best TP is %.1f,VH best TP is %.1f,origin best TP is %.1f,multile beam best TP is %.1f" %(TP1,TP2,TP3,TP4))
                # print(TPs)
                # print(sinr)
        action_ue_all.append(action_ue)
        TP_ue_all.append(TP_ue)
        sinr_ue_all.append(sinr_ue)
        mcs_ue_all.append(mcs_ue)
        Beam_ue_all.append(Beam_ue)
        Beam_power_ue_all.append(Beam_power_ue)
        TPs_ue_all.append(TPs_ue)
        sinrs_ue_all.append(sinrs_ue)
        PMIs_ue_all.append(PMIs_ue)
        print("SNR %d process OK" % SNR)
        # idx = np.argmax(np.array(TPs))
    action_ue_all = np.array(action_ue_all)
    TP_ue_all = np.array(TP_ue_all)
    sinr_ue_all = np.array(sinr_ue_all)
    mcs_ue_all = np.array(mcs_ue_all)
    Beam_ue_all = np.array(Beam_ue_all)
    Beam_power_ue_all = np.array(Beam_power_ue_all)
    TPs_ue_all = np.array(TPs_ue_all)
    sinrs_ue_all = np.array(sinrs_ue_all)
    PMIs_ue_all = np.array(PMIs_ue_all)

    result = dict()
    result['action'] = action_ue_all
    result['TP'] = TP_ue_all
    result['sinr'] = sinr_ue_all
    result['mcs'] = mcs_ue_all
    result['Beam'] = Beam_ue_all
    result['Beam_power'] = Beam_power_ue_all
    result['TPs'] = TPs_ue_all
    result['sinrs'] = sinrs_ue_all
    result['PMIs'] = PMIs_ue_all
    np.savez((Data_dir+"result.npz"),result)

    return result

def all_process(Data_dir):
    print(Data_dir)
    print("pre process begin")
    sim_par = Sim_Log(Data_dir)
    print(sim_par.seed)
    if not os.path.exists(Data_dir+"data_redo.npz"):
        pre_process(Data_dir)
    data_redo = np.load(Data_dir+"data_redo.npz",allow_pickle=True)['arr_0'].item()
    H_time = data_redo['H_time']
    H_freq = data_redo['H_freq']
    H_pg = data_redo['H_pg']
    cell_ue = data_redo['cell_ue']
    bs_ue = data_redo['bs_ue']
    loc = data_redo['loc']

    print("pos process begin")
    result = pos_process(sim_par,cell_ue,H_freq,H_pg)
    print("all process finish\n")
    
    return result

def all_process_batch(dir_path):
    for root,dirs,files in os.walk(dir_path):
        if len(files) > 0 and root != dir_path:
            Data_dir = root + '/'
            all_process(Data_dir)

def get_result(sim_par):
    if not os.path.exists(sim_par.Data_dir+"result.npz"):
        result = all_process(sim_par.Data_dir)
    else:
        result = np.load(sim_par.Data_dir+"result.npz",allow_pickle=True)['arr_0'].item()        
    sim_par.result = result

def get_data(sim_par,clear_time=False):
    if not os.path.exists(sim_par.Data_dir+"data_redo.npz"):
        pre_process(sim_par.Data_dir,~clear_time)
    data_redo = np.load(sim_par.Data_dir+"data_redo.npz",allow_pickle=True)['arr_0'].item()
    sim_par.data = data_redo
    if(clear_time):
        sim_par.data['H_time'] = list()


if __name__ == '__main__':


    print("process scenario")
    all_process("./Data/test/test_add/")
    print("\nprocess scenario test2 OK\n")

    # print("process scenario test3")
    # all_process_batch("./Data/scenario_test3/")
    # print("\nprocess scenario test3 OK\n")

    # print("process scenario test H8V4 05")
    # all_process_batch("./Data/scenario_test_H8V4_05/")
    # print("\nprocess scenario test H8V4 05 OK\n")

    # print("process scenario test H8V2")
    # all_process_batch("./Data/scenario_test_H8V2/")
    # print("\nprocess scenario test H8V2 OK\n")

    # print("process scenario test1")
    # all_process_batch("./Data/scenario_test1/")
    # print("\nprocess scenario test1 OK\n")