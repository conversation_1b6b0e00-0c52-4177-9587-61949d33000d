from bdb import set_trace
import os
#import sys
#sys.path.append(os.getcwd())
import numpy as np
import torch
import argparse
from td3 import utils,TD3
from Beam_env.BMenv import BMenv
from tensorboardX import SummaryWriter
import datetime
import random
import pdb


def Fla_state(state):#state扁平化 [nUE*(18+24+17*3)]
    nUE = state[0].shape[0]
    Beam_feature = torch.flatten(torch.FloatTensor(state[0])).repeat(3)
    PMI = torch.flatten(torch.FloatTensor(state[1])).repeat(6)
    CQI = torch.flatten(torch.FloatTensor(state[2]))
    RI = torch.flatten(torch.FloatTensor(state[3]))
    TP = torch.flatten(torch.FloatTensor(state[4]))
    state = torch.cat((Beam_feature,PMI,CQI,RI,TP),0)
    state = state.reshape(nUE,-1)
    return state

def eval_policy(ue,nUE,agent,bmenv):   
    avg_reward = 0.
    eval_episodes = 10
    agent.actor.eval()
    for _ in range(eval_episodes):
        state = Fla_state(bmenv.get_state_batch(nUE,np.zeros_like(ue),ue,np.ones_like(ue)*3))
        #done = False
        episode_time = 0
        while not episode_time % 400 == 0:

            action_idx = torch.argmax((agent.select_action(state)),dim = 1)
            action = torch.zeros([nUE,3])
            a = 0
            for t in action_idx:
                action[a] = torch.tensor(bmenv.action_space[t])
                a +=1
            state = Fla_state(bmenv.get_state_batch(nUE,np.ones_like(ue)*0,ue,action_idx))
            reward = bmenv.reward
            avg_reward += reward
            episode_time += 1

    avg_reward /= eval_episodes
    print("---------------------------------------")
    print(f"Evaluation over {eval_episodes} episodes: {avg_reward:.3f}")
    print("---------------------------------------")
    agent.actor.train()
    return avg_reward


if __name__ == "__main__":
    
    parser = argparse.ArgumentParser()
    parser.add_argument("--seed", default=777, type=int)              
    parser.add_argument("--start_timesteps", default=25e2, type=int)
    parser.add_argument("--eval_freq", default=5e2, type=int)       
    parser.add_argument("--max_timesteps", default=1e5, type=int)  
    parser.add_argument("--expl_noise", default=0.1)                
    parser.add_argument("--batch_size", default=256, type=int)      
    parser.add_argument("--discount", default=0.99)                 
    parser.add_argument("--tau", default=0.005)                    
    parser.add_argument("--policy_noise", default=0.2)             
    parser.add_argument("--noise_clip", default=0.5)                
    parser.add_argument("--policy_freq", default=2, type=int)       
    parser.add_argument("--save_model", action="store_true")       
    parser.add_argument("--load_model", default="")
    parser.add_argument('--cell', type=int, default=0) 
    parser.add_argument('--visual', type=bool, default=True)                
    args = parser.parse_args()

    file_name = f"{str(datetime.datetime.now())}__{args.seed}"

    TD3.seed_torch(args.seed)

    if not os.path.exists("./results"):
        os.makedirs("./results")

    if args.save_model and not os.path.exists("./models"):
        os.makedirs("./models")

    bmenv = BMenv(path="/data/BM/420UE_seed100_2000_0918_200height/",nSub = 17,max_time = 200,max_length = 800,seed = 777)
    ue = bmenv.cell[args.cell]
    height = bmenv.height
    ue = np.where(height > 190)
    ue = ue[0]
    nUE = len(ue)
    state = Fla_state(bmenv.get_state_batch(nUE,np.zeros_like(ue),ue,np.ones_like(ue)*3))
    state_dim = state.shape[1]
    action_dim = 3 
    date = str(str(datetime.datetime.now())).split(' ')[0]
    time = str(datetime.datetime.now()).split(' ')[1].split(':')
    comment = f"TD3"+f"-{date[-5:]}-{time[0]}-{time[1]}-{args.batch_size}batch_size-{args.seed}seed-{args.cell}cell"
    print(comment)
    ime_tag = f"{date[-5:]}-{time[0]}-{time[1]}"
    if args.visual:
       writer = SummaryWriter(logdir = os.path.join('runs', comment))

    agent = TD3.TD3(
        state_dim = state_dim,
        action_dim = action_dim,
        nUE=nUE,
        discount = args.discount,
        tau = args.tau,
        policy_noise = args.policy_noise,
        noise_clip = args.noise_clip,
        policy_freq = args.policy_freq
    )

    if args.load_model != "":
        policy_file = file_name if args.load_model == "default" else args.load_model
        agent.load(f"./models/{policy_file}")
    
    replay_buffer = utils.ReplayBuffer(state_dim, action_dim, nUE)
    #pdb.set_trace()

    evaluations = [eval_policy(ue,nUE,agent,bmenv)]

    #reset
    state = Fla_state(bmenv.get_state_batch(nUE,np.zeros_like(ue),ue,np.ones_like(ue)*3))
    done = False
    episode_reward = 0
    episode_timesteps = 0
    episode_num = 0

    for t in range(int(args.max_timesteps)):
        
        episode_timesteps += 1

        # Select action randomly or according to policy
        if t < args.start_timesteps:
            possibility = torch.rand(nUE, 8)
            noise = (torch.randn_like(possibility) * args.policy_noise).clamp(-args.noise_clip,args.noise_clip)
            
            action_idx = torch.argmax((possibility + noise).reshape(nUE,8),dim = 1)
            action = torch.zeros([nUE,3])
            a = 0
            for i in action_idx:
                action[a] = torch.tensor(bmenv.action_space[i])
                a +=1
            #action_idx = np.argmax(possibility + noise)
            #action = bmenv.action_space[action_idx]
            
            
        else:
            possibility =agent.select_action(state)
            noise = (torch.randn_like(possibility) * args.policy_noise).clamp(-args.noise_clip,args.noise_clip)
            possibility += noise
            action_idx = torch.argmax((possibility + noise).reshape(nUE,8),dim = 1)
            action = torch.zeros([nUE,3])
            for i in action_idx:
                a = 0
                action[a] = torch.tensor(bmenv.action_space[i])
            
            

        # Perform action
        next_state = Fla_state(bmenv.get_state_batch(nUE,np.ones_like(ue)*0,ue,action_idx))
        reward = bmenv.reward
        done = (episode_timesteps % 400 == 0)
        #pdb.set_trace()
        # Store data in replay buffer
        replay_buffer.add(state,possibility.cpu().data.numpy(), action.cpu().data.numpy(), next_state, reward, done)

        state = next_state
        episode_reward += reward

        # Train agent after collecting sufficient data
        if t >= args.start_timesteps:
            agent.train(replay_buffer, args.batch_size)

        if done: 
            # +1 to account for 0 indexing. +0 on ep_timesteps since it will increment +1 even if done=True
            print(f"Total T: {t+1} Episode Num: {episode_num+1} Episode T: {episode_timesteps} Reward: {episode_reward.mean()}")
            # Reset environment
            state = Fla_state(bmenv.get_state_batch(nUE,np.zeros_like(ue),ue,np.ones_like(ue)*3))
            done = False
            episode_reward = 0
            episode_timesteps = 0
            episode_num += 1 

        # Evaluate episode
        if (t + 1) % args.eval_freq == 0:
            evaluations.append(eval_policy(ue,nUE,agent,bmenv))
            np.save(f"./results/{file_name}", evaluations)
            if args.save_model: agent.save(f"./models/{file_name}")
    